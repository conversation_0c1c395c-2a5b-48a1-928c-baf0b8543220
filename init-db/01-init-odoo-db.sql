-- Initialize Odoo database
-- This script runs when the PostgreSQL container starts for the first time

-- Create the main database (already created by POSTGRES_DB env var)
-- CREATE DATABASE odoo_production OWNER odooprod;

-- Grant necessary permissions
GRANT ALL PRIVILEGES ON DATABASE odoo_production TO odooprod;

-- Create additional extensions that Odoo might need
\c odoo_production;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Set proper permissions for the user
ALTER USER odooprod CREATEDB;
ALTER USER odooprod SUPERUSER;
