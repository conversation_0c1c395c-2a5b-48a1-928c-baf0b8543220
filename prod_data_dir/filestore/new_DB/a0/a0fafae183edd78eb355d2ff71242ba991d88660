
/* /web_editor/static/lib/summernote/src/js/enable_summernote.js defined in bundle 'web_editor.assets_summernote' */
odoo.__enable_summernote__=true;;

/* <inline asset> defined in bundle 'web_editor.assets_summernote' */
(function(){"use strict";odoo.define('jquery',function(){return $;});var uniqId=0;odoo.__define__=window.define;window.define=function(id){if(!odoo.__enable_summernote__)return;var args=Array.prototype.slice.call(arguments);var factorie=args.pop();var id=args[0];if(id instanceof Array){var name="summernote_factorie_"+(++uniqId);odoo[name]=factorie;var head='';var fn='var fn = odoo.'+name+';\ndelete odoo.'+name+';\n';fn+='return fn(';for(var k=0;k<id.length;k++){head+='var a'+(++uniqId)+' = require("'+id[k]+'");\n';fn+='a'+uniqId+', ';}
fn+='null);';odoo.define(odoo.website_next_define,new Function('require',head+fn));}else{odoo.define(id,factorie);}};})();;

/* <inline asset> defined in bundle 'web_editor.assets_summernote' */
odoo.website_next_define='summernote/core/async';;

/* /web_editor/static/lib/summernote/src/js/core/async.js defined in bundle 'web_editor.assets_summernote' */
define('summernote/core/async',function(){var async=(function(){var readFileAsDataURL=function(file){return $.Deferred(function(deferred){$.extend(new FileReader(),{onload:function(e){var sDataURL=e.target.result;deferred.resolve(sDataURL);},onerror:function(){deferred.reject(this);}}).readAsDataURL(file);}).promise();};var createImage=function(sUrl,filename){return $.Deferred(function(deferred){var $img=$('<img>');$img.one('load',function(){$img.off('error abort');deferred.resolve($img);}).one('error abort',function(){$img.off('load').detach();deferred.reject($img);}).css({display:'none'}).appendTo(document.body).attr({'src':sUrl,'data-filename':filename});}).promise();};return{readFileAsDataURL:readFileAsDataURL,createImage:createImage};})();return async;});;

/* <inline asset> defined in bundle 'web_editor.assets_summernote' */
odoo.website_next_define='summernote/core/func';;

/* /web_editor/static/lib/summernote/src/js/core/func.js defined in bundle 'web_editor.assets_summernote' */
define('summernote/core/func',function(){var func=(function(){var eq=function(itemA){return function(itemB){return itemA===itemB;};};var eq2=function(itemA,itemB){return itemA===itemB;};var peq2=function(propName){return function(itemA,itemB){return itemA[propName]===itemB[propName];};};var ok=function(){return true;};var fail=function(){return false;};var not=function(f){return function(){return!f.apply(f,arguments);};};var and=function(fA,fB){return function(item){return fA(item)&&fB(item);};};var self=function(a){return a;};var idCounter=0;var uniqueId=function(prefix){var id=++idCounter+'';return prefix?prefix+id:id;};var rect2bnd=function(rect){var $document=$(document);return{top:rect.top+$document.scrollTop(),left:rect.left+$document.scrollLeft(),width:rect.right-rect.left,height:rect.bottom-rect.top};};var invertObject=function(obj){var inverted={};for(var key in obj){if(obj.hasOwnProperty(key)){inverted[obj[key]]=key;}}
return inverted;};var namespaceToCamel=function(namespace,prefix){prefix=prefix||'';return prefix+namespace.split('.').map(function(name){return name.substring(0,1).toUpperCase()+name.substring(1);}).join('');};return{eq:eq,eq2:eq2,peq2:peq2,ok:ok,fail:fail,self:self,not:not,and:and,uniqueId:uniqueId,rect2bnd:rect2bnd,invertObject:invertObject,namespaceToCamel:namespaceToCamel};})();return func;});;

/* <inline asset> defined in bundle 'web_editor.assets_summernote' */
odoo.website_next_define='summernote/core/agent';;

/* /web_editor/static/lib/summernote/src/js/core/agent.js defined in bundle 'web_editor.assets_summernote' */
define(['jquery'],function($){if(!Array.prototype.reduce){Array.prototype.reduce=function(callback){var t=Object(this),len=t.length>>>0,k=0,value;if(arguments.length===2){value=arguments[1];}else{while(k<len&&!(k in t)){k++;}
if(k>=len){throw new TypeError('Reduce of empty array with no initial value');}
value=t[k++];}
for(;k<len;k++){if(k in t){value=callback(value,t[k],k,t);}}
return value;};}
if('function'!==typeof Array.prototype.filter){Array.prototype.filter=function(func){var t=Object(this),len=t.length>>>0;var res=[];var thisArg=arguments.length>=2?arguments[1]:void 0;for(var i=0;i<len;i++){if(i in t){var val=t[i];if(func.call(thisArg,val,i,t)){res.push(val);}}}
return res;};}
if(!Array.prototype.map){Array.prototype.map=function(callback,thisArg){var T,A,k;if(this===null){throw new TypeError(' this is null or not defined');}
var O=Object(this);var len=O.length>>>0;if(typeof callback!=='function'){throw new TypeError(callback+' is not a function');}
if(arguments.length>1){T=thisArg;}
A=new Array(len);k=0;while(k<len){var kValue,mappedValue;if(k in O){kValue=O[k];mappedValue=callback.call(T,kValue,k,O);A[k]=mappedValue;}
k++;}
return A;};}
var isSupportAmd=typeof define==='function'&&define.amd;var isFontInstalled=function(fontName){var testFontName=fontName==='Comic Sans MS'?'Courier New':'Comic Sans MS';var $tester=$('<div>').css({position:'absolute',left:'-9999px',top:'-9999px',fontSize:'200px'}).text('mmmmmmmmmwwwwwww').appendTo(document.body);var originalWidth=$tester.css('fontFamily',testFontName).width();var width=$tester.css('fontFamily',fontName+','+testFontName).width();$tester.remove();return originalWidth!==width;};var userAgent=navigator.userAgent;var isMSIE=/MSIE|Trident/i.test(userAgent);var browserVersion;if(isMSIE){var matches=/MSIE (\d+[.]\d+)/.exec(userAgent);if(matches){browserVersion=parseFloat(matches[1]);}
matches=/Trident\/.*rv:([0-9]{1,}[\.0-9]{0,})/.exec(userAgent);if(matches){browserVersion=parseFloat(matches[1]);}}
var agent={isMac:navigator.appVersion.indexOf('Mac')>-1,isMSIE:isMSIE,isFF:/firefox/i.test(userAgent),isWebkit:/webkit/i.test(userAgent),isSafari:/safari/i.test(userAgent),browserVersion:browserVersion,jqueryVersion:parseFloat($.fn.jquery),isSupportAmd:isSupportAmd,hasCodeMirror:isSupportAmd?require.specified('CodeMirror'):!!window.CodeMirror,isFontInstalled:isFontInstalled,isW3CRangeSupport:!!document.createRange};return agent;});;

/* <inline asset> defined in bundle 'web_editor.assets_summernote' */
odoo.website_next_define='summernote/core/list';;

/* /web_editor/static/lib/summernote/src/js/core/list.js defined in bundle 'web_editor.assets_summernote' */
define(['summernote/core/func'],function(func){var list=(function(){var head=function(array){return array[0];};var last=function(array){return array[array.length-1];};var initial=function(array){return array.slice(0,array.length-1);};var tail=function(array){return array.slice(1);};var find=function(array,pred){for(var idx=0,len=array.length;idx<len;idx++){var item=array[idx];if(pred(item)){return item;}}};var all=function(array,pred){for(var idx=0,len=array.length;idx<len;idx++){if(!pred(array[idx])){return false;}}
return true;};var indexOf=function(array,item){return $.inArray(item,array);};var contains=function(array,item){return indexOf(array,item)!==-1;};var sum=function(array,fn){fn=fn||func.self;return array.reduce(function(memo,v){return memo+fn(v);},0);};var from=function(collection){var result=[],idx=-1,length=collection.length;while(++idx<length){result[idx]=collection[idx];}
return result;};var clusterBy=function(array,fn){if(!array.length){return[];}
var aTail=tail(array);return aTail.reduce(function(memo,v){var aLast=last(memo);if(fn(last(aLast),v)){aLast[aLast.length]=v;}else{memo[memo.length]=[v];}
return memo;},[[head(array)]]);};var compact=function(array){var aResult=[];for(var idx=0,len=array.length;idx<len;idx++){if(array[idx]){aResult.push(array[idx]);}}
return aResult;};var unique=function(array){var results=[];for(var idx=0,len=array.length;idx<len;idx++){if(!contains(results,array[idx])){results.push(array[idx]);}}
return results;};var next=function(array,item){var idx=indexOf(array,item);if(idx===-1){return null;}
return array[idx+1];};var prev=function(array,item){var idx=indexOf(array,item);if(idx===-1){return null;}
return array[idx-1];};return{head:head,last:last,initial:initial,tail:tail,prev:prev,next:next,find:find,contains:contains,all:all,sum:sum,from:from,clusterBy:clusterBy,compact:compact,unique:unique};})();return list;});;

/* <inline asset> defined in bundle 'web_editor.assets_summernote' */
odoo.website_next_define='summernote/core/dom';;

/* /web_editor/static/lib/summernote/src/js/core/dom.js defined in bundle 'web_editor.assets_summernote' */
define(['summernote/core/func','summernote/core/list','summernote/core/agent'],function(func,list,agent){var NBSP_CHAR=String.fromCharCode(160);var ZERO_WIDTH_NBSP_CHAR='\ufeff';var dom=(function(){var isEditable=function(node){return node&&$(node).hasClass('note-editable');};var isControlSizing=function(node){return node&&$(node).hasClass('note-control-sizing');};var buildLayoutInfo=function($editor){var makeFinder;if($editor.hasClass('note-air-editor')){var id=list.last($editor.attr('data-note-id').split('-'));makeFinder=function(sIdPrefix){return function(){return $(sIdPrefix+id);};};return{editor:function(){return $editor;},holder:function(){return $editor.data('holder');},editable:function(){return $editor;},popover:makeFinder('#note-popover-'),handle:makeFinder('#note-handle-'),dialog:makeFinder('#note-dialog-')};}else{makeFinder=function(className,$base){$base=$base||$editor;return function(){return $base.find(className);};};var options=$editor.data('options');var $dialogHolder=(options&&options.dialogsInBody)?$(document.body):null;return{editor:function(){return $editor;},holder:function(){return $editor.data('holder');},dropzone:makeFinder('.note-dropzone'),toolbar:makeFinder('.note-toolbar'),editable:makeFinder('.note-editable'),codable:makeFinder('.note-codable'),statusbar:makeFinder('.note-statusbar'),popover:makeFinder('.note-popover'),handle:makeFinder('.note-handle'),dialog:makeFinder('.note-dialog',$dialogHolder)};}};var makeLayoutInfo=function(descendant){var $target=$(descendant).closest('.note-editor, .note-air-editor, .note-air-layout');if(!$target.length){return null;}
var $editor;if($target.is('.note-editor, .note-air-editor')){$editor=$target;}else{$editor=$('[data-note-id="'+list.last($target.attr('id').split('-'))+'"]');}
return buildLayoutInfo($editor);};var makePredByNodeName=function(nodeName){nodeName=nodeName.toUpperCase();return function(node){return node&&node.nodeName.toUpperCase()===nodeName;};};var isText=function(node){return node&&node.nodeType===3;};var isVoid=function(node){return node&&/^BR|^IMG|^HR|^IFRAME|^BUTTON/.test(node.nodeName.toUpperCase());};var isPara=function(node){if(isEditable(node)){return false;}
return node&&/^DIV|^P|^LI|^H[1-7]/.test(node.nodeName.toUpperCase());};var isLi=makePredByNodeName('LI');var isPurePara=function(node){return isPara(node)&&!isLi(node);};var isTable=makePredByNodeName('TABLE');var isInline=function(node){return!isBodyContainer(node)&&!isList(node)&&!isHr(node)&&!isPara(node)&&!isTable(node)&&!isBlockquote(node);};var isList=function(node){return node&&/^UL|^OL/.test(node.nodeName.toUpperCase());};var isHr=makePredByNodeName('HR');var isCell=function(node){return node&&/^TD|^TH/.test(node.nodeName.toUpperCase());};var isBlockquote=makePredByNodeName('BLOCKQUOTE');var isBodyContainer=function(node){return isCell(node)||isBlockquote(node)||isEditable(node);};var isAnchor=makePredByNodeName('A');var isParaInline=function(node){return isInline(node)&&!!ancestor(node,isPara);};var isBodyInline=function(node){return isInline(node)&&!ancestor(node,isPara);};var isBody=makePredByNodeName('BODY');var isClosestSibling=function(nodeA,nodeB){return nodeA.nextSibling===nodeB||nodeA.previousSibling===nodeB;};var withClosestSiblings=function(node,pred){pred=pred||func.ok;var siblings=[];if(node.previousSibling&&pred(node.previousSibling)){siblings.push(node.previousSibling);}
siblings.push(node);if(node.nextSibling&&pred(node.nextSibling)){siblings.push(node.nextSibling);}
return siblings;};var blankHTML=agent.isMSIE&&agent.browserVersion<11?'&nbsp;':'<br>';var nodeLength=function(node){if(isText(node)){return node.nodeValue.length;}
return node.childNodes.length;};var isEmpty=function(node){var len=nodeLength(node);if(len===0){return true;}else if(!isText(node)&&len===1&&node.innerHTML===blankHTML){return true;}else if(list.all(node.childNodes,isText)&&node.innerHTML===''){return true;}
return false;};var paddingBlankHTML=function(node){if(!isVoid(node)&&!nodeLength(node)){node.innerHTML=blankHTML;}};var ancestor=function(node,pred){while(node){if(pred(node)){return node;}
if(isEditable(node)){break;}
node=node.parentNode;}
return null;};var singleChildAncestor=function(node,pred){node=node.parentNode;while(node){if(nodeLength(node)!==1){break;}
if(pred(node)){return node;}
if(isEditable(node)){break;}
node=node.parentNode;}
return null;};var listAncestor=function(node,pred){pred=pred||func.fail;var ancestors=[];ancestor(node,function(el){if(!isEditable(el)){ancestors.push(el);}
return pred(el);});return ancestors;};var lastAncestor=function(node,pred){var ancestors=listAncestor(node);return list.last(ancestors.filter(pred));};var commonAncestor=function(nodeA,nodeB){var ancestors=listAncestor(nodeA);for(var n=nodeB;n;n=n.parentNode){if($.inArray(n,ancestors)>-1){return n;}}
return null;};var listPrev=function(node,pred){pred=pred||func.fail;var nodes=[];while(node){if(pred(node)){break;}
nodes.push(node);node=node.previousSibling;}
return nodes;};var listNext=function(node,pred){pred=pred||func.fail;var nodes=[];while(node){if(pred(node)){break;}
nodes.push(node);node=node.nextSibling;}
return nodes;};var listDescendant=function(node,pred){var descendents=[];pred=pred||func.ok;(function fnWalk(current){if(node!==current&&pred(current)){descendents.push(current);}
for(var idx=0,len=current.childNodes.length;idx<len;idx++){fnWalk(current.childNodes[idx]);}})(node);return descendents;};var wrap=function(node,wrapperName){var parent=node.parentNode;var wrapper=$('<'+wrapperName+'>')[0];parent.insertBefore(wrapper,node);wrapper.appendChild(node);return wrapper;};var insertAfter=function(node,preceding){var next=preceding.nextSibling,parent=preceding.parentNode;if(next){parent.insertBefore(node,next);}else{parent.appendChild(node);}
return node;};var appendChildNodes=function(node,aChild){$.each(aChild,function(idx,child){node.appendChild(child);});return node;};var isLeftEdgePoint=function(point){return point.offset===0;};var isRightEdgePoint=function(point){return point.offset===nodeLength(point.node);};var isEdgePoint=function(point){return isLeftEdgePoint(point)||isRightEdgePoint(point);};var isLeftEdgeOf=function(node,ancestor){while(node&&node!==ancestor){if(position(node)!==0){return false;}
node=node.parentNode;}
return true;};var isRightEdgeOf=function(node,ancestor){while(node&&node!==ancestor){if(position(node)!==nodeLength(node.parentNode)-1){return false;}
node=node.parentNode;}
return true;};var isLeftEdgePointOf=function(point,ancestor){return isLeftEdgePoint(point)&&isLeftEdgeOf(point.node,ancestor);};var isRightEdgePointOf=function(point,ancestor){return isRightEdgePoint(point)&&isRightEdgeOf(point.node,ancestor);};var position=function(node){var offset=0;while((node=node.previousSibling)){offset+=1;}
return offset;};var hasChildren=function(node){return!!(node&&node.childNodes&&node.childNodes.length);};var prevPoint=function(point,isSkipInnerOffset){var node,offset;if(point.offset===0){if(isEditable(point.node)){return null;}
node=point.node.parentNode;offset=position(point.node);}else if(hasChildren(point.node)){node=point.node.childNodes[point.offset-1];offset=nodeLength(node);}else{node=point.node;offset=isSkipInnerOffset?0:point.offset-1;}
return{node:node,offset:offset};};var nextPoint=function(point,isSkipInnerOffset){var node,offset;if(nodeLength(point.node)===point.offset){if(isEditable(point.node)){return null;}
node=point.node.parentNode;offset=position(point.node)+1;}else if(hasChildren(point.node)){node=point.node.childNodes[point.offset];offset=0;}else{node=point.node;offset=isSkipInnerOffset?nodeLength(point.node):point.offset+1;}
return{node:node,offset:offset};};var isSamePoint=function(pointA,pointB){return pointA.node===pointB.node&&pointA.offset===pointB.offset;};var isVisiblePoint=function(point){if(isText(point.node)||!hasChildren(point.node)||isEmpty(point.node)){return true;}
var leftNode=point.node.childNodes[point.offset-1];var rightNode=point.node.childNodes[point.offset];if((!leftNode||isVoid(leftNode))&&(!rightNode||isVoid(rightNode))){return true;}
return false;};var prevPointUntil=function(point,pred){while(point){if(pred(point)){return point;}
point=prevPoint(point);}
return null;};var nextPointUntil=function(point,pred){while(point){if(pred(point)){return point;}
point=nextPoint(point);}
return null;};var isCharPoint=function(point){if(!isText(point.node)){return false;}
var ch=point.node.nodeValue.charAt(point.offset-1);return ch&&(ch!==' '&&ch!==NBSP_CHAR);};var walkPoint=function(startPoint,endPoint,handler,isSkipInnerOffset){var point=startPoint;while(point){handler(point);if(isSamePoint(point,endPoint)){break;}
var isSkipOffset=isSkipInnerOffset&&startPoint.node!==point.node&&endPoint.node!==point.node;point=nextPoint(point,isSkipOffset);}};var makeOffsetPath=function(ancestor,node){var ancestors=listAncestor(node,func.eq(ancestor));return ancestors.map(position).reverse();};var fromOffsetPath=function(ancestor,offsets){var current=ancestor;for(var i=0,len=offsets.length;i<len;i++){if(current.childNodes.length<=offsets[i]){current=current.childNodes[current.childNodes.length-1];}else{current=current.childNodes[offsets[i]];}}
return current;};var splitNode=function(point,options){var isSkipPaddingBlankHTML=options&&options.isSkipPaddingBlankHTML;var isNotSplitEdgePoint=options&&options.isNotSplitEdgePoint;if(isEdgePoint(point)&&(isText(point.node)||isNotSplitEdgePoint)){if(isLeftEdgePoint(point)){return point.node;}else if(isRightEdgePoint(point)){return point.node.nextSibling;}}
if(isText(point.node)){return point.node.splitText(point.offset);}else{var childNode=point.node.childNodes[point.offset];var clone=insertAfter(point.node.cloneNode(false),point.node);appendChildNodes(clone,listNext(childNode));if(!isSkipPaddingBlankHTML){paddingBlankHTML(point.node);paddingBlankHTML(clone);}
return clone;}};var splitTree=function(root,point,options){var ancestors=listAncestor(point.node,func.eq(root));if(!ancestors.length){return null;}else if(ancestors.length===1){return splitNode(point,options);}
return ancestors.reduce(function(node,parent){if(node===point.node){node=splitNode(point,options);}
return splitNode({node:parent,offset:node?dom.position(node):nodeLength(parent)},options);});};var splitPoint=function(point,isInline){var pred=isInline?isPara:isBodyContainer;var ancestors=listAncestor(point.node,pred);var topAncestor=list.last(ancestors)||point.node;var splitRoot,container;if(pred(topAncestor)){splitRoot=ancestors[ancestors.length-2];container=topAncestor;}else{splitRoot=topAncestor;container=splitRoot.parentNode;}
var pivot=splitRoot&&splitTree(splitRoot,point,{isSkipPaddingBlankHTML:isInline,isNotSplitEdgePoint:isInline});if(!pivot&&container===point.node){pivot=point.node.childNodes[point.offset];}
return{rightNode:pivot,container:container};};var create=function(nodeName){return document.createElement(nodeName);};var createText=function(text){return document.createTextNode(text);};var remove=function(node,isRemoveChild){if(!node||!node.parentNode){return;}
if(node.removeNode){return node.removeNode(isRemoveChild);}
var parent=node.parentNode;if(!isRemoveChild){var nodes=[];var i,len;for(i=0,len=node.childNodes.length;i<len;i++){nodes.push(node.childNodes[i]);}
for(i=0,len=nodes.length;i<len;i++){parent.insertBefore(nodes[i],node);}}
parent.removeChild(node);};var removeWhile=function(node,pred){while(node){if(isEditable(node)||!pred(node)){break;}
var parent=node.parentNode;remove(node);node=parent;}};var replace=function(node,nodeName){if(node.nodeName.toUpperCase()===nodeName.toUpperCase()){return node;}
var newNode=create(nodeName);if(node.style.cssText){newNode.style.cssText=node.style.cssText;}
appendChildNodes(newNode,list.from(node.childNodes));insertAfter(newNode,node);remove(node);return newNode;};var isTextarea=makePredByNodeName('TEXTAREA');var value=function($node,stripLinebreaks){var val=isTextarea($node[0])?$node.val():$node.html();if(stripLinebreaks){return val.replace(/[\n\r]/g,'');}
return val;};var html=function($node,isNewlineOnBlock){var markup=value($node);if(isNewlineOnBlock){var regexTag=/<(\/?)(\b(?!!)[^>\s]*)(.*?)(\s*\/?>)/g;markup=markup.replace(regexTag,function(match,endSlash,name){name=name.toUpperCase();var isEndOfInlineContainer=/^DIV|^TD|^TH|^P|^LI|^H[1-7]/.test(name)&&!!endSlash;var isBlockNode=/^BLOCKQUOTE|^TABLE|^TBODY|^TR|^HR|^UL|^OL/.test(name);return match+((isEndOfInlineContainer||isBlockNode)?'\n':'');});markup=$.trim(markup);}
return markup;};return{NBSP_CHAR:NBSP_CHAR,ZERO_WIDTH_NBSP_CHAR:ZERO_WIDTH_NBSP_CHAR,blank:blankHTML,emptyPara:'<p>'+blankHTML+'</p>',makePredByNodeName:makePredByNodeName,isEditable:isEditable,isControlSizing:isControlSizing,buildLayoutInfo:buildLayoutInfo,makeLayoutInfo:makeLayoutInfo,isText:isText,isVoid:isVoid,isPara:isPara,isPurePara:isPurePara,isInline:isInline,isBlock:func.not(isInline),isBodyInline:isBodyInline,isBody:isBody,isParaInline:isParaInline,isList:isList,isTable:isTable,isCell:isCell,isBlockquote:isBlockquote,isBodyContainer:isBodyContainer,isAnchor:isAnchor,isDiv:makePredByNodeName('DIV'),isLi:isLi,isBR:makePredByNodeName('BR'),isSpan:makePredByNodeName('SPAN'),isB:makePredByNodeName('B'),isU:makePredByNodeName('U'),isS:makePredByNodeName('S'),isI:makePredByNodeName('I'),isImg:makePredByNodeName('IMG'),isTextarea:isTextarea,isEmpty:isEmpty,isEmptyAnchor:func.and(isAnchor,isEmpty),isClosestSibling:isClosestSibling,withClosestSiblings:withClosestSiblings,nodeLength:nodeLength,isLeftEdgePoint:isLeftEdgePoint,isRightEdgePoint:isRightEdgePoint,isEdgePoint:isEdgePoint,isLeftEdgeOf:isLeftEdgeOf,isRightEdgeOf:isRightEdgeOf,isLeftEdgePointOf:isLeftEdgePointOf,isRightEdgePointOf:isRightEdgePointOf,prevPoint:prevPoint,nextPoint:nextPoint,isSamePoint:isSamePoint,isVisiblePoint:isVisiblePoint,prevPointUntil:prevPointUntil,nextPointUntil:nextPointUntil,isCharPoint:isCharPoint,walkPoint:walkPoint,ancestor:ancestor,singleChildAncestor:singleChildAncestor,listAncestor:listAncestor,lastAncestor:lastAncestor,listNext:listNext,listPrev:listPrev,listDescendant:listDescendant,commonAncestor:commonAncestor,wrap:wrap,insertAfter:insertAfter,appendChildNodes:appendChildNodes,position:position,hasChildren:hasChildren,makeOffsetPath:makeOffsetPath,fromOffsetPath:fromOffsetPath,splitTree:splitTree,splitPoint:splitPoint,create:create,createText:createText,remove:remove,removeWhile:removeWhile,replace:replace,html:html,value:value};})();return dom;});;

/* <inline asset> defined in bundle 'web_editor.assets_summernote' */
odoo.website_next_define='summernote/core/key';;

/* /web_editor/static/lib/summernote/src/js/core/key.js defined in bundle 'web_editor.assets_summernote' */
define(['summernote/core/list','summernote/core/func'],function(list,func){var key=(function(){var keyMap={'BACKSPACE':8,'TAB':9,'ENTER':13,'SPACE':32,'LEFT':37,'UP':38,'RIGHT':39,'DOWN':40,'NUM0':48,'NUM1':49,'NUM2':50,'NUM3':51,'NUM4':52,'NUM5':53,'NUM6':54,'NUM7':55,'NUM8':56,'B':66,'E':69,'I':73,'J':74,'K':75,'L':76,'R':82,'S':83,'U':85,'V':86,'Y':89,'Z':90,'SLASH':191,'LEFTBRACKET':219,'BACKSLASH':220,'RIGHTBRACKET':221};return{isEdit:function(keyCode){return list.contains([keyMap.BACKSPACE,keyMap.TAB,keyMap.ENTER,keyMap.SPACe],keyCode);},isMove:function(keyCode){return list.contains([keyMap.LEFT,keyMap.UP,keyMap.RIGHT,keyMap.DOWN],keyCode);},nameFromCode:func.invertObject(keyMap),code:keyMap};})();return key;});;

/* <inline asset> defined in bundle 'web_editor.assets_summernote' */
odoo.website_next_define='summernote/core/range';;

/* /web_editor/static/lib/summernote/src/js/core/range.js defined in bundle 'web_editor.assets_summernote' */
define(['summernote/core/agent','summernote/core/func','summernote/core/list','summernote/core/dom'],function(agent,func,list,dom){var range=(function(){var textRangeToPoint=function(textRange,isStart){var container=textRange.parentElement(),offset;var tester=document.body.createTextRange(),prevContainer;var childNodes=list.from(container.childNodes);for(offset=0;offset<childNodes.length;offset++){if(dom.isText(childNodes[offset])){continue;}
tester.moveToElementText(childNodes[offset]);if(tester.compareEndPoints('StartToStart',textRange)>=0){break;}
prevContainer=childNodes[offset];}
if(offset!==0&&dom.isText(childNodes[offset-1])){var textRangeStart=document.body.createTextRange(),curTextNode=null;textRangeStart.moveToElementText(prevContainer||container);textRangeStart.collapse(!prevContainer);curTextNode=prevContainer?prevContainer.nextSibling:container.firstChild;var pointTester=textRange.duplicate();pointTester.setEndPoint('StartToStart',textRangeStart);var textCount=pointTester.text.replace(/[\r\n]/g,'').length;while(textCount>curTextNode.nodeValue.length&&curTextNode.nextSibling){textCount-=curTextNode.nodeValue.length;curTextNode=curTextNode.nextSibling;}
var dummy=curTextNode.nodeValue;if(isStart&&curTextNode.nextSibling&&dom.isText(curTextNode.nextSibling)&&textCount===curTextNode.nodeValue.length){textCount-=curTextNode.nodeValue.length;curTextNode=curTextNode.nextSibling;}
container=curTextNode;offset=textCount;}
return{cont:container,offset:offset};};var pointToTextRange=function(point){var textRangeInfo=function(container,offset){var node,isCollapseToStart;if(dom.isText(container)){var prevTextNodes=dom.listPrev(container,func.not(dom.isText));var prevContainer=list.last(prevTextNodes).previousSibling;node=prevContainer||container.parentNode;offset+=list.sum(list.tail(prevTextNodes),dom.nodeLength);isCollapseToStart=!prevContainer;}else{node=container.childNodes[offset]||container;if(dom.isText(node)){return textRangeInfo(node,0);}
offset=0;isCollapseToStart=false;}
return{node:node,collapseToStart:isCollapseToStart,offset:offset};};var textRange=document.body.createTextRange();var info=textRangeInfo(point.node,point.offset);textRange.moveToElementText(info.node);textRange.collapse(info.collapseToStart);textRange.moveStart('character',info.offset);return textRange;};var WrappedRange=function(sc,so,ec,eo){this.sc=sc;this.so=so;this.ec=ec;this.eo=eo;var nativeRange=function(){if(agent.isW3CRangeSupport){var w3cRange=document.createRange();w3cRange.setStart(sc,so);w3cRange.setEnd(ec,eo);return w3cRange;}else{var textRange=pointToTextRange({node:sc,offset:so});textRange.setEndPoint('EndToEnd',pointToTextRange({node:ec,offset:eo}));return textRange;}};this.getPoints=function(){return{sc:sc,so:so,ec:ec,eo:eo};};this.getStartPoint=function(){return{node:sc,offset:so};};this.getEndPoint=function(){return{node:ec,offset:eo};};this.select=function(){var nativeRng=nativeRange();if(agent.isW3CRangeSupport){var selection=document.getSelection();if(selection.rangeCount>0){selection.removeAllRanges();}
selection.addRange(nativeRng);}else{nativeRng.select();}
return this;};this.normalize=function(){var getVisiblePoint=function(point,isLeftToRight){if((dom.isVisiblePoint(point)&&!dom.isEdgePoint(point))||(dom.isVisiblePoint(point)&&dom.isRightEdgePoint(point)&&!isLeftToRight)||(dom.isVisiblePoint(point)&&dom.isLeftEdgePoint(point)&&isLeftToRight)||(dom.isVisiblePoint(point)&&dom.isBlock(point.node)&&dom.isEmpty(point.node))){return point;}
var block=dom.ancestor(point.node,dom.isBlock);if(((dom.isLeftEdgePointOf(point,block)||dom.isVoid(dom.prevPoint(point).node))&&!isLeftToRight)||((dom.isRightEdgePointOf(point,block)||dom.isVoid(dom.nextPoint(point).node))&&isLeftToRight)){if(dom.isVisiblePoint(point)){return point;}
isLeftToRight=!isLeftToRight;}
var nextPoint=isLeftToRight?dom.nextPointUntil(dom.nextPoint(point),dom.isVisiblePoint):dom.prevPointUntil(dom.prevPoint(point),dom.isVisiblePoint);return nextPoint||point;};var endPoint=getVisiblePoint(this.getEndPoint(),false);var startPoint=this.isCollapsed()?endPoint:getVisiblePoint(this.getStartPoint(),true);return new WrappedRange(startPoint.node,startPoint.offset,endPoint.node,endPoint.offset);};this.nodes=function(pred,options){pred=pred||func.ok;var includeAncestor=options&&options.includeAncestor;var fullyContains=options&&options.fullyContains;var startPoint=this.getStartPoint();var endPoint=this.getEndPoint();var nodes=[];var leftEdgeNodes=[];dom.walkPoint(startPoint,endPoint,function(point){if(dom.isEditable(point.node)){return;}
var node;if(fullyContains){if(dom.isLeftEdgePoint(point)){leftEdgeNodes.push(point.node);}
if(dom.isRightEdgePoint(point)&&list.contains(leftEdgeNodes,point.node)){node=point.node;}}else if(includeAncestor){node=dom.ancestor(point.node,pred);}else{node=point.node;}
if(node&&pred(node)){nodes.push(node);}},true);return list.unique(nodes);};this.commonAncestor=function(){return dom.commonAncestor(sc,ec);};this.expand=function(pred){var startAncestor=dom.ancestor(sc,pred);var endAncestor=dom.ancestor(ec,pred);if(!startAncestor&&!endAncestor){return new WrappedRange(sc,so,ec,eo);}
var boundaryPoints=this.getPoints();if(startAncestor){boundaryPoints.sc=startAncestor;boundaryPoints.so=0;}
if(endAncestor){boundaryPoints.ec=endAncestor;boundaryPoints.eo=dom.nodeLength(endAncestor);}
return new WrappedRange(boundaryPoints.sc,boundaryPoints.so,boundaryPoints.ec,boundaryPoints.eo);};this.collapse=function(isCollapseToStart){if(isCollapseToStart){return new WrappedRange(sc,so,sc,so);}else{return new WrappedRange(ec,eo,ec,eo);}};this.splitText=function(){var isSameContainer=sc===ec;var boundaryPoints=this.getPoints();if(dom.isText(ec)&&!dom.isEdgePoint(this.getEndPoint())){ec.splitText(eo);}
if(dom.isText(sc)&&!dom.isEdgePoint(this.getStartPoint())){boundaryPoints.sc=sc.splitText(so);boundaryPoints.so=0;if(isSameContainer){boundaryPoints.ec=boundaryPoints.sc;boundaryPoints.eo=eo-so;}}
return new WrappedRange(boundaryPoints.sc,boundaryPoints.so,boundaryPoints.ec,boundaryPoints.eo);};if(_.isUndefined(this.deleteContents))
this.deleteContents=function(){if(this.isCollapsed()){return this;}
var rng=this.splitText();var nodes=rng.nodes(null,{fullyContains:true});var point=dom.prevPointUntil(rng.getStartPoint(),function(point){return!list.contains(nodes,point.node);});var emptyParents=[];$.each(nodes,function(idx,node){var parent=node.parentNode;if(point.node!==parent&&dom.nodeLength(parent)===1){emptyParents.push(parent);}
dom.remove(node,false);});$.each(emptyParents,function(idx,node){dom.remove(node,false);});return new WrappedRange(point.node,point.offset,point.node,point.offset).normalize();};var makeIsOn=function(pred){return function(){var ancestor=dom.ancestor(sc,pred);return!!ancestor&&(ancestor===dom.ancestor(ec,pred));};};this.isOnEditable=makeIsOn(dom.isEditable);this.isOnList=makeIsOn(dom.isList);this.isOnAnchor=makeIsOn(dom.isAnchor);this.isOnCell=makeIsOn(dom.isCell);this.isLeftEdgeOf=function(pred){if(!dom.isLeftEdgePoint(this.getStartPoint())){return false;}
var node=dom.ancestor(this.sc,pred);return node&&dom.isLeftEdgeOf(this.sc,node);};this.isCollapsed=function(){return sc===ec&&so===eo;};this.wrapBodyInlineWithPara=function(){if(dom.isBodyContainer(sc)&&dom.isEmpty(sc)){sc.innerHTML=dom.emptyPara;return new WrappedRange(sc.firstChild,0,sc.firstChild,0);}
var rng=this.normalize();if(dom.isParaInline(sc)||dom.isPara(sc)){return rng;}
if(dom.isText(sc)){var node=sc;while(node.parentNode!==document){node=node.parentNode;if(/^(P|LI|H[1-7]|BUTTON|A|SPAN)/.test(node.nodeName.toUpperCase())){return this.normalize();}}}
var topAncestor;if(dom.isInline(rng.sc)){var ancestors=dom.listAncestor(rng.sc,func.not(dom.isInline));topAncestor=list.last(ancestors);if(!dom.isInline(topAncestor)){topAncestor=ancestors[ancestors.length-2]||rng.sc.childNodes[rng.so];}}else{topAncestor=rng.sc.childNodes[rng.so>0?rng.so-1:0];}
var inlineSiblings=dom.listPrev(topAncestor,dom.isParaInline).reverse();inlineSiblings=inlineSiblings.concat(dom.listNext(topAncestor.nextSibling,dom.isParaInline));if(inlineSiblings.length){var para=dom.wrap(list.head(inlineSiblings),'p');dom.appendChildNodes(para,list.tail(inlineSiblings));}
return this.normalize();};this.insertNode=function(node){var rng=this.wrapBodyInlineWithPara().deleteContents();var info=dom.splitPoint(rng.getStartPoint(),!dom.isBodyContainer(dom.ancestor(rng.sc,function(node){return dom.isBodyContainer(node)||dom.isPara(node)})));if(info.rightNode){info.rightNode.parentNode.insertBefore(node,info.rightNode);}else{info.container.appendChild(node);}
return node;};this.pasteHTML=function(markup){var contentsContainer=$('<div></div>').html(markup)[0];var childNodes=list.from(contentsContainer.childNodes);var rng=this.wrapBodyInlineWithPara().deleteContents();return childNodes.reverse().map(function(childNode){return rng.insertNode(childNode);}).reverse();};this.toString=function(){var nativeRng=nativeRange();return agent.isW3CRangeSupport?nativeRng.toString():nativeRng.text;};this.getWordRange=function(findAfter){var endPoint=this.getEndPoint();if(!dom.isCharPoint(endPoint)){return this;}
var startPoint=dom.prevPointUntil(endPoint,function(point){return!dom.isCharPoint(point);});if(findAfter){endPoint=dom.nextPointUntil(endPoint,function(point){return!dom.isCharPoint(point);});}
return new WrappedRange(startPoint.node,startPoint.offset,endPoint.node,endPoint.offset);};this.bookmark=function(editable){return{s:{path:dom.makeOffsetPath(editable,sc),offset:so},e:{path:dom.makeOffsetPath(editable,ec),offset:eo}};};this.paraBookmark=function(paras){return{s:{path:list.tail(dom.makeOffsetPath(list.head(paras),sc)),offset:so},e:{path:list.tail(dom.makeOffsetPath(list.last(paras),ec)),offset:eo}};};this.getClientRects=function(){var nativeRng=nativeRange();return nativeRng.getClientRects();};};return{WrappedRange:WrappedRange,create:function(sc,so,ec,eo){if(!arguments.length){if(agent.isW3CRangeSupport){var selection=document.getSelection();if(!selection||selection.rangeCount===0){return null;}else{try{if(dom.isBody(selection.anchorNode)){return null;}}catch(e){return null;}}
var nativeRng=selection.getRangeAt(0);sc=nativeRng.startContainer;so=nativeRng.startOffset;ec=nativeRng.endContainer;eo=nativeRng.endOffset;}else{var textRange=document.selection.createRange();var textRangeEnd=textRange.duplicate();textRangeEnd.collapse(false);var textRangeStart=textRange;textRangeStart.collapse(true);var startPoint=textRangeToPoint(textRangeStart,true),endPoint=textRangeToPoint(textRangeEnd,false);if(dom.isText(startPoint.node)&&dom.isLeftEdgePoint(startPoint)&&dom.isTextNode(endPoint.node)&&dom.isRightEdgePoint(endPoint)&&endPoint.node.nextSibling===startPoint.node){startPoint=endPoint;}
sc=startPoint.cont;so=startPoint.offset;ec=endPoint.cont;eo=endPoint.offset;}}else if(arguments.length===2){ec=sc;eo=so;}
return new WrappedRange(sc,so,ec,eo);},createFromNode:function(node){var sc=node;var so=0;var ec=node;var eo=dom.nodeLength(ec);if(dom.isVoid(sc)){so=dom.listPrev(sc).length-1;sc=sc.parentNode;}
if(dom.isBR(ec)){eo=dom.listPrev(ec).length-1;ec=ec.parentNode;}else if(dom.isVoid(ec)){eo=dom.listPrev(ec).length;ec=ec.parentNode;}
return this.create(sc,so,ec,eo);},createFromNodeBefore:function(node){return this.createFromNode(node).collapse(true);},createFromNodeAfter:function(node){return this.createFromNode(node).collapse();},createFromBookmark:function(editable,bookmark){var sc=dom.fromOffsetPath(editable,bookmark.s.path);var so=bookmark.s.offset;var ec=dom.fromOffsetPath(editable,bookmark.e.path);var eo=bookmark.e.offset;return new WrappedRange(sc,so,ec,eo);},createFromParaBookmark:function(bookmark,paras){var so=bookmark.s.offset;var eo=bookmark.e.offset;var sc=dom.fromOffsetPath(list.head(paras),bookmark.s.path);var ec=dom.fromOffsetPath(list.last(paras),bookmark.e.path);return new WrappedRange(sc,so,ec,eo);}};})();return range;});;

/* <inline asset> defined in bundle 'web_editor.assets_summernote' */
odoo.website_next_define='summernote/editing/Bullet';;

/* /web_editor/static/lib/summernote/src/js/editing/Bullet.js defined in bundle 'web_editor.assets_summernote' */
define(['summernote/core/list','summernote/core/func','summernote/core/dom','summernote/core/range'],function(list,func,dom,range){var Bullet=function(){this.insertOrderedList=function(){this.toggleList('OL');};this.insertUnorderedList=function(){this.toggleList('UL');};this.indent=function(){var self=this;var rng=range.create().wrapBodyInlineWithPara();var paras=rng.nodes(dom.isPara,{includeAncestor:true});var clustereds=list.clusterBy(paras,func.peq2('parentNode'));$.each(clustereds,function(idx,paras){var head=list.head(paras);if(dom.isLi(head)){self.wrapList(paras,head.parentNode.nodeName);}else{$.each(paras,function(idx,para){$(para).css('marginLeft',function(idx,val){return(parseInt(val,10)||0)+25;});});}});rng.select();};this.outdent=function(){var self=this;var rng=range.create().wrapBodyInlineWithPara();var paras=rng.nodes(dom.isPara,{includeAncestor:true});var clustereds=list.clusterBy(paras,func.peq2('parentNode'));$.each(clustereds,function(idx,paras){var head=list.head(paras);if(dom.isLi(head)){self.releaseList([paras]);}else{$.each(paras,function(idx,para){$(para).css('marginLeft',function(idx,val){val=(parseInt(val,10)||0);return val>25?val-25:'';});});}});rng.select();};this.toggleList=function(listName){var self=this;var rng=range.create().wrapBodyInlineWithPara();var paras=rng.nodes(dom.isPara,{includeAncestor:true});var bookmark=rng.paraBookmark(paras);var clustereds=list.clusterBy(paras,func.peq2('parentNode'));if(list.find(paras,dom.isPurePara)){var wrappedParas=[];$.each(clustereds,function(idx,paras){wrappedParas=wrappedParas.concat(self.wrapList(paras,listName));});paras=wrappedParas;}else{var diffLists=rng.nodes(dom.isList,{includeAncestor:true}).filter(function(listNode){return!$.nodeName(listNode,listName);});if(diffLists.length){$.each(diffLists,function(idx,listNode){dom.replace(listNode,listName);});}else{paras=this.releaseList(clustereds,true);}}
range.createFromParaBookmark(bookmark,paras).select();};this.wrapList=function(paras,listName){var head=list.head(paras);var last=list.last(paras);var prevList=dom.isList(head.previousSibling)&&head.previousSibling;var nextList=dom.isList(last.nextSibling)&&last.nextSibling;var listNode=prevList||dom.insertAfter(dom.create(listName||'UL'),last);paras=paras.map(function(para){return dom.isPurePara(para)?dom.replace(para,'LI'):para;});dom.appendChildNodes(listNode,paras);if(nextList){dom.appendChildNodes(listNode,list.from(nextList.childNodes));dom.remove(nextList);}
return paras;};this.releaseList=function(clustereds,isEscapseToBody){var releasedParas=[];$.each(clustereds,function(idx,paras){var head=list.head(paras);var last=list.last(paras);var headList=isEscapseToBody?dom.lastAncestor(head,dom.isList):head.parentNode;var lastList=headList.childNodes.length>1?dom.splitTree(headList,{node:last.parentNode,offset:dom.position(last)+1},{isSkipPaddingBlankHTML:true}):null;var middleList=dom.splitTree(headList,{node:head.parentNode,offset:dom.position(head)},{isSkipPaddingBlankHTML:true});paras=isEscapseToBody?dom.listDescendant(middleList,dom.isLi):list.from(middleList.childNodes).filter(dom.isLi);if(isEscapseToBody||!dom.isList(headList.parentNode)){paras=paras.map(function(para){return dom.replace(para,'P');});}
$.each(list.from(paras).reverse(),function(idx,para){dom.insertAfter(para,headList);});var rootLists=list.compact([headList,middleList,lastList]);$.each(rootLists,function(idx,rootList){var listNodes=[rootList].concat(dom.listDescendant(rootList,dom.isList));$.each(listNodes.reverse(),function(idx,listNode){if(!dom.nodeLength(listNode)){dom.remove(listNode,true);}});});releasedParas=releasedParas.concat(paras);});return releasedParas;};};return Bullet;});;

/* <inline asset> defined in bundle 'web_editor.assets_summernote' */
odoo.website_next_define='summernote/editing/History';;

/* /web_editor/static/lib/summernote/src/js/editing/History.js defined in bundle 'web_editor.assets_summernote' */
define(['summernote/core/range','summernote/core/dom'],function(range,dom){var History=function($editable){var stack=[],stackOffset=-1;var editable=$editable[0];var makeSnapshot=function(){var rng=range.create();var emptyBookmark={s:{path:[],offset:0},e:{path:[],offset:0}};return{contents:$editable.html(),bookmark:(rng&&dom.ancestor(rng.sc,dom.isEditable)?rng.bookmark(editable):emptyBookmark)};};var applySnapshot=function(snapshot){if(snapshot.contents!==null){$editable.html(snapshot.contents);}
if(snapshot.bookmark!==null){range.createFromBookmark(editable,snapshot.bookmark).select();}};this.undo=function(){if($editable.html()!==stack[stackOffset].contents){this.recordUndo();}
if(0<stackOffset){stackOffset--;applySnapshot(stack[stackOffset]);}};this.hasUndo=function(){return 0<stackOffset;};this.redo=function(){if(stack.length-1>stackOffset){stackOffset++;applySnapshot(stack[stackOffset]);}};this.hasRedo=function(){return stack.length-1>stackOffset;};var last;this.recordUndo=function(){var key=typeof event!=='undefined'?event:false;if(key&&!event.metaKey&&!event.ctrlKey&&!event.altKey&&event.type==="keydown"){key=event.type+"-";if(event.which===8||event.which===46)key+='delete';else if(event.which===13)key+='enter';else key+='other';if(key===last)return;hasUndo=true;}
last=key;if(stack.length>stackOffset+1){stack=stack.slice(0,stackOffset+1);}
if(stack[stackOffset]&&stack[stackOffset].contents===$editable.html()){return;}
stackOffset++;stack.push(makeSnapshot());};this.splitNext=function(){last=false;};this.reset=function(){last=false;stack=[];stackOffset=-1;this.recordUndo();};this.recordUndo();};return History;});;

/* <inline asset> defined in bundle 'web_editor.assets_summernote' */
odoo.website_next_define='summernote/editing/Style';;

/* /web_editor/static/lib/summernote/src/js/editing/Style.js defined in bundle 'web_editor.assets_summernote' */
define(['summernote/core/agent','summernote/core/func','summernote/core/list','summernote/core/dom'],function(agent,func,list,dom){var Style=function(){var jQueryCSS=function($obj,propertyNames){if(agent.jqueryVersion<1.9){var result={};$.each(propertyNames,function(idx,propertyName){result[propertyName]=$obj.css(propertyName);});return result;}
return $obj.css.call($obj,propertyNames);};this.fromNode=function($node){var properties=['font-family','font-size','text-align','list-style-type','line-height'];var styleInfo=jQueryCSS($node,properties)||{};styleInfo['font-size']=parseInt(styleInfo['font-size'],10);return styleInfo;};this.stylePara=function(rng,styleInfo){$.each(rng.nodes(dom.isPara,{includeAncestor:true}),function(idx,para){$(para).css(styleInfo);});};this.styleNodes=function(rng,options){rng=rng.splitText();var nodeName=options&&options.nodeName||'SPAN';var expandClosestSibling=!!(options&&options.expandClosestSibling);var onlyPartialContains=!!(options&&options.onlyPartialContains);if(rng.isCollapsed()){return[rng.insertNode(dom.create(nodeName))];}
var pred=dom.makePredByNodeName(nodeName);var nodes=rng.nodes(dom.isText,{fullyContains:true}).map(function(text){return dom.singleChildAncestor(text,pred)||dom.wrap(text,nodeName);});if(expandClosestSibling){if(onlyPartialContains){var nodesInRange=rng.nodes();pred=func.and(pred,function(node){return list.contains(nodesInRange,node);});}
return nodes.map(function(node){var siblings=dom.withClosestSiblings(node,pred);var head=list.head(siblings);var tails=list.tail(siblings);$.each(tails,function(idx,elem){dom.appendChildNodes(head,elem.childNodes);dom.remove(elem);});return list.head(siblings);});}else{return nodes;}};this.current=function(rng){var $cont=$(dom.isText(rng.sc)?rng.sc.parentNode:rng.sc);var styleInfo=this.fromNode($cont);styleInfo['font-bold']=document.queryCommandState('bold')?'bold':'normal';styleInfo['font-italic']=document.queryCommandState('italic')?'italic':'normal';styleInfo['font-underline']=document.queryCommandState('underline')?'underline':'normal';styleInfo['font-strikethrough']=document.queryCommandState('strikeThrough')?'strikethrough':'normal';styleInfo['font-superscript']=document.queryCommandState('superscript')?'superscript':'normal';styleInfo['font-subscript']=document.queryCommandState('subscript')?'subscript':'normal';if(!rng.isOnList()){styleInfo['list-style']='none';}else{var aOrderedType=['circle','disc','disc-leading-zero','square'];var isUnordered=$.inArray(styleInfo['list-style-type'],aOrderedType)>-1;styleInfo['list-style']=isUnordered?'unordered':'ordered';}
var para=dom.ancestor(rng.sc,dom.isPara);if(para&&para.style['line-height']){styleInfo['line-height']=para.style.lineHeight;}else{var lineHeight=parseInt(styleInfo['line-height'],10)/parseInt(styleInfo['font-size'],10);styleInfo['line-height']=lineHeight.toFixed(1);}
styleInfo.anchor=rng.isOnAnchor()&&dom.ancestor(rng.sc,dom.isAnchor);styleInfo.ancestors=dom.listAncestor(rng.sc,dom.isEditable);styleInfo.range=rng;return styleInfo;};};return Style;});;

/* <inline asset> defined in bundle 'web_editor.assets_summernote' */
odoo.website_next_define='summernote/editing/Table';;

/* /web_editor/static/lib/summernote/src/js/editing/Table.js defined in bundle 'web_editor.assets_summernote' */
define(['summernote/core/dom','summernote/core/range','summernote/core/list'],function(dom,range,list){var Table=function(){this.tab=function(rng,isShift){var cell=dom.ancestor(rng.commonAncestor(),dom.isCell);var table=dom.ancestor(cell,dom.isTable);var cells=dom.listDescendant(table,dom.isCell);var nextCell=list[isShift?'prev':'next'](cells,cell);if(nextCell){range.create(nextCell,0).select();}};this.createTable=function(colCount,rowCount){var tds=[],tdHTML;for(var idxCol=0;idxCol<colCount;idxCol++){tds.push('<td>'+dom.blank+'</td>');}
tdHTML=tds.join('');var trs=[],trHTML;for(var idxRow=0;idxRow<rowCount;idxRow++){trs.push('<tr>'+tdHTML+'</tr>');}
trHTML=trs.join('');return $('<table class="table table-bordered">'+trHTML+'</table>')[0];};};return Table;});;

/* <inline asset> defined in bundle 'web_editor.assets_summernote' */
odoo.website_next_define='summernote/editing/Typing';;

/* /web_editor/static/lib/summernote/src/js/editing/Typing.js defined in bundle 'web_editor.assets_summernote' */
define(['summernote/core/dom','summernote/core/range','summernote/editing/Bullet'],function(dom,range,Bullet){var Typing=function(){var bullet=new Bullet();this.insertTab=function($editable,rng,tabsize){var tab=dom.createText(new Array(tabsize+1).join(dom.NBSP_CHAR));rng=rng.deleteContents();rng.insertNode(tab,true);rng=range.create(tab,tabsize);rng.select();};this.insertParagraph=function(){var rng=range.create();rng=rng.deleteContents();rng=rng.wrapBodyInlineWithPara();var splitRoot=dom.ancestor(rng.sc,dom.isPara);var nextPara;if(splitRoot){if(dom.isEmpty(splitRoot)&&dom.isLi(splitRoot)){bullet.toggleList(splitRoot.parentNode.nodeName);return;}else{nextPara=dom.splitTree(splitRoot,rng.getStartPoint());var emptyAnchors=dom.listDescendant(splitRoot,dom.isEmptyAnchor);emptyAnchors=emptyAnchors.concat(dom.listDescendant(nextPara,dom.isEmptyAnchor));$.each(emptyAnchors,function(idx,anchor){dom.remove(anchor);});}}else{var next=rng.sc.childNodes[rng.so];nextPara=$(dom.emptyPara)[0];if(next){rng.sc.insertBefore(nextPara,next);}else{rng.sc.appendChild(nextPara);}}
range.create(nextPara,0).normalize().select();};};return Typing;});;

/* <inline asset> defined in bundle 'web_editor.assets_summernote' */
odoo.website_next_define='summernote/module/Editor';;

/* /web_editor/static/lib/summernote/src/js/module/Editor.js defined in bundle 'web_editor.assets_summernote' */
define(['summernote/core/agent','summernote/core/func','summernote/core/list','summernote/core/dom','summernote/core/range','summernote/core/async','summernote/editing/Style','summernote/editing/Typing','summernote/editing/Table','summernote/editing/Bullet'],function(agent,func,list,dom,range,async,Style,Typing,Table,Bullet){var KEY_BOGUS='bogus';var Editor=function(handler){var self=this;var style=new Style();var table=new Table();var typing=new Typing();var bullet=new Bullet();this.style=style;this.table=table;this.typing=typing;this.bullet=bullet;this.createRange=function($editable){this.focus($editable);return range.create();};this.saveRange=function($editable,thenCollapse){var r=range.create();if(!r||($editable[0]!==r.sc&&!$.contains($editable[0],r.sc))){$editable.focus();}
$editable.data('range',range.create());if(thenCollapse){range.create().collapse().select();}};this.saveNode=function($editable){var copy=[];for(var key=0,len=$editable[0].childNodes.length;key<len;key++){copy.push($editable[0].childNodes[key]);}
$editable.data('childNodes',copy);};this.restoreRange=function($editable){var rng=$editable.data('range');if(rng){rng.select();this.focus($editable);}};this.restoreNode=function($editable){$editable.html('');var child=$editable.data('childNodes');for(var index=0,len=child.length;index<len;index++){$editable[0].appendChild(child[index]);}};this.currentStyle=function(target){var rng=range.create();var styleInfo=rng&&rng.isOnEditable()?style.current(rng.normalize()):{};if(dom.isImg(target)){styleInfo.image=target;}
return styleInfo;};this.styleFromNode=function($node){return style.fromNode($node);};var triggerOnBeforeChange=function($editable){var $holder=dom.makeLayoutInfo($editable).holder();handler.bindCustomEvent($holder,$editable.data('callbacks'),'before.command')($editable.html(),$editable);};var triggerOnChange=function($editable){var $holder=dom.makeLayoutInfo($editable).holder();handler.bindCustomEvent($holder,$editable.data('callbacks'),'change')($editable.html(),$editable);};this.undo=function($editable){triggerOnBeforeChange($editable);$editable.data('NoteHistory').undo();triggerOnChange($editable);};this.redo=function($editable){triggerOnBeforeChange($editable);$editable.data('NoteHistory').redo();triggerOnChange($editable);};var beforeCommand=this.beforeCommand=function($editable){triggerOnBeforeChange($editable);self.focus($editable);};var afterCommand=this.afterCommand=function($editable,isPreventTrigger){$editable.data('NoteHistory').recordUndo();if(!isPreventTrigger){triggerOnChange($editable);}};var commands=['bold','italic','underline','strikethrough','superscript','subscript','justifyLeft','justifyCenter','justifyRight','justifyFull','formatBlock','removeFormat','backColor','foreColor','fontName'];for(var idx=0,len=commands.length;idx<len;idx++){this[commands[idx]]=(function(sCmd){return function($editable,value){beforeCommand($editable);document.execCommand(sCmd,false,value);afterCommand($editable,true);};})(commands[idx]);}
this.tab=function($editable,options){var rng=this.createRange($editable);if(rng.isCollapsed()&&rng.isOnCell()){table.tab(rng);}else{beforeCommand($editable);typing.insertTab($editable,rng,options.tabsize);afterCommand($editable);}};this.untab=function($editable){var rng=this.createRange($editable);if(rng.isCollapsed()&&rng.isOnCell()){table.tab(rng,true);}};this.insertParagraph=function($editable){beforeCommand($editable);typing.insertParagraph($editable);afterCommand($editable);};this.insertOrderedList=function($editable){beforeCommand($editable);bullet.insertOrderedList($editable);afterCommand($editable);};this.insertUnorderedList=function($editable){beforeCommand($editable);bullet.insertUnorderedList($editable);afterCommand($editable);};this.indent=function($editable){beforeCommand($editable);bullet.indent($editable);afterCommand($editable);};this.outdent=function($editable){beforeCommand($editable);bullet.outdent($editable);afterCommand($editable);};this.insertImage=function($editable,sUrl,filename){async.createImage(sUrl,filename).then(function($image){beforeCommand($editable);$image.css({display:'',width:Math.min($editable.width(),$image.width())});range.create().insertNode($image[0]);range.createFromNodeAfter($image[0]).select();afterCommand($editable);}).fail(function(){var $holder=dom.makeLayoutInfo($editable).holder();handler.bindCustomEvent($holder,$editable.data('callbacks'),'image.upload.error')();});};this.insertNode=function($editable,node){beforeCommand($editable);range.create().insertNode(node);range.createFromNodeAfter(node).select();afterCommand($editable);};this.insertText=function($editable,text){beforeCommand($editable);var textNode=range.create().insertNode(dom.createText(text));range.create(textNode,dom.nodeLength(textNode)).select();afterCommand($editable);};this.pasteHTML=function($editable,markup){beforeCommand($editable);var contents=range.create().pasteHTML(markup);range.createFromNodeAfter(list.last(contents)).select();afterCommand($editable);};this.formatBlock=function($editable,tagName){beforeCommand($editable);tagName=agent.isMSIE?'<'+tagName+'>':tagName;document.execCommand('FormatBlock',false,tagName);afterCommand($editable);};this.formatPara=function($editable){beforeCommand($editable);this.formatBlock($editable,'P');afterCommand($editable);};for(var idx=1;idx<=6;idx++){this['formatH'+idx]=function(idx){return function($editable){this.formatBlock($editable,'H'+idx);};}(idx);};this.fontSize=function($editable,value){var rng=range.create();if(rng.isCollapsed()){var spans=style.styleNodes(rng);var firstSpan=list.head(spans);$(spans).css({'font-size':value+'px'});if(firstSpan&&!dom.nodeLength(firstSpan)){firstSpan.innerHTML=dom.ZERO_WIDTH_NBSP_CHAR;range.createFromNodeAfter(firstSpan.firstChild).select();$editable.data(KEY_BOGUS,firstSpan);}}else{beforeCommand($editable);$(style.styleNodes(rng)).css({'font-size':value+'px'});afterCommand($editable);}};this.insertHorizontalRule=function($editable){beforeCommand($editable);var rng=range.create();var hrNode=rng.insertNode($('<HR/>')[0]);if(hrNode.nextSibling){range.create(hrNode.nextSibling,0).normalize().select();}
afterCommand($editable);};this.removeBogus=function($editable){var bogusNode=$editable.data(KEY_BOGUS);if(!bogusNode){return;}
var textNode=list.find(list.from(bogusNode.childNodes),dom.isText);var bogusCharIdx=textNode.nodeValue.indexOf(dom.ZERO_WIDTH_NBSP_CHAR);if(bogusCharIdx!==-1){textNode.deleteData(bogusCharIdx,1);}
if(dom.isEmpty(bogusNode)){dom.remove(bogusNode);}
$editable.removeData(KEY_BOGUS);};this.lineHeight=function($editable,value){beforeCommand($editable);style.stylePara(range.create(),{lineHeight:value});afterCommand($editable);};this.unlink=function($editable){var rng=this.createRange($editable);if(rng.isOnAnchor()){var anchor=dom.ancestor(rng.sc,dom.isAnchor);rng=range.createFromNode(anchor);rng.select();beforeCommand($editable);document.execCommand('unlink');afterCommand($editable);}};this.createLink=function($editable,linkInfo,options){var linkUrl=linkInfo.url;var linkText=linkInfo.text;var isNewWindow=linkInfo.isNewWindow;var rng=linkInfo.range||this.createRange($editable);var isTextChanged=rng.toString()!==linkText;const nodeName=linkInfo.isButton?'BUTTON':'A';const pred=dom.makePredByNodeName(nodeName);options=options||dom.makeLayoutInfo($editable).editor().data('options');beforeCommand($editable);if(options.onCreateLink){linkUrl=options.onCreateLink(linkUrl);}
var anchors=[];var ancestor_anchor=dom.ancestor(rng.sc,pred);if(ancestor_anchor&&ancestor_anchor===dom.ancestor(rng.ec,pred)){anchors.push($(ancestor_anchor).html(linkText).get(0));}else if(isTextChanged){var anchor=rng.insertNode($(`<${nodeName}>${linkText}</${nodeName}>`)[0]);anchors.push(anchor);}else{anchors=style.styleNodes(rng,{nodeName:nodeName,expandClosestSibling:true,onlyPartialContains:true});}
$.each(anchors,function(idx,anchor){if(!linkInfo.isButton){$(anchor).attr('href',linkUrl);}
$(anchor).attr('class',linkInfo.className||null);$(anchor).css(linkInfo.style||{});if(isNewWindow){$(anchor).attr('target','_blank');}else{$(anchor).removeAttr('target');}});var startRange=range.createFromNodeBefore(list.head(anchors));var startPoint=startRange.getStartPoint();var endRange=range.createFromNodeAfter(list.last(anchors));var endPoint=endRange.getEndPoint();range.create(startPoint.node,startPoint.offset,endPoint.node,endPoint.offset).select();afterCommand($editable);};this.getLinkInfo=function($editable){var selection;var currentSelection=null;if(document.getSelection){selection=document.getSelection();if(selection.getRangeAt&&selection.rangeCount){currentSelection=selection.getRangeAt(0);}}
this.focus($editable);if(currentSelection&&document.getSelection){selection=document.getSelection();if(!selection||selection.rangeCount===0){selection.removeAllRanges();selection.addRange(currentSelection);}}
var rng=range.create().expand(dom.isAnchor);var anchor=list.head(rng.nodes(dom.isAnchor));const $anchor=$(anchor);if($anchor.length&&!rng.nodes()[0].isSameNode(anchor)){rng=range.createFromNode(anchor);rng.select();}
let isButton=false;if(!$anchor.length){const pred=dom.makePredByNodeName('BUTTON');const rngNew=range.create().expand(pred);const target=list.head(rngNew.nodes(pred));if(target&&target.nodeName==='BUTTON'){isButton=true;rng=rngNew;}}
return{range:rng,text:rng.toString(),isNewWindow:$anchor.length?$anchor.attr('target')==='_blank':false,url:$anchor.length?$anchor.attr('href'):'',isButton:isButton,};};this.color=function($editable,sObjColor){var oColor=JSON.parse(sObjColor);var foreColor=oColor.foreColor,backColor=oColor.backColor;beforeCommand($editable);if(foreColor){document.execCommand('foreColor',false,foreColor);}
if(backColor){document.execCommand('backColor',false,backColor);}
afterCommand($editable);};this.insertTable=function($editable,sDim){var dimension=sDim.split('x');beforeCommand($editable);var rng=range.create().deleteContents();rng.insertNode(table.createTable(dimension[0],dimension[1]));afterCommand($editable);};this.floatMe=function($editable,value,$target){beforeCommand($editable);$target.removeClass('float-left float-right');if(value&&value!=='none'){$target.addClass('pull-'+value);}
$target.css('float',value);afterCommand($editable);};this.imageShape=function($editable,value,$target){beforeCommand($editable);$target.removeClass('rounded rounded-circle img-thumbnail');if(value){$target.addClass(value);}
afterCommand($editable);};this.resize=function($editable,value,$target){beforeCommand($editable);$target.css({width:value*100+'%',height:''});afterCommand($editable);};this.resizeTo=function(pos,$target,bKeepRatio){var imageSize;if(bKeepRatio){var newRatio=pos.y/pos.x;var ratio=$target.data('ratio');imageSize={width:ratio>newRatio?pos.x:pos.y/ratio,height:ratio>newRatio?pos.x*ratio:pos.y};}else{imageSize={width:pos.x,height:pos.y};}
$target.css(imageSize);};this.removeMedia=function($editable,value,$target){beforeCommand($editable);$target.detach();handler.bindCustomEvent($(),$editable.data('callbacks'),'media.delete')($target,$editable);afterCommand($editable);};this.focus=function($editable){$editable.focus();if(agent.isFF){var rng=range.create();if(!rng||rng.isOnEditable()){return;}
range.createFromNode($editable[0]).normalize().collapse().select();}};this.isEmpty=function($editable){return dom.isEmpty($editable[0])||dom.emptyPara===$editable.html();};};return Editor;});;

/* <inline asset> defined in bundle 'web_editor.assets_summernote' */
odoo.website_next_define='summernote/module/Button';;

/* /web_editor/static/lib/summernote/src/js/module/Button.js defined in bundle 'web_editor.assets_summernote' */
define(['summernote/core/list','summernote/core/agent'],function(list,agent){var Button=function(){this.update=function($container,styleInfo){var checkDropdownMenu=function($btn,value){$btn.find('.dropdown-menu .dropdown-item').each(function(){var isChecked=($(this).data('value')+'')===(value+'');this.className='dropdown-item'+(isChecked?' checked':'');});};var btnState=function(selector,pred){var $btn=$container.find(selector);$btn.toggleClass('active',pred());};if(styleInfo.image){var $img=$(styleInfo.image);btnState('button[data-event="imageShape"][data-value="rounded"]',function(){return $img.hasClass('rounded');});btnState('button[data-event="imageShape"][data-value="rounded-circle"]',function(){return $img.hasClass('rounded-circle');});btnState('button[data-event="imageShape"][data-value="img-thumbnail"]',function(){return $img.hasClass('img-thumbnail');});btnState('button[data-event="imageShape"]:not([data-value])',function(){return!$img.is('.rounded, .rounded-circle, .img-thumbnail');});var imgFloat=$img.css('float');btnState('button[data-event="floatMe"][data-value="left"]',function(){return imgFloat==='left';});btnState('button[data-event="floatMe"][data-value="right"]',function(){return imgFloat==='right';});btnState('button[data-event="floatMe"][data-value="none"]',function(){return imgFloat!=='left'&&imgFloat!=='right';});var style=$img.attr('style');btnState('button[data-event="resize"][data-value="1"]',function(){return!!/(^|\s)(max-)?width\s*:\s*100%/.test(style);});btnState('button[data-event="resize"][data-value="0.5"]',function(){return!!/(^|\s)(max-)?width\s*:\s*50%/.test(style);});btnState('button[data-event="resize"][data-value="0.25"]',function(){return!!/(^|\s)(max-)?width\s*:\s*25%/.test(style);});return;}
var $fontname=$container.find('.note-fontname');if($fontname.length){var selectedFont=styleInfo['font-family'];if(!!selectedFont){var list=selectedFont.split(',');for(var i=0,len=list.length;i<len;i++){selectedFont=list[i].replace(/[\'\"]/g,'').replace(/\s+$/,'').replace(/^\s+/,'');if(agent.isFontInstalled(selectedFont)){break;}}
$fontname.find('.note-current-fontname').text(selectedFont);checkDropdownMenu($fontname,selectedFont);}}
var $fontsize=$container.find('.note-fontsize');$fontsize.find('.note-current-fontsize').text(styleInfo['font-size']);checkDropdownMenu($fontsize,parseFloat(styleInfo['font-size']));var $lineHeight=$container.find('.note-height');checkDropdownMenu($lineHeight,parseFloat(styleInfo['line-height']));btnState('button[data-event="bold"]',function(){return styleInfo['font-bold']==='bold';});btnState('button[data-event="italic"]',function(){return styleInfo['font-italic']==='italic';});btnState('button[data-event="underline"]',function(){return styleInfo['font-underline']==='underline';});btnState('button[data-event="strikethrough"]',function(){return styleInfo['font-strikethrough']==='strikethrough';});btnState('button[data-event="superscript"]',function(){return styleInfo['font-superscript']==='superscript';});btnState('button[data-event="subscript"]',function(){return styleInfo['font-subscript']==='subscript';});btnState('button[data-event="justifyLeft"]',function(){return styleInfo['text-align']==='left'||styleInfo['text-align']==='start';});btnState('button[data-event="justifyCenter"]',function(){return styleInfo['text-align']==='center';});btnState('button[data-event="justifyRight"]',function(){return styleInfo['text-align']==='right';});btnState('button[data-event="justifyFull"]',function(){return styleInfo['text-align']==='justify';});btnState('button[data-event="insertUnorderedList"]',function(){return styleInfo['list-style']==='unordered';});btnState('button[data-event="insertOrderedList"]',function(){return styleInfo['list-style']==='ordered';});};this.updateRecentColor=function(button,eventName,value){var $color=$(button).closest('.note-color');var $recentColor=$color.find('.note-recent-color');var colorInfo=JSON.parse($recentColor.attr('data-value'));colorInfo[eventName]=value;$recentColor.attr('data-value',JSON.stringify(colorInfo));var sKey=eventName==='backColor'?'background-color':'color';$recentColor.find('i').css(sKey,value);};};return Button;});;

/* <inline asset> defined in bundle 'web_editor.assets_summernote' */
odoo.website_next_define='summernote/module/Clipboard';;

/* /web_editor/static/lib/summernote/src/js/module/Clipboard.js defined in bundle 'web_editor.assets_summernote' */
define(['summernote/core/list','summernote/core/dom','summernote/core/key','summernote/core/agent','summernote/core/range'],function(list,dom,key,agent,range){var Clipboard=function(handler){var pasteByEvent=function(event){if(["INPUT","TEXTAREA"].indexOf(event.target.tagName)!==-1){return;}
var clipboardData=event.originalEvent.clipboardData;var layoutInfo=dom.makeLayoutInfo(event.currentTarget||event.target);var $editable=layoutInfo.editable();if(clipboardData&&clipboardData.items&&clipboardData.items.length){var item=list.head(clipboardData.items);if(item.kind==='file'&&item.type.indexOf('image/')!==-1){handler.insertImages(layoutInfo,[item.getAsFile()]);event.preventDefault();}
handler.invoke('editor.afterCommand',$editable);}};this.attach=function(layoutInfo){layoutInfo.editable().on('paste',pasteByEvent);};};return Clipboard;});;

/* <inline asset> defined in bundle 'web_editor.assets_summernote' */
odoo.website_next_define='summernote/module/Codeview';;

/* /web_editor/static/lib/summernote/src/js/module/Codeview.js defined in bundle 'web_editor.assets_summernote' */
define(['summernote/core/agent','summernote/core/dom'],function(agent,dom){var CodeMirror;if(agent.hasCodeMirror){if(agent.isSupportAmd){require(['CodeMirror'],function(cm){CodeMirror=cm;});}else{CodeMirror=window.CodeMirror;}}
var Codeview=function(handler){this.sync=function(layoutInfo){var isCodeview=handler.invoke('codeview.isActivated',layoutInfo);if(isCodeview&&agent.hasCodeMirror){layoutInfo.codable().data('cmEditor').save();}};this.isActivated=function(layoutInfo){var $editor=layoutInfo.editor();return $editor.hasClass('codeview');};this.toggle=function(layoutInfo){if(this.isActivated(layoutInfo)){this.deactivate(layoutInfo);}else{this.activate(layoutInfo);}};this.activate=function(layoutInfo){var $editor=layoutInfo.editor(),$toolbar=layoutInfo.toolbar(),$editable=layoutInfo.editable(),$codable=layoutInfo.codable(),$popover=layoutInfo.popover(),$handle=layoutInfo.handle();var options=$editor.data('options');$codable.val(dom.html($editable,options.prettifyHtml));$codable.height($editable.height());handler.invoke('toolbar.updateCodeview',$toolbar,true);handler.invoke('popover.hide',$popover);handler.invoke('handle.hide',$handle);$editor.addClass('codeview');$codable.focus();if(agent.hasCodeMirror){var cmEditor=CodeMirror.fromTextArea($codable[0],options.codemirror);if(options.codemirror.tern){var server=new CodeMirror.TernServer(options.codemirror.tern);cmEditor.ternServer=server;cmEditor.on('cursorActivity',function(cm){server.updateArgHints(cm);});}
cmEditor.setSize(null,$editable.outerHeight());$codable.data('cmEditor',cmEditor);}};this.deactivate=function(layoutInfo){var $holder=layoutInfo.holder(),$editor=layoutInfo.editor(),$toolbar=layoutInfo.toolbar(),$editable=layoutInfo.editable(),$codable=layoutInfo.codable();var options=$editor.data('options');if(agent.hasCodeMirror){var cmEditor=$codable.data('cmEditor');$codable.val(cmEditor.getValue());cmEditor.toTextArea();}
var value=dom.value($codable,options.prettifyHtml)||dom.emptyPara;var isChange=$editable.html()!==value;$editable.html(value);$editable.height(options.height?$codable.height():'auto');$editor.removeClass('codeview');if(isChange){handler.bindCustomEvent($holder,$editable.data('callbacks'),'change')($editable.html(),$editable);}
$editable.focus();handler.invoke('toolbar.updateCodeview',$toolbar,false);};};return Codeview;});;

/* <inline asset> defined in bundle 'web_editor.assets_summernote' */
odoo.website_next_define='summernote/module/DragAndDrop';;

/* /web_editor/static/lib/summernote/src/js/module/DragAndDrop.js defined in bundle 'web_editor.assets_summernote' */
define(['summernote/core/dom'],function(dom){var DragAndDrop=function(handler){var $document=$(document);this.attach=function(layoutInfo,options){if(options.airMode||options.disableDragAndDrop){$document.on('drop',function(e){e.preventDefault();});}else{this.attachDragAndDropEvent(layoutInfo,options);}};this.attachDragAndDropEvent=function(layoutInfo,options){var collection=$(),$editor=layoutInfo.editor(),$dropzone=layoutInfo.dropzone(),$dropzoneMessage=$dropzone.find('.note-dropzone-message');$document.on('dragenter',function(e){var isCodeview=handler.invoke('codeview.isActivated',layoutInfo);var hasEditorSize=$editor.width()>0&&$editor.height()>0;if(!isCodeview&&!collection.length&&hasEditorSize){$editor.addClass('dragover');$dropzone.width($editor.width());$dropzone.height($editor.height());$dropzoneMessage.text(options.langInfo.image.dragImageHere);}
collection=collection.add(e.target);}).on('dragleave',function(e){collection=collection.not(e.target);if(!collection.length){$editor.removeClass('dragover');}}).on('drop',function(){collection=$();$editor.removeClass('dragover');});$dropzone.on('dragenter',function(){$dropzone.addClass('hover');$dropzoneMessage.text(options.langInfo.image.dropImage);}).on('dragleave',function(){$dropzone.removeClass('hover');$dropzoneMessage.text(options.langInfo.image.dragImageHere);});$dropzone.on('drop',function(event){var dataTransfer=event.originalEvent.dataTransfer;var layoutInfo=dom.makeLayoutInfo(event.currentTarget||event.target);event.preventDefault();if(dataTransfer&&dataTransfer.files&&dataTransfer.files.length){event.preventDefault();layoutInfo.editable().focus();handler.insertImages(layoutInfo,dataTransfer.files);}else{var insertNodefunc=function(){layoutInfo.holder().summernote('insertNode',this);};for(var i=0,len=dataTransfer.types.length;i<len;i++){var type=dataTransfer.types[i];var content=dataTransfer.getData(type);if(type.toLowerCase().indexOf('_moz_')>-1){return;}
if(type.toLowerCase().indexOf('text')>-1){layoutInfo.holder().summernote('pasteHTML',content);}else{$(content).each(insertNodefunc);}}}}).on('dragover',false);};};return DragAndDrop;});;

/* <inline asset> defined in bundle 'web_editor.assets_summernote' */
odoo.website_next_define='summernote/module/Fullscreen';;

/* /web_editor/static/lib/summernote/src/js/module/Fullscreen.js defined in bundle 'web_editor.assets_summernote' */
define([],function(){var Fullscreen=function(handler){var $window=$(window);var $scrollbar=$('html, body');this.toggle=function(layoutInfo){var $editor=layoutInfo.editor(),$toolbar=layoutInfo.toolbar(),$editable=layoutInfo.editable(),$codable=layoutInfo.codable();var resize=function(size){$editable.css('height',size.h);$codable.css('height',size.h);if($codable.data('cmeditor')){$codable.data('cmeditor').setsize(null,size.h);}};$editor.toggleClass('fullscreen');var isFullscreen=$editor.hasClass('fullscreen');if(isFullscreen){$editable.data('orgheight',$editable.css('height'));$window.on('resize',function(){resize({h:$window.height()-$toolbar.outerHeight()});}).trigger('resize');$scrollbar.css('overflow','hidden');}else{$window.off('resize');resize({h:$editable.data('orgheight')});$scrollbar.css('overflow','visible');}
handler.invoke('toolbar.updateFullscreen',$toolbar,isFullscreen);};};return Fullscreen;});;

/* <inline asset> defined in bundle 'web_editor.assets_summernote' */
odoo.website_next_define='summernote/module/Handle';;

/* /web_editor/static/lib/summernote/src/js/module/Handle.js defined in bundle 'web_editor.assets_summernote' */
define(['summernote/core/dom'],function(dom){var Handle=function(handler){var $document=$(document);var hHandleMousedown=function(event){if(dom.isControlSizing(event.target)){event.preventDefault();event.stopPropagation();var layoutInfo=dom.makeLayoutInfo(event.target),$handle=layoutInfo.handle(),$popover=layoutInfo.popover(),$editable=layoutInfo.editable(),$editor=layoutInfo.editor();var target=$handle.find('.note-control-selection').data('target'),$target=$(target),posStart=$target.offset(),scrollTop=$document.scrollTop();var isAirMode=$editor.data('options').airMode;$document.on('mousemove',function(event){handler.invoke('editor.resizeTo',{x:event.clientX-posStart.left,y:event.clientY-(posStart.top-scrollTop)},$target,!event.shiftKey);handler.invoke('handle.update',$handle,{image:target},isAirMode);handler.invoke('popover.update',$popover,{image:target},isAirMode);}).one('mouseup',function(){$document.off('mousemove');handler.invoke('editor.afterCommand',$editable);});if(!$target.data('ratio')){$target.data('ratio',$target.height()/$target.width());}}};this.attach=function(layoutInfo){layoutInfo.handle().on('mousedown',hHandleMousedown);};this.update=function($handle,styleInfo,isAirMode){var $selection=$handle.find('.note-control-selection');if(styleInfo.image){var $image=$(styleInfo.image);var pos=isAirMode?$image.offset():$image.position();var imageSize={w:$image.outerWidth(true),h:$image.outerHeight(true)};$selection.css({display:'block',left:pos.left,top:pos.top,width:imageSize.w,height:imageSize.h}).data('target',styleInfo.image);var sizingText=imageSize.w+'x'+imageSize.h;$selection.find('.note-control-selection-info').text(sizingText);}else{$selection.hide();}};this.hide=function($handle){$handle.children().hide();};};return Handle;});;

/* <inline asset> defined in bundle 'web_editor.assets_summernote' */
odoo.website_next_define='summernote/module/HelpDialog';;

/* /web_editor/static/lib/summernote/src/js/module/HelpDialog.js defined in bundle 'web_editor.assets_summernote' */
define([],function(){var HelpDialog=function(handler){this.showHelpDialog=function($editable,$dialog){return $.Deferred(function(deferred){var $helpDialog=$dialog.find('.note-help-dialog');$helpDialog.one('hidden.bs.modal',function(){deferred.resolve();}).modal('show');}).promise();};this.show=function(layoutInfo){var $dialog=layoutInfo.dialog(),$editable=layoutInfo.editable();handler.invoke('editor.saveRange',$editable,true);this.showHelpDialog($editable,$dialog).then(function(){handler.invoke('editor.restoreRange',$editable);});};};return HelpDialog;});;

/* <inline asset> defined in bundle 'web_editor.assets_summernote' */
odoo.website_next_define='summernote/module/ImageDialog';;

/* /web_editor/static/lib/summernote/src/js/module/ImageDialog.js defined in bundle 'web_editor.assets_summernote' */
define(['summernote/core/key'],function(key){var ImageDialog=function(handler){var toggleBtn=function($btn,isEnable){$btn.toggleClass('disabled',!isEnable);$btn.attr('disabled',!isEnable);};var bindEnterKey=function($input,$btn){$input.on('keypress',function(event){if(event.keyCode===key.code.ENTER){$btn.trigger('click');}});};this.show=function(layoutInfo){var $dialog=layoutInfo.dialog(),$editable=layoutInfo.editable();handler.invoke('editor.saveRange',$editable);this.showImageDialog($editable,$dialog).then(function(data){handler.invoke('editor.restoreRange',$editable);if(typeof data==='string'){handler.invoke('editor.insertImage',$editable,data);}else{handler.insertImages(layoutInfo,data);}}).fail(function(){handler.invoke('editor.restoreRange',$editable);});};this.showImageDialog=function($editable,$dialog){return $.Deferred(function(deferred){var $imageDialog=$dialog.find('.note-image-dialog');var $imageInput=$dialog.find('.note-image-input'),$imageUrl=$dialog.find('.note-image-url'),$imageBtn=$dialog.find('.note-image-btn');$imageDialog.one('shown.bs.modal',function(){$imageInput.replaceWith($imageInput.clone().on('change',function(){deferred.resolve(this.files||this.value);$imageDialog.modal('hide');}).val(''));$imageBtn.click(function(event){event.preventDefault();deferred.resolve($imageUrl.val());$imageDialog.modal('hide');});$imageUrl.on('keyup paste',function(event){var url;if(event.type==='paste'){url=event.originalEvent.clipboardData.getData('text');}else{url=$imageUrl.val();}
toggleBtn($imageBtn,url);}).val('').trigger('focus');bindEnterKey($imageUrl,$imageBtn);}).one('hidden.bs.modal',function(){$imageInput.off('change');$imageUrl.off('keyup paste keypress');$imageBtn.off('click');if(deferred.state()==='pending'){deferred.reject();}}).modal('show');});};};return ImageDialog;});;

/* <inline asset> defined in bundle 'web_editor.assets_summernote' */
odoo.website_next_define='summernote/module/LinkDialog';;

/* /web_editor/static/lib/summernote/src/js/module/LinkDialog.js defined in bundle 'web_editor.assets_summernote' */
define(['summernote/core/key'],function(key){var LinkDialog=function(handler){var toggleBtn=function($btn,isEnable){$btn.toggleClass('disabled',!isEnable);$btn.attr('disabled',!isEnable);};var bindEnterKey=function($input,$btn){$input.on('keypress',function(event){if(event.keyCode===key.code.ENTER){$btn.trigger('click');}});};this.showLinkDialog=function($editable,$dialog,linkInfo){return $.Deferred(function(deferred){var $linkDialog=$dialog.find('.note-link-dialog');var $linkText=$linkDialog.find('.note-link-text'),$linkUrl=$linkDialog.find('.note-link-url'),$linkBtn=$linkDialog.find('.note-link-btn'),$openInNewWindow=$linkDialog.find('input[type=checkbox]');$linkDialog.one('shown.bs.modal',function(){$linkText.val(linkInfo.text);$linkText.on('input',function(){toggleBtn($linkBtn,$linkText.val()&&$linkUrl.val());linkInfo.text=$linkText.val();});if(!linkInfo.url){linkInfo.url=linkInfo.text||'http://';toggleBtn($linkBtn,linkInfo.text);}
$linkUrl.on('input',function(){toggleBtn($linkBtn,$linkText.val()&&$linkUrl.val());if(!linkInfo.text){$linkText.val($linkUrl.val());}}).val(linkInfo.url).trigger('focus').trigger('select');bindEnterKey($linkUrl,$linkBtn);bindEnterKey($linkText,$linkBtn);$openInNewWindow.prop('checked',linkInfo.isNewWindow);$linkBtn.one('click',function(event){event.preventDefault();deferred.resolve({range:linkInfo.range,url:$linkUrl.val(),text:$linkText.val(),isNewWindow:$openInNewWindow.is(':checked')});$linkDialog.modal('hide');});}).one('hidden.bs.modal',function(){$linkText.off('input keypress');$linkUrl.off('input keypress');$linkBtn.off('click');if(deferred.state()==='pending'){deferred.reject();}}).modal('show');}).promise();};this.show=function(layoutInfo){var $editor=layoutInfo.editor(),$dialog=layoutInfo.dialog(),$editable=layoutInfo.editable(),$popover=layoutInfo.popover(),linkInfo=handler.invoke('editor.getLinkInfo',$editable);var options=$editor.data('options');handler.invoke('editor.saveRange',$editable);this.showLinkDialog($editable,$dialog,linkInfo).then(function(linkInfo){handler.invoke('editor.restoreRange',$editable);handler.invoke('editor.createLink',$editable,linkInfo,options);handler.invoke('popover.hide',$popover);}).fail(function(){handler.invoke('editor.restoreRange',$editable);});};};return LinkDialog;});;

/* <inline asset> defined in bundle 'web_editor.assets_summernote' */
odoo.website_next_define='summernote/module/Popover';;

/* /web_editor/static/lib/summernote/src/js/module/Popover.js defined in bundle 'web_editor.assets_summernote' */
define(['summernote/core/func','summernote/core/list','summernote/module/Button'],function(func,list,Button){var Popover=function(){var button=new Button();this.button=button;var posFromPlaceholder=function(placeholder,options){var isAirMode=options&&options.isAirMode;var isLeftTop=options&&options.isLeftTop;var $placeholder=$(placeholder);var pos=isAirMode?$placeholder.offset():$placeholder.position();var height=isLeftTop?0:$placeholder.outerHeight(true);return{left:pos.left,top:pos.top+height};};var showPopover=function($popover,pos){$popover.css({display:'block',left:pos.left,top:pos.top});};var PX_POPOVER_ARROW_OFFSET_X=20;this.update=function($popover,styleInfo,isAirMode){button.update($popover,styleInfo);var $linkPopover=$popover.find('.note-link-popover');if(styleInfo.anchor){var $anchor=$linkPopover.find('a');var href=$(styleInfo.anchor).attr('href');var target=$(styleInfo.anchor).attr('target');$anchor.attr('href',href).text(href);if(!target){$anchor.removeAttr('target');}else{$anchor.attr('target','_blank');}
showPopover($linkPopover,posFromPlaceholder(styleInfo.anchor,{isAirMode:isAirMode}));}else{$linkPopover.hide();}
var $imagePopover=$popover.find('.note-image-popover');if(styleInfo.image){showPopover($imagePopover,posFromPlaceholder(styleInfo.image,{isAirMode:isAirMode,isLeftTop:true}));}else{$imagePopover.hide();}
var $airPopover=$popover.find('.note-air-popover');if(isAirMode&&styleInfo.range&&!styleInfo.range.isCollapsed()){var rect=list.last(styleInfo.range.getClientRects());if(rect){var bnd=func.rect2bnd(rect);showPopover($airPopover,{left:Math.max(bnd.left+bnd.width/2-PX_POPOVER_ARROW_OFFSET_X,0),top:bnd.top+bnd.height});}}else{$airPopover.hide();}};this.updateRecentColor=function(button,eventName,value){button.updateRecentColor(button,eventName,value);};this.hide=function($popover){$popover.children().hide();};};return Popover;});;

/* <inline asset> defined in bundle 'web_editor.assets_summernote' */
odoo.website_next_define='summernote/module/Statusbar';;

/* /web_editor/static/lib/summernote/src/js/module/Statusbar.js defined in bundle 'web_editor.assets_summernote' */
define(['summernote/core/dom'],function(dom){var EDITABLE_PADDING=24;var Statusbar=function(){var $document=$(document);this.attach=function(layoutInfo,options){if(!options.disableResizeEditor){layoutInfo.statusbar().on('mousedown',hStatusbarMousedown);}};var hStatusbarMousedown=function(event){event.preventDefault();event.stopPropagation();var $editable=dom.makeLayoutInfo(event.target).editable();var editableTop=$editable.offset().top-$document.scrollTop();var layoutInfo=dom.makeLayoutInfo(event.currentTarget||event.target);var options=layoutInfo.editor().data('options');$document.on('mousemove',function(event){var nHeight=event.clientY-(editableTop+EDITABLE_PADDING);nHeight=(options.minHeight>0)?Math.max(nHeight,options.minHeight):nHeight;nHeight=(options.maxHeight>0)?Math.min(nHeight,options.maxHeight):nHeight;$editable.height(nHeight);}).one('mouseup',function(){$document.off('mousemove');});};};return Statusbar;});;

/* <inline asset> defined in bundle 'web_editor.assets_summernote' */
odoo.website_next_define='summernote/module/Toolbar';;

/* /web_editor/static/lib/summernote/src/js/module/Toolbar.js defined in bundle 'web_editor.assets_summernote' */
define(['summernote/core/list','summernote/core/dom','summernote/module/Button'],function(list,dom,Button){var Toolbar=function(){var button=new Button();this.button=button;this.update=function($toolbar,styleInfo){button.update($toolbar,styleInfo);};this.updateRecentColor=function(buttonNode,eventName,value){button.updateRecentColor(buttonNode,eventName,value);};this.activate=function($toolbar){$toolbar.find('button').not('button[data-event="codeview"]').removeClass('disabled');};this.deactivate=function($toolbar){$toolbar.find('button').not('button[data-event="codeview"]').addClass('disabled');};this.updateFullscreen=function($container,bFullscreen){var $btn=$container.find('button[data-event="fullscreen"]');$btn.toggleClass('active',bFullscreen);};this.updateCodeview=function($container,isCodeview){var $btn=$container.find('button[data-event="codeview"]');$btn.toggleClass('active',isCodeview);if(isCodeview){this.deactivate($container);}else{this.activate($container);}};this.get=function($editable,name){var $toolbar=dom.makeLayoutInfo($editable).toolbar();return $toolbar.find('[data-name='+name+']');};this.setButtonState=function($editable,name,isActive){isActive=(isActive===false)?false:true;var $button=this.get($editable,name);$button.toggleClass('active',isActive);};};return Toolbar;});;

/* <inline asset> defined in bundle 'web_editor.assets_summernote' */
odoo.website_next_define='summernote/Renderer';;

/* /web_editor/static/lib/summernote/src/js/Renderer.js defined in bundle 'web_editor.assets_summernote' */
define(['summernote/core/agent','summernote/core/dom','summernote/core/func','summernote/core/list'],function(agent,dom,func,list){var Renderer=function(){var tplButton=function(label,options){var event=options.event;var value=options.value;var title=options.title;var className=options.className;var dropdown=options.dropdown;var hide=options.hide;return(dropdown?'<div class="btn-group'+
(className?' '+className:'')+'">':'')+'<button type="button"'+' class="btn btn-secondary btn-sm'+
((!dropdown&&className)?' '+className:'')+
(dropdown?' dropdown-toggle':'')+'"'+
(dropdown?' data-toggle="dropdown"':'')+
(title?' title="'+title+'"':'')+
(event?' data-event="'+event+'"':'')+
(value?' data-value=\''+value+'\'':'')+
(hide?' data-hide=\''+hide+'\'':'')+' tabindex="-1">'+
label+
(dropdown?' <span class="caret"></span>':'')+'</button>'+
(dropdown||'')+
(dropdown?'</div>':'');};var tplIconButton=function(iconClassName,options){var label='<i class="'+iconClassName+'"></i>';return tplButton(label,options);};var tplPopover=function(className,content){var $popover=$('<div class="'+className+' popover bottom in" style="display: none;">'+'<div class="arrow"></div>'+'<div class="popover-body">'+'</div>'+'</div>');$popover.find('.popover-body').append(content);return $popover;};var tplDialog=function(className,title,body,footer){return'<div class="'+className+' modal" role="dialog" aria-hidden="false">'+'<div class="modal-dialog">'+'<div class="modal-content">'+
(title?'<header class="modal-header">'+'<h4 class="modal-title">'+title+'</h4>'+'<button type="button" class="close" aria-hidden="true" tabindex="-1">&times;</button>'+'</header>':'')+'<main class="modal-body">'+body+'</main>'+
(footer?'<header class="modal-footer">'+footer+'</header>':'')+'</div>'+'</div>'+'</div>';};var tplDropdown=function(contents,className,nodeName){var classes='dropdown-menu'+(className?' '+className:'');nodeName=nodeName||'ul';if(contents instanceof Array){contents=contents.join('');}
return'<'+nodeName+' class="'+classes+'">'+contents+'</'+nodeName+'>';};var tplButtonInfo={picture:function(lang,options){return tplIconButton(options.iconPrefix+options.icons.image.image,{event:'showImageDialog',title:lang.image.image,hide:true});},link:function(lang,options){return tplIconButton(options.iconPrefix+options.icons.link.link,{event:'showLinkDialog',title:lang.link.link,hide:true});},table:function(lang,options){var dropdown=['<div class="note-dimension-picker">','<div class="note-dimension-picker-mousecatcher" data-event="insertTable" data-value="1x1"></div>','<div class="note-dimension-picker-highlighted"></div>','<div class="note-dimension-picker-unhighlighted"></div>','</div>','<div class="note-dimension-display"> 1 x 1 </div>'];return tplIconButton(options.iconPrefix+options.icons.table.table,{title:lang.table.table,dropdown:tplDropdown(dropdown,'note-table')});},style:function(lang,options){var items=options.styleTags.reduce(function(memo,v){var label=lang.style[v==='p'?'normal':v];return memo+'<li><a class="dropdown-item" data-event="formatBlock" href="#" data-value="'+v+'">'+
((v==='p'||v==='pre')?label:'<'+v+'>'+label+'</'+v+'>')+'</a></li>';},'');return tplIconButton(options.iconPrefix+options.icons.style.style,{title:lang.style.style,dropdown:tplDropdown(items)});},fontname:function(lang,options){var realFontList=[];var items=options.fontNames.reduce(function(memo,v){if(!agent.isFontInstalled(v)&&!list.contains(options.fontNamesIgnoreCheck,v)){return memo;}
realFontList.push(v);return memo+'<li><a data-event="fontName" href="#" data-value="'+v+'" style="font-family:\''+v+'\'">'+'<i class="'+options.iconPrefix+options.icons.misc.check+'"></i> '+v+'</a></li>';},'');var hasDefaultFont=agent.isFontInstalled(options.defaultFontName);var defaultFontName=(hasDefaultFont)?options.defaultFontName:realFontList[0];var label='<span class="note-current-fontname">'+
defaultFontName+'</span>';return tplButton(label,{title:lang.font.name,className:'note-fontname',dropdown:tplDropdown(items,'note-check')});},fontsize:function(lang,options){var items=options.fontSizes.reduce(function(memo,v){return memo+'<li><a data-event="fontSize" href="#" data-value="'+v+'">'+'<i class="'+options.iconPrefix+options.icons.misc.check+'"></i> '+v+'</a></li>';},'');var label='<span class="note-current-fontsize">11</span>';return tplButton(label,{title:lang.font.size,className:'note-fontsize',dropdown:tplDropdown(items,'note-check')});},color:function(lang,options){var colorButtonLabel='<i class="'+
options.iconPrefix+options.icons.color.recent+'" id="colors_preview" style="color:white;background-color:#B35E9B"></i>';var colorButton=tplButton(colorButtonLabel,{className:'note-recent-color',title:lang.color.recent,event:'color',value:'{"backColor":"#B35E9B"}'});var items=['<li class="flex"><div class="btn-group flex-column">','<div class="note-palette-title">'+lang.color.background+'</div>','<div class="note-color-reset" data-event="backColor"',' data-value="inherit" title="'+lang.color.transparent+'">'+lang.color.setTransparent+'</div>','<div class="note-color-palette" data-target-event="backColor"></div>','</div><div class="btn-group flex-column">','<div class="note-palette-title">'+lang.color.foreground+'</div>','<div class="note-color-reset" data-event="foreColor" data-value="inherit" title="'+lang.color.reset+'">',lang.color.resetToDefault,'</div>','<div class="note-color-palette" data-target-event="foreColor"></div>','</div></li>'];var moreButton=tplButton('',{title:lang.color.more,dropdown:tplDropdown(items)});return colorButton+moreButton;},bold:function(lang,options){return tplIconButton(options.iconPrefix+options.icons.font.bold,{event:'bold',title:lang.font.bold});},italic:function(lang,options){return tplIconButton(options.iconPrefix+options.icons.font.italic,{event:'italic',title:lang.font.italic});},underline:function(lang,options){return tplIconButton(options.iconPrefix+options.icons.font.underline,{event:'underline',title:lang.font.underline});},strikethrough:function(lang,options){return tplIconButton(options.iconPrefix+options.icons.font.strikethrough,{event:'strikethrough',title:lang.font.strikethrough});},superscript:function(lang,options){return tplIconButton(options.iconPrefix+options.icons.font.superscript,{event:'superscript',title:lang.font.superscript});},subscript:function(lang,options){return tplIconButton(options.iconPrefix+options.icons.font.subscript,{event:'subscript',title:lang.font.subscript});},clear:function(lang,options){return tplIconButton(options.iconPrefix+options.icons.font.clear,{event:'removeFormat',title:lang.font.clear});},ul:function(lang,options){return tplIconButton(options.iconPrefix+options.icons.lists.unordered,{event:'insertUnorderedList',title:lang.lists.unordered});},ol:function(lang,options){return tplIconButton(options.iconPrefix+options.icons.lists.ordered,{event:'insertOrderedList',title:lang.lists.ordered});},paragraph:function(lang,options){var leftButton=tplIconButton(options.iconPrefix+options.icons.paragraph.left,{title:lang.paragraph.left,event:'justifyLeft'});var centerButton=tplIconButton(options.iconPrefix+options.icons.paragraph.center,{title:lang.paragraph.center,event:'justifyCenter'});var rightButton=tplIconButton(options.iconPrefix+options.icons.paragraph.right,{title:lang.paragraph.right,event:'justifyRight'});var justifyButton=tplIconButton(options.iconPrefix+options.icons.paragraph.justify,{title:lang.paragraph.justify,event:'justifyFull'});var outdentButton=tplIconButton(options.iconPrefix+options.icons.paragraph.outdent,{title:lang.paragraph.outdent,event:'outdent'});var indentButton=tplIconButton(options.iconPrefix+options.icons.paragraph.indent,{title:lang.paragraph.indent,event:'indent'});var dropdown=['<div class="note-align btn-group">',leftButton+centerButton+rightButton+justifyButton,'</div><div class="note-list btn-group">',indentButton+outdentButton,'</div>'];return tplIconButton(options.iconPrefix+options.icons.paragraph.paragraph,{title:lang.paragraph.paragraph,dropdown:tplDropdown(dropdown,'','div')});},height:function(lang,options){var items=options.lineHeights.reduce(function(memo,v){return memo+'<li><a data-event="lineHeight" href="#" data-value="'+parseFloat(v)+'">'+'<i class="'+options.iconPrefix+options.icons.misc.check+'"></i> '+v+'</a></li>';},'');return tplIconButton(options.iconPrefix+options.icons.font.height,{title:lang.font.height,dropdown:tplDropdown(items,'note-check')});},help:function(lang,options){return tplIconButton(options.iconPrefix+options.icons.options.help,{event:'showHelpDialog',title:lang.options.help,hide:true});},fullscreen:function(lang,options){return tplIconButton(options.iconPrefix+options.icons.options.fullscreen,{event:'fullscreen',title:lang.options.fullscreen});},codeview:function(lang,options){return tplIconButton(options.iconPrefix+options.icons.options.codeview,{event:'codeview',title:lang.options.codeview});},undo:function(lang,options){return tplIconButton(options.iconPrefix+options.icons.history.undo,{event:'undo',title:lang.history.undo});},redo:function(lang,options){return tplIconButton(options.iconPrefix+options.icons.history.redo,{event:'redo',title:lang.history.redo});},hr:function(lang,options){return tplIconButton(options.iconPrefix+options.icons.hr.insert,{event:'insertHorizontalRule',title:lang.hr.insert});}};var tplPopovers=function(lang,options){var tplLinkPopover=function(){var linkButton=tplIconButton(options.iconPrefix+options.icons.link.edit,{title:lang.link.edit,event:'showLinkDialog',hide:true});var unlinkButton=tplIconButton(options.iconPrefix+options.icons.link.unlink,{title:lang.link.unlink,event:'unlink'});var content='<a href="http://www.google.com" target="_blank">www.google.com</a>&nbsp;&nbsp;'+'<div class="note-insert btn-group">'+
linkButton+unlinkButton+'</div>';return tplPopover('note-link-popover',content);};var tplImagePopover=function(){var autoButton=tplButton('<span class="note-fontsize-10">Auto</span>',{event:'resize',value:'auto'});var fullButton=tplButton('<span class="note-fontsize-10">100%</span>',{title:lang.image.resizeFull,event:'resize',value:'1'});var halfButton=tplButton('<span class="note-fontsize-10">50%</span>',{title:lang.image.resizeHalf,event:'resize',value:'0.5'});var quarterButton=tplButton('<span class="note-fontsize-10">25%</span>',{title:lang.image.resizeQuarter,event:'resize',value:'0.25'});var leftButton=tplIconButton(options.iconPrefix+options.icons.image.floatLeft,{title:lang.image.floatLeft,event:'floatMe',value:'left'});var rightButton=tplIconButton(options.iconPrefix+options.icons.image.floatRight,{title:lang.image.floatRight,event:'floatMe',value:'right'});var justifyButton=tplIconButton(options.iconPrefix+options.icons.image.floatNone,{title:lang.image.floatNone,event:'floatMe',value:'none'});var roundedButton=tplIconButton(options.iconPrefix+options.icons.image.shapeRounded,{title:lang.image.shapeRounded,event:'imageShape',value:'rounded'});var circleButton=tplIconButton(options.iconPrefix+options.icons.image.shapeCircle,{title:lang.image.shapeCircle,event:'imageShape',value:'rounded-circle'});var thumbnailButton=tplIconButton(options.iconPrefix+options.icons.image.shapeThumbnail,{title:lang.image.shapeThumbnail,event:'imageShape',value:'img-thumbnail'});var noneButton=tplIconButton(options.iconPrefix+options.icons.image.shapeNone,{title:lang.image.shapeNone,event:'imageShape',value:''});var removeButton=tplIconButton(options.iconPrefix+options.icons.image.remove,{title:lang.image.remove,event:'removeMedia',value:'none'});var content=(options.disableResizeImage?'':'<div class="btn-group">'+autoButton+fullButton+halfButton+quarterButton+'</div>')+'<div class="btn-group">'+leftButton+rightButton+justifyButton+'</div>'+'<div class="btn-group">'+roundedButton+circleButton+thumbnailButton+noneButton+'</div>'+'<div class="btn-group">'+removeButton+'</div>';return tplPopover('note-image-popover',content);};var tplAirPopover=function(){var $content=$('<div />');for(var idx=0,len=options.airPopover.length;idx<len;idx++){var group=options.airPopover[idx];var $group=$('<div class="note-'+group[0]+' btn-group">');for(var i=0,lenGroup=group[1].length;i<lenGroup;i++){var $button=$(tplButtonInfo[group[1][i]](lang,options));$button.attr('data-name',group[1][i]);$group.append($button);}
$content.append($group);}
return tplPopover('note-air-popover',$content.children());};var $notePopover=$('<div class="note-popover" />');$notePopover.append(tplLinkPopover());$notePopover.append(tplImagePopover());if(options.airMode){$notePopover.append(tplAirPopover());}
return $notePopover;};this.tplButtonInfo=tplButtonInfo;this.tplPopovers=tplPopovers;var tplHandles=function(options){return'<div class="note-handle">'+'<div class="note-control-selection">'+'<div class="note-control-selection-bg"></div>'+'<div class="note-control-holder note-control-nw"></div>'+'<div class="note-control-holder note-control-ne"></div>'+'<div class="note-control-holder note-control-sw"></div>'+'<div class="'+
(options.disableResizeImage?'note-control-holder':'note-control-sizing')+' note-control-se"></div>'+
(options.disableResizeImage?'':'<div class="note-control-selection-info"></div>')+'</div>'+'</div>';};var tplShortcut=function(title,keys){var keyClass='note-shortcut-col col-6 note-shortcut-';var body=[];for(var i in keys){if(keys.hasOwnProperty(i)){body.push('<div class="'+keyClass+'key">'+keys[i].kbd+'</div>'+'<div class="'+keyClass+'name">'+keys[i].text+'</div>');}}
return'<div class="note-shortcut-row row"><div class="'+keyClass+'title offset-6">'+title+'</div></div>'+'<div class="note-shortcut-row row">'+body.join('</div><div class="note-shortcut-row row">')+'</div>';};var tplShortcutText=function(lang){var keys=[{kbd:'⌘ + B',text:lang.font.bold},{kbd:'⌘ + I',text:lang.font.italic},{kbd:'⌘ + U',text:lang.font.underline},{kbd:'⌘ + \\',text:lang.font.clear}];return tplShortcut(lang.shortcut.textFormatting,keys);};var tplShortcutAction=function(lang){var keys=[{kbd:'⌘ + Z',text:lang.history.undo},{kbd:'⌘ + ⇧ + Z',text:lang.history.redo},{kbd:'⌘ + ]',text:lang.paragraph.indent},{kbd:'⌘ + [',text:lang.paragraph.outdent},{kbd:'⌘ + ENTER',text:lang.hr.insert}];return tplShortcut(lang.shortcut.action,keys);};var tplShortcutPara=function(lang){var keys=[{kbd:'⌘ + ⇧ + L',text:lang.paragraph.left},{kbd:'⌘ + ⇧ + E',text:lang.paragraph.center},{kbd:'⌘ + ⇧ + R',text:lang.paragraph.right},{kbd:'⌘ + ⇧ + J',text:lang.paragraph.justify},{kbd:'⌘ + ⇧ + NUM7',text:lang.lists.ordered},{kbd:'⌘ + ⇧ + NUM8',text:lang.lists.unordered}];return tplShortcut(lang.shortcut.paragraphFormatting,keys);};var tplShortcutStyle=function(lang){var keys=[{kbd:'⌘ + NUM0',text:lang.style.normal},{kbd:'⌘ + NUM1',text:lang.style.h1},{kbd:'⌘ + NUM2',text:lang.style.h2},{kbd:'⌘ + NUM3',text:lang.style.h3},{kbd:'⌘ + NUM4',text:lang.style.h4},{kbd:'⌘ + NUM5',text:lang.style.h5},{kbd:'⌘ + NUM6',text:lang.style.h6}];return tplShortcut(lang.shortcut.documentStyle,keys);};var tplExtraShortcuts=function(lang,options){var extraKeys=options.extraKeys;var keys=[];for(var key in extraKeys){if(extraKeys.hasOwnProperty(key)){keys.push({kbd:key,text:extraKeys[key]});}}
return tplShortcut(lang.shortcut.extraKeys,keys);};var tplShortcutTable=function(lang,options){var colClass='class="note-shortcut note-shortcut-col col-md-6 col-12"';var template=['<div '+colClass+'>'+tplShortcutAction(lang,options)+'</div>'+'<div '+colClass+'>'+tplShortcutText(lang,options)+'</div>','<div '+colClass+'>'+tplShortcutStyle(lang,options)+'</div>'+'<div '+colClass+'>'+tplShortcutPara(lang,options)+'</div>'];if(options.extraKeys){template.push('<div '+colClass+'>'+tplExtraShortcuts(lang,options)+'</div>');}
return'<div class="note-shortcut-row row">'+
template.join('</div><div class="note-shortcut-row row">')+'</div>';};var replaceMacKeys=function(sHtml){return sHtml.replace(/⌘/g,'Ctrl').replace(/⇧/g,'Shift');};var tplDialogInfo={image:function(lang,options){var imageLimitation='';if(options.maximumImageFileSize){var unit=Math.floor(Math.log(options.maximumImageFileSize)/Math.log(1024));var readableSize=(options.maximumImageFileSize/Math.pow(1024,unit)).toFixed(2)*1+' '+' KMGTP'[unit]+'B';imageLimitation='<small>'+lang.image.maximumFileSize+' : '+readableSize+'</small>';}
var body='<div class="form-group row note-group-select-from-files">'+'<label>'+lang.image.selectFromFiles+'</label>'+'<input class="note-image-input form-control" type="file" name="files" accept="image/*" multiple="multiple" />'+
imageLimitation+'</div>'+'<div class="form-group row">'+'<label>'+lang.image.url+'</label>'+'<input class="note-image-url form-control col-md-12" type="text" />'+'</div>';var footer='<button href="#" class="btn btn-primary note-image-btn disabled" disabled>'+lang.image.insert+'</button>';return tplDialog('note-image-dialog',lang.image.insert,body,footer);},link:function(lang,options){var body='<div class="form-group row">'+'<label>'+lang.link.textToDisplay+'</label>'+'<input class="note-link-text form-control col-md-12" type="text" />'+'</div>'+'<div class="form-group row">'+'<label>'+lang.link.url+'</label>'+'<input class="note-link-url form-control col-md-12" type="text" value="http://" />'+'</div>'+
(!options.disableLinkTarget?'<div class="checkbox">'+'<label>'+'<input type="checkbox" checked> '+
lang.link.openInNewWindow+'</label>'+'</div>':'');var footer='<button href="#" class="btn btn-primary note-link-btn disabled" disabled>'+lang.link.insert+'</button>';return tplDialog('note-link-dialog',lang.link.insert,body,footer);},help:function(lang,options){var body='<a class="modal-close float-right" aria-hidden="true" tabindex="-1">'+lang.shortcut.close+'</a>'+'<div class="title">'+lang.shortcut.shortcuts+'</div>'+
(agent.isMac?tplShortcutTable(lang,options):replaceMacKeys(tplShortcutTable(lang,options)))+'<p class="text-center">'+'<a href="//summernote.org/" target="_blank">Summernote @VERSION</a> · '+'<a href="//github.com/summernote/summernote" target="_blank">Project</a> · '+'<a href="//github.com/summernote/summernote/issues" target="_blank">Issues</a>'+'</p>';return tplDialog('note-help-dialog','',body,'');}};var tplDialogs=function(lang,options){var dialogs='';$.each(tplDialogInfo,function(idx,tplDialog){dialogs+=tplDialog(lang,options);});return'<div class="note-dialog">'+dialogs+'</div>';};var tplStatusbar=function(){return'<div class="note-resizebar">'+'<div class="note-icon-bar"></div>'+'<div class="note-icon-bar"></div>'+'<div class="note-icon-bar"></div>'+'</div>';};var representShortcut=function(str){if(agent.isMac){str=str.replace('CMD','⌘').replace('SHIFT','⇧');}
return str.replace('BACKSLASH','\\').replace('SLASH','/').replace('LEFTBRACKET','[').replace('RIGHTBRACKET',']');};var createTooltip=function($container,keyMap,sPlacement){var invertedKeyMap=func.invertObject(keyMap);var $buttons=$container.find('button');$buttons.each(function(i,elBtn){var $btn=$(elBtn);var sShortcut=invertedKeyMap[$btn.data('event')];if(sShortcut){$btn.attr('title',function(i,v){return v+' ('+representShortcut(sShortcut)+')';});}}).tooltip({container:'body',trigger:'hover',placement:sPlacement||'top'}).on('click',function(){$(this).tooltip('hide');});};var createPalette=function($container,options){var colorInfo=options.colors;$container.find('.note-color-palette').each(function(){var $palette=$(this),eventName=$palette.attr('data-target-event');var paletteContents=[];for(var row=0,lenRow=colorInfo.length;row<lenRow;row++){var colors=colorInfo[row];var buttons=[];for(var col=0,lenCol=colors.length;col<lenCol;col++){var color=colors[col];buttons.push(['<button type="button" class="note-color-btn" style="background-color:',color,';" data-event="',eventName,'" data-value="',color,'" title="',color,'" data-toggle="button" tabindex="-1"></button>'].join(''));}
paletteContents.push('<div class="note-color-row">'+buttons.join('')+'</div>');}
$palette.html(paletteContents.join(''));});};this.createPalette=createPalette;this.createLayoutByAirMode=function($holder,options){var langInfo=options.langInfo;var keyMap=options.keyMap[agent.isMac?'mac':'pc'];var id=func.uniqueId();$holder.addClass('note-air-editor note-editable');$holder.attr({'data-note-id':id,'contentEditable':true});var body=document.body;var $container=$('#web_editor-toolbars')
var $popover=$(this.tplPopovers(langInfo,options));$popover.addClass('note-air-layout');$popover.attr('id','note-popover-'+id);$popover.appendTo($container);createTooltip($popover,keyMap);this.createPalette($popover,options);var $handle=$(tplHandles(options));$handle.addClass('note-air-layout');$handle.attr('id','note-handle-'+id);$handle.appendTo($container);var $dialog=$(tplDialogs(langInfo,options));$dialog.addClass('note-air-layout');$dialog.attr('id','note-dialog-'+id);$dialog.find('button.close, a.modal-close').click(function(){$(this).closest('.modal').modal('hide');});$dialog.appendTo($container);};this.createLayoutByFrame=function($holder,options){var langInfo=options.langInfo;var $editor=$('<div class="note-editor panel panel-default" />');if(options.width){$editor.width(options.width);}
if(options.height>0){$('<div class="note-statusbar">'+(options.disableResizeEditor?'':tplStatusbar())+'</div>').prependTo($editor);}
var $editingArea=$('<div class="note-editing-area" />');var isContentEditable=!$holder.is(':disabled');var $editable=$('<div class="note-editable panel-body" contentEditable="'+isContentEditable+'"></div>').prependTo($editingArea);if(options.height){$editable.height(options.height);}
if(options.direction){$editable.attr('dir',options.direction);}
var placeholder=$holder.attr('placeholder')||options.placeholder;if(placeholder){$editable.attr('data-placeholder',placeholder);}
$editable.html(dom.html($holder)||dom.emptyPara);$('<textarea class="note-codable"></textarea>').prependTo($editingArea);var $popover=$(this.tplPopovers(langInfo,options)).prependTo($editingArea);this.createPalette($popover,options);createTooltip($popover,keyMap);$(tplHandles(options)).prependTo($editingArea);$editingArea.prependTo($editor);var $toolbar=$('<div class="note-toolbar panel-heading" />');for(var idx=0,len=options.toolbar.length;idx<len;idx++){var groupName=options.toolbar[idx][0];var groupButtons=options.toolbar[idx][1];var $group=$('<div class="note-'+groupName+' btn-group" />');for(var i=0,btnLength=groupButtons.length;i<btnLength;i++){var buttonInfo=tplButtonInfo[groupButtons[i]];if(!$.isFunction(buttonInfo)){continue;}
var $button=$(buttonInfo(langInfo,options));$button.attr('data-name',groupButtons[i]);$group.append($button);}
$toolbar.append($group);}
var keyMap=options.keyMap[agent.isMac?'mac':'pc'];this.createPalette($toolbar,options);createTooltip($toolbar,keyMap,'bottom');$toolbar.prependTo($editor);$('<div class="note-dropzone"><div class="note-dropzone-message"></div></div>').prependTo($editor);var $dialogContainer=options.dialogsInBody?$(document.body):$editor;var $dialog=$(tplDialogs(langInfo,options)).prependTo($dialogContainer);$dialog.find('button.close, a.modal-close').click(function(){$(this).closest('.modal').modal('hide');});$editor.insertAfter($holder);$holder.hide();};this.hasNoteEditor=function($holder){return this.noteEditorFromHolder($holder).length>0;};this.noteEditorFromHolder=function($holder){if($holder.hasClass('note-air-editor')){return $holder;}else if($holder.next().hasClass('note-editor')){return $holder.next();}else{return $();}};this.createLayout=function($holder,options){if(options.airMode){this.createLayoutByAirMode($holder,options);}else{this.createLayoutByFrame($holder,options);}};this.layoutInfoFromHolder=function($holder){var $editor=this.noteEditorFromHolder($holder);if(!$editor.length){return;}
$editor.data('holder',$holder);return dom.buildLayoutInfo($editor);};this.removeLayout=function($holder,layoutInfo,options){if(options.airMode){$holder.removeClass('note-air-editor note-editable').removeAttr('contentEditable');layoutInfo.popover().remove();layoutInfo.handle().remove();layoutInfo.dialog().remove();}else{$holder.html(layoutInfo.editable().html());if(options.dialogsInBody){layoutInfo.dialog().remove();}
layoutInfo.editor().remove();$holder.show();}};this.getTemplate=function(){return{button:tplButton,iconButton:tplIconButton,dialog:tplDialog,dropdown:tplDropdown};};this.addButtonInfo=function(name,buttonInfo){tplButtonInfo[name]=buttonInfo;};this.addDialogInfo=function(name,dialogInfo){tplDialogInfo[name]=dialogInfo;};};return Renderer;});;

/* <inline asset> defined in bundle 'web_editor.assets_summernote' */
odoo.website_next_define='summernote/EventHandler';;

/* /web_editor/static/lib/summernote/src/js/EventHandler.js defined in bundle 'web_editor.assets_summernote' */
define(['summernote/core/agent','summernote/core/func','summernote/core/dom','summernote/core/async','summernote/core/key','summernote/core/list','summernote/editing/History','summernote/module/Editor','summernote/module/Toolbar','summernote/module/Statusbar','summernote/module/Popover','summernote/module/Handle','summernote/module/Fullscreen','summernote/module/Codeview','summernote/module/DragAndDrop','summernote/module/Clipboard','summernote/module/LinkDialog','summernote/module/ImageDialog','summernote/module/HelpDialog'],function(agent,func,dom,async,key,list,History,Editor,Toolbar,Statusbar,Popover,Handle,Fullscreen,Codeview,DragAndDrop,Clipboard,LinkDialog,ImageDialog,HelpDialog){var EventHandler=function(){var self=this;var modules=this.modules={editor:new Editor(this),toolbar:new Toolbar(this),statusbar:new Statusbar(this),popover:new Popover(this),handle:new Handle(this),fullscreen:new Fullscreen(this),codeview:new Codeview(this),dragAndDrop:new DragAndDrop(this),clipboard:new Clipboard(this),linkDialog:new LinkDialog(this),imageDialog:new ImageDialog(this),helpDialog:new HelpDialog(this)};this.invoke=function(){var moduleAndMethod=list.head(list.from(arguments));var args=list.tail(list.from(arguments));var splits=moduleAndMethod.split('.');var hasSeparator=splits.length>1;var moduleName=hasSeparator&&list.head(splits);var methodName=hasSeparator?list.last(splits):list.head(splits);var module=this.getModule(moduleName);var method=module[methodName];return method&&method.apply(module,args);};this.getModule=function(moduleName){return this.modules[moduleName]||this.modules.editor;};var bindCustomEvent=this.bindCustomEvent=function($holder,callbacks,eventNamespace){return function(){var callback=callbacks[func.namespaceToCamel(eventNamespace,'on')];if(callback){callback.apply($holder[0],arguments);}
return $holder.trigger('summernote.'+eventNamespace,arguments);};};this.insertImages=function(layoutInfo,files){var $editor=layoutInfo.editor(),$editable=layoutInfo.editable(),$holder=layoutInfo.holder();var callbacks=$editable.data('callbacks');var options=$editor.data('options');if(callbacks.onImageUpload){bindCustomEvent($holder,callbacks,'image.upload')(files);}else{$.each(files,function(idx,file){var filename=file.name;if(options.maximumImageFileSize&&options.maximumImageFileSize<file.size){bindCustomEvent($holder,callbacks,'image.upload.error')(options.langInfo.image.maximumFileSizeError);}else{async.readFileAsDataURL(file).then(function(sDataURL){modules.editor.insertImage($editable,sDataURL,filename);}).fail(function(){bindCustomEvent($holder,callbacks,'image.upload.error')(options.langInfo.image.maximumFileSizeError);});}});}};var commands={showLinkDialog:function(layoutInfo){modules.linkDialog.show(layoutInfo);},showImageDialog:function(layoutInfo){modules.imageDialog.show(layoutInfo);},showHelpDialog:function(layoutInfo){modules.helpDialog.show(layoutInfo);},fullscreen:function(layoutInfo){modules.fullscreen.toggle(layoutInfo);},codeview:function(layoutInfo){modules.codeview.toggle(layoutInfo);}};var hMousedown=function(event){if(dom.isImg(event.target)){event.preventDefault();}};var hKeyupAndMouseup=function(event){var layoutInfo=dom.makeLayoutInfo(event.currentTarget||event.target);modules.editor.removeBogus(layoutInfo.editable());hToolbarAndPopoverUpdate(event);};this.updateStyleInfo=function(styleInfo,layoutInfo){if(!styleInfo){return;}
var isAirMode=(layoutInfo.editor().data('options')||{}).airMode;if(!isAirMode){modules.toolbar.update(layoutInfo.toolbar(),styleInfo);}
modules.popover.update(layoutInfo.popover(),styleInfo,isAirMode);modules.handle.update(layoutInfo.handle(),styleInfo,isAirMode);};var hToolbarAndPopoverUpdate=function(event){var target=event.target;setTimeout(function(){var layoutInfo=dom.makeLayoutInfo(target);if(!layoutInfo){return;}
var $editable=layoutInfo.editable();if(event.setStyleInfoFromEditable){var styleInfo=modules.editor.styleFromNode($editable);}else{if(!event.isDefaultPrevented()){modules.editor.saveRange($editable);}
var styleInfo=modules.editor.currentStyle(target);}
self.updateStyleInfo(styleInfo,layoutInfo);},0);};var hScroll=function(event){var layoutInfo=dom.makeLayoutInfo(event.currentTarget||event.target);modules.popover.hide(layoutInfo.popover());modules.handle.hide(layoutInfo.handle());};var hToolbarAndPopoverMousedown=function(event){var $btn=$(event.target).closest('[data-event]');if($btn.length){event.preventDefault();}};var hToolbarAndPopoverClick=function(event){var $btn=$(event.target).closest('[data-event]');if(!$btn.length){return;}
var eventName=$btn.attr('data-event'),value=$btn.attr('data-value'),hide=$btn.attr('data-hide');var layoutInfo=dom.makeLayoutInfo(event.target);var $target;if($.inArray(eventName,['resize','floatMe','removeMedia','imageShape'])!==-1){var $selection=layoutInfo.handle().find('.note-control-selection');$target=$($selection.data('target'));}
if(hide){$btn.parents('.popover').hide();}
if($.isFunction($.summernote.pluginEvents[eventName])){$.summernote.pluginEvents[eventName](event,modules.editor,layoutInfo,value);}else if(modules.editor[eventName]){var $editable=layoutInfo.editable();$editable.focus();modules.editor[eventName]($editable,value,$target);event.preventDefault();}else if(commands[eventName]){commands[eventName].call(this,layoutInfo);event.preventDefault();}
if($.inArray(eventName,['backColor','foreColor'])!==-1){var options=layoutInfo.editor().data('options',options);var module=options.airMode?modules.popover:modules.toolbar;module.updateRecentColor(list.head($btn),eventName,value);}
hToolbarAndPopoverUpdate(event);};var PX_PER_EM=18;var hDimensionPickerMove=function(event,options){var $picker=$(event.target.parentNode);var $dimensionDisplay=$picker.next();var $catcher=$picker.find('.note-dimension-picker-mousecatcher');var $highlighted=$picker.find('.note-dimension-picker-highlighted');var $unhighlighted=$picker.find('.note-dimension-picker-unhighlighted');var posOffset;if(event.offsetX===undefined){var posCatcher=$(event.target).offset();posOffset={x:event.pageX-posCatcher.left,y:event.pageY-posCatcher.top};}else{posOffset={x:event.offsetX,y:event.offsetY};}
var dim={c:Math.ceil(posOffset.x/PX_PER_EM)||1,r:Math.ceil(posOffset.y/PX_PER_EM)||1};$highlighted.css({width:dim.c+'em',height:dim.r+'em'});$catcher.attr('data-value',dim.c+'x'+dim.r);if(3<dim.c&&dim.c<options.insertTableMaxSize.col){$unhighlighted.css({width:dim.c+1+'em'});}
if(3<dim.r&&dim.r<options.insertTableMaxSize.row){$unhighlighted.css({height:dim.r+1+'em'});}
$dimensionDisplay.html(dim.c+' x '+dim.r);};this.bindKeyMap=function(layoutInfo,keyMap){var $editor=layoutInfo.editor();var $editable=layoutInfo.editable();$editable.on('keydown',function(event){var keys=[];if(event.metaKey){keys.push('CMD');}
if(event.ctrlKey&&!event.altKey){keys.push('CTRL');}
if(event.shiftKey){keys.push('SHIFT');}
var keyName=key.nameFromCode[event.keyCode];if(keyName){keys.push(keyName);}
var pluginEvent;var keyString=keys.join('+');var eventName=keyMap[keyString];var keycode=event.keyCode;if(!eventName&&!event.ctrlKey&&!event.metaKey&&((keycode>47&&keycode<58)||keycode==32||keycode==13||(keycode>64&&keycode<91)||(keycode>95&&keycode<112)||(keycode>185&&keycode<193)||(keycode>218&&keycode<223))){eventName='visible';}else if(!keycode&&event.key!=='Dead'){self.invoke('restoreRange',$editable);}
if(eventName){pluginEvent=$.summernote.pluginEvents[keyString];if($.isFunction(pluginEvent)){if(pluginEvent(event,modules.editor,layoutInfo)){return false;}}
pluginEvent=$.summernote.pluginEvents[eventName];if($.isFunction(pluginEvent)){pluginEvent(event,modules.editor,layoutInfo);}else if(modules.editor[eventName]){modules.editor[eventName]($editable,$editor.data('options'));event.preventDefault();}else if(commands[eventName]){commands[eventName].call(this,layoutInfo);event.preventDefault();}}else if(key.isEdit(event.keyCode)){modules.editor.afterCommand($editable);}});};this.attach=function(layoutInfo,options){if(options.shortcuts){this.bindKeyMap(layoutInfo,options.keyMap[agent.isMac?'mac':'pc']);}
layoutInfo.editable().on('mousedown',hMousedown);layoutInfo.editable().on('keyup mouseup',hKeyupAndMouseup);layoutInfo.editable().on('scroll',hScroll);modules.clipboard.attach(layoutInfo,options);modules.handle.attach(layoutInfo,options);layoutInfo.popover().on('click',hToolbarAndPopoverClick);layoutInfo.popover().on('mousedown',hToolbarAndPopoverMousedown);modules.dragAndDrop.attach(layoutInfo,options);if(!options.airMode){layoutInfo.toolbar().on('click',hToolbarAndPopoverClick);layoutInfo.toolbar().on('mousedown',hToolbarAndPopoverMousedown);modules.statusbar.attach(layoutInfo,options);}
var $catcherContainer=options.airMode?layoutInfo.popover():layoutInfo.toolbar();var $catcher=$catcherContainer.find('.note-dimension-picker-mousecatcher');$catcher.css({width:options.insertTableMaxSize.col+'em',height:options.insertTableMaxSize.row+'em'}).on('mousemove',function(event){hDimensionPickerMove(event,options);});layoutInfo.editor().data('options',options);if(!agent.isMSIE){setTimeout(function(){document.execCommand('styleWithCSS',0,options.styleWithSpan);},0);}
var history=new History(layoutInfo.editable());layoutInfo.editable().data('NoteHistory',history);layoutInfo.editable().data('callbacks',{onInit:options.onInit,onFocus:options.onFocus,onBlur:options.onBlur,onKeydown:options.onKeydown,onKeyup:options.onKeyup,onMousedown:options.onMousedown,onEnter:options.onEnter,onPaste:options.onPaste,onBeforeCommand:options.onBeforeCommand,onChange:options.onChange,onImageUpload:options.onImageUpload,onImageUploadError:options.onImageUploadError,onMediaDelete:options.onMediaDelete,onToolbarClick:options.onToolbarClick,onUpload:options.onUpload,});var styleInfo=modules.editor.styleFromNode(layoutInfo.editable());this.updateStyleInfo(styleInfo,layoutInfo);};this.attachCustomEvent=function(layoutInfo,options){var $holder=layoutInfo.holder();var $editable=layoutInfo.editable();var callbacks=$editable.data('callbacks');$editable.focus(bindCustomEvent($holder,callbacks,'focus'));$editable.blur(bindCustomEvent($holder,callbacks,'blur'));$editable.keydown(function(event){if(event.keyCode===key.code.ENTER){bindCustomEvent($holder,callbacks,'enter').call(this,event);}
bindCustomEvent($holder,callbacks,'keydown').call(this,event);});$editable.keyup(bindCustomEvent($holder,callbacks,'keyup'));$editable.on('mousedown',bindCustomEvent($holder,callbacks,'mousedown'));$editable.on('mouseup',bindCustomEvent($holder,callbacks,'mouseup'));$editable.on('scroll',bindCustomEvent($holder,callbacks,'scroll'));$editable.on('paste',bindCustomEvent($holder,callbacks,'paste'));var changeEventName=agent.isMSIE?'DOMCharacterDataModified DOMSubtreeModified DOMNodeInserted':'input';$editable.on(changeEventName,function(){bindCustomEvent($holder,callbacks,'change')($editable.html(),$editable);});if(!options.airMode){layoutInfo.toolbar().click(bindCustomEvent($holder,callbacks,'toolbar.click'));layoutInfo.popover().click(bindCustomEvent($holder,callbacks,'popover.click'));}
if(dom.isTextarea(list.head($holder))){$holder.closest('form').submit(function(e){layoutInfo.holder().val(layoutInfo.holder().code());bindCustomEvent($holder,callbacks,'submit').call(this,e,$holder.code());});}
if(dom.isTextarea(list.head($holder))&&options.textareaAutoSync){$holder.on('summernote.change',function(){layoutInfo.holder().val(layoutInfo.holder().code());});}
bindCustomEvent($holder,callbacks,'init')(layoutInfo);for(var i=0,len=$.summernote.plugins.length;i<len;i++){if($.isFunction($.summernote.plugins[i].init)){$.summernote.plugins[i].init(layoutInfo);}}};this.detach=function(layoutInfo,options){layoutInfo.holder().off();layoutInfo.editable().off();layoutInfo.popover().off();layoutInfo.handle().off();layoutInfo.dialog().off();if(!options.airMode){layoutInfo.dropzone().off();layoutInfo.toolbar().off();layoutInfo.statusbar().off();}};};return EventHandler;});;

/* <inline asset> defined in bundle 'web_editor.assets_summernote' */
odoo.website_next_define='summernote/defaults';;

/* /web_editor/static/lib/summernote/src/js/defaults.js defined in bundle 'web_editor.assets_summernote' */
define('summernote/defaults',function(){var defaults={version:'@VERSION',options:{width:null,height:null,minHeight:null,maxHeight:null,focus:false,tabsize:4,styleWithSpan:true,disableLinkTarget:false,disableDragAndDrop:false,disableResizeEditor:false,disableResizeImage:false,shortcuts:true,textareaAutoSync:true,placeholder:false,prettifyHtml:true,iconPrefix:'fa fa-',icons:{font:{bold:'bold',italic:'italic',underline:'underline',clear:'eraser',height:'text-height',strikethrough:'strikethrough',superscript:'superscript',subscript:'subscript'},image:{image:'picture-o',floatLeft:'align-left',floatRight:'align-right',floatNone:'align-justify',shapeRounded:'square',shapeCircle:'circle-o',shapeThumbnail:'picture-o',shapeNone:'times',remove:'trash-o'},link:{link:'link',unlink:'unlink',edit:'edit'},table:{table:'table'},hr:{insert:'minus'},style:{style:'magic'},lists:{unordered:'list-ul',ordered:'list-ol'},options:{help:'question',fullscreen:'arrows-alt',codeview:'code'},paragraph:{paragraph:'align-left',outdent:'outdent',indent:'indent',left:'align-left',center:'align-center',right:'align-right',justify:'align-justify'},color:{recent:'font'},history:{undo:'undo',redo:'repeat'},misc:{check:'check'}},dialogsInBody:false,codemirror:{mode:'text/html',htmlMode:true,lineNumbers:true},lang:'en-US',direction:null,toolbar:[['style',['style']],['font',['bold','italic','underline','clear']],['fontname',['fontname']],['fontsize',['fontsize']],['color',['color']],['para',['ul','ol','paragraph']],['height',['height']],['table',['table']],['insert',['link','picture','hr']],['view',['fullscreen','codeview']],['help',['help']]],plugin:{},airMode:false,airPopover:[['color',['color']],['font',['bold','underline','clear']],['para',['ul','paragraph']],['table',['table']],['insert',['link','picture']]],styleTags:['p','blockquote','pre','h1','h2','h3','h4','h5','h6'],defaultFontName:'Helvetica Neue',fontNames:['Arial','Arial Black','Comic Sans MS','Courier New','Helvetica Neue','Helvetica','Impact','Lucida Grande','Tahoma','Times New Roman','Verdana'],fontNamesIgnoreCheck:[],fontSizes:['8','9','10','11','12','14','18','24','36'],colors:[['#000000','#424242','#636363','#9C9C94','#CEC6CE','#EFEFEF','#F7F7F7','#FFFFFF'],['#FF0000','#FF9C00','#FFFF00','#00FF00','#00FFFF','#0000FF','#9C00FF','#FF00FF'],['#F7C6CE','#FFE7CE','#FFEFC6','#D6EFD6','#CEDEE7','#CEE7F7','#D6D6E7','#E7D6DE'],['#E79C9C','#FFC69C','#FFE79C','#B5D6A5','#A5C6CE','#9CC6EF','#B5A5D6','#D6A5BD'],['#E76363','#F7AD6B','#FFD663','#94BD7B','#73A5AD','#6BADDE','#8C7BC6','#C67BA5'],['#CE0000','#E79439','#EFC631','#6BA54A','#4A7B8C','#3984C6','#634AA5','#A54A7B'],['#9C0000','#B56308','#BD9400','#397B21','#104A5A','#085294','#311873','#731842'],['#630000','#7B3900','#846300','#295218','#083139','#003163','#21104A','#4A1031']],lineHeights:['1.0','1.2','1.4','1.5','1.6','1.8','2.0','3.0'],insertTableMaxSize:{col:10,row:10},maximumImageFileSize:null,oninit:null,onfocus:null,onblur:null,onenter:null,onkeyup:null,onkeydown:null,onImageUpload:null,onImageUploadError:null,onMediaDelete:null,onToolbarClick:null,onsubmit:null,onCreateLink:function(sLinkUrl){if(sLinkUrl.indexOf('@')!==-1&&sLinkUrl.indexOf(':')===-1){sLinkUrl='mailto:'+sLinkUrl;}
return sLinkUrl;},keyMap:{pc:{'ENTER':'insertParagraph','CTRL+Z':'undo','CTRL+Y':'redo','TAB':'tab','SHIFT+TAB':'untab','CTRL+B':'bold','CTRL+I':'italic','CTRL+U':'underline','CTRL+SHIFT+S':'strikethrough','CTRL+BACKSLASH':'removeFormat','CTRL+SHIFT+L':'justifyLeft','CTRL+SHIFT+E':'justifyCenter','CTRL+SHIFT+R':'justifyRight','CTRL+SHIFT+J':'justifyFull','CTRL+SHIFT+NUM7':'insertUnorderedList','CTRL+SHIFT+NUM8':'insertOrderedList','CTRL+LEFTBRACKET':'outdent','CTRL+RIGHTBRACKET':'indent','CTRL+NUM0':'formatPara','CTRL+NUM1':'formatH1','CTRL+NUM2':'formatH2','CTRL+NUM3':'formatH3','CTRL+NUM4':'formatH4','CTRL+NUM5':'formatH5','CTRL+NUM6':'formatH6','CTRL+ENTER':'insertHorizontalRule','CTRL+K':'showLinkDialog'},mac:{'ENTER':'insertParagraph','CMD+Z':'undo','CMD+SHIFT+Z':'redo','TAB':'tab','SHIFT+TAB':'untab','CMD+B':'bold','CMD+I':'italic','CMD+U':'underline','CMD+SHIFT+S':'strikethrough','CMD+BACKSLASH':'removeFormat','CMD+SHIFT+L':'justifyLeft','CMD+SHIFT+E':'justifyCenter','CMD+SHIFT+R':'justifyRight','CMD+SHIFT+J':'justifyFull','CMD+SHIFT+NUM7':'insertUnorderedList','CMD+SHIFT+NUM8':'insertOrderedList','CMD+LEFTBRACKET':'outdent','CMD+RIGHTBRACKET':'indent','CMD+NUM0':'formatPara','CMD+NUM1':'formatH1','CMD+NUM2':'formatH2','CMD+NUM3':'formatH3','CMD+NUM4':'formatH4','CMD+NUM5':'formatH5','CMD+NUM6':'formatH6','CMD+ENTER':'insertHorizontalRule','CMD+K':'showLinkDialog'}}},lang:{'en-US':{font:{bold:'Bold',italic:'Italic',underline:'Underline',clear:'Remove Font Style',height:'Line Height',name:'Font Family',strikethrough:'Strikethrough',subscript:'Subscript',superscript:'Superscript',size:'Font Size'},image:{image:'Picture',insert:'Insert Image',resizeFull:'Resize Full',resizeHalf:'Resize Half',resizeQuarter:'Resize Quarter',floatLeft:'Float Left',floatRight:'Float Right',floatNone:'Float None',shapeRounded:'Shape: Rounded',shapeCircle:'Shape: Circle',shapeThumbnail:'Shape: Thumbnail',shapeNone:'Shape: None',dragImageHere:'Drag image or text here',dropImage:'Drop image or Text',selectFromFiles:'Select from files',maximumFileSize:'Maximum file size',maximumFileSizeError:'Maximum file size exceeded.',url:'Image URL',remove:'Remove Image'},link:{link:'Link',insert:'Insert Link',unlink:'Unlink',edit:'Edit',textToDisplay:'Text to display',url:'To what URL should this link go?',openInNewWindow:'Open in new window'},table:{table:'Table'},hr:{insert:'Insert Horizontal Rule'},style:{style:'Style',normal:'Paragraph',blockquote:'Quote',pre:'Code',h1:'Header 1',h2:'Header 2',h3:'Header 3',h4:'Header 4',h5:'Header 5',h6:'Header 6'},lists:{unordered:'Unordered list',ordered:'Ordered list'},options:{help:'Help',fullscreen:'Full Screen',codeview:'Code View'},paragraph:{paragraph:'Paragraph',outdent:'Outdent',indent:'Indent',left:'Align left',center:'Align center',right:'Align right',justify:'Justify full'},color:{recent:'Recent Color',more:'More Color',background:'Background Color',foreground:'Foreground Color',transparent:'Transparent',setTransparent:'Set transparent',reset:'Reset',resetToDefault:'Reset to default'},shortcut:{shortcuts:'Keyboard shortcuts',close:'Close',textFormatting:'Text formatting',action:'Action',paragraphFormatting:'Paragraph formatting',documentStyle:'Document Style',extraKeys:'Extra keys'},history:{undo:'Undo',redo:'Redo'}}}};return defaults;});;

/* <inline asset> defined in bundle 'web_editor.assets_summernote' */
odoo.website_next_define='summernote/summernote';;

/* /web_editor/static/lib/summernote/src/js/summernote.js defined in bundle 'web_editor.assets_summernote' */
define(['summernote/core/agent','summernote/core/list','summernote/core/dom','summernote/core/range','summernote/defaults','summernote/EventHandler','summernote/Renderer','summernote/core/key'],function(agent,list,dom,range,defaults,EventHandler,Renderer,key){$.summernote=$.summernote||{};$.extend($.summernote,defaults);var renderer=new Renderer();var eventHandler=new EventHandler();$.extend($.summernote,{renderer:renderer,eventHandler:eventHandler,core:{agent:agent,list:list,dom:dom,range:range,key:key},pluginEvents:{},plugins:[]});$.summernote.addPlugin=function(plugin){$.summernote.plugins.push(plugin);if(plugin.buttons){$.each(plugin.buttons,function(name,button){renderer.addButtonInfo(name,button);});}
if(plugin.dialogs){$.each(plugin.dialogs,function(name,dialog){renderer.addDialogInfo(name,dialog);});}
if(plugin.events){$.each(plugin.events,function(name,event){$.summernote.pluginEvents[name]=event;});}
if(plugin.langs){$.each(plugin.langs,function(locale,lang){if($.summernote.lang[locale]){$.extend($.summernote.lang[locale],lang);}});}
if(plugin.options){$.extend($.summernote.options,plugin.options);}};$.fn.extend({summernote:function(){var type=$.type(list.head(arguments));var isExternalAPICalled=type==='string';var hasInitOptions=type==='object';var options=hasInitOptions?list.head(arguments):{};options=$.extend({},$.summernote.options,options);options.icons=$.extend({},$.summernote.options.icons,options.icons);options.langInfo=$.extend(true,{},$.summernote.lang['en-US'],$.summernote.lang[options.lang]);if(!isExternalAPICalled&&hasInitOptions){for(var i=0,len=$.summernote.plugins.length;i<len;i++){var plugin=$.summernote.plugins[i];if(options.plugin[plugin.name]){$.summernote.plugins[i]=$.extend(true,plugin,options.plugin[plugin.name]);}}}
this.each(function(idx,holder){var $holder=$(holder);if(!renderer.hasNoteEditor($holder)){renderer.createLayout($holder,options);var layoutInfo=renderer.layoutInfoFromHolder($holder);$holder.data('layoutInfo',layoutInfo);eventHandler.attach(layoutInfo,options);eventHandler.attachCustomEvent(layoutInfo,options);}});var $first=this.first();if($first.length){var layoutInfo=renderer.layoutInfoFromHolder($first);if(isExternalAPICalled){var moduleAndMethod=list.head(list.from(arguments));var args=list.tail(list.from(arguments));var params=[moduleAndMethod,layoutInfo.editable()].concat(args);return eventHandler.invoke.apply(eventHandler,params);}else if(options.focus){layoutInfo.editable().focus();}}
return this;},code:function(html){if(html===undefined){var $holder=this.first();if(!$holder.length){return;}
var layoutInfo=renderer.layoutInfoFromHolder($holder);var $editable=layoutInfo&&layoutInfo.editable();if($editable&&$editable.length){var isCodeview=eventHandler.invoke('codeview.isActivated',layoutInfo);eventHandler.invoke('codeview.sync',layoutInfo);return isCodeview?layoutInfo.codable().val():layoutInfo.editable().html();}
return dom.value($holder);}
this.each(function(i,holder){var layoutInfo=renderer.layoutInfoFromHolder($(holder));var $editable=layoutInfo&&layoutInfo.editable();if($editable){$editable.html(html);}});return this;},destroy:function(){this.each(function(idx,holder){var $holder=$(holder);if(!renderer.hasNoteEditor($holder)){return;}
var info=renderer.layoutInfoFromHolder($holder);var options=info.editor().data('options');eventHandler.detach(info,options);renderer.removeLayout($holder,info,options);});return this;}});});;

/* /web_editor/static/src/js/editor/summernote.js defined in bundle 'web_editor.assets_summernote' */
odoo.define('web_editor.summernote',function(require){'use strict';var core=require('web.core');require('summernote/summernote');var weDefaultOptions=require('web_editor.wysiwyg.default_options');var _t=core._t;var dom=$.summernote.core.dom;var range=$.summernote.core.range;var list=$.summernote.core.list;var key=$.summernote.core.key;var eventHandler=$.summernote.eventHandler;var editor=eventHandler.modules.editor;var renderer=$.summernote.renderer;var options=$.summernote.options;var oldJustify={};_.each(['Left','Right','Full','Center'],function(align){oldJustify[align]=editor['justify'+align];editor['justify'+align]=function($editable,value){var $align=$editable.find('[align]');_.each($align,function(el){var $el=$(el);$el.data('__align',$el.attr('align'));});oldJustify[align].apply(this,arguments);var $newAlign=$editable.find('[align]');$align.not($newAlign).css('text-align','');_.each($newAlign,function(el){var $el=$(el);var oldAlignValue=$align.data('__align');var alignValue=$el.attr('align');if(oldAlignValue===alignValue){return;}
$el.removeAttr('align');$el.css('text-align',alignValue);$el.find('*').css('text-align','');});$align.removeData('__align');};});dom.hasContentAfter=function(node){var next;if(dom.isEditable(node))return;while(node.nextSibling){next=node.nextSibling;if(next.tagName||dom.isVisibleText(next)||dom.isBR(next))return next;node=next;}};dom.hasContentBefore=function(node){var prev;if(dom.isEditable(node))return;while(node.previousSibling){prev=node.previousSibling;if(prev.tagName||dom.isVisibleText(prev)||dom.isBR(prev))return prev;node=prev;}};dom.ancestorHaveNextSibling=function(node,pred){pred=pred||dom.hasContentAfter;while(!dom.isEditable(node)&&(!node.nextSibling||!pred(node))){node=node.parentNode;}
return node;};dom.ancestorHavePreviousSibling=function(node,pred){pred=pred||dom.hasContentBefore;while(!dom.isEditable(node)&&(!node.previousSibling||!pred(node))){node=node.parentNode;}
return node;};dom.nextElementSibling=function(node){while(node){node=node.nextSibling;if(node&&node.tagName){break;}}
return node;};dom.previousElementSibling=function(node){while(node){node=node.previousSibling;if(node&&node.tagName){break;}}
return node;};dom.lastChild=function(node){while(node.lastChild){node=node.lastChild;}
return node;};dom.firstChild=function(node){while(node.firstChild){node=node.firstChild;}
return node;};dom.lastElementChild=function(node,deep){node=deep?dom.lastChild(node):node.lastChild;return!node||node.tagName?node:dom.previousElementSibling(node);};dom.firstElementChild=function(node,deep){node=deep?dom.firstChild(node):node.firstChild;return!node||node.tagName?node:dom.nextElementSibling(node);};dom.isEqual=function(prev,cur){if(prev.tagName!==cur.tagName){return false;}
if((prev.attributes?prev.attributes.length:0)!==(cur.attributes?cur.attributes.length:0)){return false;}
function strip(text){return text&&text.replace(/^\s+|\s+$/g,'').replace(/\s+/g,' ');}
var att,att2;loop_prev:for(var a in prev.attributes){att=prev.attributes[a];for(var b in cur.attributes){att2=cur.attributes[b];if(att.name===att2.name){if(strip(att.value)!==strip(att2.value))return false;continue loop_prev;}}
return false;}
return true;};dom.hasOnlyStyle=function(node){for(var i=0;i<node.attributes.length;i++){var attr=node.attributes[i];if(attr.attributeName!=='style'){return false;}}
return true;};dom.hasProgrammaticStyle=function(node){var styles=["float","display","position","top","left","right","bottom"];for(var i=0;i<node.style.length;i++){var style=node.style[i];if(styles.indexOf(style)!==-1){return true;}}
return false;};dom.mergeFilter=function(prev,cur,parent){if(prev&&(dom.isText(prev)||(['H1','H2','H3','H4','H5','H6','LI','P'].indexOf(prev.tagName)!==-1&&prev!==cur.parentNode))&&dom.isText(cur)){return true;}
if(prev&&prev.tagName==="P"&&dom.isText(cur)){return true;}
if(prev&&dom.isText(cur)&&!dom.isVisibleText(cur)&&(dom.isText(prev)||dom.isVisibleText(prev))){return true;}
if(prev&&!dom.isBR(prev)&&dom.isEqual(prev,cur)&&((prev.tagName&&dom.getComputedStyle(prev).display==="inline"&&cur.tagName&&dom.getComputedStyle(cur).display==="inline"))){return true;}
if(dom.isEqual(parent,cur)&&((parent.tagName&&dom.getComputedStyle(parent).display==="inline"&&cur.tagName&&dom.getComputedStyle(cur).display==="inline"))){return true;}
if(parent&&cur.tagName==="FONT"&&(!cur.firstChild||(!cur.attributes.getNamedItem('style')&&!cur.className.length))){return true;}
if(parent&&cur.tagName==="SPAN"&&dom.hasOnlyStyle(cur)&&!dom.hasProgrammaticStyle(cur)){return true;}};dom.doMerge=function(prev,cur){if(prev.tagName){if(prev.childNodes.length&&!prev.textContent.match(/\S/)&&dom.firstElementChild(prev)&&dom.isBR(dom.firstElementChild(prev))){prev.removeChild(dom.firstElementChild(prev));}
if(cur.tagName){while(cur.firstChild){prev.appendChild(cur.firstChild);}
cur.parentNode.removeChild(cur);}else{prev.appendChild(cur);}}else{if(cur.tagName){var deep=cur;while(deep.tagName&&deep.firstChild){deep=deep.firstChild;}
prev.appendData(deep.textContent);cur.parentNode.removeChild(cur);}else{prev.appendData(cur.textContent);cur.parentNode.removeChild(cur);}}};dom.merge=function(node,begin,so,end,eo,mergeFilter,all){mergeFilter=mergeFilter||dom.mergeFilter;var _merged=false;var add=all||false;if(!begin){begin=node;while(begin.firstChild){begin=begin.firstChild;}
so=0;}else if(begin.tagName&&begin.childNodes[so]){begin=begin.childNodes[so];so=0;}
if(!end){end=node;while(end.lastChild){end=end.lastChild;}
eo=end.textContent.length-1;}else if(end.tagName&&end.childNodes[so]){end=end.childNodes[so];so=0;}
begin=dom.firstChild(begin);if(dom.isText(begin)&&so>begin.textContent.length){so=0;}
end=dom.firstChild(end);if(dom.isText(end)&&eo>end.textContent.length){eo=0;}
function __merge(node){var merged=false;var prev;for(var k=0;k<node.childNodes.length;k++){var cur=node.childNodes[k];if(cur===begin){if(!all)add=true;}
__merge(cur);dom.orderClass(dom.node(cur));if(!add||!cur)continue;if(cur===end){if(!all)add=false;}
if(!prev){if(mergeFilter.call(dom,prev,cur,node)){prev=prev||cur.previousSibling;dom.moveTo(cur,cur.parentNode,cur);k--;}else{prev=cur;}
continue;}else if(mergeFilter.call(dom,null,cur,node)){prev=prev||cur.previousSibling;dom.moveTo(cur,cur.parentNode,cur);k--;continue;}
if(mergeFilter.call(dom,prev,cur,node)){var p=prev;var c=cur;if(prev.tagName){if(cur.tagName){if(cur===begin)begin=prev;if(cur===end)end=prev;}}else{if(cur.tagName){var deep=cur;while(deep.tagName&&deep.lastChild){deep=deep.lastChild;}
if(deep===begin){so+=prev.textContent.length;begin=prev;}
if(deep===end){eo+=prev.textContent.length;end=prev;}}else{if(cur===begin){so+=prev.textContent.length;begin=prev;}
if(cur===end){eo+=prev.textContent.length;end=prev;}}}
dom.doMerge(p,c);merged=true;k--;continue;}
prev=cur;}
if(merged){_merged=true;__merge(node);}}
if(node){__merge(node);}
return{merged:_merged,sc:begin,ec:end,so:so,eo:eo};};dom.autoMerge=function(target,previous){var node=dom.lastChild(target);var nodes=[];var temp;while(node){nodes.push(node);temp=(previous?dom.hasContentBefore(node):dom.hasContentAfter(node));if(temp){if(!dom.isText(node)&&!dom.isMergable(node)&&temp.tagName!==node.tagName){nodes=[];}
break;}
node=node.parentNode;}
while(nodes.length){node=nodes.pop();if(node&&(temp=(previous?dom.hasContentBefore(node):dom.hasContentAfter(node)))&&temp.tagName===node.tagName&&!dom.isText(node)&&dom.isMergable(node)&&!dom.isNotBreakable(node)&&!dom.isNotBreakable(previous?dom.previousElementSibling(node):dom.nextElementSibling(node))){if(previous){dom.doMerge(temp,node);}else{dom.doMerge(node,temp);}}}};dom.removeSpace=function(node,begin,so,end,eo){var removed=false;var add=node===begin;if(node===begin&&begin===end&&dom.isBR(node)){return{removed:removed,sc:begin,ec:end,so:so,eo:eo};}
(function __remove_space(node){if(!node)return;var t_begin,t_end;for(var k=0;k<node.childNodes.length;k++){var cur=node.childNodes[k];if(cur===begin)add=true;if(cur.tagName&&cur.tagName!=="SCRIPT"&&cur.tagName!=="STYLE"&&dom.getComputedStyle(cur).whiteSpace!=="pre"){__remove_space(cur);}
if(!add)continue;if(cur===end)add=false;if(node.childNodes.length>1&&dom.isText(cur)&&!dom.isVisibleText(cur)){removed=true;if(cur===begin){t_begin=dom.hasContentBefore(dom.ancestorHavePreviousSibling(cur));if(t_begin){so=0;begin=dom.lastChild(t_begin);}}
if(cur===end){t_end=dom.hasContentAfter(dom.ancestorHaveNextSibling(cur));if(t_end){eo=1;end=dom.firstChild(t_end);if(dom.isText(end)){eo=end.textContent.length;}}}
cur.parentNode.removeChild(cur);begin=dom.lastChild(begin);end=dom.lastChild(end);k--;continue;}
if(dom.isText(cur)){var text;var temp;var _temp;var exp1=/[\t\n\r ]+/g;var exp2=/(?!([ ]|\u00A0)|^)\u00A0(?!([ ]|\u00A0)|$)/g;if(cur===begin){temp=cur.textContent.substr(0,so);_temp=temp.replace(exp1,' ').replace(exp2,' ');so-=temp.length-_temp.length;}
if(cur===end){temp=cur.textContent.substr(0,eo);_temp=temp.replace(exp1,' ').replace(exp2,' ');eo-=temp.length-_temp.length;}
text=cur.textContent.replace(exp1,' ').replace(exp2,' ');removed=removed||cur.textContent.length!==text.length;cur.textContent=text;}}})(node);return{removed:removed,sc:begin,ec:end,so:!dom.isBR(begin)&&so>0?so:0,eo:dom.isBR(end)?0:eo};};dom.removeBetween=function(sc,so,ec,eo,towrite){var text;if(ec.tagName){if(ec.childNodes[eo]){ec=ec.childNodes[eo];eo=0;}else{ec=dom.lastChild(ec);eo=dom.nodeLength(ec);}}
if(sc.tagName){sc=sc.childNodes[so]||dom.firstChild(ec);so=0;if(!dom.hasContentBefore(sc)&&towrite){sc.parentNode.insertBefore(document.createTextNode('\u00A0'),sc);}}
if(!eo&&sc!==ec){ec=dom.lastChild(dom.hasContentBefore(dom.ancestorHavePreviousSibling(ec))||ec);eo=ec.textContent.length;}
var ancestor=dom.commonAncestor(sc.tagName?sc.parentNode:sc,ec.tagName?ec.parentNode:ec)||dom.ancestor(sc,dom.isEditable);if(!dom.isContentEditable(ancestor)){return{sc:sc,so:so,ec:sc,eo:eo};}
if(ancestor.tagName){var ancestor_sc=sc;var ancestor_ec=ec;while(ancestor!==ancestor_sc&&ancestor!==ancestor_sc.parentNode){ancestor_sc=ancestor_sc.parentNode;}
while(ancestor!==ancestor_ec&&ancestor!==ancestor_ec.parentNode){ancestor_ec=ancestor_ec.parentNode;}
var node=dom.node(sc);if(!dom.isNotBreakable(node)&&!dom.isVoid(sc)){sc=dom.splitTree(ancestor_sc,{'node':sc,'offset':so});}
var before=dom.hasContentBefore(dom.ancestorHavePreviousSibling(sc));var after;if(ec.textContent.slice(eo,Infinity).match(/\S|\u00A0/)){after=dom.splitTree(ancestor_ec,{'node':ec,'offset':eo});}else{after=dom.hasContentAfter(dom.ancestorHaveNextSibling(ec));}
var nodes=dom.listBetween(sc,ec);var ancestor_first_last=function(node){return node===before||node===after;};for(var i=0;i<nodes.length;i++){if(!dom.ancestor(nodes[i],ancestor_first_last)&&!$.contains(nodes[i],before)&&!$.contains(nodes[i],after)&&!dom.isEditable(nodes[i])){nodes[i].parentNode.removeChild(nodes[i]);}}
if(dom.listAncestor(after).length<=dom.listAncestor(before).length){sc=dom.lastChild(before||ancestor);so=dom.nodeLength(sc);}else{sc=dom.firstChild(after);so=0;}
if(dom.isVoid(node)){}else if(towrite&&!node.firstChild&&node.parentNode&&!dom.isNotBreakable(node)){var br=$("<br/>")[0];node.appendChild(sc);sc=br;so=0;}else if(!ancestor.children.length&&!ancestor.textContent.match(/\S|\u00A0/)){sc=$("<br/>")[0];so=0;$(ancestor).prepend(sc);}else if(dom.isText(sc)){text=sc.textContent.replace(/[ \t\n\r]+$/,'\u00A0');so=Math.min(so,text.length);sc.textContent=text;}}else{text=ancestor.textContent;ancestor.textContent=text.slice(0,so)+text.slice(eo,Infinity).replace(/^[ \t\n\r]+/,'\u00A0');}
eo=so;if(!dom.isBR(sc)&&!dom.isVisibleText(sc)&&!dom.isText(dom.hasContentBefore(sc))&&!dom.isText(dom.hasContentAfter(sc))){ancestor=dom.node(sc);text=document.createTextNode('\u00A0');$(sc).before(text);sc=text;so=0;eo=1;}
var parentNode=sc&&sc.parentNode;if(parentNode&&sc.tagName==='BR'){sc=parentNode;ec=parentNode;}
return{sc:sc,so:so,ec:sc,eo:eo};};dom.indent=function(node){var style=dom.isCell(node)?'paddingLeft':'marginLeft';var margin=parseFloat(node.style[style]||0)****;node.style[style]=margin+"em";return margin;};dom.outdent=function(node){var style=dom.isCell(node)?'paddingLeft':'marginLeft';var margin=parseFloat(node.style[style]||0)-1.5;node.style[style]=margin>0?margin+"em":"";return margin;};dom.scrollIntoViewIfNeeded=function(node){node=dom.node(node);var $span;if(dom.isBR(node)){$span=$('<span/>').text('\u00A0');$(node).after($span);node=$span[0];}
if(node.scrollIntoViewIfNeeded){node.scrollIntoViewIfNeeded(false);}else{var offsetParent=node.offsetParent;while(offsetParent){var elY=0;var elH=node.offsetHeight;var parent=node;while(offsetParent&&parent){elY+=node.offsetTop;parent=node.parentNode;while(parent!==offsetParent&&(parent.tagName==="BODY"||["auto","scroll"].indexOf(dom.getComputedStyle(parent).overflowY)===-1)){parent=parent.parentNode;}
node=parent;if(parent!==offsetParent){elY-=parent.offsetTop;parent=null;}
offsetParent=node.offsetParent;}
if((node.tagName==="BODY"||["auto","scroll"].indexOf(dom.getComputedStyle(node).overflowY)!==-1)&&(node.scrollTop+node.clientHeight)<(elY+elH)){node.scrollTop=(elY+elH)-node.clientHeight;}}}
if($span){$span.remove();}
return;};dom.moveTo=function(node,target,before){var nodes=[];while(node.firstChild){nodes.push(node.firstChild);if(before){target.insertBefore(node.firstChild,before);}else{target.appendChild(node.firstChild);}}
node.parentNode.removeChild(node);return nodes;};dom.isMergable=function(node){return node.tagName&&"h1 h2 h3 h4 h5 h6 p b bold i u code sup strong small li a ul ol font".indexOf(node.tagName.toLowerCase())!==-1;};dom.isSplitable=function(node){return node.tagName&&"h1 h2 h3 h4 h5 h6 p b bold i u code sup strong small li a font".indexOf(node.tagName.toLowerCase())!==-1;};dom.isRemovableEmptyNode=function(node){return"h1 h2 h3 h4 h5 h6 p b bold i u code sup strong small li a ul ol font span br".indexOf(node.tagName.toLowerCase())!==-1;};dom.isForbiddenNode=function(node){return node.tagName==="BR"||$(node).is(".fa, img");};dom.listBetween=function(sc,ec,so,eo){var nodes=[];var ancestor=dom.commonAncestor(sc,ec);dom.walkPoint({'node':sc,'offset':so||0},{'node':ec,'offset':eo||0},function(point){if(ancestor!==point.node||ancestor===sc||ancestor===ec){nodes.push(point.node);}});return list.unique(nodes);};dom.isNotBreakable=function(node){return!dom.isText(node)&&!dom.isBR(dom.firstChild(node))&&dom.isVoid(dom.firstChild(node));};dom.isContentEditable=function(node){return $(node).closest('[contenteditable]').prop('contenteditable')==='true';};dom.isContentEditableFalse=function(node){return $(node).closest('[contenteditable]').prop('contenteditable')==='false';};dom.isFont=function(node){var nodeName=node&&node.nodeName.toUpperCase();return node&&(nodeName==="FONT"||(nodeName==="SPAN"&&(node.className.match(/(^|\s)fa(\s|$)/i)||node.className.match(/(^|\s)(text|bg)-/i)||(node.attributes.style&&node.attributes.style.value.match(/(^|\s)(color|background-color|font-size):/i)))));};dom.isVisibleText=function(textNode){return!!textNode.textContent.match(/\S|\u00A0/);};var old_isVisiblePoint=dom.isVisiblePoint;dom.isVisiblePoint=function(point){return point.node.nodeType!==8&&old_isVisiblePoint.apply(this,arguments);};dom.orderStyle=function(node){var style=node.getAttribute('style');if(!style)return null;style=style.replace(/[\s\n\r]+/,' ').replace(/^ ?;? ?| ?;? ?$/g,'').replace(/ ?; ?/g,';');if(!style.length){node.removeAttribute("style");return null;}
style=style.split(";");style.sort();style=style.join("; ")+";";node.setAttribute('style',style);return style;};dom.orderClass=function(node){var className=node.getAttribute&&node.getAttribute('class');if(!className)return null;className=className.replace(/[\s\n\r]+/,' ').replace(/^ | $/g,'').replace(/ +/g,' ');if(!className.length){node.removeAttribute("class");return null;}
className=className.split(" ");className.sort();className=className.join(" ");node.setAttribute('class',className);return className;};dom.node=function(node){return dom.isText(node)?node.parentNode:node;};dom.moveContent=function(from,to){if(from===to){return;}
if(from.parentNode===to){while(from.lastChild){dom.insertAfter(from.lastChild,from);}}else{while(from.firstChild&&from.firstChild!==to){to.appendChild(from.firstChild);}}};dom.getComputedStyle=function(node){return node.nodeType===Node.COMMENT_NODE?{}:window.getComputedStyle(node);};range.WrappedRange.prototype.reRange=function(keep_end,isNotBreakable){var sc=this.sc;var so=this.so;var ec=this.ec;var eo=this.eo;isNotBreakable=isNotBreakable||dom.isNotBreakable;var start=keep_end?ec:sc;while(start){if(isNotBreakable(start,sc,so,ec,eo)){break;}
start=start.parentNode;}
var lastFilterEnd;var end=keep_end?sc:ec;while(end){if(start===end){break;}
if(isNotBreakable(end,sc,so,ec,eo)){lastFilterEnd=end;}
end=end.parentNode;}
if(lastFilterEnd){end=lastFilterEnd;}
if(!end){end=document.getElementsByTagName('body')[0];}
if(start===end||!start){return this;}
if($.contains(start,end)){if(keep_end){sc=dom.lastChild(dom.hasContentBefore(dom.ancestorHavePreviousSibling(end))||sc);so=sc.textContent.length;}else if(!eo){ec=dom.lastChild(dom.hasContentBefore(dom.ancestorHavePreviousSibling(end))||ec);eo=ec.textContent.length;}else{ec=dom.firstChild(dom.hasContentAfter(dom.ancestorHaveNextSibling(end))||ec);eo=0;}}else{if(keep_end){sc=dom.firstChild(start);so=0;}else{ec=dom.lastChild(start);eo=ec.textContent.length;}}
return new range.WrappedRange(sc,so,ec,eo);};range.WrappedRange.prototype.isOnImg=function(){if(this.sc===this.ec&&this.so===this.eo){return dom.ancestor(this.sc,dom.isImg);}
var startPoint={node:this.sc,offset:this.so};var endPoint={node:this.ec,offset:this.eo};var nb=0;var image;var textNode;dom.walkPoint(startPoint,endPoint,function(point){if(dom.hasChildren(point.node)){return;}
var pointImg=dom.ancestor(point.node,dom.isImg);var isText=dom.isText(point.node);if(pointImg?(image!==pointImg):((!dom.isBR(point.node)&&!isText)||(textNode===point.node&&point.node.textContent.match(/\S|\u00A0/)))){nb++;}
if(pointImg){image=pointImg;}
if(isText){textNode=point.node;}});return nb===1&&image;};range.WrappedRange.prototype.deleteContents=function(towrite){if(this.sc===this.ec&&this.so===this.eo){return this;}
var r;var image=this.isOnImg();if(image){var parentNode=image.parentNode;var index=_.indexOf(parentNode.childNodes,image);parentNode.removeChild(image);r=new range.WrappedRange(parentNode,index,parentNode,index);}else{r=dom.removeBetween(this.sc,this.so,this.ec,this.eo,towrite);}
$(dom.node(r.sc)).trigger("click");return new range.WrappedRange(r.sc,r.so,r.ec,r.eo);};range.WrappedRange.prototype.clean=function(mergeFilter,all){var node=dom.node(this.sc===this.ec?this.sc:this.commonAncestor());node=node||$(this.sc).closest('[contenteditable]')[0];if(node.childNodes.length<=1){return this;}
var merge=dom.merge(node,this.sc,this.so,this.ec,this.eo,mergeFilter,all);var rem=dom.removeSpace(node.parentNode,merge.sc,merge.so,merge.ec,merge.eo);if(merge.merged||rem.removed){return range.create(rem.sc,rem.so,rem.ec,rem.eo);}
return this;};range.WrappedRange.prototype.remove=function(mergeFilter){};range.WrappedRange.prototype.isOnCellFirst=function(){var node=dom.ancestor(this.sc,function(node){return["LI","DIV","TD","TH"].indexOf(node.tagName)!==-1;});return node&&["TD","TH"].indexOf(node.tagName)!==-1;};range.WrappedRange.prototype.isContentEditable=function(){return dom.isContentEditable(this.sc)&&(this.sc===this.ec||dom.isContentEditable(this.ec));};renderer.tplButtonInfo.fontsize=function(lang,options){var items=options.fontSizes.reduce(function(memo,v){return memo+'<a data-event="fontSize" href="#" class="dropdown-item" data-value="'+v+'">'+'<i class="fa fa-check"></i> '+v+'</a>';},'');var sLabel='<span class="note-current-fontsize">11</span>';return renderer.getTemplate().button(sLabel,{title:lang.font.size,dropdown:'<div class="dropdown-menu">'+items+'</div>'});};renderer.tplButtonInfo.color=function(lang,options){var foreColorButtonLabel='<i class="'+options.iconPrefix+options.icons.color.recent+'"></i>';var backColorButtonLabel='<i class="'+options.iconPrefix+'paint-brush"></i>';var recentColorButton=renderer.getTemplate().button(foreColorButtonLabel,{className:'note-recent-color d-none',title:lang.color.foreground,event:'color',value:'{"backColor":"#B35E9B"}'});var foreColorButton=renderer.getTemplate().button(foreColorButtonLabel,{className:'note-fore-color-preview',title:lang.color.foreground,dropdown:renderer.getTemplate().dropdown('<li><div data-event-name="foreColor" class="colorPalette"/></li>'),});var backColorButton=renderer.getTemplate().button(backColorButtonLabel,{className:'note-back-color-preview',title:lang.color.background,dropdown:renderer.getTemplate().dropdown('<li><div data-event-name="backColor" class="colorPalette"/></li>'),});return recentColorButton+foreColorButton+backColorButton;};renderer.tplButtonInfo.checklist=function(lang,options){return'<button '+'type="button" '+'class="btn btn-secondary btn-sm" '+'title="'+_t('Checklist')+'" '+'data-event="insertCheckList" '+'tabindex="-1" '+'data-name="ul" '+'><i class="fa fa-check-square"></i></button>';};key.nameFromCode[46]='DELETE';key.nameFromCode[27]='ESCAPE';options.keyMap.pc['BACKSPACE']='backspace';options.keyMap.pc['DELETE']='delete';options.keyMap.pc['ENTER']='enter';options.keyMap.pc['ESCAPE']='cancel';options.keyMap.mac['SHIFT+TAB']='untab';options.keyMap.pc['UP']='up';options.keyMap.pc['DOWN']='down';options.keyMap.mac['BACKSPACE']='backspace';options.keyMap.mac['DELETE']='delete';options.keyMap.mac['ENTER']='enter';options.keyMap.mac['ESCAPE']='cancel';options.keyMap.mac['UP']='up';options.keyMap.mac['DOWN']='down';options.styleTags=weDefaultOptions.styleTags;$.summernote.pluginEvents.insertTable=function(event,editor,layoutInfo,sDim){var $editable=layoutInfo.editable();$editable.focus();var dimension=sDim.split('x');var r=range.create();if(!r)return;r=r.deleteContents(true);var table=editor.table.createTable(dimension[0],dimension[1]);var parent=r.sc;while(dom.isText(parent.parentNode)||dom.isRemovableEmptyNode(parent.parentNode)){parent=parent.parentNode;}
var node=dom.splitTree(parent,{'node':r.sc,'offset':r.so})||r.sc;node.parentNode.insertBefore(table,node);if($(node).text()===''||node.textContent==='\u00A0'){node.parentNode.removeChild(node);}
editor.afterCommand($editable);event.preventDefault();return false;};$.summernote.pluginEvents.tab=function(event,editor,layoutInfo,outdent){var $editable=layoutInfo.editable();$editable.data('NoteHistory').recordUndo($editable,'tab');var r=range.create();outdent=outdent||false;event.preventDefault();if(r&&(dom.ancestor(r.sc,dom.isCell)||dom.ancestor(r.ec,dom.isCell))){if(r.isCollapsed()&&r.isOnCell()&&r.isOnCellFirst()){var td=dom.ancestor(r.sc,dom.isCell);if(!outdent&&!dom.nextElementSibling(td)&&!dom.nextElementSibling(td.parentNode)){var last=dom.lastChild(td);range.create(last,dom.nodeLength(last),last,dom.nodeLength(last)).select();$.summernote.pluginEvents.enter(event,editor,layoutInfo);}else if(outdent&&!dom.previousElementSibling(td)&&!$(td.parentNode).text().match(/\S/)){$.summernote.pluginEvents.backspace(event,editor,layoutInfo);}else{editor.table.tab(r,outdent);}}else{$.summernote.pluginEvents.indent(event,editor,layoutInfo,outdent);}}else if(r&&r.isCollapsed()){if(!r.sc.textContent.slice(0,r.so).match(/\S/)&&r.isOnList()){if(outdent){$.summernote.pluginEvents.outdent(event,editor,layoutInfo);}else{$.summernote.pluginEvents.indent(event,editor,layoutInfo);}}else{var next;if(!outdent){if(dom.isText(r.sc)){next=r.sc.splitText(r.so);}else{next=document.createTextNode('');$(r.sc.childNodes[r.so]).before(next);}
editor.typing.insertTab($editable,r,options.tabsize);r=range.create(next,0,next,0);r=dom.merge(r.sc.parentNode,r.sc,r.so,r.ec,r.eo,null,true);range.create(r.sc,r.so,r.ec,r.eo).select();}else{r=dom.merge(r.sc.parentNode,r.sc,r.so,r.ec,r.eo,null,true);r=range.create(r.sc,r.so,r.ec,r.eo);if(r.sc.splitText){next=r.sc.splitText(r.so);r.sc.textContent=r.sc.textContent.replace(/(\u00A0)+$/g,'');next.textContent=next.textContent.replace(/^(\u00A0)+/g,'');range.create(r.sc,r.sc.textContent.length,r.sc,r.sc.textContent.length).select();}}}}
return false;};$.summernote.pluginEvents.untab=function(event,editor,layoutInfo){return $.summernote.pluginEvents.tab(event,editor,layoutInfo,true);};$.summernote.pluginEvents.up=function(event,editor,layoutInfo){var r=range.create();var node=dom.firstChild(r.sc.childNodes[r.so]||r.sc);if(!r.isOnCell()){return;}
var ancestor=dom.ancestor(node,function(ancestorNode){return dom.hasContentBefore(ancestorNode)||dom.isCell(ancestorNode);});if(!dom.isCell(ancestor)&&(!dom.isBR(dom.hasContentBefore(ancestor))||!dom.isText(node)||dom.isVisibleText(node)||dom.hasContentBefore(dom.hasContentBefore(ancestor)))){return;}
event.preventDefault();var td=dom.ancestor(r.sc,dom.isCell);var tr=td.parentNode;var target=tr.previousElementSibling&&tr.previousElementSibling.children[_.indexOf(tr.children,td)];if(!target){target=(dom.ancestorHavePreviousSibling(tr)||tr).previousSibling;}
if(target){range.create(dom.lastChild(target),dom.lastChild(target).textContent.length).select();}};$.summernote.pluginEvents.down=function(event,editor,layoutInfo){var r=range.create();var node=dom.firstChild(r.sc.childNodes[r.so]||r.sc);if(!r.isOnCell()){return;}
var ancestor=dom.ancestor(node,function(ancestorNode){return dom.hasContentAfter(ancestorNode)||dom.isCell(ancestorNode);});if(!dom.isCell(ancestor)&&(!dom.isBR(dom.hasContentAfter(ancestor))||!dom.isText(node)||dom.isVisibleText(node)||dom.hasContentAfter(dom.hasContentAfter(ancestor)))){return;}
event.preventDefault();var td=dom.ancestor(r.sc,dom.isCell);var tr=td.parentNode;var target=tr.nextElementSibling&&tr.nextElementSibling.children[_.indexOf(tr.children,td)];if(!target){target=(dom.ancestorHaveNextSibling(tr)||tr).nextSibling;}
if(target){range.create(dom.firstChild(target),0).select();}};$.summernote.pluginEvents.enter=function(event,editor,layoutInfo){var $editable=layoutInfo.editable();$editable.data('NoteHistory').recordUndo($editable,'enter');var r=range.create();if(!r.isContentEditable()){event.preventDefault();return false;}
if(!r.isCollapsed()){r=r.deleteContents();r.select();}
var br=$("<br/>")[0];var elem=dom.isBR(elem)?elem.parentNode:dom.node(r.sc);if(elem.tagName==="A"){if(r.so===0&&dom.firstChild(elem)===r.sc){r.ec=r.sc=dom.hasContentBefore(elem)||$(dom.createText('')).insertBefore(elem)[0];r.eo=r.so=dom.nodeLength(r.sc);r.select();}else if(dom.nodeLength(r.sc)===r.so&&dom.lastChild(elem)===r.sc){r.ec=r.sc=dom.hasContentAfter(elem)||dom.insertAfter(dom.createText(''),elem);r.eo=r.so=0;r.select();}}
var node;var $node;var $clone;var contentBefore=r.sc.textContent.slice(0,r.so).match(/\S|\u00A0/);if(!contentBefore&&dom.isText(r.sc)){node=r.sc.previousSibling;while(!contentBefore&&node&&dom.isText(node)){contentBefore=dom.isVisibleText(node);node=node.previousSibling;}}
node=dom.node(r.sc);var exist=r.sc.childNodes[r.so]||r.sc;exist=dom.isVisibleText(exist)||dom.isBR(exist)?exist:dom.hasContentAfter(exist)||(dom.hasContentBefore(exist)||exist);var td=dom.ancestor(node,dom.isCell);if(td&&!dom.nextElementSibling(node)&&!dom.nextElementSibling(td)&&!dom.nextElementSibling(td.parentNode)&&(!dom.isText(r.sc)||!r.sc.textContent.slice(r.so).match(/\S|\u00A0/))){$node=$(td.parentNode);$clone=$node.clone();$clone.children().html(dom.blank);$node.after($clone);node=dom.firstElementChild($clone[0])||$clone[0];range.create(node,0,node,0).select();dom.scrollIntoViewIfNeeded(br);event.preventDefault();return false;}
var last=node;while(node&&dom.isSplitable(node)&&!dom.isList(node)){last=node;node=node.parentNode;}
if(last===node&&!dom.isBR(node)){node=r.insertNode(br,true);if(isFormatNode(last.firstChild)&&$(last).closest(options.styleTags.join(',')).length){dom.moveContent(last.firstChild,last);last.removeChild(last.firstChild);}
do{node=dom.hasContentAfter(node);}while(node&&dom.isBR(node));if(!node&&(!br.nextElementSibling||!dom.isBR(br.nextElementSibling))){$(br).before($("<br/>")[0]);}
node=br.nextSibling||br;}else if(last===node&&dom.isBR(node)){$(node).after(br);node=br;}else if(!r.so&&r.isOnList()&&!r.sc.textContent.length&&!dom.ancestor(r.sc,dom.isLi).nextElementSibling){$('<p></p>').append(br).insertAfter(dom.ancestor(r.sc,dom.isList));node=br;}else if(dom.isBR(exist)&&$(r.sc).closest('blockquote, pre').length&&!dom.hasContentAfter($(exist.parentNode).closest('blockquote *, pre *').length?exist.parentNode:exist)){$('<p></p>').append(br).insertAfter($(r.sc).closest('blockquote, pre'));node=br;}else if(dom.isEditable(dom.node(r.sc))){node=null;}else if(last===r.sc){if(dom.isBR(last)){last=last.parentNode;}
$node=$(last);$clone=$node.clone().text("");$node.after($clone);node=dom.node(dom.firstElementChild($clone[0])||$clone[0]);$(node).html(br);node=br;}else{node=dom.splitTree(last,{'node':r.sc,'offset':r.so})||r.sc;if(!contentBefore){var cur=dom.node(dom.lastChild(node.previousSibling));if(!dom.isBR(cur)){$(cur).html(cur.innerHTML+br.outerHTML);}}
if(!dom.isVisibleText(node)){node=dom.firstChild(node);$(dom.node(dom.isBR(node)?node.parentNode:node)).html(br);node=br;}}
if(node){node=dom.firstChild(node);if(dom.isBR(node)){range.createFromNode(node).select();}else{range.create(node,0).select();}
dom.scrollIntoViewIfNeeded(node);}
event.preventDefault();return false;};$.summernote.pluginEvents.visible=function(event,editor,layoutInfo){var $editable=layoutInfo.editable();$editable.data('NoteHistory').recordUndo($editable,"visible");var r=range.create();if(!r)return;if(!r.isCollapsed()){if((dom.isCell(dom.node(r.sc))||dom.isCell(dom.node(r.ec)))&&dom.node(r.sc)!==dom.node(r.ec)){remove_table_content(r);r=range.create(r.ec,0);}
r.select();}
var node=dom.firstChild(r.sc.tagName&&r.so?r.sc.childNodes[r.so]||r.sc:r.sc);while(node.parentNode){if(dom.isForbiddenNode(node)){var text=node.previousSibling;if(text&&dom.isText(text)&&dom.isVisibleText(text)){range.create(text,text.textContent.length,text,text.textContent.length).select();}else{text=node.parentNode.insertBefore(document.createTextNode("."),node);range.create(text,1,text,1).select();setTimeout(function(){var text=range.create().sc;text.textContent=text.textContent.replace(/^./,'');range.create(text,text.textContent.length,text,text.textContent.length).select();},0);}
break;}
node=node.parentNode;}
return true;};function remove_table_content(r){var nodes=dom.listBetween(r.sc,r.ec,r.so,r.eo);if(dom.isText(r.sc)){r.sc.textContent=r.sc.textContent.slice(0,r.so);}
if(dom.isText(r.ec)){r.ec.textContent=r.ec.textContent.slice(r.eo);}
for(var i in nodes){var node=nodes[i];if(node===r.sc||node===r.ec||$.contains(node,r.sc)||$.contains(node,r.ec)){continue;}else if(dom.isCell(node)){$(node).html("<br/>");}else if(node.parentNode){do{var parent=node.parentNode;parent.removeChild(node);node=parent;}while(!dom.isVisibleText(node)&&!dom.firstElementChild(node)&&!dom.isCell(node)&&node.parentNode&&!$(node.parentNode).hasClass('o_editable'));}}
return false;}
$.summernote.pluginEvents.delete=function(event,editor,layoutInfo){var $editable=layoutInfo.editable();$editable.data('NoteHistory').recordUndo($editable,"delete");var r=range.create();if(!r)return;if(!r.isContentEditable()){event.preventDefault();return false;}
if(!r.isCollapsed()){if(dom.isCell(dom.node(r.sc))||dom.isCell(dom.node(r.ec))){remove_table_content(r);range.create(r.ec,0).select();}else{r=r.deleteContents();r.select();}
event.preventDefault();return false;}
var target=r.ec;var offset=r.eo;if(target.tagName&&target.childNodes[offset]){target=target.childNodes[offset];offset=0;}
var node=dom.node(target);var data=dom.merge(node,target,offset,target,offset,null,true);data=dom.removeSpace(node.parentNode,data.sc,data.so,data.ec,data.eo);r=range.create(data.sc,data.so);r.select();target=r.sc;offset=r.so;while(!dom.hasContentAfter(node)&&!dom.hasContentBefore(node)&&!dom.isImg(node)){node=node.parentNode;}
var contentAfter=target.textContent.slice(offset,Infinity).match(/\S|\u00A0/);var content=target.textContent.replace(/[ \t\r\n]+$/,'');var temp;var temp2;var next;if(dom.isImg(node)||(!contentAfter&&dom.isImg(dom.hasContentAfter(node)))){var parent;var index;if(!dom.isImg(node)){node=dom.hasContentAfter(node);}
while(dom.isImg(node)){parent=node.parentNode;index=dom.position(node);if(index>0){next=node.previousSibling;r=range.create(next,next.textContent.length);}else{r=range.create(parent,0);}
if(!dom.hasContentAfter(node)&&!dom.hasContentBefore(node)){parent.appendChild($('<br/>')[0]);}
parent.removeChild(node);node=parent;r.select();}}
else if(!content.length&&target.tagName&&dom.isRemovableEmptyNode(dom.isBR(target)?target.parentNode:target)){if(node===$editable[0]||$.contains(node,$editable[0])){event.preventDefault();return false;}
var before=false;next=dom.hasContentAfter(dom.ancestorHaveNextSibling(node));if(!dom.isContentEditable(next)){before=true;next=dom.hasContentBefore(dom.ancestorHavePreviousSibling(node));}
dom.removeSpace(next.parentNode,next,0,next,0);next=dom.firstChild(next);node.parentNode.removeChild(node);range.create(next,before?next.textContent.length:0).select();}
else if(contentAfter){return true;}
else if(dom.isText(target)&&(temp=dom.hasContentAfter(target))&&dom.isText(temp)){return true;}
else if((temp=dom.ancestorHaveNextSibling(target))&&!r.isOnCell()&&dom.isMergable(temp)&&dom.isMergable(temp2=dom.hasContentAfter(temp))&&temp.tagName===temp2.tagName&&(temp.tagName!=="LI"||!$('ul,ol',temp).length)&&(temp2.tagName!=="LI"||!$('ul,ol',temp2).length)&&!dom.isNotBreakable(temp)&&!dom.isNotBreakable(temp2)){dom.autoMerge(target,false);next=dom.firstChild(dom.hasContentAfter(dom.ancestorHaveNextSibling(target)));if(dom.isBR(next)){if(dom.position(next)===0){range.create(next.parentNode,0).select();}
else{range.create(next.previousSibling,next.previousSibling.textContent.length).select();}
next.parentNode.removeChild(next);}else{range.create(next,0).select();}}
else if((temp=dom.ancestorHaveNextSibling(target))&&(temp2=dom.hasContentAfter(temp))&&dom.isContentEditable(temp2)){dom.removeSpace(temp2.parentNode,temp2,0,temp,0);temp2=dom.firstChild(temp2);r=range.create(temp2,0);r.select();if((dom.isText(temp)||dom.getComputedStyle(temp).display==="inline")&&(dom.isText(temp2)||dom.getComputedStyle(temp2).display==="inline")){if(dom.isText(temp2)){temp2.textContent=temp2.textContent.replace(/^\s*\S/,'');}else{$.summernote.pluginEvents.delete(event,editor,layoutInfo);}}}
$(dom.node(r.sc)).trigger("click");event.preventDefault();return false;};$.summernote.pluginEvents.backspace=function(event,editor,layoutInfo){var $editable=layoutInfo.editable();$editable.data('NoteHistory').recordUndo($editable,"backspace");var r=range.create();if(!r)return;if(!r.isContentEditable()){event.preventDefault();return false;}
if(!r.isCollapsed()){if(dom.isCell(dom.node(r.sc))||dom.isCell(dom.node(r.ec))){remove_table_content(r);range.create(r.sc,dom.nodeLength(r.sc)).select();}else{r=r.deleteContents();r.select();}
event.preventDefault();return false;}
var target=r.sc;var offset=r.so;if(target.tagName&&target.childNodes[offset]){target=target.childNodes[offset];offset=0;}
var node=dom.node(target);var data=dom.merge(node,target,offset,target,offset,null,true);data=dom.removeSpace(node.parentNode,data.sc,data.so,data.ec,data.eo);r=dom.isVoid(data.sc)?range.createFromNode(data.sc):range.create(data.sc,data.so);r.select();target=r.sc;offset=r.so;if(target.tagName&&target.childNodes[offset]){target=target.childNodes[offset];offset=0;node=dom.node(target);}
while(node.parentNode&&!dom.hasContentAfter(node)&&!dom.hasContentBefore(node)&&!dom.isImg(node)){node=node.parentNode;}
var contentBefore=target.textContent.slice(0,offset).match(/\S|\u00A0/);var content=target.textContent.replace(/[ \t\r\n]+$/,'');var temp;var temp2;var prev;if(dom.isImg(node)||(!contentBefore&&dom.isImg(dom.hasContentBefore(node)))){if(!dom.isImg(node)){node=dom.hasContentBefore(node);}
range.createFromNode(node).select();$.summernote.pluginEvents.delete(event,editor,layoutInfo);}
else if(r.isOnCell()&&!offset&&(target===(temp=dom.ancestor(target,dom.isCell))||target===temp.firstChild||(dom.isText(temp.firstChild)&&!dom.isVisibleText(temp.firstChild)&&target===temp.firstChild.nextSibling))){if(dom.previousElementSibling(temp)){var td=dom.previousElementSibling(temp);node=td.lastChild||td;}else{var tr=temp.parentNode;var prevTr=dom.previousElementSibling(tr);if(!$(temp.parentNode).text().match(/\S|\u00A0/)){if(prevTr){node=dom.lastChild(dom.lastElementChild(prevTr));}else{node=dom.lastChild(dom.hasContentBefore(dom.ancestorHavePreviousSibling(tr))||$editable.get(0));}
$(tr).empty();if(!$(tr).closest('table').has('td, th').length){$(tr).closest('table').remove();}
$(tr).remove();range.create(node,node.textContent.length,node,node.textContent.length).select();}else{node=dom.lastElementChild(prevTr).lastChild||dom.lastElementChild(prevTr);}}
if(dom.isBR(node)){range.createFromNode(node).select();}else{range.create(node,dom.nodeLength(node)).select();}}
else if(!content.length&&target.tagName&&dom.isRemovableEmptyNode(target)){if(node===$editable[0]||$.contains(node,$editable[0])){event.preventDefault();return false;}
var before=true;prev=dom.hasContentBefore(dom.ancestorHavePreviousSibling(node));if(!dom.isContentEditable(prev)){before=false;prev=dom.hasContentAfter(dom.ancestorHaveNextSibling(node));}
dom.removeSpace(prev.parentNode,prev,0,prev,0);prev=dom.lastChild(prev);node.parentNode.removeChild(node);range.createFromNode(prev).select();range.create(prev,before?prev.textContent.length:0).select();}
else if(contentBefore){return true;}
else if(dom.isText(target)&&(temp=dom.hasContentBefore(target))&&(dom.isText(temp)||dom.isBR(temp))){return true;}
else if((temp=dom.ancestorHavePreviousSibling(target))&&dom.isMergable(temp)&&dom.isMergable(temp2=dom.hasContentBefore(temp))&&temp.tagName===temp2.tagName&&(temp.tagName!=="LI"||!$('ul,ol',temp).length)&&(temp2.tagName!=="LI"||!$('ul,ol',temp2).length)&&!dom.isNotBreakable(temp)&&!dom.isNotBreakable(temp2)){prev=dom.firstChild(target);dom.autoMerge(target,true);range.create(prev,0).select();}
else if((temp=dom.ancestorHavePreviousSibling(target))&&(temp2=dom.hasContentBefore(temp))&&dom.isContentEditable(temp2)){dom.removeSpace(temp2.parentNode,temp2,0,temp,0);temp2=dom.lastChild(temp2);r=range.create(temp2,temp2.textContent.length,temp2,temp2.textContent.length);r.select();if((dom.isText(temp)||dom.getComputedStyle(temp).display==="inline")&&(dom.isText(temp2)||dom.getComputedStyle(temp2).display==="inline")){if(dom.isText(temp2)){temp2.textContent=temp2.textContent.replace(/\S\s*$/,'');}else{$.summernote.pluginEvents.backspace(event,editor,layoutInfo);}}}
r=range.create();if(r){$(dom.node(r.sc)).trigger("click");dom.scrollIntoViewIfNeeded(r.sc.parentNode.previousElementSibling||r.sc);}
event.preventDefault();return false;};function isFormatNode(node){return node.tagName&&options.styleTags.indexOf(node.tagName.toLowerCase())!==-1;}
$.summernote.pluginEvents.insertUnorderedList=function(event,editor,layoutInfo,type){var $editable=layoutInfo.editable();$editable.focus();$editable.data('NoteHistory').recordUndo($editable);type=type||"UL";var sorted=type==="OL";var parent;var r=range.create();if(!r)return;var node=r.sc;while(node&&node!==$editable[0]){parent=node.parentNode;if(node.tagName===(sorted?"UL":"OL")){var ul=document.createElement(sorted?"ol":"ul");ul.className=node.className;if(type!=='checklist'){ul.classList.remove('o_checklist');}else{ul.classList.add('o_checklist');}
parent.insertBefore(ul,node);while(node.firstChild){ul.appendChild(node.firstChild);}
parent.removeChild(node);r.select();return;}else if(node.tagName===(sorted?"OL":"UL")){if(type==='checklist'&&!node.classList.contains('o_checklist')){node.classList.add('o_checklist');return;}else if(type==='UL'&&node.classList.contains('o_checklist')){node.classList.remove('o_checklist');return;}
var lis=[];for(var i=0;i<node.children.length;i++){lis.push(node.children[i]);}
if(parent.tagName==="LI"){node=parent;parent=node.parentNode;_.each(lis,function(li){parent.insertBefore(li,node);});}else{_.each(lis,function(li){while(li.firstChild){parent.insertBefore(li.firstChild,node);}});}
parent.removeChild(node);r.select();return;}
node=parent;}
var p0=r.sc;while(p0&&p0.parentNode&&p0.parentNode!==$editable[0]&&!isFormatNode(p0)){p0=p0.parentNode;}
if(!p0)return;var p1=r.ec;while(p1&&p1.parentNode&&p1.parentNode!==$editable[0]&&!isFormatNode(p1)){p1=p1.parentNode;}
if(!p0.parentNode||p0.parentNode!==p1.parentNode){return;}
parent=p0.parentNode;ul=document.createElement(sorted?"ol":"ul");if(type==='checklist'){ul.classList.add('o_checklist');}
parent.insertBefore(ul,p0);var childNodes=parent.childNodes;var brs=[];var begin=false;for(i=0;i<childNodes.length;i++){if(begin&&dom.isBR(childNodes[i])){parent.removeChild(childNodes[i]);i--;}
if((!dom.isText(childNodes[i])&&!isFormatNode(childNodes[i]))||(!ul.firstChild&&childNodes[i]!==p0)||$.contains(ul,childNodes[i])||(dom.isText(childNodes[i])&&!childNodes[i].textContent.match(/\S|u00A0/))){continue;}
begin=true;var li=document.createElement('li');ul.appendChild(li);li.appendChild(childNodes[i]);if(li.firstChild===p1){break;}
i--;}
if(dom.isBR(childNodes[i])){parent.removeChild(childNodes[i]);}
for(i=0;i<brs.length;i++){parent.removeChild(brs[i]);}
r.clean().select();event.preventDefault();return false;};$.summernote.pluginEvents.insertOrderedList=function(event,editor,layoutInfo){$.summernote.pluginEvents.insertUnorderedList(event,editor,layoutInfo,"OL");};$.summernote.pluginEvents.insertCheckList=function(event,editor,layoutInfo){$.summernote.pluginEvents.insertUnorderedList(event,editor,layoutInfo,"checklist");$(range.create().sc.parentNode).trigger('input');};$.summernote.pluginEvents.indent=function(event,editor,layoutInfo,outdent){var $editable=layoutInfo.editable();$editable.data('NoteHistory').recordUndo($editable);var r=range.create();if(!r)return;var flag=false;function indentUL(UL,start,end){var next;var previous;var tagName=UL.tagName;var node=UL.firstChild;var ul=document.createElement(tagName);ul.className=UL.className;var li=document.createElement("li");li.classList.add('o_indent');li.appendChild(ul);if(flag){flag=1;}
while(node){if(flag===1||node===start||$.contains(node,start)){flag=true;if(previous){if(dom.isList(previous.lastChild)){ul=previous.lastChild;}else{previous.appendChild(ul);}}else{node.parentNode.insertBefore(li,node);}}
next=dom.nextElementSibling(node);if(flag){ul.appendChild(node);}
if(node===end||$.contains(node,end)){flag=false;break;}
previous=node;node=next;}
var temp;var prev=dom.previousElementSibling(li);if(prev&&prev.tagName==="LI"&&(temp=dom.firstElementChild(prev))&&temp.tagName===tagName&&((dom.firstElementChild(prev)||prev.firstChild)!==ul)){dom.doMerge(dom.firstElementChild(prev)||prev.firstChild,ul);li=prev;li.parentNode.removeChild(dom.nextElementSibling(li));}
next=dom.nextElementSibling(li);if(next&&next.tagName==="LI"&&(temp=dom.firstElementChild(next))&&temp.tagName===tagName&&(dom.firstElementChild(li)!==dom.firstElementChild(next))){dom.doMerge(dom.firstElementChild(li),dom.firstElementChild(next));li.parentNode.removeChild(dom.nextElementSibling(li));}}
function outdenttUL(UL,start,end){var next;var node=UL.firstChild;var parent=UL.parentNode;var li=UL.parentNode.tagName==="LI"?UL.parentNode:UL;var ul=UL.parentNode.tagName==="LI"?UL.parentNode.parentNode:UL.parentNode;start=dom.ancestor(start,dom.isLi);end=dom.ancestor(end,dom.isLi);if(ul.tagName!=="UL"&&ul.tagName!=="OL")return;while(node){if(node===start||$.contains(node,start)){flag=true;if(dom.previousElementSibling(node)&&li.tagName==="LI"){li=dom.splitTree(li,dom.prevPoint({'node':node,'offset':0}));}}
next=dom.nextElementSibling(node);if(flag){var $succeeding=$(node).nextAll();ul=node.parentNode;if(dom.previousElementSibling(ul)){dom.insertAfter(node,li);}else{li.parentNode.insertBefore(node,li);}
$succeeding.insertAfter(node);if(!ul.children.length){if(ul.parentNode.tagName==="LI"&&!dom.previousElementSibling(ul)){ul=ul.parentNode;}
ul.parentNode.removeChild(ul);}
flag=false;break;}
if(node===end||$.contains(node,end)){flag=false;break;}
node=next;}
dom.merge(parent,start,0,end,1,null,true);}
function indentOther(p,start,end){if(p===start||$.contains(p,start)||$.contains(start,p)){flag=true;}
if(flag){if(outdent){dom.outdent(p);}else{dom.indent(p);}}
if(p===end||$.contains(p,end)||$.contains(end,p)){flag=false;}}
var ancestor=r.commonAncestor();var $dom=$(ancestor);if(!dom.isList(ancestor)){if(dom.isList(ancestor.parentNode)){$dom=$(ancestor.parentNode);}else{$dom=$(dom.node(ancestor)).children();}}
if(!$dom.not('br').length){$dom=$(dom.ancestor(r.sc,dom.isList));if(!$dom.length){$dom=$(r.sc).closest(options.styleTags.join(',')+',td');}}
$dom=$dom.map(function(){return this.tagName==="TR"?dom.firstElementChild(this):this;});$dom.each(function(){if(flag||$.contains(this,r.sc)){if(dom.isList(this)){if(outdent){outdenttUL(this,r.sc,r.ec);}else{indentUL(this,r.sc,r.ec);}}else if(isFormatNode(this)||dom.ancestor(this,dom.isCell)){indentOther(this,r.sc,r.ec);}}});if($dom.length){var $parent=$dom.parent();var $ul=$parent.find('ul, ol');if(!$ul.length){$ul=$(dom.ancestor(r.sc,dom.isList));}
$ul.each(function(){if(this.previousSibling&&this.previousSibling!==dom.previousElementSibling(this)&&!this.previousSibling.textContent.match(/\S/)){this.parentNode.removeChild(this.previousSibling);}
if(this.nextSibling&&this.nextSibling!==dom.nextElementSibling(this)&&!this.nextSibling.textContent.match(/\S/)){this.parentNode.removeChild(this.nextSibling);}});r=dom.merge($parent[0],r.sc,r.so,r.ec,r.eo,function(prev,cur){if(prev&&dom.isList(prev)&&dom.isEqual(prev,cur)){return true;}},true);range.create(r.sc,r.so,r.ec,r.eo).select();}
event.preventDefault();return false;};$.summernote.pluginEvents.outdent=function(event,editor,layoutInfo){return $.summernote.pluginEvents.indent(event,editor,layoutInfo,true);};$.summernote.pluginEvents.formatBlock=function(event,editor,layoutInfo,sTagName){$.summernote.pluginEvents.applyFont(event,editor,layoutInfo,null,null,"Default");var $editable=layoutInfo.editable();$editable.focus();$editable.data('NoteHistory').recordUndo($editable);event.preventDefault();var r=range.create();if(!r){return;}
if(r.so===0){r.sc=dom.firstChild(r.sc);}
if(dom.nodeLength(r.ec)>=r.eo){r.ec=dom.lastChild(r.ec);r.eo=dom.nodeLength(r.ec);}
r=range.create(r.sc,r.so,r.ec,r.eo);r.reRange().select();if(sTagName==="blockquote"||sTagName==="pre"){sTagName=$.summernote.core.agent.isMSIE?'<'+sTagName+'>':sTagName;document.execCommand('FormatBlock',false,sTagName);return;}
var nodes=dom.listBetween(r.sc,r.ec,r.so,r.eo);for(var i=0;i<nodes.length;i++){if(dom.isBR(nodes[i])||(dom.isText(nodes[i])&&dom.isVisibleText(nodes[i]))||dom.isB(nodes[i])||dom.isU(nodes[i])||dom.isS(nodes[i])||dom.isI(nodes[i])||dom.isFont(nodes[i])){var ancestor=dom.ancestor(nodes[i],isFormatNode);if($(ancestor).parent().is('blockquote')){$(ancestor).unwrap();}
if(!ancestor){dom.wrap(nodes[i],sTagName);}else if(ancestor.tagName.toLowerCase()!==sTagName){var tag=document.createElement(sTagName);ancestor.parentNode.insertBefore(tag,ancestor);dom.moveContent(ancestor,tag);if(ancestor.className){tag.className=ancestor.className;}
ancestor.parentNode.removeChild(ancestor);}}}
r.select();};$.summernote.pluginEvents.removeFormat=function(event,editor,layoutInfo,value){var $editable=layoutInfo.editable();$editable.data('NoteHistory').recordUndo($editable);var r=range.create();if(!r)return;var node=range.create().sc.parentNode;document.execCommand('removeFormat');document.execCommand('removeFormat');r=range.create();if(!r)return;r=dom.merge(node,r.sc,r.so,r.ec,r.eo,null,true);range.create(r.sc,r.so,r.ec,r.eo).select();event.preventDefault();return false;};eventHandler.modules.editor.undo=function($popover){if(!$popover.attr('disabled'))$popover.data('NoteHistory').undo();};eventHandler.modules.editor.redo=function($popover){if(!$popover.attr('disabled'))$popover.data('NoteHistory').redo();};var fn_from_node=eventHandler.modules.editor.style.fromNode;eventHandler.modules.editor.style.fromNode=function($node){var styleInfo=fn_from_node.apply(this,arguments);styleInfo['color']=$node.css('color');styleInfo['background-color']=$node.css('background-color');return styleInfo;};var fn_editor_currentstyle=eventHandler.modules.editor.currentStyle;eventHandler.modules.editor.currentStyle=function(target){var styleInfo=fn_editor_currentstyle.apply(this,arguments);if(!styleInfo.image||!dom.isEditable(styleInfo.image)){styleInfo.image=undefined;var r=range.create();if(r&&r.isOnEditable()){styleInfo.image=r.isOnImg();}}
if(styleInfo.anchor){styleInfo['text-align']=$(styleInfo.anchor).parent().css('text-align');}
return styleInfo;};options.fontSizes=weDefaultOptions.fontSizes;$.summernote.pluginEvents.applyFont=function(event,editor,layoutInfo,color,bgcolor,size){var r=range.create();if(!r)return;var startPoint=r.getStartPoint();var endPoint=r.getEndPoint();if(r.isCollapsed()&&!dom.isFont(r.sc)){return{sc:startPoint.node,so:startPoint.offset,ec:endPoint.node,offset:endPoint.offset};}
if(startPoint.node.tagName&&startPoint.node.childNodes[startPoint.offset]){startPoint.node=startPoint.node.childNodes[startPoint.offset];startPoint.offset=0;}
if(endPoint.node.tagName&&endPoint.node.childNodes[endPoint.offset]){endPoint.node=endPoint.node.childNodes[endPoint.offset];endPoint.offset=0;}
var ancestor;var node;if(endPoint.offset&&endPoint.offset!==dom.nodeLength(endPoint.node)){ancestor=dom.ancestor(endPoint.node,dom.isFont)||endPoint.node;dom.splitTree(ancestor,endPoint);}
if(startPoint.offset&&startPoint.offset!==dom.nodeLength(startPoint.node)){ancestor=dom.ancestor(startPoint.node,dom.isFont)||startPoint.node;node=dom.splitTree(ancestor,startPoint);if(endPoint.node===startPoint.node){endPoint.node=node;endPoint.offset=dom.nodeLength(node);}
startPoint.node=node;startPoint.offset=0;}
var nodes=[];dom.walkPoint(startPoint,endPoint,function(point){var node=point.node;if(((dom.isText(node)&&dom.isVisibleText(node))||(dom.isFont(node)&&!dom.isVisibleText(node)))&&(node!==endPoint.node||endPoint.offset)){nodes.push(point.node);}});nodes=list.unique(nodes);if(r.isCollapsed()){nodes.push(startPoint.node);}
var font,$font,fonts=[],className;var i;if(color||bgcolor||size){for(i=0;i<nodes.length;i++){node=nodes[i];font=dom.ancestor(node,dom.isFont);if(!font){if(node.textContent.match(/^[ ]|[ ]$/)){node.textContent=node.textContent.replace(/^[ ]|[ ]$/g,'\u00A0');}
font=dom.create("font");node.parentNode.insertBefore(font,node);font.appendChild(node);}
fonts.push(font);className=font.className.split(/\s+/);var k;if(color){for(k=0;k<className.length;k++){if(className[k].length&&className[k].slice(0,5)==="text-"){className.splice(k,1);k--;}}
if(color.indexOf('text-')!==-1){font.className=className.join(" ")+" "+color;font.style.color="inherit";}else{font.className=className.join(" ");font.style.color=color;}}
if(bgcolor){for(k=0;k<className.length;k++){if(className[k].length&&className[k].slice(0,3)==="bg-"){className.splice(k,1);k--;}}
if(bgcolor.indexOf('bg-')!==-1){font.className=className.join(" ")+" "+bgcolor;font.style.backgroundColor="inherit";}else{font.className=className.join(" ");font.style.backgroundColor=bgcolor;}}
if(size){font.style.fontSize="inherit";if(!isNaN(size)&&Math.abs(parseInt(dom.getComputedStyle(font).fontSize,10)-size)/size>0.05){font.style.fontSize=size+"px";}}}}
for(i=0;i<fonts.length;i++){font=fonts[i];if(font.style.backgroundColor==="inherit"){font.style.backgroundColor="";}
if(font.style.color==="inherit"){font.style.color="";}
if(font.style.fontSize==="inherit"){font.style.fontSize="";}
$font=$(font);if(!$font.css("color")&&!$font.css("background-color")&&!$font.css("font-size")){$font.removeAttr("style");}
if(!font.className.length){$font.removeAttr("class");}}
nodes=[];dom.walkPoint(startPoint,endPoint,function(point){nodes.push(point.node.childNodes[point.offset]||point.node);});nodes=list.unique(nodes);function remove(node,to){if(node===endPoint.node){endPoint=dom.prevPoint(endPoint);}
if(to){dom.moveContent(node,to);}
dom.remove(node);}
var className2,style,style2,hasBefore,hasAfter;var noContent=['none',null,undefined];for(i=0;i<nodes.length;i++){node=nodes[i];if(dom.isText(node)&&!node.nodeValue){remove(node);continue;}
font=dom.ancestor(node,dom.isFont);node=font||dom.ancestor(node,dom.isSpan);if(!node){continue;}
$font=$(node);className=dom.orderClass(node);style=dom.orderStyle(node);hasBefore=noContent.indexOf(window.getComputedStyle(node,'::before').content)===-1;hasAfter=noContent.indexOf(window.getComputedStyle(node,'::after').content)===-1;if(!className&&!style&&!hasBefore&&!hasAfter){remove(node,node.parentNode);continue;}
if(font=dom.ancestor(node.previousSibling,dom.isFont)){className2=font.getAttribute('class');style2=font.getAttribute('style');if(node!==font&&className===className2&&style===style2){remove(node,font);continue;}}}
range.create(startPoint.node,startPoint.offset,endPoint.node,endPoint.offset).select();};$.summernote.pluginEvents.fontSize=function(event,editor,layoutInfo,value){var $editable=layoutInfo.editable();event.preventDefault();$.summernote.pluginEvents.applyFont(event,editor,layoutInfo,null,null,value);editor.afterCommand($editable);};$.summernote.pluginEvents.color=function(event,editor,layoutInfo,sObjColor){var oColor=JSON.parse(sObjColor);var foreColor=oColor.foreColor,backColor=oColor.backColor;if(foreColor){$.summernote.pluginEvents.foreColor(event,editor,layoutInfo,foreColor);}
if(backColor){$.summernote.pluginEvents.backColor(event,editor,layoutInfo,backColor);}};$.summernote.pluginEvents.foreColor=function(event,editor,layoutInfo,foreColor,preview){var $editable=layoutInfo.editable();$.summernote.pluginEvents.applyFont(event,editor,layoutInfo,foreColor,null,null);if(!preview){editor.afterCommand($editable);}};$.summernote.pluginEvents.backColor=function(event,editor,layoutInfo,backColor,preview){var $editable=layoutInfo.editable();var r=range.create();if(!r)return;if(r.isCollapsed()&&r.isOnCell()){var cell=dom.ancestor(r.sc,dom.isCell);cell.className=cell.className.replace(new RegExp('(^|\\s+)bg-[^\\s]+(\\s+|$)','gi'),'');cell.style.backgroundColor="";if(backColor.indexOf('bg-')!==-1){cell.className+=' '+backColor;}else if(backColor!=='inherit'){cell.style.backgroundColor=backColor;}
return;}
$.summernote.pluginEvents.applyFont(event,editor,layoutInfo,null,backColor,null);if(!preview){editor.afterCommand($editable);}};options.onCreateLink=function(sLinkUrl){if(sLinkUrl.indexOf('mailto:')===0||sLinkUrl.indexOf('tel:')===0){sLinkUrl=sLinkUrl.replace(/^tel:([0-9]+)$/,'tel://$1');}else if(sLinkUrl.indexOf('@')!==-1&&sLinkUrl.indexOf(':')===-1){sLinkUrl='mailto:'+sLinkUrl;}else if(sLinkUrl.indexOf('://')===-1&&sLinkUrl[0]!=='/'&&sLinkUrl[0]!=='#'&&sLinkUrl.slice(0,2)!=='${'){sLinkUrl='http://'+sLinkUrl;}
return sLinkUrl;};function summernote_table_scroll(event){var r=range.create();if(r&&r.isOnCell()){$('.o_table_handler').remove();}}
function summernote_table_update(oStyle){var r=range.create();if(!oStyle.range||!r||!r.isOnCell()||!r.isOnCellFirst()){$('.o_table_handler').remove();return;}
var table=dom.ancestor(oStyle.range.sc,dom.isTable);if(!table){return;}
var $editable=$(table).closest('.o_editable');$('.o_table_handler').remove();var $dels=$();var $adds=$();var $tds=$('tr:first',table).children();$tds.each(function(){var $td=$(this);var pos=$td.offset();var $del=$('<span class="o_table_handler fa fa-minus-square"/>').appendTo('body');$del.data('td',this);$dels=$dels.add($del);$del.css({left:((pos.left+$td.outerWidth()/2)-6)+"px",top:(pos.top-6)+"px"});var $add=$('<span class="o_table_handler fa fa-plus-square"/>').appendTo('body');$add.data('td',this);$adds=$adds.add($add);$add.css({left:(pos.left-6)+"px",top:(pos.top-6)+"px"});});var $last=$tds.last();var pos=$last.offset();var $add=$('<span class="o_table_handler fa fa-plus-square"/>').appendTo('body');$adds=$adds.add($add);$add.css({left:(pos.left+$last.outerWidth()-6)+"px",top:(pos.top-6)+"px"});var $table=$(table);$dels.data('table',table).on('mousedown',function(event){var td=$(this).data('td');$editable.data('NoteHistory').recordUndo($editable);var newTd;if($(td).siblings().length){var eq=$(td).index();$table.find('tr').each(function(){$('> td:eq('+eq+')',this).remove();});newTd=$table.find('tr:first > td:eq('+eq+'), tr:first > td:last').first();}else{var prev=dom.lastChild(dom.hasContentBefore(dom.ancestorHavePreviousSibling($table[0])));$table.remove();$('.o_table_handler').remove();r=range.create(prev,prev.textContent.length);r.select();$(r.sc).trigger('mouseup');return;}
$('.o_table_handler').remove();range.create(newTd[0],0,newTd[0],0).select();newTd.trigger('mouseup');});$adds.data('table',table).on('mousedown',function(event){var td=$(this).data('td');$editable.data('NoteHistory').recordUndo($editable);var newTd;if(td){var eq=$(td).index();$table.find('tr').each(function(){$('td:eq('+eq+')',this).before('<td>'+dom.blank+'</td>');});newTd=$table.find('tr:first td:eq('+eq+')');}else{$table.find('tr').each(function(){$(this).append('<td>'+dom.blank+'</td>');});newTd=$table.find('tr:first td:last');}
$('.o_table_handler').remove();range.create(newTd[0],0,newTd[0],0).select();newTd.trigger('mouseup');});$dels.css({'position':'absolute','cursor':'pointer','background-color':'#fff','color':'#ff0000'});$adds.css({'position':'absolute','cursor':'pointer','background-color':'#fff','color':'#00ff00'});}
var fn_popover_update=eventHandler.modules.popover.update;eventHandler.modules.popover.update=function($popover,oStyle,isAirMode){fn_popover_update.call(this,$popover,oStyle,isAirMode);if((isAirMode?$popover:$popover.parent()).find('.note-table').length){summernote_table_update(oStyle);}};function mouseDownChecklist(e){if(!dom.isLi(e.target)||!$(e.target).parent('ul.o_checklist').length||e.offsetX>0){return;}
e.stopPropagation();e.preventDefault();var checked=$(e.target).hasClass('o_checked');$(e.target).toggleClass('o_checked',!checked);var $sublevel=$(e.target).next('ul.o_checklist, li:has(> ul.o_checklist)').find('> li, ul.o_checklist > li');var $parents=$(e.target).parents('ul.o_checklist').map(function(){return this.parentNode.tagName==='LI'?this.parentNode:this;});if(checked){$sublevel.removeClass('o_checked');do{$parents=$parents.prev('ul.o_checklist li').removeClass('o_checked');}while($parents.length);}else{$sublevel.addClass('o_checked');var $lis;do{$lis=$parents.not(':has(li[id^="checklist-id"]:not(.o_checked))').prev('ul.o_checklist li:not(.o_checked)');$lis.addClass('o_checked');}while($lis.length);}}
var fn_attach=eventHandler.attach;eventHandler.attach=function(oLayoutInfo,options){var $editable=oLayoutInfo.editor().hasClass('note-editable')?oLayoutInfo.editor():oLayoutInfo.editor().find('.note-editable');fn_attach.call(this,oLayoutInfo,options);$editable.on("scroll",summernote_table_scroll);$editable.on("mousedown",mouseDownChecklist);};var fn_detach=eventHandler.detach;eventHandler.detach=function(oLayoutInfo,options){var $editable=oLayoutInfo.editor().hasClass('note-editable')?oLayoutInfo.editor():oLayoutInfo.editor().find('.note-editable');fn_detach.call(this,oLayoutInfo,options);$editable.off("scroll",summernote_table_scroll);$editable.off("mousedown",mouseDownChecklist);$('.o_table_handler').remove();};options.icons.image.image="file-image-o";$.summernote.lang['en-US'].image.image="File / Image";return $.summernote;});;

/* <inline asset> defined in bundle 'web_editor.assets_summernote' */
window.define=odoo.__define__;delete odoo.__define__;delete odoo.website_next_define;delete odoo.__enable_summernote__;