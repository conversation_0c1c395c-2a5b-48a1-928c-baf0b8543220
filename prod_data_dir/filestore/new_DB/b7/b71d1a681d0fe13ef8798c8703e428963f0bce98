
/* /web/static/lib/bootstrap/scss/_functions.scss defined in bundle 'web.assets_common' */
 

/* /web/static/lib/bootstrap/scss/_mixins.scss defined in bundle 'web.assets_common' */
 

/* /web/static/src/scss/bs_mixins_overrides.scss defined in bundle 'web.assets_common' */
 

/* /web/static/src/scss/utils.scss defined in bundle 'web.assets_common' */
 .o_colorpicker_widget .o_opacity_slider, .o_colorpicker_widget .o_color_preview{position: relative; z-index: 0;}.o_colorpicker_widget .o_opacity_slider::before, .o_colorpicker_widget .o_color_preview::before{content: ""; position: absolute; top: 0; left: 0; bottom: 0; right: 0; z-index: -1; background-image: url("/web/static/src/img/transparent.png"); background-size: 10px auto; border-radius: inherit;}.o_colorpicker_widget .o_opacity_slider::after, .o_colorpicker_widget .o_color_preview::after{content: ""; position: absolute; top: 0; left: 0; bottom: 0; right: 0; z-index: -1; background: inherit; border-radius: inherit;}

/* /web/static/src/scss/primary_variables.scss defined in bundle 'web.assets_common' */
 

/* /odex25_web/static/src/scss/primary_variables.scss defined in bundle 'web.assets_common' */
 

/* /odex_backend_theme/static/src/scss/primary_variables.scss defined in bundle 'web.assets_common' */
 

/* /web_editor/static/src/scss/web_editor.variables.scss defined in bundle 'web.assets_common' */
 

/* /mail/static/src/scss/variables.scss defined in bundle 'web.assets_common' */
 

/* /hr_org_chart/static/src/scss/variables.scss defined in bundle 'web.assets_common' */
 

/* /portal/static/src/scss/primary_variables.scss defined in bundle 'web.assets_common' */
 

/* /website/static/src/scss/primary_variables.scss defined in bundle 'web.assets_common' */
 

/* /website/static/src/scss/options/user_values.scss defined in bundle 'web.assets_common' */
 

/* /website/static/src/scss/options/colors/user_color_palette.scss defined in bundle 'web.assets_common' */
 

/* /website/static/src/scss/options/colors/user_theme_color_palette.scss defined in bundle 'web.assets_common' */
 

/* /website/static/src/snippets/s_badge/000_variables.scss defined in bundle 'web.assets_common' */
 

/* /website/static/src/snippets/s_product_list/000_variables.scss defined in bundle 'web.assets_common' */
 

/* /website_sale/static/src/scss/primary_variables.scss defined in bundle 'web.assets_common' */
 

/* /account/static/src/scss/variables.scss defined in bundle 'web.assets_common' */
 @keyframes animate-red{0%{color: red;}100%{color: inherit;}}.animate{animation: animate-red 1s ease;}

/* /website/static/src/scss/secondary_variables.scss defined in bundle 'web.assets_common' */
 

/* /web_editor/static/src/scss/secondary_variables.scss defined in bundle 'web.assets_common' */
 

/* /odex25_web/static/src/scss/secondary_variables.scss defined in bundle 'web.assets_common' */
 

/* /web/static/src/scss/secondary_variables.scss defined in bundle 'web.assets_common' */
 

/* /web/static/lib/bootstrap/scss/_variables.scss defined in bundle 'web.assets_common' */
 

/* /web/static/lib/jquery.ui/jquery-ui.css defined in bundle 'web.assets_common' */
 .ui-draggable-handle{-ms-touch-action: none; touch-action: none;}.ui-helper-hidden{display: none;}.ui-helper-hidden-accessible{border: 0; clip: rect(0 0 0 0); height: 1px; margin: -1px; overflow: hidden; padding: 0; position: absolute; width: 1px;}.ui-helper-reset{margin: 0; padding: 0; border: 0; outline: 0; line-height: 1.3; text-decoration: none; font-size: 100%; list-style: none;}.ui-helper-clearfix:before, .ui-helper-clearfix:after{content: ""; display: table; border-collapse: collapse;}.ui-helper-clearfix:after{clear: both;}.ui-helper-zfix{width: 100%; height: 100%; top: 0; left: 0; position: absolute; opacity: 0; filter:Alpha(Opacity=0);}.ui-front{z-index: 100;}.ui-state-disabled{cursor: default !important; pointer-events: none;}.ui-icon{display: inline-block; vertical-align: middle; margin-top: -.25em; position: relative; text-indent: -99999px; overflow: hidden; background-repeat: no-repeat;}.ui-widget-icon-block{left: 50%; margin-left: -8px; display: block;}.ui-widget-overlay{position: fixed; top: 0; left: 0; width: 100%; height: 100%;}.ui-resizable{position: relative;}.ui-resizable-handle{position: absolute; font-size: 0.1px; display: block; -ms-touch-action: none; touch-action: none;}.ui-resizable-disabled .ui-resizable-handle, .ui-resizable-autohide .ui-resizable-handle{display: none;}.ui-resizable-n{cursor: n-resize; height: 7px; width: 100%; top: -5px; left: 0;}.ui-resizable-s{cursor: s-resize; height: 7px; width: 100%; bottom: -5px; left: 0;}.ui-resizable-e{cursor: e-resize; width: 7px; right: -5px; top: 0; height: 100%;}.ui-resizable-w{cursor: w-resize; width: 7px; left: -5px; top: 0; height: 100%;}.ui-resizable-se{cursor: se-resize; width: 12px; height: 12px; right: 1px; bottom: 1px;}.ui-resizable-sw{cursor: sw-resize; width: 9px; height: 9px; left: -5px; bottom: -5px;}.ui-resizable-nw{cursor: nw-resize; width: 9px; height: 9px; left: -5px; top: -5px;}.ui-resizable-ne{cursor: ne-resize; width: 9px; height: 9px; right: -5px; top: -5px;}.ui-selectable{-ms-touch-action: none; touch-action: none;}.ui-selectable-helper{position: absolute; z-index: 100; border: 1px dotted black;}.ui-sortable-handle{-ms-touch-action: none; touch-action: none;}.ui-autocomplete{position: absolute; top: 0; left: 0; cursor: default;}.ui-menu{list-style: none; padding: 0; margin: 0; display: block; outline: 0;}.ui-menu .ui-menu{position: absolute;}.ui-menu .ui-menu-item{margin: 0; cursor: pointer; list-style-image: url("data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7");}.ui-menu .ui-menu-item-wrapper{position: relative; padding: 3px 1em 3px .4em;}.ui-menu .ui-menu-divider{margin: 5px 0; height: 0; font-size: 0; line-height: 0; border-width: 1px 0 0 0;}.ui-menu .ui-state-focus, .ui-menu .ui-state-active{margin: -1px;}.ui-menu-icons{position: relative;}.ui-menu-icons .ui-menu-item-wrapper{padding-left: 2em;}.ui-menu .ui-icon{position: absolute; top: 0; bottom: 0; left: .2em; margin: auto 0;}.ui-menu .ui-menu-icon{left: auto; right: 0;}.ui-datepicker{width: 17em; padding: .2em .2em 0; display: none;}.ui-datepicker .ui-datepicker-header{position: relative; padding: .2em 0;}.ui-datepicker .ui-datepicker-prev, .ui-datepicker .ui-datepicker-next{position: absolute; top: 2px; width: 1.8em; height: 1.8em;}.ui-datepicker .ui-datepicker-prev-hover, .ui-datepicker .ui-datepicker-next-hover{top: 1px;}.ui-datepicker .ui-datepicker-prev{left: 2px;}.ui-datepicker .ui-datepicker-next{right: 2px;}.ui-datepicker .ui-datepicker-prev-hover{left: 1px;}.ui-datepicker .ui-datepicker-next-hover{right: 1px;}.ui-datepicker .ui-datepicker-prev span, .ui-datepicker .ui-datepicker-next span{display: block; position: absolute; left: 50%; margin-left: -8px; top: 50%; margin-top: -8px;}.ui-datepicker .ui-datepicker-title{margin: 0 2.3em; line-height: 1.8em; text-align: center;}.ui-datepicker .ui-datepicker-title select{font-size: 1em; margin: 1px 0;}.ui-datepicker select.ui-datepicker-month, .ui-datepicker select.ui-datepicker-year{width: 45%;}.ui-datepicker table{width: 100%; font-size: .9em; border-collapse: collapse; margin: 0 0 .4em;}.ui-datepicker th{padding: .7em .3em; text-align: center; font-weight: bold; border: 0;}.ui-datepicker td{border: 0; padding: 1px;}.ui-datepicker td span, .ui-datepicker td a{display: block; padding: .2em; text-align: right; text-decoration: none;}.ui-datepicker .ui-datepicker-buttonpane{background-image: none; margin: .7em 0 0 0; padding: 0 .2em; border-left: 0; border-right: 0; border-bottom: 0;}.ui-datepicker .ui-datepicker-buttonpane button{float: right; margin: .5em .2em .4em; cursor: pointer; padding: .2em .6em .3em .6em; width: auto; overflow: visible;}.ui-datepicker .ui-datepicker-buttonpane button.ui-datepicker-current{float: left;}.ui-datepicker.ui-datepicker-multi{width: auto;}.ui-datepicker-multi .ui-datepicker-group{float: left;}.ui-datepicker-multi .ui-datepicker-group table{width: 95%; margin: 0 auto .4em;}.ui-datepicker-multi-2 .ui-datepicker-group{width: 50%;}.ui-datepicker-multi-3 .ui-datepicker-group{width: 33.3%;}.ui-datepicker-multi-4 .ui-datepicker-group{width: 25%;}.ui-datepicker-multi .ui-datepicker-group-last .ui-datepicker-header, .ui-datepicker-multi .ui-datepicker-group-middle .ui-datepicker-header{border-left-width: 0;}.ui-datepicker-multi .ui-datepicker-buttonpane{clear: left;}.ui-datepicker-row-break{clear: both; width: 100%; font-size: 0;}.ui-datepicker-rtl{direction: rtl;}.ui-datepicker-rtl .ui-datepicker-prev{right: 2px; left: auto;}.ui-datepicker-rtl .ui-datepicker-next{left: 2px; right: auto;}.ui-datepicker-rtl .ui-datepicker-prev:hover{right: 1px; left: auto;}.ui-datepicker-rtl .ui-datepicker-next:hover{left: 1px; right: auto;}.ui-datepicker-rtl .ui-datepicker-buttonpane{clear: right;}.ui-datepicker-rtl .ui-datepicker-buttonpane button{float: left;}.ui-datepicker-rtl .ui-datepicker-buttonpane button.ui-datepicker-current, .ui-datepicker-rtl .ui-datepicker-group{float: right;}.ui-datepicker-rtl .ui-datepicker-group-last .ui-datepicker-header, .ui-datepicker-rtl .ui-datepicker-group-middle .ui-datepicker-header{border-right-width: 0; border-left-width: 1px;}.ui-datepicker .ui-icon{display: block; text-indent: -99999px; overflow: hidden; background-repeat: no-repeat; left: .5em; top: .3em;}.ui-tooltip{padding: 8px; position: absolute; z-index: 9999; max-width: 300px;}body .ui-tooltip{border-width: 2px;}.ui-widget{font-family: Arial,Helvetica,sans-serif; font-size: 1em;}.ui-widget .ui-widget{font-size: 1em;}.ui-widget input, .ui-widget select, .ui-widget textarea, .ui-widget button{font-family: Arial,Helvetica,sans-serif; font-size: 1em;}.ui-widget.ui-widget-content{border: 1px solid #c5c5c5;}.ui-widget-content{border: 1px solid #dddddd; background: #ffffff; color: #333333;}.ui-widget-content a{color: #333333;}.ui-widget-header{border: 1px solid #dddddd; background: #e9e9e9; color: #333333; font-weight: bold;}.ui-widget-header a{color: #333333;}.ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default, .ui-button, html .ui-button.ui-state-disabled:hover, html .ui-button.ui-state-disabled:active{border: 1px solid #c5c5c5; background: #f6f6f6; font-weight: normal; color: #454545;}.ui-state-default a, .ui-state-default a:link, .ui-state-default a:visited, a.ui-button, a:link.ui-button, a:visited.ui-button, .ui-button{color: #454545; text-decoration: none;}.ui-state-hover, .ui-widget-content .ui-state-hover, .ui-widget-header .ui-state-hover, .ui-state-focus, .ui-widget-content .ui-state-focus, .ui-widget-header .ui-state-focus, .ui-button:hover, .ui-button:focus{border: 1px solid #cccccc; background: #ededed; font-weight: normal; color: #2b2b2b;}.ui-state-hover a, .ui-state-hover a:hover, .ui-state-hover a:link, .ui-state-hover a:visited, .ui-state-focus a, .ui-state-focus a:hover, .ui-state-focus a:link, .ui-state-focus a:visited, a.ui-button:hover, a.ui-button:focus{color: #2b2b2b; text-decoration: none;}.ui-visual-focus{box-shadow: 0 0 3px 1px rgb(94, 158, 214);}.ui-state-active, .ui-widget-content .ui-state-active, .ui-widget-header .ui-state-active, a.ui-button:active, .ui-button:active, .ui-button.ui-state-active:hover{border: 1px solid #003eff; background: #007fff; font-weight: normal; color: #ffffff;}.ui-icon-background, .ui-state-active .ui-icon-background{border: #003eff; background-color: #ffffff;}.ui-state-active a, .ui-state-active a:link, .ui-state-active a:visited{color: #ffffff; text-decoration: none;}.ui-state-highlight, .ui-widget-content .ui-state-highlight, .ui-widget-header .ui-state-highlight{border: 1px solid #dad55e; background: #fffa90; color: #777620;}.ui-state-checked{border: 1px solid #dad55e; background: #fffa90;}.ui-state-highlight a, .ui-widget-content .ui-state-highlight a, .ui-widget-header .ui-state-highlight a{color: #777620;}.ui-state-error, .ui-widget-content .ui-state-error, .ui-widget-header .ui-state-error{border: 1px solid #f1a899; background: #fddfdf; color: #5f3f3f;}.ui-state-error a, .ui-widget-content .ui-state-error a, .ui-widget-header .ui-state-error a{color: #5f3f3f;}.ui-state-error-text, .ui-widget-content .ui-state-error-text, .ui-widget-header .ui-state-error-text{color: #5f3f3f;}.ui-priority-primary, .ui-widget-content .ui-priority-primary, .ui-widget-header .ui-priority-primary{font-weight: bold;}.ui-priority-secondary, .ui-widget-content .ui-priority-secondary, .ui-widget-header .ui-priority-secondary{opacity: .7; filter:Alpha(Opacity=70); font-weight: normal;}.ui-state-disabled, .ui-widget-content .ui-state-disabled, .ui-widget-header .ui-state-disabled{opacity: .35; filter:Alpha(Opacity=35); background-image: none;}.ui-state-disabled .ui-icon{filter:Alpha(Opacity=35);}.ui-icon{width: 16px; height: 16px;}.ui-icon, .ui-widget-content .ui-icon{background-image: url("/web/static/lib/jquery.ui/images/ui-icons_444444_256x240.png");}.ui-widget-header .ui-icon{background-image: url("/web/static/lib/jquery.ui/images/ui-icons_444444_256x240.png");}.ui-state-hover .ui-icon, .ui-state-focus .ui-icon, .ui-button:hover .ui-icon, .ui-button:focus .ui-icon{background-image: url("/web/static/lib/jquery.ui/images/ui-icons_555555_256x240.png");}.ui-state-active .ui-icon, .ui-button:active .ui-icon{background-image: url("/web/static/lib/jquery.ui/images/ui-icons_ffffff_256x240.png");}.ui-state-highlight .ui-icon, .ui-button .ui-state-highlight.ui-icon{background-image: url("/web/static/lib/jquery.ui/images/ui-icons_777620_256x240.png");}.ui-state-error .ui-icon, .ui-state-error-text .ui-icon{background-image: url("/web/static/lib/jquery.ui/images/ui-icons_cc0000_256x240.png");}.ui-button .ui-icon{background-image: url("/web/static/lib/jquery.ui/images/ui-icons_777777_256x240.png");}.ui-icon-blank{background-position: 16px 16px;}.ui-icon-caret-1-n{background-position: 0 0;}.ui-icon-caret-1-ne{background-position: -16px 0;}.ui-icon-caret-1-e{background-position: -32px 0;}.ui-icon-caret-1-se{background-position: -48px 0;}.ui-icon-caret-1-s{background-position: -65px 0;}.ui-icon-caret-1-sw{background-position: -80px 0;}.ui-icon-caret-1-w{background-position: -96px 0;}.ui-icon-caret-1-nw{background-position: -112px 0;}.ui-icon-caret-2-n-s{background-position: -128px 0;}.ui-icon-caret-2-e-w{background-position: -144px 0;}.ui-icon-triangle-1-n{background-position: 0 -16px;}.ui-icon-triangle-1-ne{background-position: -16px -16px;}.ui-icon-triangle-1-e{background-position: -32px -16px;}.ui-icon-triangle-1-se{background-position: -48px -16px;}.ui-icon-triangle-1-s{background-position: -65px -16px;}.ui-icon-triangle-1-sw{background-position: -80px -16px;}.ui-icon-triangle-1-w{background-position: -96px -16px;}.ui-icon-triangle-1-nw{background-position: -112px -16px;}.ui-icon-triangle-2-n-s{background-position: -128px -16px;}.ui-icon-triangle-2-e-w{background-position: -144px -16px;}.ui-icon-arrow-1-n{background-position: 0 -32px;}.ui-icon-arrow-1-ne{background-position: -16px -32px;}.ui-icon-arrow-1-e{background-position: -32px -32px;}.ui-icon-arrow-1-se{background-position: -48px -32px;}.ui-icon-arrow-1-s{background-position: -65px -32px;}.ui-icon-arrow-1-sw{background-position: -80px -32px;}.ui-icon-arrow-1-w{background-position: -96px -32px;}.ui-icon-arrow-1-nw{background-position: -112px -32px;}.ui-icon-arrow-2-n-s{background-position: -128px -32px;}.ui-icon-arrow-2-ne-sw{background-position: -144px -32px;}.ui-icon-arrow-2-e-w{background-position: -160px -32px;}.ui-icon-arrow-2-se-nw{background-position: -176px -32px;}.ui-icon-arrowstop-1-n{background-position: -192px -32px;}.ui-icon-arrowstop-1-e{background-position: -208px -32px;}.ui-icon-arrowstop-1-s{background-position: -224px -32px;}.ui-icon-arrowstop-1-w{background-position: -240px -32px;}.ui-icon-arrowthick-1-n{background-position: 1px -48px;}.ui-icon-arrowthick-1-ne{background-position: -16px -48px;}.ui-icon-arrowthick-1-e{background-position: -32px -48px;}.ui-icon-arrowthick-1-se{background-position: -48px -48px;}.ui-icon-arrowthick-1-s{background-position: -64px -48px;}.ui-icon-arrowthick-1-sw{background-position: -80px -48px;}.ui-icon-arrowthick-1-w{background-position: -96px -48px;}.ui-icon-arrowthick-1-nw{background-position: -112px -48px;}.ui-icon-arrowthick-2-n-s{background-position: -128px -48px;}.ui-icon-arrowthick-2-ne-sw{background-position: -144px -48px;}.ui-icon-arrowthick-2-e-w{background-position: -160px -48px;}.ui-icon-arrowthick-2-se-nw{background-position: -176px -48px;}.ui-icon-arrowthickstop-1-n{background-position: -192px -48px;}.ui-icon-arrowthickstop-1-e{background-position: -208px -48px;}.ui-icon-arrowthickstop-1-s{background-position: -224px -48px;}.ui-icon-arrowthickstop-1-w{background-position: -240px -48px;}.ui-icon-arrowreturnthick-1-w{background-position: 0 -64px;}.ui-icon-arrowreturnthick-1-n{background-position: -16px -64px;}.ui-icon-arrowreturnthick-1-e{background-position: -32px -64px;}.ui-icon-arrowreturnthick-1-s{background-position: -48px -64px;}.ui-icon-arrowreturn-1-w{background-position: -64px -64px;}.ui-icon-arrowreturn-1-n{background-position: -80px -64px;}.ui-icon-arrowreturn-1-e{background-position: -96px -64px;}.ui-icon-arrowreturn-1-s{background-position: -112px -64px;}.ui-icon-arrowrefresh-1-w{background-position: -128px -64px;}.ui-icon-arrowrefresh-1-n{background-position: -144px -64px;}.ui-icon-arrowrefresh-1-e{background-position: -160px -64px;}.ui-icon-arrowrefresh-1-s{background-position: -176px -64px;}.ui-icon-arrow-4{background-position: 0 -80px;}.ui-icon-arrow-4-diag{background-position: -16px -80px;}.ui-icon-extlink{background-position: -32px -80px;}.ui-icon-newwin{background-position: -48px -80px;}.ui-icon-refresh{background-position: -64px -80px;}.ui-icon-shuffle{background-position: -80px -80px;}.ui-icon-transfer-e-w{background-position: -96px -80px;}.ui-icon-transferthick-e-w{background-position: -112px -80px;}.ui-icon-folder-collapsed{background-position: 0 -96px;}.ui-icon-folder-open{background-position: -16px -96px;}.ui-icon-document{background-position: -32px -96px;}.ui-icon-document-b{background-position: -48px -96px;}.ui-icon-note{background-position: -64px -96px;}.ui-icon-mail-closed{background-position: -80px -96px;}.ui-icon-mail-open{background-position: -96px -96px;}.ui-icon-suitcase{background-position: -112px -96px;}.ui-icon-comment{background-position: -128px -96px;}.ui-icon-person{background-position: -144px -96px;}.ui-icon-print{background-position: -160px -96px;}.ui-icon-trash{background-position: -176px -96px;}.ui-icon-locked{background-position: -192px -96px;}.ui-icon-unlocked{background-position: -208px -96px;}.ui-icon-bookmark{background-position: -224px -96px;}.ui-icon-tag{background-position: -240px -96px;}.ui-icon-home{background-position: 0 -112px;}.ui-icon-flag{background-position: -16px -112px;}.ui-icon-calendar{background-position: -32px -112px;}.ui-icon-cart{background-position: -48px -112px;}.ui-icon-pencil{background-position: -64px -112px;}.ui-icon-clock{background-position: -80px -112px;}.ui-icon-disk{background-position: -96px -112px;}.ui-icon-calculator{background-position: -112px -112px;}.ui-icon-zoomin{background-position: -128px -112px;}.ui-icon-zoomout{background-position: -144px -112px;}.ui-icon-search{background-position: -160px -112px;}.ui-icon-wrench{background-position: -176px -112px;}.ui-icon-gear{background-position: -192px -112px;}.ui-icon-heart{background-position: -208px -112px;}.ui-icon-star{background-position: -224px -112px;}.ui-icon-link{background-position: -240px -112px;}.ui-icon-cancel{background-position: 0 -128px;}.ui-icon-plus{background-position: -16px -128px;}.ui-icon-plusthick{background-position: -32px -128px;}.ui-icon-minus{background-position: -48px -128px;}.ui-icon-minusthick{background-position: -64px -128px;}.ui-icon-close{background-position: -80px -128px;}.ui-icon-closethick{background-position: -96px -128px;}.ui-icon-key{background-position: -112px -128px;}.ui-icon-lightbulb{background-position: -128px -128px;}.ui-icon-scissors{background-position: -144px -128px;}.ui-icon-clipboard{background-position: -160px -128px;}.ui-icon-copy{background-position: -176px -128px;}.ui-icon-contact{background-position: -192px -128px;}.ui-icon-image{background-position: -208px -128px;}.ui-icon-video{background-position: -224px -128px;}.ui-icon-script{background-position: -240px -128px;}.ui-icon-alert{background-position: 0 -144px;}.ui-icon-info{background-position: -16px -144px;}.ui-icon-notice{background-position: -32px -144px;}.ui-icon-help{background-position: -48px -144px;}.ui-icon-check{background-position: -64px -144px;}.ui-icon-bullet{background-position: -80px -144px;}.ui-icon-radio-on{background-position: -96px -144px;}.ui-icon-radio-off{background-position: -112px -144px;}.ui-icon-pin-w{background-position: -128px -144px;}.ui-icon-pin-s{background-position: -144px -144px;}.ui-icon-play{background-position: 0 -160px;}.ui-icon-pause{background-position: -16px -160px;}.ui-icon-seek-next{background-position: -32px -160px;}.ui-icon-seek-prev{background-position: -48px -160px;}.ui-icon-seek-end{background-position: -64px -160px;}.ui-icon-seek-start{background-position: -80px -160px;}.ui-icon-seek-first{background-position: -80px -160px;}.ui-icon-stop{background-position: -96px -160px;}.ui-icon-eject{background-position: -112px -160px;}.ui-icon-volume-off{background-position: -128px -160px;}.ui-icon-volume-on{background-position: -144px -160px;}.ui-icon-power{background-position: 0 -176px;}.ui-icon-signal-diag{background-position: -16px -176px;}.ui-icon-signal{background-position: -32px -176px;}.ui-icon-battery-0{background-position: -48px -176px;}.ui-icon-battery-1{background-position: -64px -176px;}.ui-icon-battery-2{background-position: -80px -176px;}.ui-icon-battery-3{background-position: -96px -176px;}.ui-icon-circle-plus{background-position: 0 -192px;}.ui-icon-circle-minus{background-position: -16px -192px;}.ui-icon-circle-close{background-position: -32px -192px;}.ui-icon-circle-triangle-e{background-position: -48px -192px;}.ui-icon-circle-triangle-s{background-position: -64px -192px;}.ui-icon-circle-triangle-w{background-position: -80px -192px;}.ui-icon-circle-triangle-n{background-position: -96px -192px;}.ui-icon-circle-arrow-e{background-position: -112px -192px;}.ui-icon-circle-arrow-s{background-position: -128px -192px;}.ui-icon-circle-arrow-w{background-position: -144px -192px;}.ui-icon-circle-arrow-n{background-position: -160px -192px;}.ui-icon-circle-zoomin{background-position: -176px -192px;}.ui-icon-circle-zoomout{background-position: -192px -192px;}.ui-icon-circle-check{background-position: -208px -192px;}.ui-icon-circlesmall-plus{background-position: 0 -208px;}.ui-icon-circlesmall-minus{background-position: -16px -208px;}.ui-icon-circlesmall-close{background-position: -32px -208px;}.ui-icon-squaresmall-plus{background-position: -48px -208px;}.ui-icon-squaresmall-minus{background-position: -64px -208px;}.ui-icon-squaresmall-close{background-position: -80px -208px;}.ui-icon-grip-dotted-vertical{background-position: 0 -224px;}.ui-icon-grip-dotted-horizontal{background-position: -16px -224px;}.ui-icon-grip-solid-vertical{background-position: -32px -224px;}.ui-icon-grip-solid-horizontal{background-position: -48px -224px;}.ui-icon-gripsmall-diagonal-se{background-position: -64px -224px;}.ui-icon-grip-diagonal-se{background-position: -80px -224px;}.ui-corner-all, .ui-corner-top, .ui-corner-left, .ui-corner-tl{border-top-left-radius: 3px;}.ui-corner-all, .ui-corner-top, .ui-corner-right, .ui-corner-tr{border-top-right-radius: 3px;}.ui-corner-all, .ui-corner-bottom, .ui-corner-left, .ui-corner-bl{border-bottom-left-radius: 3px;}.ui-corner-all, .ui-corner-bottom, .ui-corner-right, .ui-corner-br{border-bottom-right-radius: 3px;}.ui-widget-overlay{background: #aaaaaa; opacity: .3; filter: Alpha(Opacity=30);}.ui-widget-shadow{-webkit-box-shadow: 0px 0px 5px #666666; box-shadow: 0px 0px 5px #666666;}

/* /web/static/lib/fontawesome/css/font-awesome.css defined in bundle 'web.assets_common' */
 @font-face{font-family: 'FontAwesome'; src: url('/web/static/lib/fontawesome/css/../fonts/fontawesome-webfont.eot?v=4.7.0'); src: url('/web/static/lib/fontawesome/css/../fonts/fontawesome-webfont.eot?#iefix&v=4.7.0') format('embedded-opentype'), url('/web/static/lib/fontawesome/css/../fonts/fontawesome-webfont.woff2?v=4.7.0') format('woff2'), url('/web/static/lib/fontawesome/css/../fonts/fontawesome-webfont.woff?v=4.7.0') format('woff'), url('/web/static/lib/fontawesome/css/../fonts/fontawesome-webfont.ttf?v=4.7.0') format('truetype'), url('/web/static/lib/fontawesome/css/../fonts/fontawesome-webfont.svg?v=4.7.0#fontawesomeregular') format('svg'); font-weight: normal; font-style: normal; font-display: block;}.fa{display: inline-block; font: normal normal normal 14px/1 FontAwesome; font-size: inherit; text-rendering: auto; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale;}.fa-lg{font-size: 1.33333333em; line-height: 0.75em; vertical-align: -15%;}.fa-2x{font-size: 2em;}.fa-3x{font-size: 3em;}.fa-4x{font-size: 4em;}.fa-5x{font-size: 5em;}.fa-fw{width: 1.28571429em; text-align: center;}.fa-ul{padding-left: 0; margin-left: 2.14285714em; list-style-type: none;}.fa-ul > li{position: relative;}.fa-li{position: absolute; left: -2.14285714em; width: 2.14285714em; top: 0.14285714em; text-align: center;}.fa-li.fa-lg{left: -1.85714286em;}.fa-border{padding: .2em .25em .15em; border: solid 0.08em #eeeeee; border-radius: .1em;}.fa-pull-left{float: left;}.fa-pull-right{float: right;}.fa.fa-pull-left{margin-right: .3em;}.fa.fa-pull-right{margin-left: .3em;}.pull-right{float: right;}.pull-left{float: left;}.fa.pull-left{margin-right: .3em;}.fa.pull-right{margin-left: .3em;}.fa-spin{-webkit-animation: fa-spin 2s infinite linear; animation: fa-spin 2s infinite linear;}.fa-pulse{-webkit-animation: fa-spin 1s infinite steps(8); animation: fa-spin 1s infinite steps(8);}@-webkit-keyframes fa-spin{0%{-webkit-transform: rotate(0deg); transform: rotate(0deg);}100%{-webkit-transform: rotate(359deg); transform: rotate(359deg);}}@keyframes fa-spin{0%{-webkit-transform: rotate(0deg); transform: rotate(0deg);}100%{-webkit-transform: rotate(359deg); transform: rotate(359deg);}}.fa-rotate-90{-ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=1)"; -webkit-transform: rotate(90deg); -ms-transform: rotate(90deg); transform: rotate(90deg);}.fa-rotate-180{-ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2)"; -webkit-transform: rotate(180deg); -ms-transform: rotate(180deg); transform: rotate(180deg);}.fa-rotate-270{-ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=3)"; -webkit-transform: rotate(270deg); -ms-transform: rotate(270deg); transform: rotate(270deg);}.fa-flip-horizontal{-ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=0, mirror=1)"; -webkit-transform: scale(-1, 1); -ms-transform: scale(-1, 1); transform: scale(-1, 1);}.fa-flip-vertical{-ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2, mirror=1)"; -webkit-transform: scale(1, -1); -ms-transform: scale(1, -1); transform: scale(1, -1);}:root .fa-rotate-90, :root .fa-rotate-180, :root .fa-rotate-270, :root .fa-flip-horizontal, :root .fa-flip-vertical{filter: none;}.fa-stack{position: relative; display: inline-block; width: 2em; height: 2em; line-height: 2em; vertical-align: middle;}.fa-stack-1x, .fa-stack-2x{position: absolute; left: 0; width: 100%; text-align: center;}.fa-stack-1x{line-height: inherit;}.fa-stack-2x{font-size: 2em;}.fa-inverse{color: #ffffff;}.fa-glass:before{content: "\f000";}.fa-music:before{content: "\f001";}.fa-search:before{content: "\f002";}.fa-envelope-o:before{content: "\f003";}.fa-heart:before{content: "\f004";}.fa-star:before{content: "\f005";}.fa-star-o:before{content: "\f006";}.fa-user:before{content: "\f007";}.fa-film:before{content: "\f008";}.fa-th-large:before{content: "\f009";}.fa-th:before{content: "\f00a";}.fa-th-list:before{content: "\f00b";}.fa-check:before{content: "\f00c";}.fa-remove:before, .fa-close:before, .fa-times:before{content: "\f00d";}.fa-search-plus:before{content: "\f00e";}.fa-search-minus:before{content: "\f010";}.fa-power-off:before{content: "\f011";}.fa-signal:before{content: "\f012";}.fa-gear:before, .fa-cog:before{content: "\f013";}.fa-trash-o:before{content: "\f014";}.fa-home:before{content: "\f015";}.fa-file-o:before{content: "\f016";}.fa-clock-o:before{content: "\f017";}.fa-road:before{content: "\f018";}.fa-download:before{content: "\f019";}.fa-arrow-circle-o-down:before{content: "\f01a";}.fa-arrow-circle-o-up:before{content: "\f01b";}.fa-inbox:before{content: "\f01c";}.fa-play-circle-o:before{content: "\f01d";}.fa-rotate-right:before, .fa-repeat:before{content: "\f01e";}.fa-refresh:before{content: "\f021";}.fa-list-alt:before{content: "\f022";}.fa-lock:before{content: "\f023";}.fa-flag:before{content: "\f024";}.fa-headphones:before{content: "\f025";}.fa-volume-off:before{content: "\f026";}.fa-volume-down:before{content: "\f027";}.fa-volume-up:before{content: "\f028";}.fa-qrcode:before{content: "\f029";}.fa-barcode:before{content: "\f02a";}.fa-tag:before{content: "\f02b";}.fa-tags:before{content: "\f02c";}.fa-book:before{content: "\f02d";}.fa-bookmark:before{content: "\f02e";}.fa-print:before{content: "\f02f";}.fa-camera:before{content: "\f030";}.fa-font:before{content: "\f031";}.fa-bold:before{content: "\f032";}.fa-italic:before{content: "\f033";}.fa-text-height:before{content: "\f034";}.fa-text-width:before{content: "\f035";}.fa-align-left:before{content: "\f036";}.fa-align-center:before{content: "\f037";}.fa-align-right:before{content: "\f038";}.fa-align-justify:before{content: "\f039";}.fa-list:before{content: "\f03a";}.fa-dedent:before, .fa-outdent:before{content: "\f03b";}.fa-indent:before{content: "\f03c";}.fa-video-camera:before{content: "\f03d";}.fa-photo:before, .fa-image:before, .fa-picture-o:before{content: "\f03e";}.fa-pencil:before{content: "\f040";}.fa-map-marker:before{content: "\f041";}.fa-adjust:before{content: "\f042";}.fa-tint:before{content: "\f043";}.fa-edit:before, .fa-pencil-square-o:before{content: "\f044";}.fa-share-square-o:before{content: "\f045";}.fa-check-square-o:before{content: "\f046";}.fa-arrows:before{content: "\f047";}.fa-step-backward:before{content: "\f048";}.fa-fast-backward:before{content: "\f049";}.fa-backward:before{content: "\f04a";}.fa-play:before{content: "\f04b";}.fa-pause:before{content: "\f04c";}.fa-stop:before{content: "\f04d";}.fa-forward:before{content: "\f04e";}.fa-fast-forward:before{content: "\f050";}.fa-step-forward:before{content: "\f051";}.fa-eject:before{content: "\f052";}.fa-chevron-left:before{content: "\f053";}.fa-chevron-right:before{content: "\f054";}.fa-plus-circle:before{content: "\f055";}.fa-minus-circle:before{content: "\f056";}.fa-times-circle:before{content: "\f057";}.fa-check-circle:before{content: "\f058";}.fa-question-circle:before{content: "\f059";}.fa-info-circle:before{content: "\f05a";}.fa-crosshairs:before{content: "\f05b";}.fa-times-circle-o:before{content: "\f05c";}.fa-check-circle-o:before{content: "\f05d";}.fa-ban:before{content: "\f05e";}.fa-arrow-left:before{content: "\f060";}.fa-arrow-right:before{content: "\f061";}.fa-arrow-up:before{content: "\f062";}.fa-arrow-down:before{content: "\f063";}.fa-mail-forward:before, .fa-share:before{content: "\f064";}.fa-expand:before{content: "\f065";}.fa-compress:before{content: "\f066";}.fa-plus:before{content: "\f067";}.fa-minus:before{content: "\f068";}.fa-asterisk:before{content: "\f069";}.fa-exclamation-circle:before{content: "\f06a";}.fa-gift:before{content: "\f06b";}.fa-leaf:before{content: "\f06c";}.fa-fire:before{content: "\f06d";}.fa-eye:before{content: "\f06e";}.fa-eye-slash:before{content: "\f070";}.fa-warning:before, .fa-exclamation-triangle:before{content: "\f071";}.fa-plane:before{content: "\f072";}.fa-calendar:before{content: "\f073";}.fa-random:before{content: "\f074";}.fa-comment:before{content: "\f075";}.fa-magnet:before{content: "\f076";}.fa-chevron-up:before{content: "\f077";}.fa-chevron-down:before{content: "\f078";}.fa-retweet:before{content: "\f079";}.fa-shopping-cart:before{content: "\f07a";}.fa-folder:before{content: "\f07b";}.fa-folder-open:before{content: "\f07c";}.fa-arrows-v:before{content: "\f07d";}.fa-arrows-h:before{content: "\f07e";}.fa-bar-chart-o:before, .fa-bar-chart:before{content: "\f080";}.fa-twitter-square:before{content: "\f081";}.fa-facebook-square:before{content: "\f082";}.fa-camera-retro:before{content: "\f083";}.fa-key:before{content: "\f084";}.fa-gears:before, .fa-cogs:before{content: "\f085";}.fa-comments:before{content: "\f086";}.fa-thumbs-o-up:before{content: "\f087";}.fa-thumbs-o-down:before{content: "\f088";}.fa-star-half:before{content: "\f089";}.fa-heart-o:before{content: "\f08a";}.fa-sign-out:before{content: "\f08b";}.fa-linkedin-square:before{content: "\f08c";}.fa-thumb-tack:before{content: "\f08d";}.fa-external-link:before{content: "\f08e";}.fa-sign-in:before{content: "\f090";}.fa-trophy:before{content: "\f091";}.fa-github-square:before{content: "\f092";}.fa-upload:before{content: "\f093";}.fa-lemon-o:before{content: "\f094";}.fa-phone:before{content: "\f095";}.fa-square-o:before{content: "\f096";}.fa-bookmark-o:before{content: "\f097";}.fa-phone-square:before{content: "\f098";}.fa-twitter:before{content: "\f099";}.fa-facebook-f:before, .fa-facebook:before{content: "\f09a";}.fa-github:before{content: "\f09b";}.fa-unlock:before{content: "\f09c";}.fa-credit-card:before{content: "\f09d";}.fa-feed:before, .fa-rss:before{content: "\f09e";}.fa-hdd-o:before{content: "\f0a0";}.fa-bullhorn:before{content: "\f0a1";}.fa-bell:before{content: "\f0f3";}.fa-certificate:before{content: "\f0a3";}.fa-hand-o-right:before{content: "\f0a4";}.fa-hand-o-left:before{content: "\f0a5";}.fa-hand-o-up:before{content: "\f0a6";}.fa-hand-o-down:before{content: "\f0a7";}.fa-arrow-circle-left:before{content: "\f0a8";}.fa-arrow-circle-right:before{content: "\f0a9";}.fa-arrow-circle-up:before{content: "\f0aa";}.fa-arrow-circle-down:before{content: "\f0ab";}.fa-globe:before{content: "\f0ac";}.fa-wrench:before{content: "\f0ad";}.fa-tasks:before{content: "\f0ae";}.fa-filter:before{content: "\f0b0";}.fa-briefcase:before{content: "\f0b1";}.fa-arrows-alt:before{content: "\f0b2";}.fa-group:before, .fa-users:before{content: "\f0c0";}.fa-chain:before, .fa-link:before{content: "\f0c1";}.fa-cloud:before{content: "\f0c2";}.fa-flask:before{content: "\f0c3";}.fa-cut:before, .fa-scissors:before{content: "\f0c4";}.fa-copy:before, .fa-files-o:before{content: "\f0c5";}.fa-paperclip:before{content: "\f0c6";}.fa-save:before, .fa-floppy-o:before{content: "\f0c7";}.fa-square:before{content: "\f0c8";}.fa-navicon:before, .fa-reorder:before, .fa-bars:before{content: "\f0c9";}.fa-list-ul:before{content: "\f0ca";}.fa-list-ol:before{content: "\f0cb";}.fa-strikethrough:before{content: "\f0cc";}.fa-underline:before{content: "\f0cd";}.fa-table:before{content: "\f0ce";}.fa-magic:before{content: "\f0d0";}.fa-truck:before{content: "\f0d1";}.fa-pinterest:before{content: "\f0d2";}.fa-pinterest-square:before{content: "\f0d3";}.fa-google-plus-square:before{content: "\f0d4";}.fa-google-plus:before{content: "\f0d5";}.fa-money:before{content: "\f0d6";}.fa-caret-down:before{content: "\f0d7";}.fa-caret-up:before{content: "\f0d8";}.fa-caret-left:before{content: "\f0d9";}.fa-caret-right:before{content: "\f0da";}.fa-columns:before{content: "\f0db";}.fa-unsorted:before, .fa-sort:before{content: "\f0dc";}.fa-sort-down:before, .fa-sort-desc:before{content: "\f0dd";}.fa-sort-up:before, .fa-sort-asc:before{content: "\f0de";}.fa-envelope:before{content: "\f0e0";}.fa-linkedin:before{content: "\f0e1";}.fa-rotate-left:before, .fa-undo:before{content: "\f0e2";}.fa-legal:before, .fa-gavel:before{content: "\f0e3";}.fa-dashboard:before, .fa-tachometer:before{content: "\f0e4";}.fa-comment-o:before{content: "\f0e5";}.fa-comments-o:before{content: "\f0e6";}.fa-flash:before, .fa-bolt:before{content: "\f0e7";}.fa-sitemap:before{content: "\f0e8";}.fa-umbrella:before{content: "\f0e9";}.fa-paste:before, .fa-clipboard:before{content: "\f0ea";}.fa-lightbulb-o:before{content: "\f0eb";}.fa-exchange:before{content: "\f0ec";}.fa-cloud-download:before{content: "\f0ed";}.fa-cloud-upload:before{content: "\f0ee";}.fa-user-md:before{content: "\f0f0";}.fa-stethoscope:before{content: "\f0f1";}.fa-suitcase:before{content: "\f0f2";}.fa-bell-o:before{content: "\f0a2";}.fa-coffee:before{content: "\f0f4";}.fa-cutlery:before{content: "\f0f5";}.fa-file-text-o:before{content: "\f0f6";}.fa-building-o:before{content: "\f0f7";}.fa-hospital-o:before{content: "\f0f8";}.fa-ambulance:before{content: "\f0f9";}.fa-medkit:before{content: "\f0fa";}.fa-fighter-jet:before{content: "\f0fb";}.fa-beer:before{content: "\f0fc";}.fa-h-square:before{content: "\f0fd";}.fa-plus-square:before{content: "\f0fe";}.fa-angle-double-left:before{content: "\f100";}.fa-angle-double-right:before{content: "\f101";}.fa-angle-double-up:before{content: "\f102";}.fa-angle-double-down:before{content: "\f103";}.fa-angle-left:before{content: "\f104";}.fa-angle-right:before{content: "\f105";}.fa-angle-up:before{content: "\f106";}.fa-angle-down:before{content: "\f107";}.fa-desktop:before{content: "\f108";}.fa-laptop:before{content: "\f109";}.fa-tablet:before{content: "\f10a";}.fa-mobile-phone:before, .fa-mobile:before{content: "\f10b";}.fa-circle-o:before{content: "\f10c";}.fa-quote-left:before{content: "\f10d";}.fa-quote-right:before{content: "\f10e";}.fa-spinner:before{content: "\f110";}.fa-circle:before{content: "\f111";}.fa-mail-reply:before, .fa-reply:before{content: "\f112";}.fa-github-alt:before{content: "\f113";}.fa-folder-o:before{content: "\f114";}.fa-folder-open-o:before{content: "\f115";}.fa-smile-o:before{content: "\f118";}.fa-frown-o:before{content: "\f119";}.fa-meh-o:before{content: "\f11a";}.fa-gamepad:before{content: "\f11b";}.fa-keyboard-o:before{content: "\f11c";}.fa-flag-o:before{content: "\f11d";}.fa-flag-checkered:before{content: "\f11e";}.fa-terminal:before{content: "\f120";}.fa-code:before{content: "\f121";}.fa-mail-reply-all:before, .fa-reply-all:before{content: "\f122";}.fa-star-half-empty:before, .fa-star-half-full:before, .fa-star-half-o:before{content: "\f123";}.fa-location-arrow:before{content: "\f124";}.fa-crop:before{content: "\f125";}.fa-code-fork:before{content: "\f126";}.fa-unlink:before, .fa-chain-broken:before{content: "\f127";}.fa-question:before{content: "\f128";}.fa-info:before{content: "\f129";}.fa-exclamation:before{content: "\f12a";}.fa-superscript:before{content: "\f12b";}.fa-subscript:before{content: "\f12c";}.fa-eraser:before{content: "\f12d";}.fa-puzzle-piece:before{content: "\f12e";}.fa-microphone:before{content: "\f130";}.fa-microphone-slash:before{content: "\f131";}.fa-shield:before{content: "\f132";}.fa-calendar-o:before{content: "\f133";}.fa-fire-extinguisher:before{content: "\f134";}.fa-rocket:before{content: "\f135";}.fa-maxcdn:before{content: "\f136";}.fa-chevron-circle-left:before{content: "\f137";}.fa-chevron-circle-right:before{content: "\f138";}.fa-chevron-circle-up:before{content: "\f139";}.fa-chevron-circle-down:before{content: "\f13a";}.fa-html5:before{content: "\f13b";}.fa-css3:before{content: "\f13c";}.fa-anchor:before{content: "\f13d";}.fa-unlock-alt:before{content: "\f13e";}.fa-bullseye:before{content: "\f140";}.fa-ellipsis-h:before{content: "\f141";}.fa-ellipsis-v:before{content: "\f142";}.fa-rss-square:before{content: "\f143";}.fa-play-circle:before{content: "\f144";}.fa-ticket:before{content: "\f145";}.fa-minus-square:before{content: "\f146";}.fa-minus-square-o:before{content: "\f147";}.fa-level-up:before{content: "\f148";}.fa-level-down:before{content: "\f149";}.fa-check-square:before{content: "\f14a";}.fa-pencil-square:before{content: "\f14b";}.fa-external-link-square:before{content: "\f14c";}.fa-share-square:before{content: "\f14d";}.fa-compass:before{content: "\f14e";}.fa-toggle-down:before, .fa-caret-square-o-down:before{content: "\f150";}.fa-toggle-up:before, .fa-caret-square-o-up:before{content: "\f151";}.fa-toggle-right:before, .fa-caret-square-o-right:before{content: "\f152";}.fa-euro:before, .fa-eur:before{content: "\f153";}.fa-gbp:before{content: "\f154";}.fa-dollar:before, .fa-usd:before{content: "\f155";}.fa-rupee:before, .fa-inr:before{content: "\f156";}.fa-cny:before, .fa-rmb:before, .fa-yen:before, .fa-jpy:before{content: "\f157";}.fa-ruble:before, .fa-rouble:before, .fa-rub:before{content: "\f158";}.fa-won:before, .fa-krw:before{content: "\f159";}.fa-bitcoin:before, .fa-btc:before{content: "\f15a";}.fa-file:before{content: "\f15b";}.fa-file-text:before{content: "\f15c";}.fa-sort-alpha-asc:before{content: "\f15d";}.fa-sort-alpha-desc:before{content: "\f15e";}.fa-sort-amount-asc:before{content: "\f160";}.fa-sort-amount-desc:before{content: "\f161";}.fa-sort-numeric-asc:before{content: "\f162";}.fa-sort-numeric-desc:before{content: "\f163";}.fa-thumbs-up:before{content: "\f164";}.fa-thumbs-down:before{content: "\f165";}.fa-youtube-square:before{content: "\f166";}.fa-youtube:before{content: "\f167";}.fa-xing:before{content: "\f168";}.fa-xing-square:before{content: "\f169";}.fa-youtube-play:before{content: "\f16a";}.fa-dropbox:before{content: "\f16b";}.fa-stack-overflow:before{content: "\f16c";}.fa-instagram:before{content: "\f16d";}.fa-flickr:before{content: "\f16e";}.fa-adn:before{content: "\f170";}.fa-bitbucket:before{content: "\f171";}.fa-bitbucket-square:before{content: "\f172";}.fa-tumblr:before{content: "\f173";}.fa-tumblr-square:before{content: "\f174";}.fa-long-arrow-down:before{content: "\f175";}.fa-long-arrow-up:before{content: "\f176";}.fa-long-arrow-left:before{content: "\f177";}.fa-long-arrow-right:before{content: "\f178";}.fa-apple:before{content: "\f179";}.fa-windows:before{content: "\f17a";}.fa-android:before{content: "\f17b";}.fa-linux:before{content: "\f17c";}.fa-dribbble:before{content: "\f17d";}.fa-skype:before{content: "\f17e";}.fa-foursquare:before{content: "\f180";}.fa-trello:before{content: "\f181";}.fa-female:before{content: "\f182";}.fa-male:before{content: "\f183";}.fa-gittip:before, .fa-gratipay:before{content: "\f184";}.fa-sun-o:before{content: "\f185";}.fa-moon-o:before{content: "\f186";}.fa-archive:before{content: "\f187";}.fa-bug:before{content: "\f188";}.fa-vk:before{content: "\f189";}.fa-weibo:before{content: "\f18a";}.fa-renren:before{content: "\f18b";}.fa-pagelines:before{content: "\f18c";}.fa-stack-exchange:before{content: "\f18d";}.fa-arrow-circle-o-right:before{content: "\f18e";}.fa-arrow-circle-o-left:before{content: "\f190";}.fa-toggle-left:before, .fa-caret-square-o-left:before{content: "\f191";}.fa-dot-circle-o:before{content: "\f192";}.fa-wheelchair:before{content: "\f193";}.fa-vimeo-square:before{content: "\f194";}.fa-turkish-lira:before, .fa-try:before{content: "\f195";}.fa-plus-square-o:before{content: "\f196";}.fa-space-shuttle:before{content: "\f197";}.fa-slack:before{content: "\f198";}.fa-envelope-square:before{content: "\f199";}.fa-wordpress:before{content: "\f19a";}.fa-openid:before{content: "\f19b";}.fa-institution:before, .fa-bank:before, .fa-university:before{content: "\f19c";}.fa-mortar-board:before, .fa-graduation-cap:before{content: "\f19d";}.fa-yahoo:before{content: "\f19e";}.fa-google:before{content: "\f1a0";}.fa-reddit:before{content: "\f1a1";}.fa-reddit-square:before{content: "\f1a2";}.fa-stumbleupon-circle:before{content: "\f1a3";}.fa-stumbleupon:before{content: "\f1a4";}.fa-delicious:before{content: "\f1a5";}.fa-digg:before{content: "\f1a6";}.fa-pied-piper-pp:before{content: "\f1a7";}.fa-pied-piper-alt:before{content: "\f1a8";}.fa-drupal:before{content: "\f1a9";}.fa-joomla:before{content: "\f1aa";}.fa-language:before{content: "\f1ab";}.fa-fax:before{content: "\f1ac";}.fa-building:before{content: "\f1ad";}.fa-child:before{content: "\f1ae";}.fa-paw:before{content: "\f1b0";}.fa-spoon:before{content: "\f1b1";}.fa-cube:before{content: "\f1b2";}.fa-cubes:before{content: "\f1b3";}.fa-behance:before{content: "\f1b4";}.fa-behance-square:before{content: "\f1b5";}.fa-steam:before{content: "\f1b6";}.fa-steam-square:before{content: "\f1b7";}.fa-recycle:before{content: "\f1b8";}.fa-automobile:before, .fa-car:before{content: "\f1b9";}.fa-cab:before, .fa-taxi:before{content: "\f1ba";}.fa-tree:before{content: "\f1bb";}.fa-spotify:before{content: "\f1bc";}.fa-deviantart:before{content: "\f1bd";}.fa-soundcloud:before{content: "\f1be";}.fa-database:before{content: "\f1c0";}.fa-file-pdf-o:before{content: "\f1c1";}.fa-file-word-o:before{content: "\f1c2";}.fa-file-excel-o:before{content: "\f1c3";}.fa-file-powerpoint-o:before{content: "\f1c4";}.fa-file-photo-o:before, .fa-file-picture-o:before, .fa-file-image-o:before{content: "\f1c5";}.fa-file-zip-o:before, .fa-file-archive-o:before{content: "\f1c6";}.fa-file-sound-o:before, .fa-file-audio-o:before{content: "\f1c7";}.fa-file-movie-o:before, .fa-file-video-o:before{content: "\f1c8";}.fa-file-code-o:before{content: "\f1c9";}.fa-vine:before{content: "\f1ca";}.fa-codepen:before{content: "\f1cb";}.fa-jsfiddle:before{content: "\f1cc";}.fa-life-bouy:before, .fa-life-buoy:before, .fa-life-saver:before, .fa-support:before, .fa-life-ring:before{content: "\f1cd";}.fa-circle-o-notch:before{content: "\f1ce";}.fa-ra:before, .fa-resistance:before, .fa-rebel:before{content: "\f1d0";}.fa-ge:before, .fa-empire:before{content: "\f1d1";}.fa-git-square:before{content: "\f1d2";}.fa-git:before{content: "\f1d3";}.fa-y-combinator-square:before, .fa-yc-square:before, .fa-hacker-news:before{content: "\f1d4";}.fa-tencent-weibo:before{content: "\f1d5";}.fa-qq:before{content: "\f1d6";}.fa-wechat:before, .fa-weixin:before{content: "\f1d7";}.fa-send:before, .fa-paper-plane:before{content: "\f1d8";}.fa-send-o:before, .fa-paper-plane-o:before{content: "\f1d9";}.fa-history:before{content: "\f1da";}.fa-circle-thin:before{content: "\f1db";}.fa-header:before{content: "\f1dc";}.fa-paragraph:before{content: "\f1dd";}.fa-sliders:before{content: "\f1de";}.fa-share-alt:before{content: "\f1e0";}.fa-share-alt-square:before{content: "\f1e1";}.fa-bomb:before{content: "\f1e2";}.fa-soccer-ball-o:before, .fa-futbol-o:before{content: "\f1e3";}.fa-tty:before{content: "\f1e4";}.fa-binoculars:before{content: "\f1e5";}.fa-plug:before{content: "\f1e6";}.fa-slideshare:before{content: "\f1e7";}.fa-twitch:before{content: "\f1e8";}.fa-yelp:before{content: "\f1e9";}.fa-newspaper-o:before{content: "\f1ea";}.fa-wifi:before{content: "\f1eb";}.fa-calculator:before{content: "\f1ec";}.fa-paypal:before{content: "\f1ed";}.fa-google-wallet:before{content: "\f1ee";}.fa-cc-visa:before{content: "\f1f0";}.fa-cc-mastercard:before{content: "\f1f1";}.fa-cc-discover:before{content: "\f1f2";}.fa-cc-amex:before{content: "\f1f3";}.fa-cc-paypal:before{content: "\f1f4";}.fa-cc-stripe:before{content: "\f1f5";}.fa-bell-slash:before{content: "\f1f6";}.fa-bell-slash-o:before{content: "\f1f7";}.fa-trash:before{content: "\f1f8";}.fa-copyright:before{content: "\f1f9";}.fa-at:before{content: "\f1fa";}.fa-eyedropper:before{content: "\f1fb";}.fa-paint-brush:before{content: "\f1fc";}.fa-birthday-cake:before{content: "\f1fd";}.fa-area-chart:before{content: "\f1fe";}.fa-pie-chart:before{content: "\f200";}.fa-line-chart:before{content: "\f201";}.fa-lastfm:before{content: "\f202";}.fa-lastfm-square:before{content: "\f203";}.fa-toggle-off:before{content: "\f204";}.fa-toggle-on:before{content: "\f205";}.fa-bicycle:before{content: "\f206";}.fa-bus:before{content: "\f207";}.fa-ioxhost:before{content: "\f208";}.fa-angellist:before{content: "\f209";}.fa-cc:before{content: "\f20a";}.fa-shekel:before, .fa-sheqel:before, .fa-ils:before{content: "\f20b";}.fa-meanpath:before{content: "\f20c";}.fa-buysellads:before{content: "\f20d";}.fa-connectdevelop:before{content: "\f20e";}.fa-dashcube:before{content: "\f210";}.fa-forumbee:before{content: "\f211";}.fa-leanpub:before{content: "\f212";}.fa-sellsy:before{content: "\f213";}.fa-shirtsinbulk:before{content: "\f214";}.fa-simplybuilt:before{content: "\f215";}.fa-skyatlas:before{content: "\f216";}.fa-cart-plus:before{content: "\f217";}.fa-cart-arrow-down:before{content: "\f218";}.fa-diamond:before{content: "\f219";}.fa-ship:before{content: "\f21a";}.fa-user-secret:before{content: "\f21b";}.fa-motorcycle:before{content: "\f21c";}.fa-street-view:before{content: "\f21d";}.fa-heartbeat:before{content: "\f21e";}.fa-venus:before{content: "\f221";}.fa-mars:before{content: "\f222";}.fa-mercury:before{content: "\f223";}.fa-intersex:before, .fa-transgender:before{content: "\f224";}.fa-transgender-alt:before{content: "\f225";}.fa-venus-double:before{content: "\f226";}.fa-mars-double:before{content: "\f227";}.fa-venus-mars:before{content: "\f228";}.fa-mars-stroke:before{content: "\f229";}.fa-mars-stroke-v:before{content: "\f22a";}.fa-mars-stroke-h:before{content: "\f22b";}.fa-neuter:before{content: "\f22c";}.fa-genderless:before{content: "\f22d";}.fa-facebook-official:before{content: "\f230";}.fa-pinterest-p:before{content: "\f231";}.fa-whatsapp:before{content: "\f232";}.fa-server:before{content: "\f233";}.fa-user-plus:before{content: "\f234";}.fa-user-times:before{content: "\f235";}.fa-hotel:before, .fa-bed:before{content: "\f236";}.fa-viacoin:before{content: "\f237";}.fa-train:before{content: "\f238";}.fa-subway:before{content: "\f239";}.fa-medium:before{content: "\f23a";}.fa-yc:before, .fa-y-combinator:before{content: "\f23b";}.fa-optin-monster:before{content: "\f23c";}.fa-opencart:before{content: "\f23d";}.fa-expeditedssl:before{content: "\f23e";}.fa-battery-4:before, .fa-battery:before, .fa-battery-full:before{content: "\f240";}.fa-battery-3:before, .fa-battery-three-quarters:before{content: "\f241";}.fa-battery-2:before, .fa-battery-half:before{content: "\f242";}.fa-battery-1:before, .fa-battery-quarter:before{content: "\f243";}.fa-battery-0:before, .fa-battery-empty:before{content: "\f244";}.fa-mouse-pointer:before{content: "\f245";}.fa-i-cursor:before{content: "\f246";}.fa-object-group:before{content: "\f247";}.fa-object-ungroup:before{content: "\f248";}.fa-sticky-note:before{content: "\f249";}.fa-sticky-note-o:before{content: "\f24a";}.fa-cc-jcb:before{content: "\f24b";}.fa-cc-diners-club:before{content: "\f24c";}.fa-clone:before{content: "\f24d";}.fa-balance-scale:before{content: "\f24e";}.fa-hourglass-o:before{content: "\f250";}.fa-hourglass-1:before, .fa-hourglass-start:before{content: "\f251";}.fa-hourglass-2:before, .fa-hourglass-half:before{content: "\f252";}.fa-hourglass-3:before, .fa-hourglass-end:before{content: "\f253";}.fa-hourglass:before{content: "\f254";}.fa-hand-grab-o:before, .fa-hand-rock-o:before{content: "\f255";}.fa-hand-stop-o:before, .fa-hand-paper-o:before{content: "\f256";}.fa-hand-scissors-o:before{content: "\f257";}.fa-hand-lizard-o:before{content: "\f258";}.fa-hand-spock-o:before{content: "\f259";}.fa-hand-pointer-o:before{content: "\f25a";}.fa-hand-peace-o:before{content: "\f25b";}.fa-trademark:before{content: "\f25c";}.fa-registered:before{content: "\f25d";}.fa-creative-commons:before{content: "\f25e";}.fa-gg:before{content: "\f260";}.fa-gg-circle:before{content: "\f261";}.fa-tripadvisor:before{content: "\f262";}.fa-odnoklassniki:before{content: "\f263";}.fa-odnoklassniki-square:before{content: "\f264";}.fa-get-pocket:before{content: "\f265";}.fa-wikipedia-w:before{content: "\f266";}.fa-safari:before{content: "\f267";}.fa-chrome:before{content: "\f268";}.fa-firefox:before{content: "\f269";}.fa-opera:before{content: "\f26a";}.fa-internet-explorer:before{content: "\f26b";}.fa-tv:before, .fa-television:before{content: "\f26c";}.fa-contao:before{content: "\f26d";}.fa-500px:before{content: "\f26e";}.fa-amazon:before{content: "\f270";}.fa-calendar-plus-o:before{content: "\f271";}.fa-calendar-minus-o:before{content: "\f272";}.fa-calendar-times-o:before{content: "\f273";}.fa-calendar-check-o:before{content: "\f274";}.fa-industry:before{content: "\f275";}.fa-map-pin:before{content: "\f276";}.fa-map-signs:before{content: "\f277";}.fa-map-o:before{content: "\f278";}.fa-map:before{content: "\f279";}.fa-commenting:before{content: "\f27a";}.fa-commenting-o:before{content: "\f27b";}.fa-houzz:before{content: "\f27c";}.fa-vimeo:before{content: "\f27d";}.fa-black-tie:before{content: "\f27e";}.fa-fonticons:before{content: "\f280";}.fa-reddit-alien:before{content: "\f281";}.fa-edge:before{content: "\f282";}.fa-credit-card-alt:before{content: "\f283";}.fa-codiepie:before{content: "\f284";}.fa-modx:before{content: "\f285";}.fa-fort-awesome:before{content: "\f286";}.fa-usb:before{content: "\f287";}.fa-product-hunt:before{content: "\f288";}.fa-mixcloud:before{content: "\f289";}.fa-scribd:before{content: "\f28a";}.fa-pause-circle:before{content: "\f28b";}.fa-pause-circle-o:before{content: "\f28c";}.fa-stop-circle:before{content: "\f28d";}.fa-stop-circle-o:before{content: "\f28e";}.fa-shopping-bag:before{content: "\f290";}.fa-shopping-basket:before{content: "\f291";}.fa-hashtag:before{content: "\f292";}.fa-bluetooth:before{content: "\f293";}.fa-bluetooth-b:before{content: "\f294";}.fa-percent:before{content: "\f295";}.fa-gitlab:before{content: "\f296";}.fa-wpbeginner:before{content: "\f297";}.fa-wpforms:before{content: "\f298";}.fa-envira:before{content: "\f299";}.fa-universal-access:before{content: "\f29a";}.fa-wheelchair-alt:before{content: "\f29b";}.fa-question-circle-o:before{content: "\f29c";}.fa-blind:before{content: "\f29d";}.fa-audio-description:before{content: "\f29e";}.fa-volume-control-phone:before{content: "\f2a0";}.fa-braille:before{content: "\f2a1";}.fa-assistive-listening-systems:before{content: "\f2a2";}.fa-asl-interpreting:before, .fa-american-sign-language-interpreting:before{content: "\f2a3";}.fa-deafness:before, .fa-hard-of-hearing:before, .fa-deaf:before{content: "\f2a4";}.fa-glide:before{content: "\f2a5";}.fa-glide-g:before{content: "\f2a6";}.fa-signing:before, .fa-sign-language:before{content: "\f2a7";}.fa-low-vision:before{content: "\f2a8";}.fa-viadeo:before{content: "\f2a9";}.fa-viadeo-square:before{content: "\f2aa";}.fa-snapchat:before{content: "\f2ab";}.fa-snapchat-ghost:before{content: "\f2ac";}.fa-snapchat-square:before{content: "\f2ad";}.fa-pied-piper:before{content: "\f2ae";}.fa-first-order:before{content: "\f2b0";}.fa-yoast:before{content: "\f2b1";}.fa-themeisle:before{content: "\f2b2";}.fa-google-plus-circle:before, .fa-google-plus-official:before{content: "\f2b3";}.fa-fa:before, .fa-font-awesome:before{content: "\f2b4";}.fa-handshake-o:before{content: "\f2b5";}.fa-envelope-open:before{content: "\f2b6";}.fa-envelope-open-o:before{content: "\f2b7";}.fa-linode:before{content: "\f2b8";}.fa-address-book:before{content: "\f2b9";}.fa-address-book-o:before{content: "\f2ba";}.fa-vcard:before, .fa-address-card:before{content: "\f2bb";}.fa-vcard-o:before, .fa-address-card-o:before{content: "\f2bc";}.fa-user-circle:before{content: "\f2bd";}.fa-user-circle-o:before{content: "\f2be";}.fa-user-o:before{content: "\f2c0";}.fa-id-badge:before{content: "\f2c1";}.fa-drivers-license:before, .fa-id-card:before{content: "\f2c2";}.fa-drivers-license-o:before, .fa-id-card-o:before{content: "\f2c3";}.fa-quora:before{content: "\f2c4";}.fa-free-code-camp:before{content: "\f2c5";}.fa-telegram:before{content: "\f2c6";}.fa-thermometer-4:before, .fa-thermometer:before, .fa-thermometer-full:before{content: "\f2c7";}.fa-thermometer-3:before, .fa-thermometer-three-quarters:before{content: "\f2c8";}.fa-thermometer-2:before, .fa-thermometer-half:before{content: "\f2c9";}.fa-thermometer-1:before, .fa-thermometer-quarter:before{content: "\f2ca";}.fa-thermometer-0:before, .fa-thermometer-empty:before{content: "\f2cb";}.fa-shower:before{content: "\f2cc";}.fa-bathtub:before, .fa-s15:before, .fa-bath:before{content: "\f2cd";}.fa-podcast:before{content: "\f2ce";}.fa-window-maximize:before{content: "\f2d0";}.fa-window-minimize:before{content: "\f2d1";}.fa-window-restore:before{content: "\f2d2";}.fa-times-rectangle:before, .fa-window-close:before{content: "\f2d3";}.fa-times-rectangle-o:before, .fa-window-close-o:before{content: "\f2d4";}.fa-bandcamp:before{content: "\f2d5";}.fa-grav:before{content: "\f2d6";}.fa-etsy:before{content: "\f2d7";}.fa-imdb:before{content: "\f2d8";}.fa-ravelry:before{content: "\f2d9";}.fa-eercast:before{content: "\f2da";}.fa-microchip:before{content: "\f2db";}.fa-snowflake-o:before{content: "\f2dc";}.fa-superpowers:before{content: "\f2dd";}.fa-wpexplorer:before{content: "\f2de";}.fa-meetup:before{content: "\f2e0";}.sr-only{position: absolute; width: 1px; height: 1px; padding: 0; margin: -1px; overflow: hidden; clip: rect(0, 0, 0, 0); border: 0;}.sr-only-focusable:active, .sr-only-focusable:focus{position: static; width: auto; height: auto; margin: 0; overflow: visible; clip: auto;}

/* /web/static/lib/select2/select2.css defined in bundle 'web.assets_common' */
 .select2-container{margin: 0; position: relative; display: inline-block; vertical-align: middle;}.select2-container, .select2-drop, .select2-search, .select2-search input{-webkit-box-sizing: border-box; -moz-box-sizing: border-box; box-sizing: border-box;}.select2-container .select2-choice{display: block; height: 26px; padding: 0 0 0 8px; overflow: hidden; position: relative; border: 1px solid #aaa; white-space: nowrap; line-height: 26px; color: #444; text-decoration: none; border-radius: 4px; background-clip: padding-box; -webkit-touch-callout: none; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none; user-select: none; background-color: #fff; background-image: -webkit-gradient(linear, left bottom, left top, color-stop(0, #eee), color-stop(0.5, #fff)); background-image: -webkit-linear-gradient(center bottom, #eee 0%, #fff 50%); background-image: -moz-linear-gradient(center bottom, #eee 0%, #fff 50%); filter: progid:DXImageTransform.Microsoft.gradient(startColorstr = '#ffffff', endColorstr = '#eeeeee', GradientType = 0); background-image: linear-gradient(to top, #eee 0%, #fff 50%);}html[dir="rtl"] .select2-container .select2-choice{padding: 0 8px 0 0;}.select2-container.select2-drop-above .select2-choice{border-bottom-color: #aaa; border-radius: 0 0 4px 4px; background-image: -webkit-gradient(linear, left bottom, left top, color-stop(0, #eee), color-stop(0.9, #fff)); background-image: -webkit-linear-gradient(center bottom, #eee 0%, #fff 90%); background-image: -moz-linear-gradient(center bottom, #eee 0%, #fff 90%); filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffff', endColorstr='#eeeeee', GradientType=0); background-image: linear-gradient(to bottom, #eee 0%, #fff 90%);}.select2-container.select2-allowclear .select2-choice .select2-chosen{margin-right: 42px;}.select2-container .select2-choice > .select2-chosen{margin-right: 26px; display: block; overflow: hidden; white-space: nowrap; text-overflow: ellipsis; float: none; width: auto;}html[dir="rtl"] .select2-container .select2-choice > .select2-chosen{margin-left: 26px; margin-right: 0;}.select2-container .select2-choice abbr{display: none; width: 12px; height: 12px; position: absolute; right: 24px; top: 8px; font-size: 1px; text-decoration: none; border: 0; background: url('/web/static/lib/select2/select2.png') right top no-repeat; cursor: pointer; outline: 0;}.select2-container.select2-allowclear .select2-choice abbr{display: inline-block;}.select2-container .select2-choice abbr:hover{background-position: right -11px; cursor: pointer;}.select2-drop-mask{border: 0; margin: 0; padding: 0; position: fixed; left: 0; top: 0; min-height: 100%; min-width: 100%; height: auto; width: auto; opacity: 0; z-index: 9998; background-color: #fff; filter: alpha(opacity=0);}.select2-drop{width: 100%; margin-top: -1px; position: absolute; z-index: 9999; top: 100%; background: #fff; color: #000; border: 1px solid #aaa; border-top: 0; border-radius: 0 0 4px 4px; -webkit-box-shadow: 0 4px 5px rgba(0, 0, 0, .15); box-shadow: 0 4px 5px rgba(0, 0, 0, .15);}.select2-drop.select2-drop-above{margin-top: 1px; border-top: 1px solid #aaa; border-bottom: 0; border-radius: 4px 4px 0 0; -webkit-box-shadow: 0 -4px 5px rgba(0, 0, 0, .15); box-shadow: 0 -4px 5px rgba(0, 0, 0, .15);}.select2-drop-active{border: 1px solid #5897fb; border-top: none;}.select2-drop.select2-drop-above.select2-drop-active{border-top: 1px solid #5897fb;}.select2-drop-auto-width{border-top: 1px solid #aaa; width: auto;}.select2-container .select2-choice .select2-arrow{display: inline-block; width: 18px; height: 100%; position: absolute; right: 0; top: 0; border-left: 1px solid #aaa; border-radius: 0 4px 4px 0; background-clip: padding-box; background: #ccc; background-image: -webkit-gradient(linear, left bottom, left top, color-stop(0, #ccc), color-stop(0.6, #eee)); background-image: -webkit-linear-gradient(center bottom, #ccc 0%, #eee 60%); background-image: -moz-linear-gradient(center bottom, #ccc 0%, #eee 60%); filter: progid:DXImageTransform.Microsoft.gradient(startColorstr = '#eeeeee', endColorstr = '#cccccc', GradientType = 0); background-image: linear-gradient(to top, #ccc 0%, #eee 60%);}html[dir="rtl"] .select2-container .select2-choice .select2-arrow{left: 0; right: auto; border-left: none; border-right: 1px solid #aaa; border-radius: 4px 0 0 4px;}.select2-container .select2-choice .select2-arrow b{display: block; width: 100%; height: 100%; background: url('/web/static/lib/select2/select2.png') no-repeat 0 1px;}html[dir="rtl"] .select2-container .select2-choice .select2-arrow b{background-position: 2px 1px;}.select2-search{display: inline-block; width: 100%; min-height: 26px; margin: 0; padding: 4px 4px 0 4px; position: relative; z-index: 10000; white-space: nowrap;}.select2-search input{width: 100%; height: auto !important; min-height: 26px; padding: 4px 20px 4px 5px; margin: 0; outline: 0; font-family: sans-serif; font-size: 1em; border: 1px solid #aaa; border-radius: 0; -webkit-box-shadow: none; box-shadow: none; background: #fff url('/web/static/lib/select2/select2.png') no-repeat 100% -22px; background: url('/web/static/lib/select2/select2.png') no-repeat 100% -22px, -webkit-gradient(linear, left bottom, left top, color-stop(0.85, #fff), color-stop(0.99, #eee)); background: url('/web/static/lib/select2/select2.png') no-repeat 100% -22px, -webkit-linear-gradient(center bottom, #fff 85%, #eee 99%); background: url('/web/static/lib/select2/select2.png') no-repeat 100% -22px, -moz-linear-gradient(center bottom, #fff 85%, #eee 99%); background: url('/web/static/lib/select2/select2.png') no-repeat 100% -22px, linear-gradient(to bottom, #fff 85%, #eee 99%) 0 0;}html[dir="rtl"] .select2-search input{padding: 4px 5px 4px 20px; background: #fff url('/web/static/lib/select2/select2.png') no-repeat -37px -22px; background: url('/web/static/lib/select2/select2.png') no-repeat -37px -22px, -webkit-gradient(linear, left bottom, left top, color-stop(0.85, #fff), color-stop(0.99, #eee)); background: url('/web/static/lib/select2/select2.png') no-repeat -37px -22px, -webkit-linear-gradient(center bottom, #fff 85%, #eee 99%); background: url('/web/static/lib/select2/select2.png') no-repeat -37px -22px, -moz-linear-gradient(center bottom, #fff 85%, #eee 99%); background: url('/web/static/lib/select2/select2.png') no-repeat -37px -22px, linear-gradient(to bottom, #fff 85%, #eee 99%) 0 0;}.select2-search input.select2-active{background: #fff url('/web/static/lib/select2/select2-spinner.gif') no-repeat 100%; background: url('/web/static/lib/select2/select2-spinner.gif') no-repeat 100%, -webkit-gradient(linear, left bottom, left top, color-stop(0.85, #fff), color-stop(0.99, #eee)); background: url('/web/static/lib/select2/select2-spinner.gif') no-repeat 100%, -webkit-linear-gradient(center bottom, #fff 85%, #eee 99%); background: url('/web/static/lib/select2/select2-spinner.gif') no-repeat 100%, -moz-linear-gradient(center bottom, #fff 85%, #eee 99%); background: url('/web/static/lib/select2/select2-spinner.gif') no-repeat 100%, linear-gradient(to bottom, #fff 85%, #eee 99%) 0 0;}.select2-container-active .select2-choice, .select2-container-active .select2-choices{border: 1px solid #5897fb; outline: none; -webkit-box-shadow: 0 0 5px rgba(0, 0, 0, .3); box-shadow: 0 0 5px rgba(0, 0, 0, .3);}.select2-dropdown-open .select2-choice{border-bottom-color: transparent; -webkit-box-shadow: 0 1px 0 #fff inset; box-shadow: 0 1px 0 #fff inset; border-bottom-left-radius: 0; border-bottom-right-radius: 0; background-color: #eee; background-image: -webkit-gradient(linear, left bottom, left top, color-stop(0, #fff), color-stop(0.5, #eee)); background-image: -webkit-linear-gradient(center bottom, #fff 0%, #eee 50%); background-image: -moz-linear-gradient(center bottom, #fff 0%, #eee 50%); filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#eeeeee', endColorstr='#ffffff', GradientType=0); background-image: linear-gradient(to top, #fff 0%, #eee 50%);}.select2-dropdown-open.select2-drop-above .select2-choice, .select2-dropdown-open.select2-drop-above .select2-choices{border: 1px solid #5897fb; border-top-color: transparent; background-image: -webkit-gradient(linear, left top, left bottom, color-stop(0, #fff), color-stop(0.5, #eee)); background-image: -webkit-linear-gradient(center top, #fff 0%, #eee 50%); background-image: -moz-linear-gradient(center top, #fff 0%, #eee 50%); filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#eeeeee', endColorstr='#ffffff', GradientType=0); background-image: linear-gradient(to bottom, #fff 0%, #eee 50%);}.select2-dropdown-open .select2-choice .select2-arrow{background: transparent; border-left: none; filter: none;}html[dir="rtl"] .select2-dropdown-open .select2-choice .select2-arrow{border-right: none;}.select2-dropdown-open .select2-choice .select2-arrow b{background-position: -18px 1px;}html[dir="rtl"] .select2-dropdown-open .select2-choice .select2-arrow b{background-position: -16px 1px;}.select2-hidden-accessible{border: 0; clip: rect(0 0 0 0); height: 1px; margin: -1px; overflow: hidden; padding: 0; position: absolute; width: 1px;}.select2-results{max-height: 200px; padding: 0 0 0 4px; margin: 4px 4px 4px 0; position: relative; overflow-x: hidden; overflow-y: auto; -webkit-tap-highlight-color: rgba(0, 0, 0, 0);}html[dir="rtl"] .select2-results{padding: 0 4px 0 0; margin: 4px 0 4px 4px;}.select2-results ul.select2-result-sub{margin: 0; padding-left: 0;}.select2-results li{list-style: none; display: list-item; background-image: none;}.select2-results li.select2-result-with-children > .select2-result-label{font-weight: bold;}.select2-results .select2-result-label{padding: 3px 7px 4px; margin: 0; cursor: pointer; min-height: 1em; -webkit-touch-callout: none; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none; user-select: none;}.select2-results-dept-1 .select2-result-label{padding-left: 20px}.select2-results-dept-2 .select2-result-label{padding-left: 40px}.select2-results-dept-3 .select2-result-label{padding-left: 60px}.select2-results-dept-4 .select2-result-label{padding-left: 80px}.select2-results-dept-5 .select2-result-label{padding-left: 100px}.select2-results-dept-6 .select2-result-label{padding-left: 110px}.select2-results-dept-7 .select2-result-label{padding-left: 120px}.select2-results .select2-highlighted{background: #3875d7; color: #fff;}.select2-results li em{background: #feffde; font-style: normal;}.select2-results .select2-highlighted em{background: transparent;}.select2-results .select2-highlighted ul{background: #fff; color: #000;}.select2-results .select2-no-results, .select2-results .select2-searching, .select2-results .select2-ajax-error, .select2-results .select2-selection-limit{background: #f4f4f4; display: list-item; padding-left: 5px;}.select2-results .select2-disabled.select2-highlighted{color: #666; background: #f4f4f4; display: list-item; cursor: default;}.select2-results .select2-disabled{background: #f4f4f4; display: list-item; cursor: default;}.select2-results .select2-selected{display: none;}.select2-more-results.select2-active{background: #f4f4f4 url('/web/static/lib/select2/select2-spinner.gif') no-repeat 100%;}.select2-results .select2-ajax-error{background: rgba(255, 50, 50, .2);}.select2-more-results{background: #f4f4f4; display: list-item;}.select2-container.select2-container-disabled .select2-choice{background-color: #f4f4f4; background-image: none; border: 1px solid #ddd; cursor: default;}.select2-container.select2-container-disabled .select2-choice .select2-arrow{background-color: #f4f4f4; background-image: none; border-left: 0;}.select2-container.select2-container-disabled .select2-choice abbr{display: none;}.select2-container-multi .select2-choices{height: auto !important; height: 1%; margin: 0; padding: 0 5px 0 0; position: relative; border: 1px solid #aaa; cursor: text; overflow: hidden; background-color: #fff; background-image: -webkit-gradient(linear, 0% 0%, 0% 100%, color-stop(1%, #eee), color-stop(15%, #fff)); background-image: -webkit-linear-gradient(top, #eee 1%, #fff 15%); background-image: -moz-linear-gradient(top, #eee 1%, #fff 15%); background-image: linear-gradient(to bottom, #eee 1%, #fff 15%);}html[dir="rtl"] .select2-container-multi .select2-choices{padding: 0 0 0 5px;}.select2-locked{padding: 3px 5px 3px 5px !important;}.select2-container-multi .select2-choices{min-height: 26px;}.select2-container-multi.select2-container-active .select2-choices{border: 1px solid #5897fb; outline: none; -webkit-box-shadow: 0 0 5px rgba(0, 0, 0, .3); box-shadow: 0 0 5px rgba(0, 0, 0, .3);}.select2-container-multi .select2-choices li{float: left; list-style: none;}html[dir="rtl"] .select2-container-multi .select2-choices li{float: right;}.select2-container-multi .select2-choices .select2-search-field{margin: 0; padding: 0; white-space: nowrap;}.select2-container-multi .select2-choices .select2-search-field input{padding: 5px; margin: 1px 0; font-family: sans-serif; font-size: 100%; color: #666; outline: 0; border: 0; -webkit-box-shadow: none; box-shadow: none; background: transparent !important;}.select2-container-multi .select2-choices .select2-search-field input.select2-active{background: #fff url('/web/static/lib/select2/select2-spinner.gif') no-repeat 100% !important;}.select2-default{color: #999 !important;}.select2-container-multi .select2-choices .select2-search-choice{padding: 3px 5px 3px 18px; margin: 3px 0 3px 5px; position: relative; line-height: 13px; color: #333; cursor: default; border: 1px solid #aaaaaa; border-radius: 3px; -webkit-box-shadow: 0 0 2px #fff inset, 0 1px 0 rgba(0, 0, 0, 0.05); box-shadow: 0 0 2px #fff inset, 0 1px 0 rgba(0, 0, 0, 0.05); background-clip: padding-box; -webkit-touch-callout: none; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none; user-select: none; background-color: #e4e4e4; filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#eeeeee', endColorstr='#f4f4f4', GradientType=0); background-image: -webkit-gradient(linear, 0% 0%, 0% 100%, color-stop(20%, #f4f4f4), color-stop(50%, #f0f0f0), color-stop(52%, #e8e8e8), color-stop(100%, #eee)); background-image: -webkit-linear-gradient(top, #f4f4f4 20%, #f0f0f0 50%, #e8e8e8 52%, #eee 100%); background-image: -moz-linear-gradient(top, #f4f4f4 20%, #f0f0f0 50%, #e8e8e8 52%, #eee 100%); background-image: linear-gradient(to bottom, #f4f4f4 20%, #f0f0f0 50%, #e8e8e8 52%, #eee 100%);}html[dir="rtl"] .select2-container-multi .select2-choices .select2-search-choice{margin: 3px 5px 3px 0; padding: 3px 18px 3px 5px;}.select2-container-multi .select2-choices .select2-search-choice .select2-chosen{cursor: default;}.select2-container-multi .select2-choices .select2-search-choice-focus{background: #d4d4d4;}.select2-search-choice-close{display: block; width: 12px; height: 13px; position: absolute; right: 3px; top: 4px; font-size: 1px; outline: none; background: url('/web/static/lib/select2/select2.png') right top no-repeat;}html[dir="rtl"] .select2-search-choice-close{right: auto; left: 3px;}.select2-container-multi .select2-search-choice-close{left: 3px;}html[dir="rtl"] .select2-container-multi .select2-search-choice-close{left: auto; right: 2px;}.select2-container-multi .select2-choices .select2-search-choice .select2-search-choice-close:hover{background-position: right -11px;}.select2-container-multi .select2-choices .select2-search-choice-focus .select2-search-choice-close{background-position: right -11px;}.select2-container-multi.select2-container-disabled .select2-choices{background-color: #f4f4f4; background-image: none; border: 1px solid #ddd; cursor: default;}.select2-container-multi.select2-container-disabled .select2-choices .select2-search-choice{padding: 3px 5px 3px 5px; border: 1px solid #ddd; background-image: none; background-color: #f4f4f4;}.select2-container-multi.select2-container-disabled .select2-choices .select2-search-choice .select2-search-choice-close{display: none; background: none;}.select2-result-selectable .select2-match, .select2-result-unselectable .select2-match{text-decoration: underline;}.select2-offscreen, .select2-offscreen:focus{clip: rect(0 0 0 0) !important; width: 1px !important; height: 1px !important; border: 0 !important; margin: 0 !important; padding: 0 !important; overflow: hidden !important; position: absolute !important; outline: 0 !important; left: 0px !important; top: 0px !important;}.select2-display-none{display: none;}.select2-measure-scrollbar{position: absolute; top: -10000px; left: -10000px; width: 100px; height: 100px; overflow: scroll;}@media only screen and (-webkit-min-device-pixel-ratio: 1.5), only screen and (min-resolution: 2dppx){.select2-search input, .select2-search-choice-close, .select2-container .select2-choice abbr, .select2-container .select2-choice .select2-arrow b{background-image: url('/web/static/lib/select2/select2x2.png') !important; background-repeat: no-repeat !important; background-size: 60px 40px !important;}.select2-search input{background-position: 100% -21px !important;}}

/* /web/static/lib/select2-bootstrap-css/select2-bootstrap.css defined in bundle 'web.assets_common' */
 .form-control .select2-choice{border: 0; border-radius: 2px;}.form-control .select2-choice .select2-arrow{border-radius: 0 2px 2px 0;}.form-control.select2-container{height: auto !important; padding: 0;}.form-control.select2-container.select2-dropdown-open{border-color: #5897FB; border-radius: 3px 3px 0 0;}.form-control .select2-container.select2-dropdown-open .select2-choices{border-radius: 3px 3px 0 0;}.form-control.select2-container .select2-choices{border: 0 !important; border-radius: 3px;}.control-group.warning .select2-container .select2-choice, .control-group.warning .select2-container .select2-choices, .control-group.warning .select2-container-active .select2-choice, .control-group.warning .select2-container-active .select2-choices, .control-group.warning .select2-dropdown-open.select2-drop-above .select2-choice, .control-group.warning .select2-dropdown-open.select2-drop-above .select2-choices, .control-group.warning .select2-container-multi.select2-container-active .select2-choices{border: 1px solid #C09853 !important;}.control-group.warning .select2-container .select2-choice div{border-left: 1px solid #C09853 !important; background: #FCF8E3 !important;}.control-group.error .select2-container .select2-choice, .control-group.error .select2-container .select2-choices, .control-group.error .select2-container-active .select2-choice, .control-group.error .select2-container-active .select2-choices, .control-group.error .select2-dropdown-open.select2-drop-above .select2-choice, .control-group.error .select2-dropdown-open.select2-drop-above .select2-choices, .control-group.error .select2-container-multi.select2-container-active .select2-choices{border: 1px solid #B94A48 !important;}.control-group.error .select2-container .select2-choice div{border-left: 1px solid #B94A48 !important; background: #F2DEDE !important;}.control-group.info .select2-container .select2-choice, .control-group.info .select2-container .select2-choices, .control-group.info .select2-container-active .select2-choice, .control-group.info .select2-container-active .select2-choices, .control-group.info .select2-dropdown-open.select2-drop-above .select2-choice, .control-group.info .select2-dropdown-open.select2-drop-above .select2-choices, .control-group.info .select2-container-multi.select2-container-active .select2-choices{border: 1px solid #3A87AD !important;}.control-group.info .select2-container .select2-choice div{border-left: 1px solid #3A87AD !important; background: #D9EDF7 !important;}.control-group.success .select2-container .select2-choice, .control-group.success .select2-container .select2-choices, .control-group.success .select2-container-active .select2-choice, .control-group.success .select2-container-active .select2-choices, .control-group.success .select2-dropdown-open.select2-drop-above .select2-choice, .control-group.success .select2-dropdown-open.select2-drop-above .select2-choices, .control-group.success .select2-container-multi.select2-container-active .select2-choices{border: 1px solid #468847 !important;}.control-group.success .select2-container .select2-choice div{border-left: 1px solid #468847 !important; background: #DFF0D8 !important;}

/* /web/static/lib/tempusdominus/tempusdominus.scss defined in bundle 'web.assets_common' */
 .bootstrap-datetimepicker-widget{list-style: none;}.bootstrap-datetimepicker-widget.dropdown-menu{display: block; margin: 2px 0; padding: 4px; width: 14rem;}@media (min-width: 576px){.bootstrap-datetimepicker-widget.dropdown-menu.timepicker-sbs{width: 38em;}}@media (min-width: 768px){.bootstrap-datetimepicker-widget.dropdown-menu.timepicker-sbs{width: 38em;}}@media (min-width: 992px){.bootstrap-datetimepicker-widget.dropdown-menu.timepicker-sbs{width: 38em;}}.bootstrap-datetimepicker-widget.dropdown-menu:before, .bootstrap-datetimepicker-widget.dropdown-menu:after{content: ''; display: inline-block; position: absolute;}.bootstrap-datetimepicker-widget.dropdown-menu.bottom:before{border-left: 7px solid transparent; border-right: 7px solid transparent; border-bottom: 7px solid #ccc; border-bottom-color: rgba(0, 0, 0, 0.2); top: -7px; left: 7px;}.bootstrap-datetimepicker-widget.dropdown-menu.bottom:after{border-left: 6px solid transparent; border-right: 6px solid transparent; border-bottom: 6px solid white; top: -6px; left: 8px;}.bootstrap-datetimepicker-widget.dropdown-menu.top:before{border-left: 7px solid transparent; border-right: 7px solid transparent; border-top: 7px solid #ccc; border-top-color: rgba(0, 0, 0, 0.2); bottom: -7px; left: 6px;}.bootstrap-datetimepicker-widget.dropdown-menu.top:after{border-left: 6px solid transparent; border-right: 6px solid transparent; border-top: 6px solid white; bottom: -6px; left: 7px;}.bootstrap-datetimepicker-widget.dropdown-menu.float-right:before{left: auto; right: 6px;}.bootstrap-datetimepicker-widget.dropdown-menu.float-right:after{left: auto; right: 7px;}.bootstrap-datetimepicker-widget.dropdown-menu.wider{width: 16rem;}.bootstrap-datetimepicker-widget .list-unstyled{margin: 0;}.bootstrap-datetimepicker-widget a[data-action]{padding: 6px 0;}.bootstrap-datetimepicker-widget a[data-action]:active{box-shadow: none;}.bootstrap-datetimepicker-widget .timepicker-hour, .bootstrap-datetimepicker-widget .timepicker-minute, .bootstrap-datetimepicker-widget .timepicker-second{width: 54px; font-weight: bold; font-size: 1.2em; margin: 0;}.bootstrap-datetimepicker-widget button[data-action]{padding: 6px;}.bootstrap-datetimepicker-widget .btn[data-action="incrementHours"]::after{content: "Increment Hours";}.bootstrap-datetimepicker-widget .btn[data-action="incrementMinutes"]::after{content: "Increment Minutes";}.bootstrap-datetimepicker-widget .btn[data-action="decrementHours"]::after{content: "Decrement Hours";}.bootstrap-datetimepicker-widget .btn[data-action="decrementMinutes"]::after{content: "Decrement Minutes";}.bootstrap-datetimepicker-widget .btn[data-action="showHours"]::after{content: "Show Hours";}.bootstrap-datetimepicker-widget .btn[data-action="showMinutes"]::after{content: "Show Minutes";}.bootstrap-datetimepicker-widget .btn[data-action="togglePeriod"]::after{content: "Toggle AM/PM";}.bootstrap-datetimepicker-widget .btn[data-action="clear"]::after{content: "Clear the picker";}.bootstrap-datetimepicker-widget .btn[data-action="today"]::after{content: "Set the date to today";}.bootstrap-datetimepicker-widget .picker-switch{text-align: center;}.bootstrap-datetimepicker-widget .picker-switch::after{content: "Toggle Date and Time Screens";}.bootstrap-datetimepicker-widget .picker-switch td{padding: 0; margin: 0; height: auto; width: auto; line-height: inherit;}.bootstrap-datetimepicker-widget .picker-switch td span{line-height: 2.5; height: 2.5em; width: 100%;}.bootstrap-datetimepicker-widget table{width: 100%; margin: 0;}.bootstrap-datetimepicker-widget table td, .bootstrap-datetimepicker-widget table th{text-align: center; border-radius: 0.25rem;}.bootstrap-datetimepicker-widget table th{height: 20px; line-height: 20px; width: 20px;}.bootstrap-datetimepicker-widget table th.picker-switch{width: 145px;}.bootstrap-datetimepicker-widget table th.disabled, .bootstrap-datetimepicker-widget table th.disabled:hover{background: none; color: #6c757d; cursor: not-allowed;}.bootstrap-datetimepicker-widget table th.prev::after{content: "Previous Month";}.bootstrap-datetimepicker-widget table th.next::after{content: "Next Month";}.bootstrap-datetimepicker-widget table thead tr:first-child th{cursor: pointer;}.bootstrap-datetimepicker-widget table thead tr:first-child th:hover{background: #e9ecef;}.bootstrap-datetimepicker-widget table td{height: 54px; line-height: 54px; width: 54px;}.bootstrap-datetimepicker-widget table td.cw{font-size: .8em; height: 20px; line-height: 20px; color: #6c757d;}.bootstrap-datetimepicker-widget table td.day{height: 20px; line-height: 20px; width: 20px;}.bootstrap-datetimepicker-widget table td.day:hover, .bootstrap-datetimepicker-widget table td.hour:hover, .bootstrap-datetimepicker-widget table td.minute:hover, .bootstrap-datetimepicker-widget table td.second:hover{background: #e9ecef; cursor: pointer;}.bootstrap-datetimepicker-widget table td.old, .bootstrap-datetimepicker-widget table td.new{color: #6c757d;}.bootstrap-datetimepicker-widget table td.today{position: relative;}.bootstrap-datetimepicker-widget table td.today:before{content: ''; display: inline-block; border: solid transparent; border-width: 0 0 7px 7px; border-bottom-color: #007bff; border-top-color: rgba(0, 0, 0, 0.2); position: absolute; bottom: 4px; right: 4px;}.bootstrap-datetimepicker-widget table td.active, .bootstrap-datetimepicker-widget table td.active:hover{background-color: #007bff; color: #fff; text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);}.bootstrap-datetimepicker-widget table td.active.today:before{border-bottom-color: #fff;}.bootstrap-datetimepicker-widget table td.disabled, .bootstrap-datetimepicker-widget table td.disabled:hover{background: none; color: #6c757d; cursor: not-allowed;}.bootstrap-datetimepicker-widget table td span{display: inline-block; width: 54px; height: 54px; line-height: 54px; margin: 2px 1.5px; cursor: pointer; border-radius: 0.25rem;}.bootstrap-datetimepicker-widget table td span:hover{background: #e9ecef;}.bootstrap-datetimepicker-widget table td span.active{background-color: #007bff; color: #fff; text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);}.bootstrap-datetimepicker-widget table td span.old{color: #6c757d;}.bootstrap-datetimepicker-widget table td span.disabled, .bootstrap-datetimepicker-widget table td span.disabled:hover{background: none; color: #6c757d; cursor: not-allowed;}.bootstrap-datetimepicker-widget.usetwentyfour td.hour{height: 27px; line-height: 27px;}.input-group [data-toggle="datetimepicker"]{cursor: pointer;}

/* /web/static/src/scss/fonts.scss defined in bundle 'web.assets_common' */
 @font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSans-Hai.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSans-Hai.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSans-Hai.ttf") format("truetype"); font-weight: 100; font-style: normal; unicode-range: U+0400-04FF, U+0500-052F;}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-Hai.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-Hai.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-Hai.ttf") format("truetype"); font-weight: 100; font-style: normal; unicode-range: U+0590-05FF, U+FB1D-FB4F;}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-Hai.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-Hai.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-Hai.ttf") format("truetype"); font-weight: 100; font-style: normal; unicode-range: U+0600-06FF, U+0750-077F, U+08A0-08FF;}@font-face{font-family: 'Lato'; src: url("/web/static/src/scss/../fonts/lato/Lato-Hai-webfont.eot"); src: url("/web/static/src/scss/../fonts/lato/Lato-Hai-webfont.eot?#iefix") format("embedded-opentype"), url("/web/static/src/scss/../fonts/lato/Lato-Hai-webfont.woff") format("woff"), url("/web/static/src/scss/../fonts/lato/Lato-Hai-webfont.ttf") format("truetype"), url("/web/static/src/scss/../fonts/lato/Lato-Hai-webfont.svg#Lato") format("svg"); font-weight: 100; font-style: normal;}@font-face{font-family: "Lato-Hai"; src: url("/web/static/src/scss/../fonts/lato/Lato-Hai-webfont.eot"); src: url("/web/static/src/scss/../fonts/lato/Lato-Hai-webfont.eot?#iefix") format("embedded-opentype"), url("/web/static/src/scss/../fonts/lato/Lato-Hai-webfont.woff") format("woff"), url("/web/static/src/scss/../fonts/lato/Lato-Hai-webfont.ttf") format("truetype"), url("/web/static/src/scss/../fonts/lato/Lato-Hai-webfont.svg#Roboto") format("svg");}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSans-HaiIta.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSans-HaiIta.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSans-HaiIta.ttf") format("truetype"); font-weight: 100; font-style: italic; unicode-range: U+0400-04FF, U+0500-052F;}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-HaiIta.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-HaiIta.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-HaiIta.ttf") format("truetype"); font-weight: 100; font-style: italic; unicode-range: U+0590-05FF, U+FB1D-FB4F;}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-HaiIta.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-HaiIta.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-HaiIta.ttf") format("truetype"); font-weight: 100; font-style: italic; unicode-range: U+0600-06FF, U+0750-077F, U+08A0-08FF;}@font-face{font-family: 'Lato'; src: url("/web/static/src/scss/../fonts/lato/Lato-HaiIta-webfont.eot"); src: url("/web/static/src/scss/../fonts/lato/Lato-HaiIta-webfont.eot?#iefix") format("embedded-opentype"), url("/web/static/src/scss/../fonts/lato/Lato-HaiIta-webfont.woff") format("woff"), url("/web/static/src/scss/../fonts/lato/Lato-HaiIta-webfont.ttf") format("truetype"), url("/web/static/src/scss/../fonts/lato/Lato-HaiIta-webfont.svg#Lato") format("svg"); font-weight: 100; font-style: italic;}@font-face{font-family: "Lato-HaiIta"; src: url("/web/static/src/scss/../fonts/lato/Lato-HaiIta-webfont.eot"); src: url("/web/static/src/scss/../fonts/lato/Lato-HaiIta-webfont.eot?#iefix") format("embedded-opentype"), url("/web/static/src/scss/../fonts/lato/Lato-HaiIta-webfont.woff") format("woff"), url("/web/static/src/scss/../fonts/lato/Lato-HaiIta-webfont.ttf") format("truetype"), url("/web/static/src/scss/../fonts/lato/Lato-HaiIta-webfont.svg#Roboto") format("svg");}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSans-Lig.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSans-Lig.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSans-Lig.ttf") format("truetype"); font-weight: 300; font-style: normal; unicode-range: U+0400-04FF, U+0500-052F;}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-Lig.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-Lig.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-Lig.ttf") format("truetype"); font-weight: 300; font-style: normal; unicode-range: U+0590-05FF, U+FB1D-FB4F;}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-Lig.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-Lig.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-Lig.ttf") format("truetype"); font-weight: 300; font-style: normal; unicode-range: U+0600-06FF, U+0750-077F, U+08A0-08FF;}@font-face{font-family: 'Lato'; src: url("/web/static/src/scss/../fonts/lato/Lato-Lig-webfont.eot"); src: url("/web/static/src/scss/../fonts/lato/Lato-Lig-webfont.eot?#iefix") format("embedded-opentype"), url("/web/static/src/scss/../fonts/lato/Lato-Lig-webfont.woff") format("woff"), url("/web/static/src/scss/../fonts/lato/Lato-Lig-webfont.ttf") format("truetype"), url("/web/static/src/scss/../fonts/lato/Lato-Lig-webfont.svg#Lato") format("svg"); font-weight: 300; font-style: normal;}@font-face{font-family: "Lato-Lig"; src: url("/web/static/src/scss/../fonts/lato/Lato-Lig-webfont.eot"); src: url("/web/static/src/scss/../fonts/lato/Lato-Lig-webfont.eot?#iefix") format("embedded-opentype"), url("/web/static/src/scss/../fonts/lato/Lato-Lig-webfont.woff") format("woff"), url("/web/static/src/scss/../fonts/lato/Lato-Lig-webfont.ttf") format("truetype"), url("/web/static/src/scss/../fonts/lato/Lato-Lig-webfont.svg#Roboto") format("svg");}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSans-LigIta.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSans-LigIta.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSans-LigIta.ttf") format("truetype"); font-weight: 300; font-style: italic; unicode-range: U+0400-04FF, U+0500-052F;}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-LigIta.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-LigIta.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-LigIta.ttf") format("truetype"); font-weight: 300; font-style: italic; unicode-range: U+0590-05FF, U+FB1D-FB4F;}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-LigIta.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-LigIta.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-LigIta.ttf") format("truetype"); font-weight: 300; font-style: italic; unicode-range: U+0600-06FF, U+0750-077F, U+08A0-08FF;}@font-face{font-family: 'Lato'; src: url("/web/static/src/scss/../fonts/lato/Lato-LigIta-webfont.eot"); src: url("/web/static/src/scss/../fonts/lato/Lato-LigIta-webfont.eot?#iefix") format("embedded-opentype"), url("/web/static/src/scss/../fonts/lato/Lato-LigIta-webfont.woff") format("woff"), url("/web/static/src/scss/../fonts/lato/Lato-LigIta-webfont.ttf") format("truetype"), url("/web/static/src/scss/../fonts/lato/Lato-LigIta-webfont.svg#Lato") format("svg"); font-weight: 300; font-style: italic;}@font-face{font-family: "Lato-LigIta"; src: url("/web/static/src/scss/../fonts/lato/Lato-LigIta-webfont.eot"); src: url("/web/static/src/scss/../fonts/lato/Lato-LigIta-webfont.eot?#iefix") format("embedded-opentype"), url("/web/static/src/scss/../fonts/lato/Lato-LigIta-webfont.woff") format("woff"), url("/web/static/src/scss/../fonts/lato/Lato-LigIta-webfont.ttf") format("truetype"), url("/web/static/src/scss/../fonts/lato/Lato-LigIta-webfont.svg#Roboto") format("svg");}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSans-Reg.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSans-Reg.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSans-Reg.ttf") format("truetype"); font-weight: 400; font-style: normal; unicode-range: U+0400-04FF, U+0500-052F;}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-Reg.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-Reg.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-Reg.ttf") format("truetype"); font-weight: 400; font-style: normal; unicode-range: U+0590-05FF, U+FB1D-FB4F;}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-Reg.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-Reg.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-Reg.ttf") format("truetype"); font-weight: 400; font-style: normal; unicode-range: U+0600-06FF, U+0750-077F, U+08A0-08FF;}@font-face{font-family: 'Lato'; src: url("/web/static/src/scss/../fonts/lato/Lato-Reg-webfont.eot"); src: url("/web/static/src/scss/../fonts/lato/Lato-Reg-webfont.eot?#iefix") format("embedded-opentype"), url("/web/static/src/scss/../fonts/lato/Lato-Reg-webfont.woff") format("woff"), url("/web/static/src/scss/../fonts/lato/Lato-Reg-webfont.ttf") format("truetype"), url("/web/static/src/scss/../fonts/lato/Lato-Reg-webfont.svg#Lato") format("svg"); font-weight: 400; font-style: normal;}@font-face{font-family: "Lato-Reg"; src: url("/web/static/src/scss/../fonts/lato/Lato-Reg-webfont.eot"); src: url("/web/static/src/scss/../fonts/lato/Lato-Reg-webfont.eot?#iefix") format("embedded-opentype"), url("/web/static/src/scss/../fonts/lato/Lato-Reg-webfont.woff") format("woff"), url("/web/static/src/scss/../fonts/lato/Lato-Reg-webfont.ttf") format("truetype"), url("/web/static/src/scss/../fonts/lato/Lato-Reg-webfont.svg#Roboto") format("svg");}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSans-RegIta.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSans-RegIta.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSans-RegIta.ttf") format("truetype"); font-weight: 400; font-style: italic; unicode-range: U+0400-04FF, U+0500-052F;}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-RegIta.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-RegIta.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-RegIta.ttf") format("truetype"); font-weight: 400; font-style: italic; unicode-range: U+0590-05FF, U+FB1D-FB4F;}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-RegIta.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-RegIta.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-RegIta.ttf") format("truetype"); font-weight: 400; font-style: italic; unicode-range: U+0600-06FF, U+0750-077F, U+08A0-08FF;}@font-face{font-family: 'Lato'; src: url("/web/static/src/scss/../fonts/lato/Lato-RegIta-webfont.eot"); src: url("/web/static/src/scss/../fonts/lato/Lato-RegIta-webfont.eot?#iefix") format("embedded-opentype"), url("/web/static/src/scss/../fonts/lato/Lato-RegIta-webfont.woff") format("woff"), url("/web/static/src/scss/../fonts/lato/Lato-RegIta-webfont.ttf") format("truetype"), url("/web/static/src/scss/../fonts/lato/Lato-RegIta-webfont.svg#Lato") format("svg"); font-weight: 400; font-style: italic;}@font-face{font-family: "Lato-RegIta"; src: url("/web/static/src/scss/../fonts/lato/Lato-RegIta-webfont.eot"); src: url("/web/static/src/scss/../fonts/lato/Lato-RegIta-webfont.eot?#iefix") format("embedded-opentype"), url("/web/static/src/scss/../fonts/lato/Lato-RegIta-webfont.woff") format("woff"), url("/web/static/src/scss/../fonts/lato/Lato-RegIta-webfont.ttf") format("truetype"), url("/web/static/src/scss/../fonts/lato/Lato-RegIta-webfont.svg#Roboto") format("svg");}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSans-Bol.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSans-Bol.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSans-Bol.ttf") format("truetype"); font-weight: 700; font-style: normal; unicode-range: U+0400-04FF, U+0500-052F;}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-Bol.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-Bol.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-Bol.ttf") format("truetype"); font-weight: 700; font-style: normal; unicode-range: U+0590-05FF, U+FB1D-FB4F;}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-Bol.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-Bol.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-Bol.ttf") format("truetype"); font-weight: 700; font-style: normal; unicode-range: U+0600-06FF, U+0750-077F, U+08A0-08FF;}@font-face{font-family: 'Lato'; src: url("/web/static/src/scss/../fonts/lato/Lato-Bol-webfont.eot"); src: url("/web/static/src/scss/../fonts/lato/Lato-Bol-webfont.eot?#iefix") format("embedded-opentype"), url("/web/static/src/scss/../fonts/lato/Lato-Bol-webfont.woff") format("woff"), url("/web/static/src/scss/../fonts/lato/Lato-Bol-webfont.ttf") format("truetype"), url("/web/static/src/scss/../fonts/lato/Lato-Bol-webfont.svg#Lato") format("svg"); font-weight: 700; font-style: normal;}@font-face{font-family: "Lato-Bol"; src: url("/web/static/src/scss/../fonts/lato/Lato-Bol-webfont.eot"); src: url("/web/static/src/scss/../fonts/lato/Lato-Bol-webfont.eot?#iefix") format("embedded-opentype"), url("/web/static/src/scss/../fonts/lato/Lato-Bol-webfont.woff") format("woff"), url("/web/static/src/scss/../fonts/lato/Lato-Bol-webfont.ttf") format("truetype"), url("/web/static/src/scss/../fonts/lato/Lato-Bol-webfont.svg#Roboto") format("svg");}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSans-BolIta.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSans-BolIta.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSans-BolIta.ttf") format("truetype"); font-weight: 700; font-style: italic; unicode-range: U+0400-04FF, U+0500-052F;}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-BolIta.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-BolIta.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-BolIta.ttf") format("truetype"); font-weight: 700; font-style: italic; unicode-range: U+0590-05FF, U+FB1D-FB4F;}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-BolIta.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-BolIta.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-BolIta.ttf") format("truetype"); font-weight: 700; font-style: italic; unicode-range: U+0600-06FF, U+0750-077F, U+08A0-08FF;}@font-face{font-family: 'Lato'; src: url("/web/static/src/scss/../fonts/lato/Lato-BolIta-webfont.eot"); src: url("/web/static/src/scss/../fonts/lato/Lato-BolIta-webfont.eot?#iefix") format("embedded-opentype"), url("/web/static/src/scss/../fonts/lato/Lato-BolIta-webfont.woff") format("woff"), url("/web/static/src/scss/../fonts/lato/Lato-BolIta-webfont.ttf") format("truetype"), url("/web/static/src/scss/../fonts/lato/Lato-BolIta-webfont.svg#Lato") format("svg"); font-weight: 700; font-style: italic;}@font-face{font-family: "Lato-BolIta"; src: url("/web/static/src/scss/../fonts/lato/Lato-BolIta-webfont.eot"); src: url("/web/static/src/scss/../fonts/lato/Lato-BolIta-webfont.eot?#iefix") format("embedded-opentype"), url("/web/static/src/scss/../fonts/lato/Lato-BolIta-webfont.woff") format("woff"), url("/web/static/src/scss/../fonts/lato/Lato-BolIta-webfont.ttf") format("truetype"), url("/web/static/src/scss/../fonts/lato/Lato-BolIta-webfont.svg#Roboto") format("svg");}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSans-Bla.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSans-Bla.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSans-Bla.ttf") format("truetype"); font-weight: 900; font-style: normal; unicode-range: U+0400-04FF, U+0500-052F;}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-Bla.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-Bla.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-Bla.ttf") format("truetype"); font-weight: 900; font-style: normal; unicode-range: U+0590-05FF, U+FB1D-FB4F;}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-Bla.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-Bla.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-Bla.ttf") format("truetype"); font-weight: 900; font-style: normal; unicode-range: U+0600-06FF, U+0750-077F, U+08A0-08FF;}@font-face{font-family: 'Lato'; src: url("/web/static/src/scss/../fonts/lato/Lato-Bla-webfont.eot"); src: url("/web/static/src/scss/../fonts/lato/Lato-Bla-webfont.eot?#iefix") format("embedded-opentype"), url("/web/static/src/scss/../fonts/lato/Lato-Bla-webfont.woff") format("woff"), url("/web/static/src/scss/../fonts/lato/Lato-Bla-webfont.ttf") format("truetype"), url("/web/static/src/scss/../fonts/lato/Lato-Bla-webfont.svg#Lato") format("svg"); font-weight: 900; font-style: normal;}@font-face{font-family: "Lato-Bla"; src: url("/web/static/src/scss/../fonts/lato/Lato-Bla-webfont.eot"); src: url("/web/static/src/scss/../fonts/lato/Lato-Bla-webfont.eot?#iefix") format("embedded-opentype"), url("/web/static/src/scss/../fonts/lato/Lato-Bla-webfont.woff") format("woff"), url("/web/static/src/scss/../fonts/lato/Lato-Bla-webfont.ttf") format("truetype"), url("/web/static/src/scss/../fonts/lato/Lato-Bla-webfont.svg#Roboto") format("svg");}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSans-BlaIta.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSans-BlaIta.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSans-BlaIta.ttf") format("truetype"); font-weight: 900; font-style: italic; unicode-range: U+0400-04FF, U+0500-052F;}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-BlaIta.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-BlaIta.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-BlaIta.ttf") format("truetype"); font-weight: 900; font-style: italic; unicode-range: U+0590-05FF, U+FB1D-FB4F;}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-BlaIta.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-BlaIta.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-BlaIta.ttf") format("truetype"); font-weight: 900; font-style: italic; unicode-range: U+0600-06FF, U+0750-077F, U+08A0-08FF;}@font-face{font-family: 'Lato'; src: url("/web/static/src/scss/../fonts/lato/Lato-BlaIta-webfont.eot"); src: url("/web/static/src/scss/../fonts/lato/Lato-BlaIta-webfont.eot?#iefix") format("embedded-opentype"), url("/web/static/src/scss/../fonts/lato/Lato-BlaIta-webfont.woff") format("woff"), url("/web/static/src/scss/../fonts/lato/Lato-BlaIta-webfont.ttf") format("truetype"), url("/web/static/src/scss/../fonts/lato/Lato-BlaIta-webfont.svg#Lato") format("svg"); font-weight: 900; font-style: italic;}@font-face{font-family: "Lato-BlaIta"; src: url("/web/static/src/scss/../fonts/lato/Lato-BlaIta-webfont.eot"); src: url("/web/static/src/scss/../fonts/lato/Lato-BlaIta-webfont.eot?#iefix") format("embedded-opentype"), url("/web/static/src/scss/../fonts/lato/Lato-BlaIta-webfont.woff") format("woff"), url("/web/static/src/scss/../fonts/lato/Lato-BlaIta-webfont.ttf") format("truetype"), url("/web/static/src/scss/../fonts/lato/Lato-BlaIta-webfont.svg#Roboto") format("svg");}@font-face{font-family: "Montserrat"; src: url("/web/static/src/scss/../fonts/google/Montserrat/Montserrat-Regular.ttf") format("truetype"); font-weight: 400; font-style: normal;}@font-face{font-family: "Open_Sans"; src: url("/web/static/src/scss/../fonts/google/Open_Sans/Open_Sans-Regular.ttf") format("truetype"); font-weight: 400; font-style: normal;}@font-face{font-family: "Oswald"; src: url("/web/static/src/scss/../fonts/google/Oswald/Oswald-Regular.ttf") format("truetype"); font-weight: 400; font-style: normal;}@font-face{font-family: "Raleway"; src: url("/web/static/src/scss/../fonts/google/Raleway/Raleway-Regular.ttf") format("truetype"); font-weight: 400; font-style: normal;}@font-face{font-family: "Roboto"; src: url("/web/static/src/scss/../fonts/google/Roboto/Roboto-Regular.ttf") format("truetype"); font-weight: 400; font-style: normal;}

/* /web/static/src/scss/ui.scss defined in bundle 'web.assets_common' */
 .o_hidden{display: none !important;}.o_text_overflow{display: inline-block; max-width: 100%; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; vertical-align: top;}.ui-autocomplete{z-index: 1051; max-width: 600px;}.ui-autocomplete .ui-menu-item > a{display: block;}.dropdown-toggle{white-space: nowrap;}.dropdown-toggle.o-no-caret::before, .dropdown-toggle.o-no-caret::after{content: normal;}:not(.collapsed) > .o-collapsed-label, .collapsed > .o-not-collapsed-label{display: none;}.o_rtl .ui-autocomplete{direction: ltr; right: 0; left: auto;}.o_rtl .ui-datepicker-next, .o_rtl .ui-datepicker-prev{-webkit-transform: rotate(180deg); -o-transform: rotate(180deg); transform: rotate(180deg);}.custom-control.custom-checkbox .custom-control-input:not(:checked):not(:indeterminate) ~ .custom-control-label:before{background: none; outline: 1px solid #666666;}.custom-control.custom-checkbox .custom-control-input:not(:checked):not(:indeterminate):disabled ~ .custom-control-label:before{background: #f8f9fa; opacity: 0.25;}@media print{.custom-control.custom-checkbox{-webkit-print-color-adjust: exact !important; color-adjust: exact !important;}}.o_catch_attention{position: relative; z-index: 1; animation: catchAttention 200ms ease 0s infinite normal;}@keyframes catchAttention{0%, 20%, 40%, 60%, 80%, 100%{transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);}0%{transform: translateY(-30%);}20%{transform: translateY(-25%);}40%{transform: translateY(-20%);}60%{transform: translateY(-15%);}80%{transform: translateY(-10%);}100%{transform: translateY(-5%);}}.o_debounce_disabled{pointer-events: none;}span.o_force_ltr{display: inline;}.o_force_ltr, .o_field_phone{unicode-bidi: embed; direction: ltr;}.o_object_fit_cover{object-fit: cover;}.o_image_24_cover{width: 24px; height: 24px; object-fit: cover;}.o_image_40_cover{width: 40px; height: 40px; object-fit: cover;}.o_image_64_cover{width: 64px; height: 64px; object-fit: cover;}.o_image_64_contain{width: 64px; height: 64px; object-fit: contain;}.o_image_64_max{max-width: 64px; max-height: 64px;}.o_image_128_max{max-width: 128px !important; max-height: 128px !important;}.o_width_128{width: 128px;}.o_web_accesskey_overlay{position: absolute; top: 0; left: auto; bottom: auto; right: 0; display: -webkit-box; display: -webkit-flex; display: flex; justify-content: center; align-items: center; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.6); color: #FFFFFF; font-size: 1rem; font-family: sans-serif;}

/* /odex25_web/static/src/scss/ui.scss defined in bundle 'web.assets_common' */
 .o_home_menu_background{background: url(/odex25_web/static/src/img/home-menu-bg-overlay.svg), linear-gradient(to right bottom, #77717e, #c9a8a9); background-size: cover;}

/* /web/static/src/scss/navbar.scss defined in bundle 'web.assets_common' */
 body.o_is_superuser .o_menu_systray{background: repeating-linear-gradient(135deg, #d9b904, #d9b904 10px, #373435 10px, #373435 20px); border-bottom-left-radius: 20px;}body.o_is_superuser .o_menu_systray > li > a:hover, body.o_is_superuser .o_menu_systray > li > label:hover{background-color: rgba(104, 70, 95, 0.5);}body.o_is_superuser .o_menu_systray .show .dropdown-toggle{background-color: rgba(104, 70, 95, 0.5);}.o_main_navbar{position: relative; height: 46px; border-bottom: 1px solid #68465f; background-color: #875A7B; color: white;}.o_main_navbar > a, .o_main_navbar > button{float: left; height: 46px; border: none; padding: 0 12px 0 16px; line-height: 46px; background-color: transparent; text-align: center; color: inherit; font-size: 18px; user-select: none;}.o_main_navbar > a:hover, .o_main_navbar > a:focus, .o_main_navbar > button:hover, .o_main_navbar > button:focus{background-color: #68465f; color: inherit;}.o_main_navbar > a:focus, .o_main_navbar > a:active, .o_main_navbar > a:focus:active, .o_main_navbar > button:focus, .o_main_navbar > button:active, .o_main_navbar > button:focus:active{outline: none;}.o_main_navbar .o_app{cursor: pointer;}.o_main_navbar > .o_menu_brand{display: block; float: left; margin-right: 35px; user-select: none; color: white; font-size: 22px; font-weight: 500; line-height: 46px; cursor: pointer;}.o_main_navbar > .o_menu_toggle{margin-right: 5px;}.o_main_navbar > ul{display: block; float: left; margin: 0; padding: 0; list-style: none;}.o_main_navbar > ul > li{position: relative; display: block; float: left;}.o_main_navbar > ul > li > a{display: block;}.o_main_navbar > ul > li > a:focus, .o_main_navbar > ul > li > a:active, .o_main_navbar > ul > li > a:focus:active{outline: none;}.o_main_navbar > ul > li > a, .o_main_navbar > ul > li > a:hover, .o_main_navbar > ul > li > a:focus{text-decoration: none;}.o_main_navbar > ul > li > a, .o_main_navbar > ul > li > label{height: 46px; padding: 0 10px; color: white; line-height: 46px;}.o_main_navbar > ul > li > a:hover, .o_main_navbar > ul > li > label:hover{background-color: #68465f;}.o_main_navbar > ul > li.o_extra_menu_items > a{width: 46px; text-align: center;}.o_main_navbar > ul > li.o_extra_menu_items.show > ul{padding: 0;}.o_main_navbar > ul > li.o_extra_menu_items.show > ul > li > a{background-color: #875A7B; color: white; border-bottom: 1px solid #f8f9fa;}.o_main_navbar > ul > li.o_extra_menu_items.show > ul > li > a.dropdown-toggle{background-color: #ac82a0; pointer-events: none;}.o_main_navbar > ul > li.o_extra_menu_items.show > ul > li > .dropdown-menu{position: static; float: none; display: block; border: none; box-shadow: none; max-height: none;}.o_main_navbar > ul.o_menu_systray{float: right;}.o_main_navbar .dropdown-menu.show{max-height: 90vh; min-width: 100%; overflow: auto; margin-top: 0;}.o_main_navbar .dropdown-menu.show .o_menu_header_lvl_3, .o_main_navbar .dropdown-menu.show .o_menu_entry_lvl_3{padding-left: 32px;}.o_main_navbar .dropdown-menu.show .o_menu_header_lvl_4, .o_main_navbar .dropdown-menu.show .o_menu_entry_lvl_4{padding-left: 44px;}.o_main_navbar .dropdown-menu.show .o_menu_header_lvl_5, .o_main_navbar .dropdown-menu.show .o_menu_entry_lvl_5{padding-left: 56px;}.o_main_navbar .show .dropdown-toggle{background-color: #68465f;}.o_main_navbar .o_user_menu{margin-left: 6px;}.o_main_navbar .o_user_menu > a{padding-right: 16px;}.o_main_navbar .o_user_menu .oe_topbar_avatar{height: 26px; width: 26px; object-fit: cover; transform: translateY(-2px);}

/* /web/static/src/scss/mimetypes.scss defined in bundle 'web.assets_common' */
 .o_image{display: inline-block; width: 38px; height: 38px; background-image: url("/web/static/src/img/mimetypes/unknown.svg"); background-size: contain; background-repeat: no-repeat; background-position: center;}.o_image[data-mimetype^='image']{background-image: url("/web/static/src/img/mimetypes/image.svg");}.o_image[data-mimetype^='audio']{background-image: url("/web/static/src/img/mimetypes/audio.svg");}.o_image[data-mimetype^='text'], .o_image[data-mimetype$='rtf']{background-image: url("/web/static/src/img/mimetypes/text.svg");}.o_image[data-mimetype*='octet-stream'], .o_image[data-mimetype*='download'], .o_image[data-mimetype*='python']{background-image: url("/web/static/src/img/mimetypes/binary.svg");}.o_image[data-mimetype^='video'], .o_image[title$='.mp4'], .o_image[title$='.avi']{background-image: url("/web/static/src/img/mimetypes/video.svg");}.o_image[data-mimetype$='archive'], .o_image[data-mimetype$='compressed'], .o_image[data-mimetype*='zip'], .o_image[data-mimetype$='tar'], .o_image[data-mimetype*='package']{background-image: url("/web/static/src/img/mimetypes/archive.svg");}.o_image[data-mimetype='application/pdf']{background-image: url("/web/static/src/img/mimetypes/pdf.svg");}.o_image[data-mimetype^='text-master'], .o_image[data-mimetype*='document'], .o_image[data-mimetype*='msword'], .o_image[data-mimetype*='wordprocessing']{background-image: url("/web/static/src/img/mimetypes/document.svg");}.o_image[data-mimetype*='application/xml'], .o_image[data-mimetype$='html']{background-image: url("/web/static/src/img/mimetypes/web_code.svg");}.o_image[data-mimetype$='css'], .o_image[data-mimetype$='less'], .o_image[data-ext$='less']{background-image: url("/web/static/src/img/mimetypes/web_style.svg");}.o_image[data-mimetype*='-image'], .o_image[data-mimetype*='diskimage'], .o_image[data-ext$='dmg']{background-image: url("/web/static/src/img/mimetypes/disk.svg");}.o_image[data-mimetype$='csv'], .o_image[data-mimetype*='vc'], .o_image[data-mimetype*='excel'], .o_image[data-mimetype$='numbers'], .o_image[data-mimetype$='calc'], .o_image[data-mimetype*='mods'], .o_image[data-mimetype*='spreadsheet']{background-image: url("/web/static/src/img/mimetypes/spreadsheet.svg");}.o_image[data-mimetype^='key']{background-image: url("/web/static/src/img/mimetypes/certificate.svg");}.o_image[data-mimetype*='presentation'], .o_image[data-mimetype*='keynote'], .o_image[data-mimetype*='teacher'], .o_image[data-mimetype*='slideshow'], .o_image[data-mimetype*='powerpoint']{background-image: url("/web/static/src/img/mimetypes/presentation.svg");}.o_image[data-mimetype*='cert'], .o_image[data-mimetype*='rules'], .o_image[data-mimetype*='pkcs'], .o_image[data-mimetype$='stl'], .o_image[data-mimetype$='crl']{background-image: url("/web/static/src/img/mimetypes/certificate.svg");}.o_image[data-mimetype*='-font'], .o_image[data-mimetype*='font-'], .o_image[data-ext$='ttf']{background-image: url("/web/static/src/img/mimetypes/font.svg");}.o_image[data-mimetype*='-dvi']{background-image: url("/web/static/src/img/mimetypes/print.svg");}.o_image[data-mimetype*='script'], .o_image[data-mimetype*='x-sh'], .o_image[data-ext*='bat'], .o_image[data-mimetype$='bat'], .o_image[data-mimetype$='cgi'], .o_image[data-mimetype$='-c'], .o_image[data-mimetype*='java'], .o_image[data-mimetype*='ruby']{background-image: url("/web/static/src/img/mimetypes/script.svg");}.o_image[data-mimetype*='javascript']{background-image: url("/web/static/src/img/mimetypes/javascript.svg");}.o_image[data-mimetype*='calendar'], .o_image[data-mimetype$='ldif']{background-image: url("/web/static/src/img/mimetypes/calendar.svg");}.o_image[data-mimetype$='postscript'], .o_image[data-mimetype$='cdr'], .o_image[data-mimetype$='xara'], .o_image[data-mimetype$='cgm'], .o_image[data-mimetype$='graphics'], .o_image[data-mimetype$='draw'], .o_image[data-mimetype*='svg']{background-image: url("/web/static/src/img/mimetypes/vector.svg");}

/* /web/static/src/scss/modal.scss defined in bundle 'web.assets_common' */
 .modal.o_technical_modal .modal-content{border-radius: 0;}.modal.o_technical_modal .modal-content .modal-header .o_subtitle{margin-left: 10px;}.modal.o_technical_modal .modal-content .modal-header .modal-title{overflow: hidden; white-space: nowrap; text-overflow: ellipsis;}.modal.o_technical_modal .modal-content .modal-body.o_act_window{padding: 0;}.modal.o_technical_modal .modal-content .modal-body .mjs-nestedSortable-error{outline: none;}.modal.o_technical_modal .modal-content .modal-body .o_modal_header{padding-top: 10px; padding-right: 16px; padding-bottom: 10px; padding-left: 16px;}.modal.o_technical_modal .modal-content .modal-body .o_modal_header::after{display: block; clear: both; content: "";}.modal.o_technical_modal .modal-content .modal-body .o_modal_header .o_search_options{display: inline-block;}.modal.o_technical_modal .modal-content .modal-body .o_modal_header .o_pager{float: right;}.modal.o_technical_modal .modal-content .modal-body > :not(.o_view_sample_data) .o_view_nocontent{position: unset;}.modal.o_technical_modal .modal-content .modal-body .o_modal_changes td:first-child{padding-right: 10px; vertical-align: top; white-space: nowrap;}.modal.o_technical_modal .modal-content .modal-body .o_modal_changes td:not(:first-child){width: 100%;}.modal.o_technical_modal .modal-content .modal-footer{-webkit-flex-wrap: wrap; flex-wrap: wrap; text-align: left; -webkit-box-pack: start; justify-content: flex-start;}.modal.o_technical_modal .modal-content .modal-footer > div, .modal.o_technical_modal .modal-content .modal-footer > footer{-webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto;}.modal.o_technical_modal .modal-content .modal-footer footer > :not(:first-child){margin-left: .25rem;}.modal.o_technical_modal .modal-content .modal-footer footer > :not(:last-child){margin-right: .25rem;}.modal.o_technical_modal .modal-content .modal-footer footer button{margin-bottom: .5rem;}@media (min-width: 576px){.modal.o_technical_modal .modal-dialog .modal-content .modal-body.o_dialog_error{overflow: visible; display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-flow: column nowrap; flex-flow: column nowrap;}.modal.o_technical_modal .modal-dialog .modal-content .modal-body.o_dialog_error > .alert, .modal.o_technical_modal .modal-dialog .modal-content .modal-body.o_dialog_error > button{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto;}.modal.o_technical_modal .modal-dialog .modal-content .modal-body.o_dialog_error > .o_error_detail{-webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto; min-height: 0; overflow: auto;}}@media (max-width: 575.98px){.modal.o_technical_modal.o_modal_full .modal-dialog{margin: 0px; height: 100%;}.modal.o_technical_modal.o_modal_full .modal-dialog .modal-content{height: 100%; border: none;}.modal.o_technical_modal.o_modal_full .modal-dialog .modal-content .modal-header{background: #875A7B;}.modal.o_technical_modal.o_modal_full .modal-dialog .modal-content .modal-header .modal-title, .modal.o_technical_modal.o_modal_full .modal-dialog .modal-content .modal-header .o_subtitle, .modal.o_technical_modal.o_modal_full .modal-dialog .modal-content .modal-header button.close{color: white;}.modal.o_technical_modal.o_modal_full .modal-dialog .modal-content .modal-body{height: 100%; overflow-y: auto;}}.modal.o_inactive_modal{z-index: 1039;}.o_dialog > .modal{display: block;}

/* /web/static/src/scss/animation.scss defined in bundle 'web.assets_common' */
 @keyframes markAnim{0%{opacity: 0; transform: scaleX(0.5) scaleY(0.5);}30%{opacity: 1; transform: scaleX(1) scaleY(1);}100%{opacity: 0; transform: scaleX(1) scaleY(1);}}@-moz-keyframes markAnim{0%{opacity: 0; -moz-transform: scaleX(0.5) scaleY(0.5);}30%{opacity: 1; -moz-transform: scaleX(1) scaleY(1);}100%{opacity: 0; -moz-transform: scaleX(1) scaleY(1);}}@-webkit-keyframes markAnim{0%{opacity: 0; -webkit-transform: scaleX(0.5) scaleY(0.5);}30%{opacity: 1; -webkit-transform: scaleX(1) scaleY(1);}100%{opacity: 0; -webkit-transform: scaleX(1) scaleY(1);}}@-o-keyframes markAnim{0%{opacity: 0; -o-transform: scaleX(0.5) scaleY(0.5);}30%{opacity: 1; -o-transform: scaleX(1) scaleY(1);}100%{opacity: 0; -o-transform: scaleX(1) scaleY(1);}}@-ms-keyframes markAnim{0%{opacity: 0; -ms-transform: scaleX(0.5) scaleY(0.5);}30%{opacity: 1; -ms-transform: scaleX(1) scaleY(1);}100%{opacity: 0; -ms-transform: scaleX(1) scaleY(1);}}@-webkit-keyframes bounceIn{0%, 20%, 40%, 60%, 80%, 100%{-webkit-transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1); transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);}0%{opacity: 0; -webkit-transform: scale3d(0.3, 0.3, 0.3); transform: scale3d(0.3, 0.3, 0.3);}20%{-webkit-transform: scale3d(1.1, 1.1, 1.1); transform: scale3d(1.1, 1.1, 1.1);}40%{-webkit-transform: scale3d(0.9, 0.9, 0.9); transform: scale3d(0.9, 0.9, 0.9);}60%{opacity: 1; -webkit-transform: scale3d(1.03, 1.03, 1.03); transform: scale3d(1.03, 1.03, 1.03);}80%{-webkit-transform: scale3d(0.97, 0.97, 0.97); transform: scale3d(0.97, 0.97, 0.97);}100%{opacity: 1; -webkit-transform: scale3d(1, 1, 1); transform: scale3d(1, 1, 1);}}@keyframes bounceIn{0%, 20%, 40%, 60%, 80%, 100%{-webkit-transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1); transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);}0%{opacity: 0; -webkit-transform: scale3d(0.3, 0.3, 0.3); transform: scale3d(0.3, 0.3, 0.3);}20%{-webkit-transform: scale3d(1.1, 1.1, 1.1); transform: scale3d(1.1, 1.1, 1.1);}40%{-webkit-transform: scale3d(0.9, 0.9, 0.9); transform: scale3d(0.9, 0.9, 0.9);}60%{opacity: 1; -webkit-transform: scale3d(1.03, 1.03, 1.03); transform: scale3d(1.03, 1.03, 1.03);}80%{-webkit-transform: scale3d(0.97, 0.97, 0.97); transform: scale3d(0.97, 0.97, 0.97);}100%{opacity: 1; -webkit-transform: scale3d(1, 1, 1); transform: scale3d(1, 1, 1);}}

/* /web/static/src/scss/rainbow.scss defined in bundle 'web.assets_common' */
 .o_reward{will-change: transform; z-index: 1050; padding: 50px; margin: -5% auto 0 -200px; background-image: -webkit-radial-gradient(#EDEFF4 30%, transparent 70%, transparent); background-image: -o-radial-gradient(#EDEFF4 30%, transparent 70%, transparent); background-image: radial-gradient(#EDEFF4 30%, transparent 70%, transparent); animation: reward-fading 0.7s ease-in-out 0s 1 normal forwards; position: absolute; top: 20%; left: 50%; bottom: auto; right: auto; width: 400px; height: 400px;}@media (max-width: 767.98px){.o_reward{margin: -5% auto 0 -150px;}}@media (max-width: 767.98px){.o_reward{width: 300px; height: 300px;}}.o_reward.o_reward_fading{display: block; animation: reward-fading-reverse 0.56s ease-in-out 0s 1 normal forwards;}.o_reward.o_reward_fading .o_reward_face_group{animation: reward-jump-reverse 0.56s ease-in-out 0s 1 normal forwards;}.o_reward.o_reward_fading .o_reward_rainbow path{animation: reward-rainbow-reverse 0.7s ease-out 0s 1 normal forwards;}.o_reward .o_reward_face, .o_reward .o_reward_stars, .o_reward .o_reward_shadow, .o_reward .o_reward_thumbup{margin: 0 auto;}.o_reward .o_reward_rainbow path{stroke-dasharray: 600; stroke-dashoffset: 0; fill: none; stroke-linecap: round; stroke-width: 21px; animation: reward-rainbow 1.4s ease-out 0s 1 normal forwards;}.o_reward .o_reward_face_group{transform-origin: center; animation: reward-jump 1.12s ease-in-out 0s 1 normal none running; position: absolute; top: 6%; left: 0; bottom: 0; right: 0; width: 100%; height: 60%;}.o_reward .o_reward_face{display: block; top: 42%; position: relative; border-radius: 100%; background: center center / contain no-repeat; animation: reward-float 1.4s ease-in-out 1.4s infinite alternate; width: 34%; height: 56.67%;}.o_reward .o_reward_stars{display: block; width: 300px; height: 200px; position: absolute; top: 18%; left: auto; bottom: auto; right: 7%;}@media (max-width: 767.98px){.o_reward .o_reward_stars{width: 225px; height: 150px;}}.o_reward .o_reward_stars svg{transform-origin: center center; position: absolute; top: 28%; left: 3%; bottom: auto; right: auto; animation: reward-stars 1.4s ease-in-out 0s infinite alternate-reverse;}.o_reward .o_reward_stars svg.star2, .o_reward .o_reward_stars svg.star4{animation: reward-stars 1.68s ease-in-out 0s infinite alternate;}.o_reward .o_reward_stars svg.star2{left: 20%; top: 2%;}.o_reward .o_reward_stars svg.star3{left: 49%; top: 6%;}.o_reward .o_reward_stars svg.star4{left: 70%; top: 27%;}.o_reward .o_reward_thumbup{width: 40px; display: block; animation: reward-scale 0.7s ease-in-out 0s infinite alternate; position: absolute; top: 63%; left: 65%; bottom: auto; right: auto;}.o_reward .o_reward_msg_container{will-change: transform; padding-top: 11%; width: 70%; margin-left: 15%; transform: translateY(5px); animation: reward-float 1.4s ease-in-out 1.4s infinite alternate-reverse; position: absolute; top: 85%; left: 0%; bottom: auto; right: auto;}.o_reward .o_reward_msg_container .o_reward_thumb_right{height: 40px; z-index: 1; position: absolute; top: 0; left: 16%; bottom: auto; right: auto;}.o_reward .o_reward_msg_container .o_reward_msg{margin-left: 7%; margin-top: -9.5%; padding: 25px 15px 20px; background: white; border: 1px solid #ecf1ff; border-top-width: 0; display: inline-block;}.o_reward .o_reward_msg_container .o_reward_msg *:first-child{margin-top: 0;}.o_reward .o_reward_msg_container .o_reward_msg_content{position: relative; font-family: sans-serif; text-align: left; color: #727880;}.o_reward .o_reward_msg_container .o_reward_shadow_container{transform: translateY(0px) rotateZ(0); animation: reward-float 1.4s ease-in-out 1.4s infinite alternate;}.o_reward .o_reward_msg_container .o_reward_shadow{width: 100%; height: 12px; background-color: #e7eaf0; border-radius: 100%; transform: scale(0.8) rotateZ(0); animation: reward-scale 1.4s ease-in-out 1.4s infinite alternate; position: absolute; top: auto; left: 0; bottom: -40px; right: auto;}@keyframes reward-fading{0%{opacity: 0;}100%{opacity: 1;}}@keyframes reward-fading-reverse{100%{opacity: 0;}}@keyframes reward-jump{0%{transform: scale(0.5);}50%{transform: scale(1.05);}to{transform: scale(1);}}@keyframes reward-jump-reverse{0%{transform: scale(1);}50%{transform: scale(1.05);}to{transform: scale(0.5);}}@keyframes reward-rainbow{0%{stroke-dashoffset: -500;}to{stroke-dashoffset: 0;}}@keyframes reward-rainbow-reverse{to{stroke-dashoffset: -500;}}@keyframes reward-float{from{transform: translateY(0px);}to{transform: translateY(5px);}}@keyframes reward-stars{from{transform: scale(0.3) rotate(0deg);}50%{transform: scale(1) rotate(20deg);}to{transform: scale(0.3) rotate(80deg);}}@keyframes reward-scale{from{transform: scale(0.8);}to{transform: scale(1);}}

/* /web/static/src/scss/datepicker.scss defined in bundle 'web.assets_common' */
 .sr-only, .bootstrap-datetimepicker-widget .btn[data-action="incrementHours"]::after, .bootstrap-datetimepicker-widget .btn[data-action="incrementMinutes"]::after, .bootstrap-datetimepicker-widget .btn[data-action="decrementHours"]::after, .bootstrap-datetimepicker-widget .btn[data-action="decrementMinutes"]::after, .bootstrap-datetimepicker-widget .btn[data-action="showHours"]::after, .bootstrap-datetimepicker-widget .btn[data-action="showMinutes"]::after, .bootstrap-datetimepicker-widget .btn[data-action="togglePeriod"]::after, .bootstrap-datetimepicker-widget .btn[data-action="clear"]::after, .bootstrap-datetimepicker-widget .btn[data-action="today"]::after, .bootstrap-datetimepicker-widget .picker-switch::after, .bootstrap-datetimepicker-widget table th.prev::after, .bootstrap-datetimepicker-widget table th.next::after{position: absolute; width: 1px; height: 1px; margin: -1px; padding: 0; overflow: hidden; clip: rect(0, 0, 0, 0); border: 0;}.o_datepicker{position: relative;}.o_datepicker .o_datepicker_input{width: 100%; cursor: pointer;}.o_datepicker .o_datepicker_button{position: absolute; top: 2px; left: auto; bottom: auto; right: 4px; pointer-events: none;}.o_datepicker .o_datepicker_button:after{content: ""; display: inline-block; width: 0; height: 0; vertical-align: middle; border-bottom: 0; border-left: 0.3em solid transparent; border-right: 0.3em solid transparent; border-top: 0.3em solid; -moz-transform: scale(0.9999);}.o_datepicker .o_datepicker_warning{top: 0; right: 20px;}div.dropdown-menu.bootstrap-datetimepicker-widget{z-index: 1051; width: 19rem;}.datepicker .table-sm > thead{color: white; background-color: #875A7B;}.datepicker .table-sm > thead > tr:first-child th:hover{color: white; background-color: #68465f;}.datepicker .table-sm > thead > tr:last-child{color: #8f8f8f; background-color: #dcdbdb;}.datepicker .table-sm > thead > tr > th{border-radius: 0;}.datepicker .table-sm > tbody > tr > td.active, .datepicker .table-sm > tbody > tr > td .active{background-color: #00A09D; border-radius: 100px;}.datepicker .table-sm > tbody > tr > td.today:before{border-bottom-color: #00A09D;}.picker-switch span.fa{margin: 0; transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;}@media (prefers-reduced-motion: reduce){.picker-switch span.fa{transition: none;}}.picker-switch span.fa.primary{background-color: #00A09D; color: white;}.picker-switch span.fa.primary:hover{background-color: #006d6b;}

/* /web/static/src/scss/daterangepicker.scss defined in bundle 'web.assets_common' */
 .daterangepicker .drp-calendar .calendar-table thead tr:first-child{color: #FFFFFF; background-color: #875A7B;}.daterangepicker .drp-calendar .calendar-table thead tr:first-child th.prev span, .daterangepicker .drp-calendar .calendar-table thead tr:first-child th.next span{color: #FFFFFF; border-color: #FFFFFF;}.daterangepicker .drp-calendar .calendar-table thead tr:first-child th.prev:hover, .daterangepicker .drp-calendar .calendar-table thead tr:first-child th.next:hover{background-color: #68465f;}.daterangepicker .drp-calendar .calendar-table thead tr:last-child{color: #8f8f8f; background-color: #dcdbdb;}.daterangepicker .drp-calendar .calendar-table thead th{border-radius: 0;}.daterangepicker .drp-calendar .calendar-table tbody tr{border-bottom: 1px solid #dcdbdb;}.daterangepicker .drp-calendar .calendar-table tbody tr td{border-width: 0px;}.daterangepicker .drp-calendar .calendar-table tbody tr td:not(.off).in-range{background-color: #e9ecef;}.daterangepicker .drp-calendar .calendar-table tbody tr td:not(.off).available:hover{background-color: #e9ecef;}.daterangepicker .drp-calendar .calendar-table tbody tr td:not(.off).active, .daterangepicker .drp-calendar .calendar-table tbody tr td:not(.off).active:hover{background-color: #00A09D;}.daterangepicker .drp-calendar .calendar-table tbody tr td.off:hover{background: #FFFFFF; color: #999;}.daterangepicker .drp-calendar .calendar-time{direction: ltr;}.daterangepicker .drp-calendar .calendar-time select.hourselect, .daterangepicker .drp-calendar .calendar-time select.minuteselect, .daterangepicker .drp-calendar .calendar-time select.secondselect, .daterangepicker .drp-calendar .calendar-time select.ampmselect{display: initial; -webkit-appearance: menulist-button; -moz-appearance: menulist-button; appearance: menulist-button;}.daterangepicker .drp-buttons .drp-selected{display: none;}

/* /web/static/src/scss/banner.scss defined in bundle 'web.assets_common' */
 .o_has_banner .o_view_nocontent{top: 30%;}@media (max-width: 767.98px){.o_has_banner .o_view_nocontent{position: relative; margin: auto;}}

/* /web/static/src/scss/colorpicker.scss defined in bundle 'web.assets_common' */
 .o_colorpicker_widget .o_color_pick_area{height: 125px; background-image: linear-gradient(to bottom, white 0%, rgba(255, 255, 255, 0) 50%, rgba(0, 0, 0, 0) 50%, black 100%), linear-gradient(to right, #807f7f 0%, rgba(128, 127, 127, 0) 100%); cursor: crosshair;}.o_colorpicker_widget .o_color_slider{background: linear-gradient(#F00 0%, #FF0 16.66%, #0F0 33.33%, #0FF 50%, #00F 66.66%, #F0F 83.33%, #F00 100%);}.o_colorpicker_widget .o_color_slider, .o_colorpicker_widget .o_opacity_slider{width: 4%; margin-right: 2%; cursor: pointer;}.o_colorpicker_widget .o_slider_pointer, .o_colorpicker_widget .o_opacity_pointer{position: absolute; top: auto; left: -50%; bottom: auto; right: auto; width: 200%; height: 8px; margin-top: -2px;}.o_colorpicker_widget .o_slider_pointer, .o_colorpicker_widget .o_opacity_pointer, .o_colorpicker_widget .o_picker_pointer, .o_colorpicker_widget .o_color_preview{box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.9);}.o_colorpicker_widget .o_slider_pointer, .o_colorpicker_widget .o_opacity_pointer, .o_colorpicker_widget .o_picker_pointer, .o_colorpicker_widget .o_color_preview, .o_colorpicker_widget .o_hex_div, .o_colorpicker_widget .o_rgba_div{border: 1px solid black;}.o_colorpicker_widget .o_color_picker_inputs{font-size: 10px;}.o_colorpicker_widget .o_color_picker_inputs input{font-family: monospace !important; height: 18px; font-size: 11px;}.o_colorpicker_widget .o_color_picker_inputs .o_hex_div input{width: 7ch;}.o_colorpicker_widget .o_color_picker_inputs .o_rgba_div input{margin-right: 3px; width: 3ch;}

/* /web/static/src/scss/popover.scss defined in bundle 'web.assets_common' */
 @keyframes slide-top{0%{opacity: 0; transform: translateY(-5%);}100%{opacity: 1; transform: translateY(0);}}@keyframes slide-right{0%{opacity: 0; transform: translateX(5%);}100%{opacity: 1; transform: translateX(0);}}@keyframes slide-bottom{0%{opacity: 0; transform: translateY(5%);}100%{opacity: 1; transform: translateY(0);}}@keyframes slide-left{0%{opacity: 0; transform: translateX(-5%);}100%{opacity: 1; transform: translateX(0);}}.o_popover{max-width: 100vw; position: fixed; z-index: 1060; border: 1px solid #dee2e6; background-color: #fff; box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);}.o_popover .o_popover_header{background-color: #e9ecef; font-size: 1.1rem; padding: 0.5rem 0.75rem; border-bottom: 1px solid #dee2e6;}.o_popover .o_popover_header:empty{display: none;}.o_popover--top{animation: 0.2s slide-top;}.o_popover--top::before, .o_popover--top::after{content: ''; position: absolute; top: 100%; left: 50%; margin-left: -8px; border: 8px solid transparent; border-top-color: #e9ecef;}.o_popover--top::before{margin-top: 1px; border-top-color: #dee2e6;}.o_popover--right{animation: 0.2s slide-right;}.o_popover--right::before, .o_popover--right::after{content: ''; position: absolute; top: 50%; right: 100%; margin-top: -8px; border: 8px solid transparent; border-right-color: #e9ecef;}.o_popover--right::before{margin-right: 1px; border-right-color: #dee2e6;}.o_popover--bottom{animation: 0.2s slide-bottom;}.o_popover--bottom::before, .o_popover--bottom::after{content: ''; position: absolute; bottom: 100%; left: 50%; margin-left: -8px; border: 8px solid transparent; border-bottom-color: #e9ecef;}.o_popover--bottom::before{margin-bottom: 1px; border-bottom-color: #dee2e6;}.o_popover--left{animation: 0.2s slide-left;}.o_popover--left::before, .o_popover--left::after{content: ''; position: absolute; top: 50%; left: 100%; margin-top: -8px; border: 8px solid transparent; border-left-color: #e9ecef;}.o_popover--left::before{margin-left: 1px; border-left-color: #dee2e6;}

/* /web/static/src/scss/translation_dialog.scss defined in bundle 'web.assets_common' */
 .o_translation_dialog .o_language_current{font-weight: bold;}.o_translation_dialog .row{margin-bottom: 9px;}

/* /web/static/src/scss/keyboard.scss defined in bundle 'web.assets_common' */
 .o_shortcut_table{border-collapse: separate; border-spacing: 0 1em;}.o_shortcut_table .o_key{background-color: #F4F7F8; border-radius: 3px; border: 1px solid #B4B4B4; box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.2), inset 0px -1px 1px 1px rgba(230, 230, 230, 0.8), inset 0px 2px 0px 0px rgba(255, 255, 255, 0.8); display: inline-block; font-family: Consolas,"Liberation Mono",Courier,monospace; font-size: 0.85em; padding: 2px 4px;}

/* /web/static/src/scss/name_and_signature.scss defined in bundle 'web.assets_common' */
 .o_web_sign_name_and_signature{position: relative;}.card.o_web_sign_auto_font_selection{position: absolute; top: 0; left: auto; bottom: auto; right: 0;}.card.o_web_sign_auto_font_selection .o_web_sign_auto_font_list{overflow: auto;}.card.o_web_sign_auto_font_selection .o_web_sign_auto_font_list > a{height: 100px;}.card.o_web_sign_auto_font_selection .o_web_sign_auto_font_list > a > img{height: 100%;}.o_field_widget .o_signature{outline: 1px solid rgba(108, 117, 125, 0.3); position: relative;}.o_field_widget .o_signature.o_signature_empty{display: -webkit-box; display: -webkit-flex; display: flex;}.o_field_widget .o_signature > p{position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);}.o_field_invalid .o_signature{outline: 3px solid #dc3545; cursor: pointer;}.o_form_editable .o_signature:hover{outline: 3px solid #00A09D; cursor: pointer;}.o_signature_stroke{position: absolute; border-top: #D1D0CE solid 2px; bottom: 16%; width: 72%; left: 14%;}

/* /web/static/src/scss/web.zoomodoo.scss defined in bundle 'web.assets_common' */
 .zoomodoo{position: relative; display: inline-block; *display: inline; *zoom: 1;}.zoomodoo img{vertical-align: bottom;}.zoomodoo-flyout{position: absolute; z-index: 100; overflow: hidden; background: #FFF; top: 0; width: 100%; height: 100%;}.zoomodoo-flyout img{max-width: 500%;}.zoomodoo-hover .zoomodoo-flyout{left: 0;}.zoomodoo-next .zoomodoo-flyout{left: 100%;}

/* /web/static/src/scss/color_picker.scss defined in bundle 'web.assets_common' */
 .o_field_color_picker{display: -webkit-box; display: -webkit-flex; display: flex; float: right; margin-right: 7px;}.o_field_color_picker ul{display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-pack: end; justify-content: flex-end; -webkit-flex-wrap: wrap; flex-wrap: wrap; max-width: 150px; padding: 3px 15px 3px 20px; width: 100%; max-width: unset; margin: 0; padding: 0;}.o_field_color_picker ul > li{display: inline-block; margin: 5px 5px 0 0; border: 1px solid white; box-shadow: 0 0 0 1px #dee2e6;}.o_field_color_picker ul > li > a{display: block;}.o_field_color_picker ul > li > a::after{content: ""; display: block; width: 20px; height: 15px;}.o_field_color_picker ul > li:first-child > a{position: relative;}.o_field_color_picker ul > li:first-child > a::before{content: ""; position: absolute; top: -2px; left: 10px; bottom: auto; right: auto; display: block; width: 1px; height: 20px; transform: rotate(45deg); background-color: red;}.o_field_color_picker ul > li:first-child > a::after{background-color: white;}.o_field_color_picker ul .oe_kanban_color_1::after{background-color: #F06050;}.o_field_color_picker ul .oe_kanban_color_2::after{background-color: #F4A460;}.o_field_color_picker ul .oe_kanban_color_3::after{background-color: #F7CD1F;}.o_field_color_picker ul .oe_kanban_color_4::after{background-color: #6CC1ED;}.o_field_color_picker ul .oe_kanban_color_5::after{background-color: #814968;}.o_field_color_picker ul .oe_kanban_color_6::after{background-color: #EB7E7F;}.o_field_color_picker ul .oe_kanban_color_7::after{background-color: #2C8397;}.o_field_color_picker ul .oe_kanban_color_8::after{background-color: #475577;}.o_field_color_picker ul .oe_kanban_color_9::after{background-color: #D6145F;}.o_field_color_picker ul .oe_kanban_color_10::after{background-color: #30C381;}.o_field_color_picker ul .oe_kanban_color_11::after{background-color: #9365B8;}.o_field_color_picker ul > li{border: 2px solid white; box-shadow: 0 0 0 1px #dee2e6;}.o_field_color_picker ul > li > a:focus{outline: none;}.o_field_color_picker_preview{margin-right: 7px;}.o_field_color_picker_preview .oe_kanban_color_1::after{background-color: #F06050;}.o_field_color_picker_preview .oe_kanban_color_2::after{background-color: #F4A460;}.o_field_color_picker_preview .oe_kanban_color_3::after{background-color: #F7CD1F;}.o_field_color_picker_preview .oe_kanban_color_4::after{background-color: #6CC1ED;}.o_field_color_picker_preview .oe_kanban_color_5::after{background-color: #814968;}.o_field_color_picker_preview .oe_kanban_color_6::after{background-color: #EB7E7F;}.o_field_color_picker_preview .oe_kanban_color_7::after{background-color: #2C8397;}.o_field_color_picker_preview .oe_kanban_color_8::after{background-color: #475577;}.o_field_color_picker_preview .oe_kanban_color_9::after{background-color: #D6145F;}.o_field_color_picker_preview .oe_kanban_color_10::after{background-color: #30C381;}.o_field_color_picker_preview .oe_kanban_color_11::after{background-color: #9365B8;}.o_field_color_picker_preview > li{display: inline-block; margin: 5px 5px 0 0; border: 1px solid white; box-shadow: 0 0 0 1px #dee2e6;}.o_field_color_picker_preview > li > a{display: block;}.o_field_color_picker_preview > li > a::after{content: ""; display: block; width: 20px; height: 15px;}.o_field_color_picker_preview > li a.oe_kanban_color_0{position: relative;}.o_field_color_picker_preview > li a.oe_kanban_color_0::before{content: ""; position: absolute; top: -2px; left: 10px; bottom: auto; right: auto; display: block; width: 1px; height: 20px; transform: rotate(45deg); background-color: red;}.o_field_color_picker_preview > li a.oe_kanban_color_0::after{background-color: white;}

/* /web/static/src/scss/fontawesome_overridden.scss defined in bundle 'web.assets_common' */
 @font-face{font-family: 'FontAwesome-tiktok-only'; src: url("/web/static/src/scss/../../src/fonts/tiktok_only.woff"); font-weight: normal; font-style: normal; font-display: block;}.fa.fa-tiktok{font-family: 'FontAwesome-tiktok-only' !important;}.fa.fa-tiktok:before{content: '\e07b';}.o_rtl .fa.fa-align-right, .o_rtl .fa.fa-align-left, .o_rtl .fa.fa-chevron-right, .o_rtl .fa.fa-chevron-left, .o_rtl .fa.fa-arrow-right, .o_rtl .fa.fa-arrow-left, .o_rtl .fa.fa-hand-o-right, .o_rtl .fa.fa-hand-o-left, .o_rtl .fa.fa-arrow-circle-right, .o_rtl .fa.fa-arrow-circle-left, .o_rtl .fa.fa-caret-right, .o_rtl .fa.fa-caret-left, .o_rtl .fa.fa-rotate-right, .o_rtl .fa.fa-rotate-left, .o_rtl .fa.fa-angle-double-right, .o_rtl .fa.fa-angle-double-left, .o_rtl .fa.fa-angle-right, .o_rtl .fa.fa-angle-left, .o_rtl .fa.fa-quote-right, .o_rtl .fa.fa-quote-left, .o_rtl .fa.fa-chevron-circle-right, .o_rtl .fa.fa-chevron-circle-left, .o_rtl .fa.fa-long-arrow-right, .o_rtl .fa.fa-long-arrow-left, .o_rtl .fa.fa-toggle-right, .o_rtl .fa.fa-toggle-left, .o_rtl .fa.fa-caret-square-o-right, .o_rtl .fa.fa-arrow-circle-o-left, .o_rtl .fa.fa-arrow-circle-o-right, .o_rtl .fa.fa-caret-square-o-left{transform: rotate(180deg);}

/* /web_tour/static/src/scss/tip.scss defined in bundle 'web.assets_common' */
 @keyframes move-left-right{0%{transform: translate(-3px, 0);}100%{transform: translate(3px, 0);}}@keyframes move-bottom-top{0%{transform: translate(0, -3px);}100%{transform: translate(0, 3px);}}.o_tooltip_parent{position: relative !important; opacity: 0.999 !important;}.o_tooltip{position: absolute !important; top: 50% !important; left: 50% !important; z-index: 1070 !important; opacity: 0 !important; width: 28px !important; height: 28px !important; margin: 0 !important; padding: 0 !important; transition: opacity 400ms ease 0ms !important;}.o_tooltip.o_animated{animation: move-bottom-top 500ms ease-in 0ms infinite alternate !important;}.o_tooltip.o_animated.right, .o_tooltip.o_animated.left{animation-name: move-left-right !important;}.o_tooltip.o_tooltip_visible{opacity: 1 !important;}.o_tooltip.o_tooltip_fixed{position: fixed !important;}.o_tooltip::before{content: ""; position: absolute; top: 0; left: auto; bottom: auto; right: 0; width: 28px; height: 28px; border: 3px solid white; border-radius: 0 50% 50% 50%; background: radial-gradient(#1693e7, #137EC6); box-shadow: 0 0 40px 2px rgba(255, 255, 255, 0.5);}.o_tooltip.top::before{transform: rotate(180deg) translateY(8.25979621px) scaleY(1.12430027) rotate(45deg);}.o_tooltip.right::before{transform: rotate(270deg) translateY(8.25979621px) scaleY(1.12430027) rotate(45deg);}.o_tooltip.bottom::before{transform: rotate(0deg) translateY(8.25979621px) scaleY(1.12430027) rotate(45deg);}.o_tooltip.left::before{transform: rotate(90deg) translateY(8.25979621px) scaleY(1.12430027) rotate(45deg);}.o_tooltip > .o_tooltip_overlay{display: none; position: absolute; top: 0; left: 0; bottom: 0; right: 0; z-index: -1;}.o_tooltip > .o_tooltip_content{overflow: hidden; direction: ltr; position: relative; padding: 7px 14px; background-color: inherit; color: transparent; visibility: hidden; line-height: 1.5; font-size: 1rem; font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; font-weight: normal;}.o_tooltip > .o_tooltip_content .o_skip_tour{display: inline-block; margin-top: 4px; cursor: pointer; color: gray;}.o_tooltip > .o_tooltip_content .o_skip_tour:hover{color: #4d4d4d;}.o_tooltip > .o_tooltip_content > p:last-child{margin-bottom: 0;}.o_tooltip.active{border: 3px solid #137EC6 !important; background-color: white !important; transition: width 150ms ease 50ms, height 150ms ease 50ms, margin 150ms ease 50ms !important;}.o_tooltip.active::before{width: 12px; height: 12px; border-color: #137EC6; border-radius: 0; background: white; box-shadow: none;}.o_tooltip.active > .o_tooltip_overlay{display: block;}.o_tooltip.active > .o_tooltip_content{color: black; visibility: visible; transition: color 0ms ease 200ms;}.o_tooltip.active.right{transform: translateX(6px) !important;}.o_tooltip.active.right::before{position: absolute; top: 5px; left: -12px; bottom: auto; right: auto; transform: translateX(50%) rotate(45deg);}.o_tooltip.active.top{transform: translateY(-6px) !important;}.o_tooltip.active.top::before{position: absolute; top: auto; left: 5px; bottom: -12px; right: auto; transform: translateY(-50%) rotate(45deg);}.o_tooltip.active.left{transform: translateX(-6px) !important;}.o_tooltip.active.left::before{position: absolute; top: 5px; left: auto; bottom: auto; right: -12px; transform: translateX(-50%) rotate(45deg);}.o_tooltip.active.bottom{transform: translateY(6px) !important;}.o_tooltip.active.bottom::before{position: absolute; top: -12px; left: 5px; bottom: auto; right: auto; transform: translateY(50%) rotate(45deg);}.o_tooltip.active.inverse.left::before, .o_tooltip.active.inverse.right::before{top: auto; bottom: 5px;}.o_tooltip.active.inverse.top::before, .o_tooltip.active.inverse.bottom::before{left: auto; right: 5px;}@media print{.o_tooltip{display: none !important;}}

/* /web_tour/static/src/scss/keyframes.scss defined in bundle 'web.assets_common' */
 

/* /odex25_web/static/src/scss/fonts.scss defined in bundle 'web.assets_common' */
 @font-face{font-family: 'Roboto'; src: url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-Thin-webfont.eot"); src: url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-Thin-webfont.eot?#iefix") format("embedded-opentype"), url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-Thin-webfont.woff") format("woff"), url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-Thin-webfont.ttf") format("truetype"), url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-Thin-webfont.svg#Roboto") format("svg"); font-weight: 100; font-style: normal;}@font-face{font-family: "Roboto-Thin"; src: url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-Thin-webfont.eot"); src: url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-Thin-webfont.eot?#iefix") format("embedded-opentype"), url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-Thin-webfont.woff") format("woff"), url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-Thin-webfont.ttf") format("truetype"), url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-Thin-webfont.svg#Roboto") format("svg");}@font-face{font-family: 'Roboto'; src: url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-ThinItalic-webfont.eot"); src: url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-ThinItalic-webfont.eot?#iefix") format("embedded-opentype"), url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-ThinItalic-webfont.woff") format("woff"), url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-ThinItalic-webfont.ttf") format("truetype"), url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-ThinItalic-webfont.svg#Roboto") format("svg"); font-weight: 100; font-style: italic;}@font-face{font-family: "Roboto-ThinItalic"; src: url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-ThinItalic-webfont.eot"); src: url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-ThinItalic-webfont.eot?#iefix") format("embedded-opentype"), url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-ThinItalic-webfont.woff") format("woff"), url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-ThinItalic-webfont.ttf") format("truetype"), url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-ThinItalic-webfont.svg#Roboto") format("svg");}@font-face{font-family: 'Roboto'; src: url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-Light-webfont.eot"); src: url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-Light-webfont.eot?#iefix") format("embedded-opentype"), url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-Light-webfont.woff") format("woff"), url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-Light-webfont.ttf") format("truetype"), url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-Light-webfont.svg#Roboto") format("svg"); font-weight: 300; font-style: normal;}@font-face{font-family: "Roboto-Light"; src: url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-Light-webfont.eot"); src: url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-Light-webfont.eot?#iefix") format("embedded-opentype"), url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-Light-webfont.woff") format("woff"), url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-Light-webfont.ttf") format("truetype"), url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-Light-webfont.svg#Roboto") format("svg");}@font-face{font-family: 'Roboto'; src: url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-LightItalic-webfont.eot"); src: url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-LightItalic-webfont.eot?#iefix") format("embedded-opentype"), url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-LightItalic-webfont.woff") format("woff"), url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-LightItalic-webfont.ttf") format("truetype"), url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-LightItalic-webfont.svg#Roboto") format("svg"); font-weight: 300; font-style: italic;}@font-face{font-family: "Roboto-LightItalic"; src: url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-LightItalic-webfont.eot"); src: url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-LightItalic-webfont.eot?#iefix") format("embedded-opentype"), url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-LightItalic-webfont.woff") format("woff"), url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-LightItalic-webfont.ttf") format("truetype"), url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-LightItalic-webfont.svg#Roboto") format("svg");}@font-face{font-family: 'Roboto'; src: url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-Regular-webfont.eot"); src: url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-Regular-webfont.eot?#iefix") format("embedded-opentype"), url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-Regular-webfont.woff") format("woff"), url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-Regular-webfont.ttf") format("truetype"), url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-Regular-webfont.svg#Roboto") format("svg"); font-weight: 400; font-style: normal;}@font-face{font-family: "Roboto-Regular"; src: url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-Regular-webfont.eot"); src: url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-Regular-webfont.eot?#iefix") format("embedded-opentype"), url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-Regular-webfont.woff") format("woff"), url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-Regular-webfont.ttf") format("truetype"), url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-Regular-webfont.svg#Roboto") format("svg");}@font-face{font-family: 'Roboto'; src: url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-RegularItalic-webfont.eot"); src: url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-RegularItalic-webfont.eot?#iefix") format("embedded-opentype"), url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-RegularItalic-webfont.woff") format("woff"), url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-RegularItalic-webfont.ttf") format("truetype"), url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-RegularItalic-webfont.svg#Roboto") format("svg"); font-weight: 400; font-style: italic;}@font-face{font-family: "Roboto-RegularItalic"; src: url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-RegularItalic-webfont.eot"); src: url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-RegularItalic-webfont.eot?#iefix") format("embedded-opentype"), url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-RegularItalic-webfont.woff") format("woff"), url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-RegularItalic-webfont.ttf") format("truetype"), url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-RegularItalic-webfont.svg#Roboto") format("svg");}@font-face{font-family: 'Roboto'; src: url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-Medium-webfont.eot"); src: url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-Medium-webfont.eot?#iefix") format("embedded-opentype"), url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-Medium-webfont.woff") format("woff"), url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-Medium-webfont.ttf") format("truetype"), url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-Medium-webfont.svg#Roboto") format("svg"); font-weight: 500; font-style: normal;}@font-face{font-family: "Roboto-Medium"; src: url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-Medium-webfont.eot"); src: url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-Medium-webfont.eot?#iefix") format("embedded-opentype"), url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-Medium-webfont.woff") format("woff"), url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-Medium-webfont.ttf") format("truetype"), url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-Medium-webfont.svg#Roboto") format("svg");}@font-face{font-family: 'Roboto'; src: url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-MediumItalic-webfont.eot"); src: url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-MediumItalic-webfont.eot?#iefix") format("embedded-opentype"), url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-MediumItalic-webfont.woff") format("woff"), url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-MediumItalic-webfont.ttf") format("truetype"), url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-MediumItalic-webfont.svg#Roboto") format("svg"); font-weight: 500; font-style: italic;}@font-face{font-family: "Roboto-MediumItalic"; src: url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-MediumItalic-webfont.eot"); src: url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-MediumItalic-webfont.eot?#iefix") format("embedded-opentype"), url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-MediumItalic-webfont.woff") format("woff"), url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-MediumItalic-webfont.ttf") format("truetype"), url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-MediumItalic-webfont.svg#Roboto") format("svg");}@font-face{font-family: 'Roboto'; src: url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-Bold-webfont.eot"); src: url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-Bold-webfont.eot?#iefix") format("embedded-opentype"), url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-Bold-webfont.woff") format("woff"), url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-Bold-webfont.ttf") format("truetype"), url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-Bold-webfont.svg#Roboto") format("svg"); font-weight: 700; font-style: normal;}@font-face{font-family: "Roboto-Bold"; src: url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-Bold-webfont.eot"); src: url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-Bold-webfont.eot?#iefix") format("embedded-opentype"), url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-Bold-webfont.woff") format("woff"), url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-Bold-webfont.ttf") format("truetype"), url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-Bold-webfont.svg#Roboto") format("svg");}@font-face{font-family: 'Roboto'; src: url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-BoldItalic-webfont.eot"); src: url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-BoldItalic-webfont.eot?#iefix") format("embedded-opentype"), url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-BoldItalic-webfont.woff") format("woff"), url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-BoldItalic-webfont.ttf") format("truetype"), url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-BoldItalic-webfont.svg#Roboto") format("svg"); font-weight: 700; font-style: italic;}@font-face{font-family: "Roboto-BoldItalic"; src: url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-BoldItalic-webfont.eot"); src: url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-BoldItalic-webfont.eot?#iefix") format("embedded-opentype"), url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-BoldItalic-webfont.woff") format("woff"), url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-BoldItalic-webfont.ttf") format("truetype"), url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-BoldItalic-webfont.svg#Roboto") format("svg");}@font-face{font-family: 'Roboto'; src: url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-Black-webfont.eot"); src: url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-Black-webfont.eot?#iefix") format("embedded-opentype"), url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-Black-webfont.woff") format("woff"), url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-Black-webfont.ttf") format("truetype"), url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-Black-webfont.svg#Roboto") format("svg"); font-weight: 900; font-style: normal;}@font-face{font-family: "Roboto-Black"; src: url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-Black-webfont.eot"); src: url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-Black-webfont.eot?#iefix") format("embedded-opentype"), url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-Black-webfont.woff") format("woff"), url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-Black-webfont.ttf") format("truetype"), url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-Black-webfont.svg#Roboto") format("svg");}@font-face{font-family: 'Roboto'; src: url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-BlackItalic-webfont.eot"); src: url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-BlackItalic-webfont.eot?#iefix") format("embedded-opentype"), url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-BlackItalic-webfont.woff") format("woff"), url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-BlackItalic-webfont.ttf") format("truetype"), url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-BlackItalic-webfont.svg#Roboto") format("svg"); font-weight: 900; font-style: italic;}@font-face{font-family: "Roboto-BlackItalic"; src: url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-BlackItalic-webfont.eot"); src: url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-BlackItalic-webfont.eot?#iefix") format("embedded-opentype"), url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-BlackItalic-webfont.woff") format("woff"), url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-BlackItalic-webfont.ttf") format("truetype"), url("/odex25_web/static/src/scss/../fonts/Roboto/Roboto-BlackItalic-webfont.svg#Roboto") format("svg");}

/* /stock/static/src/scss/stock_traceability_report.scss defined in bundle 'web.assets_common' */
 .o_stock_reports_body_print{background-color: white; color: black;}.o_stock_reports_body_print .o_stock_reports_level0{border-width: 1px; border-left-style: hidden; border-right-style: hidden; font-weight: bold; border-top-style: solid; border-bottom-style: groove;}.o_main_content .o_stock_reports_page{position: absolute;}.o_stock_reports_page{background-color: white;}.o_stock_reports_page.o_stock_reports_no_print{margin: 16px auto; padding-top: 24px; padding-right: 16px; padding-bottom: 24px; padding-left: 16px;}.o_stock_reports_page.o_stock_reports_no_print .o_stock_reports_level0{border-width: 1px; border-left-style: hidden; border-right-style: hidden; font-weight: normal; border-top-style: solid; border-bottom-style: groove;}.o_stock_reports_page.o_stock_reports_no_print .o_stock_reports_table{white-space: nowrap; margin-top: 30px;}.o_stock_reports_page.o_stock_reports_no_print .o_stock_reports_table thead{display: table-row-group;}.o_stock_reports_page.o_stock_reports_no_print .o_report_line_header{text-align: left; padding-left: 10px;}.o_stock_reports_page.o_stock_reports_no_print .o_report_header{border-top-style: solid; border-top-style: groove; border-bottom-style: groove; border-width: 2px;}.o_stock_reports_page .o_stock_reports_unfolded{display: inline-block;}.o_stock_reports_page .o_stock_reports_nofoldable{margin-left: 17px;}.o_stock_reports_page a.o_stock_report_lot_action{cursor: pointer;}.o_stock_reports_page .o_stock_reports_unfolded td + td{visibility: hidden;}.o_stock_reports_page div.o_stock_reports_web_action, .o_stock_reports_page span.o_stock_reports_web_action, .o_stock_reports_page i.fa, .o_stock_reports_page span.o_stock_reports_unfoldable, .o_stock_reports_page span.o_stock_reports_foldable, .o_stock_reports_page a.o_stock_reports_web_action{cursor: pointer;}.o_stock_reports_page .o_stock_reports_caret_icon{margin-left: -3px;}.o_stock_reports_page th{border-bottom: thin groove;}.o_stock_reports_page .o_stock_reports_level1{border-width: 2px; border-left-style: hidden; border-right-style: hidden; font-weight: inherit; border-top-style: hidden; border-bottom-style: solid;}.o_stock_reports_page .o_stock_reports_level2{border-width: 1px; border-left-style: hidden; border-right-style: hidden; font-weight: inherit; border-top-style: solid; border-bottom-style: solid;}.o_stock_reports_page .o_stock_reports_level2 > td > span:last-child{margin-left: 25px;}.o_stock_reports_page .o_stock_reports_default_style{border-width: 0px; border-left-style: hidden; border-right-style: hidden; font-weight: inherit; border-top-style: solid; border-bottom-style: solid;}.o_stock_reports_page .o_stock_reports_default_style > td > span:last-child{margin-left: 50px;}

/* /odex_backend_theme/static/src/scss/web_tour.scss defined in bundle 'web.assets_common' */
 .o_tooltip::before{content: ""; border-radius: 0 50% 50% 50%; background: radial-gradient(#1693e7, #137EC6); box-shadow: 0 0 40px 2px rgba(255, 255, 255, 0.5);}.o_tooltip.active{background-color: white !important;}.o_tooltip.active::before{border-color: #137EC6 !important; border-radius: 0; background: white; box-shadow: none;}.o_hr_attendance_kiosk_backdrop{background-color: #137EC6 !important;}.o_ChatWindowHeader{background-color: #137EC6 !important;}.o_hr_attendance_kiosk_mode .o_hr_attendance_user_badge{background: linear-gradient(to right bottom, #137EC6, #1F77B4) !important;}.o_switch{background-color: #137EC6 !important;}.o_main_navbar > ul > li.o_extra_menu_items.show > ul > li > a.dropdown-toggle{background-color: #3E5D7F !important;}.btn .o_arrow_button .btn-secondary .disabled{background-color: #3E5D7F !important;}.o_view_nocontent_custom_face_container{text-align: center; display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column; align-items: center; justify-content: center;}.custom_image_face{width: 150px; height: 150px; border-radius: 50%; margin-bottom: 10px;}.o_view_nocontent_custom_face_text{font-size: 16px; font-weight: bold; color: solid black;}.custom_statusbar .o_statusbar_status button.btn-primary{background-color: #137EC6 !important; color: white !important;}.custom_statusbar .o_statusbar_status button.btn-secondary{color: #137EC6 !important;}.o_form_view .o_form_statusbar > .o_statusbar_status > .o_arrow_button.disabled{color: #137EC6 !important;}.datepicker .datepicker-days thead tr:first-child th, .datepicker .datepicker-months thead tr:first-child th, .datepicker .datepicker-years thead tr:first-child th, .datepicker .datepicker-decades thead tr:first-child th, .datepicker .datepicker-centuries thead tr:first-child th{background-color: #137EC6 !important; color: white !important;}.datepicker .datepicker-days thead tr:first-child th.today{background-color: #137EC6 !important;}.datepicker .datepicker-switch, .datepicker .prev, .datepicker .next{background-color: #137EC6 !important; color: white !important;}.datepicker .datepicker-days thead tr:first-child th:hover, .datepicker .datepicker-months thead tr:first-child th:hover, .datepicker .datepicker-years thead tr:first-child th:hover, .datepicker .datepicker-decades thead tr:first-child th:hover, .datepicker .datepicker-centuries thead tr:first-child th:hover{background-color: #137EC6 !important;}

/* /odex_backend_theme/static/src/scss/ui.scss defined in bundle 'web.assets_common' */
 #oe_main_menu_navbar a:hover, #oe_main_menu_navbar a:focus{text-decoration: none; background-color: #137DC5 !important;}