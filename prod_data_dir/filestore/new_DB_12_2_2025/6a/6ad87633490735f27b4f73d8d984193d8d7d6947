
/* /web/static/src/js/main.js defined in bundle 'web.assets_backend_prod_only' */
odoo.define('web.web_client',function(require){"use strict";const AbstractService=require('web.AbstractService');const env=require('web.env');const session=require("web.session");const WebClient=require('web.WebClient');owl.config.mode=env.isDebug()?"dev":"prod";owl.Component.env=env;AbstractService.prototype.deployServices(env);const webClient=new WebClient();async function startWebClient(){await session.is_bound;env.qweb.addTemplates(session.owlTemplates);await owl.utils.whenReady();webClient.setElement($(document.body));webClient.start();}
startWebClient();return webClient;});;

/* /mail/static/src/js/main.js defined in bundle 'web.assets_backend_prod_only' */
odoo.define('mail/static/src/js/main.js',function(require){'use strict';const ModelManager=require('mail/static/src/model/model_manager.js');const env=require('web.commonEnv');const{Store}=owl;const{EventBus}=owl.core;async function createMessaging(){await new Promise(resolve=>{window.addEventListener('load',resolve);});await new Promise(resolve=>setTimeout(resolve));await env.session.is_bound;env.modelManager.start();env.messaging=env.models['mail.messaging'].create();}
const store=new Store({env,state:{messagingRevNumber:0,},});env.models={};Object.assign(env,{autofetchPartnerImStatus:true,destroyMessaging(){if(env.modelManager){env.modelManager.deleteAll();env.messaging=undefined;}},disableAnimation:false,isMessagingInitialized(){if(!this.messaging){return false;}
return this.messaging.isInitialized;},isQUnitTest:false,loadingBaseDelayDuration:400,messaging:undefined,messagingBus:new EventBus(),messagingCreatedPromise:createMessaging(),modelManager:new ModelManager(env),store,});env.bus.on('hide_home_menu',null,()=>env.messagingBus.trigger('hide_home_menu'));env.bus.on('show_home_menu',null,()=>env.messagingBus.trigger('show_home_menu'));env.bus.on('will_hide_home_menu',null,()=>env.messagingBus.trigger('will_hide_home_menu'));env.bus.on('will_show_home_menu',null,()=>env.messagingBus.trigger('will_show_home_menu'));env.messagingCreatedPromise.then(()=>env.messaging.start());});