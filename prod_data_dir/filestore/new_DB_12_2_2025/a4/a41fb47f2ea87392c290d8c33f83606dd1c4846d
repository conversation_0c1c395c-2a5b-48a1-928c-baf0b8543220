
/* /web_editor/static/lib/summernote/src/css/summernote.css defined in bundle 'web_editor.assets_summernote' */
 .note-editor{border: 1px solid #a9a9a9; position: relative;}.note-editor .note-dropzone{position: absolute; display: none; z-index: 100; color: lightskyblue; background-color: white; opacity: 0.95; pointer-event: none;}.note-editor .note-dropzone .note-dropzone-message{display: table-cell; vertical-align: middle; text-align: center; font-size: 28px; font-weight: bold;}.note-editor .note-dropzone.hover{color: #098ddf;}.note-editor.dragover .note-dropzone{display: table;}.note-editor.codeview .note-editing-area .note-editable{display: none;}.note-editor.codeview .note-editing-area .note-codable{display: block;}.note-editor.fullscreen{position: fixed; top: 0; left: 0; width: 100%; z-index: 1050;}.note-editor.fullscreen .note-editable{background-color: white;}.note-editor.fullscreen .note-resizebar{display: none;}.note-editor .note-editing-area{position: relative; overflow: hidden;}.note-editor .note-editing-area .note-editable{background-color: #fff; color: #000; padding: 10px; overflow: auto; outline: none;}.note-editor .note-editing-area .note-editable[contenteditable=true]:empty:not(:focus):before{content: attr(data-placeholder);}.note-editor .note-editing-area .note-editable[contenteditable="false"]{background-color: #e5e5e5;}.note-editor .note-editing-area .note-codable{display: none; width: 100%; padding: 10px; border: none; box-shadow: none; font-family: Menlo, Monaco, monospace, sans-serif; font-size: 14px; color: #ccc; background-color: #222; resize: none; box-sizing: border-box; border-radius: 0; margin-bottom: 0;}.note-editor .note-statusbar{background-color: #f5f5f5;}.note-editor .note-statusbar .note-resizebar{padding-top: 1px; height: 8px; width: 100%; cursor: ns-resize;}.note-editor .note-statusbar .note-resizebar .note-icon-bar{width: 20px; margin: 1px auto; border-top: 1px solid #a9a9a9;}.note-air-editor{outline: none;}.note-popover .popover{max-width: none;}.note-popover .popover .popover-body a{display: inline-block; max-width: 200px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; vertical-align: middle;}.note-popover .popover .arrow{left: 20px;}.note-popover .popover .popover-body, .panel-heading.note-toolbar{margin: 0; padding: 0 0 5px 5px;}.note-popover .popover .popover-body > .btn-group, .panel-heading.note-toolbar > .btn-group{margin-top: 5px; margin-left: 0; margin-right: 5px;}.note-popover .popover .popover-body .btn-group .note-table, .panel-heading.note-toolbar .btn-group .note-table{min-width: 0; padding: 5px;}.note-popover .popover .popover-body .btn-group .note-table .note-dimension-picker, .panel-heading.note-toolbar .btn-group .note-table .note-dimension-picker{font-size: 18px;}.note-popover .popover .popover-body .btn-group .note-table .note-dimension-picker .note-dimension-picker-mousecatcher, .panel-heading.note-toolbar .btn-group .note-table .note-dimension-picker .note-dimension-picker-mousecatcher{position: absolute !important; z-index: 3; width: 10em; height: 10em; cursor: pointer;}.note-popover .popover .popover-body .btn-group .note-table .note-dimension-picker .note-dimension-picker-unhighlighted, .panel-heading.note-toolbar .btn-group .note-table .note-dimension-picker .note-dimension-picker-unhighlighted{position: relative !important; z-index: 1; width: 5em; height: 5em; background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASAgMAAAAroGbEAAAACVBMVEUAAIj4+Pjp6ekKlAqjAAAAAXRSTlMAQObYZgAAAAFiS0dEAIgFHUgAAAAJcEhZcwAACxMAAAsTAQCanBgAAAAHdElNRQfYAR0BKhmnaJzPAAAAG0lEQVQI12NgAAOtVatWMTCohoaGUY+EmIkEAEruEzK2J7tvAAAAAElFTkSuQmCC') repeat;}.note-popover .popover .popover-body .btn-group .note-table .note-dimension-picker .note-dimension-picker-highlighted, .panel-heading.note-toolbar .btn-group .note-table .note-dimension-picker .note-dimension-picker-highlighted{position: absolute !important; z-index: 2; width: 1em; height: 1em; background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASAgMAAAAroGbEAAAACVBMVEUAAIjd6vvD2f9LKLW+AAAAAXRSTlMAQObYZgAAAAFiS0dEAIgFHUgAAAAJcEhZcwAACxMAAAsTAQCanBgAAAAHdElNRQfYAR0BKwNDEVT0AAAAG0lEQVQI12NgAAOtVatWMTCohoaGUY+EmIkEAEruEzK2J7tvAAAAAElFTkSuQmCC') repeat;}.note-popover .popover .popover-body .note-style h1, .panel-heading.note-toolbar .note-style h1, .note-popover .popover .popover-body .note-style h2, .panel-heading.note-toolbar .note-style h2, .note-popover .popover .popover-body .note-style h3, .panel-heading.note-toolbar .note-style h3, .note-popover .popover .popover-body .note-style h4, .panel-heading.note-toolbar .note-style h4, .note-popover .popover .popover-body .note-style h5, .panel-heading.note-toolbar .note-style h5, .note-popover .popover .popover-body .note-style h6, .panel-heading.note-toolbar .note-style h6, .note-popover .popover .popover-body .note-style blockquote, .panel-heading.note-toolbar .note-style blockquote{margin: 0;}.note-popover .popover .popover-body .note-color .dropdown-toggle, .panel-heading.note-toolbar .note-color .dropdown-toggle{width: 20px; padding-left: 5px;}.note-popover .popover .popover-body .note-color .dropdown-menu, .panel-heading.note-toolbar .note-color .dropdown-menu{min-width: 340px;}.note-popover .popover .popover-body .note-color .dropdown-menu .btn-group, .panel-heading.note-toolbar .note-color .dropdown-menu .btn-group{margin: 0;}.note-popover .popover .popover-body .note-color .dropdown-menu .btn-group:first-child, .panel-heading.note-toolbar .note-color .dropdown-menu .btn-group:first-child{margin: 0 5px;}.note-popover .popover .popover-body .note-color .dropdown-menu .btn-group .note-palette-title, .panel-heading.note-toolbar .note-color .dropdown-menu .btn-group .note-palette-title{font-size: 12px; margin: 2px 7px; text-align: center; border-bottom: 1px solid #eee;}.note-popover .popover .popover-body .note-color .dropdown-menu .btn-group .note-color-reset, .panel-heading.note-toolbar .note-color .dropdown-menu .btn-group .note-color-reset{font-size: 11px; margin: 3px; padding: 0 3px; cursor: pointer; border-radius: 5px;}.note-popover .popover .popover-body .note-color .dropdown-menu .btn-group .note-color-row, .panel-heading.note-toolbar .note-color .dropdown-menu .btn-group .note-color-row{height: 20px;}.note-popover .popover .popover-body .note-color .dropdown-menu .btn-group .note-color-reset:hover, .panel-heading.note-toolbar .note-color .dropdown-menu .btn-group .note-color-reset:hover{background: #eee;}.note-popover .popover .popover-body .note-para .dropdown-menu, .panel-heading.note-toolbar .note-para .dropdown-menu{min-width: 216px; padding: 5px;}.note-popover .popover .popover-body .note-para .dropdown-menu > div:first-child, .panel-heading.note-toolbar .note-para .dropdown-menu > div:first-child{margin-right: 5px;}.note-popover .popover .popover-body .dropdown-menu, .panel-heading.note-toolbar .dropdown-menu{min-width: 90px;}.note-popover .popover .popover-body .dropdown-menu.right, .panel-heading.note-toolbar .dropdown-menu.right{right: 0; left: auto;}.note-popover .popover .popover-body .dropdown-menu.right::before, .panel-heading.note-toolbar .dropdown-menu.right::before{right: 9px; left: auto !important;}.note-popover .popover .popover-body .dropdown-menu.right::after, .panel-heading.note-toolbar .dropdown-menu.right::after{right: 10px; left: auto !important;}.note-popover .popover .popover-body .dropdown-menu.note-check li a i, .panel-heading.note-toolbar .dropdown-menu.note-check li a i{color: deepskyblue; visibility: hidden;}.note-popover .popover .popover-body .dropdown-menu.note-check li a.checked i, .panel-heading.note-toolbar .dropdown-menu.note-check li a.checked i{visibility: visible;}.note-popover .popover .popover-body .note-fontsize-10, .panel-heading.note-toolbar .note-fontsize-10{font-size: 10px;}.note-popover .popover .popover-body .note-color-palette, .panel-heading.note-toolbar .note-color-palette{line-height: 1;}.note-popover .popover .popover-body .note-color-palette div .note-color-btn, .panel-heading.note-toolbar .note-color-palette div .note-color-btn{width: 20px; height: 20px; padding: 0; margin: 0; border: 1px solid #fff;}.note-popover .popover .popover-body .note-color-palette div .note-color-btn:hover, .panel-heading.note-toolbar .note-color-palette div .note-color-btn:hover{border: 1px solid #000;}.note-dialog > div{display: none;}.note-dialog .form-group{margin-left: 0; margin-right: 0;}.note-dialog .note-modal-form{margin: 0;}.note-dialog .note-image-dialog .note-dropzone{min-height: 100px; font-size: 30px; line-height: 4; color: lightgray; text-align: center; border: 4px dashed lightgray; margin-bottom: 10px;}.note-dialog .note-help-dialog{font-size: 12px; color: #ccc; background-color: #222 !important; opacity: 0.9; background: transparent; border: none;}.note-dialog .note-help-dialog .modal-content{background: transparent; border: 1px solid white; box-shadow: none; border-radius: 5px;}.note-dialog .note-help-dialog a{font-size: 12px; color: white;}.note-dialog .note-help-dialog .title{color: white; font-size: 14px; font-weight: bold; padding-bottom: 5px; margin-bottom: 10px; border-bottom: white 1px solid;}.note-dialog .note-help-dialog .modal-close{font-size: 14px; color: #dd0; cursor: pointer;}.note-dialog .note-help-dialog .text-center{margin: 10px 0 0;}.note-dialog .note-help-dialog .note-shortcut{padding-top: 8px; padding-bottom: 8px;}.note-dialog .note-help-dialog .note-shortcut-row{margin-right: -5px; margin-left: -5px;}.note-dialog .note-help-dialog .note-shortcut-col{padding-right: 5px; padding-left: 5px;}.note-dialog .note-help-dialog .note-shortcut-title{font-size: 13px; font-weight: bold; color: #dd0;}.note-dialog .note-help-dialog .note-shortcut-key{font-family: "Courier New"; color: #dd0; text-align: right;}.note-handle{}.note-handle .note-control-selection{position: absolute; display: none; border: 1px solid black;}.note-handle .note-control-selection > div{position: absolute;}.note-handle .note-control-selection .note-control-selection-bg{width: 100%; height: 100%; background-color: black; opacity: 0.3;}.note-handle .note-control-selection .note-control-handle{width: 7px; height: 7px; border: 1px solid black;}.note-handle .note-control-selection .note-control-holder{width: 7px; height: 7px; border: 1px solid black;}.note-handle .note-control-selection .note-control-sizing{width: 7px; height: 7px; border: 1px solid black; background-color: white;}.note-handle .note-control-selection .note-control-nw{top: -5px; left: -5px; border-right: none; border-bottom: none;}.note-handle .note-control-selection .note-control-ne{top: -5px; right: -5px; border-bottom: none; border-left: none;}.note-handle .note-control-selection .note-control-sw{bottom: -5px; left: -5px; border-top: none; border-right: none;}.note-handle .note-control-selection .note-control-se{right: -5px; bottom: -5px; cursor: se-resize;}.note-handle .note-control-selection .note-control-se.note-control-holder{cursor: default; border-top: none; border-left: none;}.note-handle .note-control-selection .note-control-selection-info{right: 0; bottom: 0; padding: 5px; margin: 5px; color: white; background-color: black; font-size: 12px; border-radius: 5px; opacity: 0.7;}