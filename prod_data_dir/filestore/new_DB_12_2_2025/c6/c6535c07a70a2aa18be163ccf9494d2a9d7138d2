@import url("https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i&display=swap");
@import url("https://fonts.googleapis.com/css?family=Open+Sans:300,300i,400,400i,700,700i&display=swap");
@import url("https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,300i,400,400i,700,700i&display=swap");
@import url("https://fonts.googleapis.com/css?family=Raleway:300,300i,400,400i,700,700i&display=swap");
@import url("https://fonts.googleapis.com/css?family=Noto+Serif:300,300i,400,400i,700,700i&display=swap");
@import url("https://fonts.googleapis.com/css?family=Arvo:300,300i,400,400i,700,700i&display=swap");

/* /web/static/lib/bootstrap/scss/_functions.scss defined in bundle 'website.assets_wysiwyg' */
 

/* /web/static/lib/bootstrap/scss/_mixins.scss defined in bundle 'website.assets_wysiwyg' */
 

/* /web/static/src/scss/bs_mixins_overrides.scss defined in bundle 'website.assets_wysiwyg' */
 

/* /web/static/src/scss/utils.scss defined in bundle 'website.assets_wysiwyg' */
 

/* /web/static/src/scss/primary_variables.scss defined in bundle 'website.assets_wysiwyg' */
 

/* /odex25_web/static/src/scss/primary_variables.scss defined in bundle 'website.assets_wysiwyg' */
 

/* /odex_backend_theme/static/src/scss/primary_variables.scss defined in bundle 'website.assets_wysiwyg' */
 

/* /web_editor/static/src/scss/web_editor.variables.scss defined in bundle 'website.assets_wysiwyg' */
 

/* /mail/static/src/scss/variables.scss defined in bundle 'website.assets_wysiwyg' */
 

/* /hr_org_chart/static/src/scss/variables.scss defined in bundle 'website.assets_wysiwyg' */
 

/* /portal/static/src/scss/primary_variables.scss defined in bundle 'website.assets_wysiwyg' */
 

/* /website/static/src/scss/primary_variables.scss defined in bundle 'website.assets_wysiwyg' */
 

/* /website/static/src/scss/options/user_values.scss defined in bundle 'website.assets_wysiwyg' */
 

/* /website/static/src/scss/options/colors/user_color_palette.custom.web.assets_common.scss defined in bundle 'website.assets_wysiwyg' */
 

/* /website/static/src/scss/options/colors/user_theme_color_palette.scss defined in bundle 'website.assets_wysiwyg' */
 

/* /website/static/src/snippets/s_badge/000_variables.scss defined in bundle 'website.assets_wysiwyg' */
 

/* /website/static/src/snippets/s_product_list/000_variables.scss defined in bundle 'website.assets_wysiwyg' */
 

/* /website_sale/static/src/scss/primary_variables.scss defined in bundle 'website.assets_wysiwyg' */
 

/* /account/static/src/scss/variables.scss defined in bundle 'website.assets_wysiwyg' */
       @keyframes animate-red{0%{color: red;}100%{color: inherit;}}.animate{animation: animate-red 1s ease;}

/* /website/static/src/scss/secondary_variables.scss defined in bundle 'website.assets_wysiwyg' */
 

/* /web_editor/static/src/scss/secondary_variables.scss defined in bundle 'website.assets_wysiwyg' */
 

/* /odex25_web/static/src/scss/secondary_variables.scss defined in bundle 'website.assets_wysiwyg' */
 

/* /web/static/src/scss/secondary_variables.scss defined in bundle 'website.assets_wysiwyg' */
 

/* /web_editor/static/src/scss/bootstrap_overridden.scss defined in bundle 'website.assets_wysiwyg' */
 

/* /website_sale/static/src/scss/website_sale.editor.scss defined in bundle 'website.assets_wysiwyg' */
 .o_wsale_soptions_menu_sizes table{margin: auto;}.o_wsale_soptions_menu_sizes table td{margin: 0; padding: 0; width: 40px; height: 40px; border: 1px #dddddd solid; cursor: pointer;}.o_wsale_soptions_menu_sizes table td.selected{background-color: #B1D4F1;}.o_wsale_soptions_menu_sizes table.oe_hover td.selected{background-color: transparent;}.o_wsale_soptions_menu_sizes table.oe_hover td.select{background-color: #B1D4F1;}.o_wsale_color_preview{width: 1em; height: 1em; border: 1px solid white; display: inline-block; vertical-align: middle; border-radius: 50%;}.oe_drop_zone + .s_wsale_products_recently_viewed{display: block !important;}

/* /web/static/lib/bootstrap/scss/_variables.scss defined in bundle 'website.assets_wysiwyg' */
 

/* /website/static/src/scss/website.wysiwyg.scss defined in bundle 'website.assets_wysiwyg' */
 :root{--o-we-toolbar-height: 46px; --o-palette-1-o-color-1: #3aadaa; --o-palette-1-o-color-2: #407599; --o-palette-1-o-color-3: #F6F6F6; --o-palette-1-o-color-4: #FFFFFF; --o-palette-1-o-color-5: #383E45; --o-palette-2-o-color-1: #337ab7; --o-palette-2-o-color-2: #e9ecef; --o-palette-2-o-color-3: #F8F9FA; --o-palette-2-o-color-4: #FFFFFF; --o-palette-2-o-color-5: #343a40; --o-palette-2-menu: 2; --o-palette-2-footer: 2; --o-palette-2-copyright: 5; --o-palette-3-o-color-1: #984c46; --o-palette-3-o-color-2: #23323b; --o-palette-3-o-color-3: #eceae4; --o-palette-3-o-color-4: #FFFFFF; --o-palette-3-o-color-5: #16121f; --o-palette-3-menu: 3; --o-palette-3-footer: 3; --o-palette-4-o-color-1: #B99932; --o-palette-4-o-color-2: #DED1C1; --o-palette-4-o-color-3: #F5F5F5; --o-palette-4-o-color-4: #FFFFFF; --o-palette-4-o-color-5: #373737; --o-palette-4-menu: 5; --o-palette-4-copyright: 4; --o-palette-5-o-color-1: #f8882f; --o-palette-5-o-color-2: #6a7c8f; --o-palette-5-o-color-3: #fdf8ef; --o-palette-5-o-color-4: #FFFFFF; --o-palette-5-o-color-5: #212c39; --o-palette-6-o-color-1: #6E7993; --o-palette-6-o-color-2: #96848C; --o-palette-6-o-color-3: #8F9AA2; --o-palette-6-o-color-4: #D5D5D5; --o-palette-6-o-color-5: #313347; --o-palette-6-menu: 5; --o-palette-7-o-color-1: #F7CF41; --o-palette-7-o-color-2: #1A2930; --o-palette-7-o-color-3: #989898; --o-palette-7-o-color-4: #FFFFFF; --o-palette-7-o-color-5: #0B1612; --o-palette-7-menu: 3; --o-palette-7-footer: 3; --o-palette-8-o-color-1: #45859A; --o-palette-8-o-color-2: #B57D4D; --o-palette-8-o-color-3: #F5F5F5; --o-palette-8-o-color-4: #FFFFFF; --o-palette-8-o-color-5: #10273C; --o-palette-8-menu: 2; --o-palette-8-footer: 2; --o-palette-8-copyright: 5; --o-palette-9-o-color-1: #1a547a; --o-palette-9-o-color-2: #ddc76a; --o-palette-9-o-color-3: #D6E6F1; --o-palette-9-o-color-4: #FFFFFF; --o-palette-9-o-color-5: #2b3442; --o-palette-9-o-cc5-link: 'o-color-4'; --o-palette-9-o-cc5-text: #9b9ba0; --o-palette-9-menu: 5; --o-palette-9-footer: 5; --o-palette-9-copyright: 3; --o-palette-10-o-color-1: #763240; --o-palette-10-o-color-2: #C19F7F; --o-palette-10-o-color-3: #FFFFFF; --o-palette-10-o-color-4: #EAEAEA; --o-palette-10-o-color-5: #2F2F2F; --o-palette-10-o-cc4-headings: 'o-color-3'; --o-palette-10-o-cc4-link: 'o-color-3'; --o-palette-10-o-cc4-text: rgba(255, 255, 255, 0.8); --o-palette-10-o-cc5-headings: 'o-color-3'; --o-palette-10-o-cc5-link: 'o-color-3'; --o-palette-10-o-cc5-text: rgba(255, 255, 255, 0.8); --o-palette-10-footer: 1; --o-palette-10-copyright: 4; --o-palette-11-o-color-1: #4DC5C1; --o-palette-11-o-color-2: #EC576B; --o-palette-11-o-color-3: #E5E337; --o-palette-11-o-color-4: #FFFFFF; --o-palette-11-o-color-5: #000000; --o-palette-11-menu: 5; --o-palette-12-o-color-1: #b56355; --o-palette-12-o-color-2: #6ba17a; --o-palette-12-o-color-3: #ebe6ea; --o-palette-12-o-color-4: #FFFFFF; --o-palette-12-o-color-5: #343733; --o-palette-12-footer: 2; --o-palette-13-o-color-1: #01ACAB; --o-palette-13-o-color-2: #FEDC3D; --o-palette-13-o-color-3: #FAE8E0; --o-palette-13-o-color-4: #FFFFFF; --o-palette-13-o-color-5: #000000; --o-palette-13-footer: 1; --o-palette-14-o-color-1: #926190; --o-palette-14-o-color-2: #F3E0CD; --o-palette-14-o-color-3: #F9EFE9; --o-palette-14-o-color-4: #FFFFFF; --o-palette-14-o-color-5: #291528; --o-palette-14-o-cc4-headings: 'o-color-4'; --o-palette-14-o-cc4-link: 'o-color-4'; --o-palette-14-o-cc4-text: rgba(255, 255, 255, 0.8); --o-palette-14-o-cc5-headings: 'o-color-4'; --o-palette-14-o-cc5-link: 'o-color-4'; --o-palette-14-o-cc5-text: rgba(255, 255, 255, 0.6); --o-palette-15-o-color-1: #478FA2; --o-palette-15-o-color-2: #CECECE; --o-palette-15-o-color-3: #E8E9E9; --o-palette-15-o-color-4: #FFFFFF; --o-palette-15-o-color-5: #173F54; --o-palette-15-footer: 1; --o-palette-15-copyright: 1; --o-palette-16-o-color-1: #3CC37C; --o-palette-16-o-color-2: #E9C893; --o-palette-16-o-color-3: #F5F5F5; --o-palette-16-o-color-4: #FFFFFF; --o-palette-16-o-color-5: #1F3A2A; --o-palette-16-footer: 1; --o-palette-16-copyright: 5; --o-palette-17-o-color-1: #01524B; --o-palette-17-o-color-2: #1993A3; --o-palette-17-o-color-3: #dddde6; --o-palette-17-o-color-4: #FFFFFF; --o-palette-17-o-color-5: #011D1B; --o-palette-17-o-cc4-btn-primary: 'o-color-4'; --o-palette-17-o-cc4-link: 'o-color-4'; --o-palette-17-o-cc4-text: rgba(255, 255, 255, 0.8); --o-palette-17-o-cc5-btn-primary: 'o-color-4'; --o-palette-17-o-cc5-link: 'o-color-4'; --o-palette-17-o-cc5-text: rgba(255, 255, 255, 0.6); --o-palette-17-footer: 2; --o-palette-17-copyright: 5; --o-palette-18-o-color-1: #464D77; --o-palette-18-o-color-2: #36827f; --o-palette-18-o-color-3: #f2f0ec; --o-palette-18-o-color-4: #FFFFFF; --o-palette-18-o-color-5: #22263c; --o-palette-18-o-cc4-btn-primary: 'o-color-4'; --o-palette-18-o-cc4-link: 'o-color-4'; --o-palette-18-o-cc4-text: rgba(255, 255, 255, 0.8); --o-palette-18-o-cc5-btn-primary: 'o-color-4'; --o-palette-18-o-cc5-btn-secondary: #d6d4d0; --o-palette-18-o-cc5-link: 'o-color-4'; --o-palette-18-o-cc5-text: rgba(255, 255, 255, 0.6); --o-palette-18-menu: 2; --o-palette-18-footer: 2; --o-palette-18-copyright: 5; --o-palette-19-o-color-1: #4717f6; --o-palette-19-o-color-2: #A43ACB; --o-palette-19-o-color-3: #FAFAFA; --o-palette-19-o-color-4: #FFFFFF; --o-palette-19-o-color-5: #0F0A19; --o-palette-19-menu: 5; --o-palette-19-footer: 5;}.o_we_website_top_actions{position: absolute; top: 0; left: auto; bottom: auto; right: 0; z-index: 1037; display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-pack: end; justify-content: flex-end; width: 291px; height: 46px; background-color: #141217;}.o_we_website_top_actions .btn-group, .o_we_website_top_actions .btn{height: 100%;}.o_we_website_top_actions .btn{border: none; border-radius: 0; padding: 0.375rem 0.75rem; font-size: 13px; font-family: Roboto, "Montserrat", "Segoe UI", "Helvetica Neue", Helvetica, Arial, sans-serif; font-weight: 400; line-height: 1;}.o_we_website_top_actions .btn.btn-primary{color: #FFFFFF; background-color: #00A09D; border-color: #00A09D;}.o_we_website_top_actions .btn.btn-primary:hover{color: #FFFFFF; background-color: #007a77; border-color: #006d6b;}.o_we_website_top_actions .btn.btn-primary:focus, .o_we_website_top_actions .btn.btn-primary.focus{box-shadow: 0 0 0 0.2rem rgba(38, 174, 172, 0.5);}.o_we_website_top_actions .btn.btn-primary.disabled, .o_we_website_top_actions .btn.btn-primary:disabled{color: #FFFFFF; background-color: #00A09D; border-color: #00A09D;}.o_we_website_top_actions .btn.btn-primary:not(:disabled):not(.disabled):active, .o_we_website_top_actions .btn.btn-primary:not(:disabled):not(.disabled).active, .show > .o_we_website_top_actions .btn.btn-primary.dropdown-toggle{color: #FFFFFF; background-color: #006d6b; border-color: #00605e;}.o_we_website_top_actions .btn.btn-primary:not(:disabled):not(.disabled):active:focus, .o_we_website_top_actions .btn.btn-primary:not(:disabled):not(.disabled).active:focus, .show > .o_we_website_top_actions .btn.btn-primary.dropdown-toggle:focus{box-shadow: 0 0 0 0.2rem rgba(38, 174, 172, 0.5);}.o_we_website_top_actions .btn.btn-secondary{color: #FFFFFF; background-color: #141217; border-color: #141217;}.o_we_website_top_actions .btn.btn-secondary:hover{color: #FFFFFF; background-color: #010102; border-color: black;}.o_we_website_top_actions .btn.btn-secondary:focus, .o_we_website_top_actions .btn.btn-secondary.focus{box-shadow: 0 0 0 0.2rem rgba(55, 54, 58, 0.5);}.o_we_website_top_actions .btn.btn-secondary.disabled, .o_we_website_top_actions .btn.btn-secondary:disabled{color: #FFFFFF; background-color: #141217; border-color: #141217;}.o_we_website_top_actions .btn.btn-secondary:not(:disabled):not(.disabled):active, .o_we_website_top_actions .btn.btn-secondary:not(:disabled):not(.disabled).active, .show > .o_we_website_top_actions .btn.btn-secondary.dropdown-toggle{color: #FFFFFF; background-color: black; border-color: black;}.o_we_website_top_actions .btn.btn-secondary:not(:disabled):not(.disabled):active:focus, .o_we_website_top_actions .btn.btn-secondary:not(:disabled):not(.disabled).active:focus, .show > .o_we_website_top_actions .btn.btn-secondary.dropdown-toggle:focus{box-shadow: 0 0 0 0.2rem rgba(55, 54, 58, 0.5);}.o_we_website_top_actions .btn:focus, .o_we_website_top_actions .btn:active, .o_we_website_top_actions .btn:focus:active{outline: none; box-shadow: none;}.o_we_website_top_actions .dropdown-menu{left: auto; right: 0;}#oe_snippets{top: 46px;}.note-statusbar{display: none;}.oe_translate_examples li{margin: 10px; padding: 4px;}html[lang] > body.editor_enable [data-oe-translation-state], html[lang] > body.editor_enable [data-oe-translation-state] .o_translation_select_option{background: rgba(255, 255, 90, 0.5) !important;}html[lang] > body.editor_enable [data-oe-translation-state][data-oe-translation-state="translated"], html[lang] > body.editor_enable [data-oe-translation-state][data-oe-translation-state="translated"] .o_translation_select_option{background: rgba(120, 215, 110, 0.5) !important;}html[lang] > body.editor_enable [data-oe-translation-state].o_dirty, html[lang] > body.editor_enable [data-oe-translation-state].oe_translated, html[lang] > body.editor_enable [data-oe-translation-state] .oe_translated{background: rgba(120, 215, 110, 0.25) !important;}we-toggler.o_we_option_font_1, we-button.o_we_option_font_1 > div{font-family: "Roboto", "Odoo Unicode Support Noto", sans-serif;}we-toggler.o_we_option_font_1::before, we-button.o_we_option_font_1 > div::before{content: "Roboto" !important;}we-toggler.o_we_option_font_2, we-button.o_we_option_font_2 > div{font-family: "Open Sans", "Odoo Unicode Support Noto", sans-serif;}we-toggler.o_we_option_font_2::before, we-button.o_we_option_font_2 > div::before{content: "Open Sans" !important;}we-toggler.o_we_option_font_3, we-button.o_we_option_font_3 > div{font-family: "Source Sans Pro", "Odoo Unicode Support Noto", sans-serif;}we-toggler.o_we_option_font_3::before, we-button.o_we_option_font_3 > div::before{content: "Source Sans Pro" !important;}we-toggler.o_we_option_font_4, we-button.o_we_option_font_4 > div{font-family: "Raleway", "Odoo Unicode Support Noto", sans-serif;}we-toggler.o_we_option_font_4::before, we-button.o_we_option_font_4 > div::before{content: "Raleway" !important;}we-toggler.o_we_option_font_5, we-button.o_we_option_font_5 > div{font-family: "Noto Serif", "Odoo Unicode Support Noto", serif;}we-toggler.o_we_option_font_5::before, we-button.o_we_option_font_5 > div::before{content: "Noto Serif" !important;}we-toggler.o_we_option_font_6, we-button.o_we_option_font_6 > div{font-family: "Arvo", Times, "Odoo Unicode Support Noto", serif;}we-toggler.o_we_option_font_6::before, we-button.o_we_option_font_6 > div::before{content: "Arvo" !important;}.o_we_add_google_font_btn{border-top: 1px solid currentColor !important;}#oe_snippets > .o_we_customize_panel .o_we_user_value_widget.o_palette_color_preview_button > div{display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto; align-items: stretch;}#oe_snippets > .o_we_customize_panel .o_we_user_value_widget.o_palette_color_preview_button .o_palette_color_preview{-webkit-box-flex: 1; -webkit-flex: 1 0 0; flex: 1 0 0; margin: 1px 0; transition: flex 150ms ease 0s;}#oe_snippets > .o_we_customize_panel .o_we_user_value_widget.o_palette_color_preview_button:not(:hover) .o_palette_color_preview:nth-child(4), #oe_snippets > .o_we_customize_panel .o_we_user_value_widget.o_palette_color_preview_button:not(:hover) .o_palette_color_preview:nth-child(5){-webkit-box-flex: 0; -webkit-flex: 0 0 0; flex: 0 0 0;}#oe_snippets > .o_we_customize_panel we-select.o_scroll_effects_selector we-button{padding-top: 8px; padding-bottom: 8px;}#oe_snippets > .o_we_customize_panel we-select.o_scroll_effects_selector we-button img{max-height: 80px; width: auto; margin-right: 8px; margin-left: 4px;}#oe_snippets > .o_we_customize_panel .o_we_theme_colors_selector > we-title{display: none;}#oe_snippets > .o_we_customize_panel .o_we_theme_colors_selector .o_we_so_color_palette.o_we_user_value_widget + .o_we_so_color_palette{margin-left: 10px;}#oe_snippets > .o_we_customize_panel .o_we_theme_colors_selector .o_we_so_color_palette.o_we_user_value_widget .o_we_color_preview{width: 26px; height: 26px;}#oe_snippets > .o_we_customize_panel .o_we_theme_colors_selector > div, #oe_snippets > .o_we_customize_panel .o_we_theme_colors_selector we-select.o_we_theme_colors_select, #oe_snippets > .o_we_customize_panel .o_we_theme_colors_selector we-toggler{display: -webkit-box; display: -webkit-flex; display: flex;}#oe_snippets > .o_we_customize_panel .o_we_theme_colors_selector > div{align-items: stretch; width: 100%;}#oe_snippets > .o_we_customize_panel .o_we_theme_colors_selector we-select.o_we_theme_colors_select{-webkit-box-pack: end; justify-content: flex-end; margin-left: auto;}#oe_snippets > .o_we_customize_panel .o_we_theme_colors_selector we-select.o_we_theme_colors_select > div, #oe_snippets > .o_we_customize_panel .o_we_theme_colors_selector we-select.o_we_theme_colors_select we-toggler{height: 100%;}#oe_snippets > .o_we_customize_panel .o_we_theme_colors_selector we-toggler{align-items: center; padding: 0 0.4rem; font-size: 1.5em;}#oe_snippets > .o_we_customize_panel .o_we_theme_colors_selector we-toggler:after{content: none;}#oe_snippets > .o_we_customize_panel .o_palette_color_preview_button > div{min-height: 24px;}#oe_snippets > .o_we_customize_panel .o_we_cc_preview_wrapper{border: 1px solid; border-color: rgba(255, 255, 255, 0.2) #000000 transparent; box-shadow: 0 1px 0 #000000;}#oe_snippets > .o_we_customize_panel .o_we_cc_preview_wrapper + .o_we_collapse_toggler{height: 35px;}.o_we_border_preview{display: inline-block; width: 999px; max-width: 100%; margin-bottom: 2px; border-width: 4px; border-bottom: none !important;}.pac-container{z-index: 1040; width: 291px !important; font-size: 12px; margin-left: -145.5px; border-top: none; background-color: #141217; box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.5);}.pac-container:after{display: none;}.pac-container .pac-item{border-top: 1px solid #262626; border-radius: 2px; background-color: #595964; color: #D9D9D9;}.pac-container .pac-item:hover{background-color: #2b2b33; cursor: pointer;}.pac-container .pac-item-query{color: #D9D9D9;}

/* /website/static/src/scss/website.edit_mode.scss defined in bundle 'website.assets_wysiwyg' */
 .o_editable.oe_structure.oe_empty#wrap:empty, .o_editable.oe_structure.oe_empty#wrap > .oe_drop_zone.oe_insert:not(.oe_vertical):only-child, .o_editable#wrap[data-oe-type=html]:empty, .o_editable[data-oe-type=html]#wrap > .oe_drop_zone.oe_insert:not(.oe_vertical):only-child, .o_editable .oe_structure.oe_empty#wrap:empty, .o_editable .oe_structure.oe_empty#wrap > .oe_drop_zone.oe_insert:not(.oe_vertical):only-child, .o_editable.oe_structure.oe_empty > .oe_drop_zone.oe_insert:not(.oe_vertical):only-child, .o_editable[data-oe-type=html] > .oe_drop_zone.oe_insert:not(.oe_vertical):only-child, .o_editable .oe_structure.oe_empty > .oe_drop_zone.oe_insert:not(.oe_vertical):only-child{width: 96%; margin: 20px 2%; border: 2px dashed #999999; padding: 12px 0px; text-align: center; color: #999999;}.o_editable.oe_structure.oe_empty#wrap:empty:before, .o_editable.oe_structure.oe_empty#wrap > .oe_drop_zone.oe_insert:only-child:not(.oe_vertical):before, .o_editable#wrap:empty[data-oe-type=html]:before, .o_editable[data-oe-type=html]#wrap > .oe_drop_zone.oe_insert:only-child:not(.oe_vertical):before, .o_editable .oe_structure.oe_empty#wrap:empty:before, .o_editable .oe_structure.oe_empty#wrap > .oe_drop_zone.oe_insert:only-child:not(.oe_vertical):before, .o_editable.oe_structure.oe_empty > .oe_drop_zone.oe_insert:only-child:not(.oe_vertical):before, .o_editable[data-oe-type=html] > .oe_drop_zone.oe_insert:only-child:not(.oe_vertical):before, .o_editable .oe_structure.oe_empty > .oe_drop_zone.oe_insert:only-child:not(.oe_vertical):before{content: attr(data-editor-message); display: block; font-size: 20px; line-height: 50px;}.o_editable.oe_structure.oe_empty#wrap:empty:after, .o_editable.oe_structure.oe_empty#wrap > .oe_drop_zone.oe_insert:only-child:not(.oe_vertical):after, .o_editable#wrap:empty[data-oe-type=html]:after, .o_editable[data-oe-type=html]#wrap > .oe_drop_zone.oe_insert:only-child:not(.oe_vertical):after, .o_editable .oe_structure.oe_empty#wrap:empty:after, .o_editable .oe_structure.oe_empty#wrap > .oe_drop_zone.oe_insert:only-child:not(.oe_vertical):after, .o_editable.oe_structure.oe_empty > .oe_drop_zone.oe_insert:only-child:not(.oe_vertical):after, .o_editable[data-oe-type=html] > .oe_drop_zone.oe_insert:only-child:not(.oe_vertical):after, .o_editable .oe_structure.oe_empty > .oe_drop_zone.oe_insert:only-child:not(.oe_vertical):after{content: attr(data-editor-sub-message); display: block;}.o_we_snippet_area_animation{animation-delay: 999ms;}.o_we_snippet_area_animation::before{animation: inherit; animation-delay: 0ms;}.o_editable{}.o_editable:not(:empty):not([data-oe-model="ir.ui.view"]):not([data-oe-type="html"]):not(.o_editable_no_shadow):not([data-oe-type="image"]):hover, .o_editable:not(:empty).o_editable_date_field_linked, .o_editable[data-oe-type]:not([data-oe-model="ir.ui.view"]):not([data-oe-type="html"]):not(.o_editable_no_shadow):not([data-oe-type="image"]):hover, .o_editable[data-oe-type].o_editable_date_field_linked{box-shadow: #875A7B 0 0 5px 2px inset;}.o_editable:not(:empty)[data-oe-type="image"]:not(.o_editable_no_shadow):hover, .o_editable[data-oe-type][data-oe-type="image"]:not(.o_editable_no_shadow):hover{position: relative;}.o_editable:not(:empty)[data-oe-type="image"]:not(.o_editable_no_shadow):hover:after, .o_editable[data-oe-type][data-oe-type="image"]:not(.o_editable_no_shadow):hover:after{content: ""; pointer-events: none; position: absolute; top: 0; left: 0; bottom: 0; right: 0; z-index: 1; box-shadow: #875A7B 0 0 5px 2px inset;}.o_editable:focus, .o_editable[data-oe-type]{min-height: 0.8em; min-width: 8px;}.o_editable:focus#o_footer_scrolltop_wrapper, .o_editable[data-oe-type]#o_footer_scrolltop_wrapper{min-height: 0; min-width: 0;}.o_editable.o_is_inline_editable{display: inline-block;}.o_editable .btn, .o_editable.btn{-webkit-user-select: auto; -moz-user-select: auto; -ms-user-select: auto; user-select: auto; cursor: text !important;}.o_editable[placeholder]:empty:not(:focus):before{content: attr(placeholder); opacity: 0.3;}.o_editable.oe_structure.oe_empty#wrap:empty, .o_editable.oe_structure.oe_empty#wrap > .oe_drop_zone.oe_insert:not(.oe_vertical):only-child, .o_editable[data-oe-type=html]#wrap:empty, .o_editable[data-oe-type=html]#wrap > .oe_drop_zone.oe_insert:not(.oe_vertical):only-child, .o_editable .oe_structure.oe_empty#wrap:empty, .o_editable .oe_structure.oe_empty#wrap > .oe_drop_zone.oe_insert:not(.oe_vertical):only-child{padding: 112px 0px;}.o_editable.oe_structure.oe_empty > .oe_drop_zone.oe_insert:not(.oe_vertical):only-child, .o_editable[data-oe-type=html] > .oe_drop_zone.oe_insert:not(.oe_vertical):only-child, .o_editable .oe_structure.oe_empty > .oe_drop_zone.oe_insert:not(.oe_vertical):only-child{height: auto; color: #875A7B;}.o_editable.oe_structure.oe_empty > p:empty:only-child, .o_editable[data-oe-type=html] > p:empty:only-child, .o_editable .oe_structure.oe_empty > p:empty:only-child{color: #aaa;}.editor_enable [data-oe-readonly]:hover{cursor: default;}.oe_structure_solo > .oe_drop_zone{transform: translateY(10px);}[draggable]{user-select: none;}.oe_editable:focus, .css_editable_hidden, .editor_enable .css_editable_mode_hidden{outline: none !important;}.editor_enable .css_non_editable_mode_hidden, .o_editable .media_iframe_video .css_editable_mode_display{display: block !important;}.editor_enable [data-oe-type=html].oe_no_empty:empty{height: 16px !important;}table.editorbar-panel{cursor: pointer; width: 100%;}table.editorbar-panel td{border: 1px solid #aaa;}table.editorbar-panel td.selected{background-color: #b1c9d9;}.link-style .dropdown > .btn{min-width: 160px;}.link-style .link-style{display: none;}.link-style li{text-align: center;}.link-style li label{width: 100px; margin: 0 5px;}.link-style .col-md-2 > *{line-height: 2em;}.note-editable .fa{cursor: pointer;}.parallax .oe_structure > .oe_drop_zone:first-child{top: 16px;}.parallax .oe_structure > .oe_drop_zone:last-child{bottom: 16px;}.editor_enable .o_add_language{display: none !important;}.editor_enable .o_facebook_page:not(.o_facebook_preview) iframe{pointer-events: none;}.editor_enable .o_facebook_page:not(.o_facebook_preview) .o_facebook_alert .o_add_facebook_page{cursor: pointer;}body.editor_enable.editor_has_snippets{padding-top: 0 !important;}body.editor_enable.editor_has_snippets .s_popup .modal{background-color: transparent;}body.editor_enable.editor_has_snippets .s_popup .modal.fade .modal-dialog{transform: none;}body.editor_enable.editor_has_snippets #oe_main_menu_navbar + #wrapwrap .o_header_affixed{top: 0;}.editor_has_snippets .o_header_affixed{right: 291px !important;}body.editor_enable .s_countdown .s_countdown_enable_preview{display: initial !important;}body.editor_enable .s_countdown .s_countdown_none{display: none !important;}body.editor_enable .s_dynamic > *{pointer-events: none;}body.editor_enable .s_dynamic [data-url]{cursor: inherit;}.editor_enable .s_website_form input:not(.o_translatable_attribute), .editor_enable .s_wsale_products_searchbar_input input:not(.o_translatable_attribute), .editor_enable .js_subscribe input:not(.o_translatable_attribute), .editor_enable .js_follow_alias input:not(.o_translatable_attribute){pointer-events: none;}.editor_enable .s_website_form [data-toggle="datetimepicker"], .editor_enable .s_website_form textarea:not(.o_translatable_attribute){pointer-events: none;}body.editor_enable .s_newsletter_subscribe_form .o_enable_preview{display: block !important;}body.editor_enable .s_newsletter_subscribe_form .o_disable_preview{display: none !important;}