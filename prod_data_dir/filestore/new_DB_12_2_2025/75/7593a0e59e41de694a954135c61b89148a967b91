
/* /web_editor/static/lib/cropperjs/cropper.js defined in bundle 'web_editor.assets_wysiwyg' */
(function(global,factory){typeof exports==='object'&&typeof module!=='undefined'?module.exports=factory():typeof define==='function'&&define.amd?define(factory):(global=global||self,global.Cropper=factory());}(this,function(){'use strict';function _typeof(obj){if(typeof Symbol==="function"&&typeof Symbol.iterator==="symbol"){_typeof=function(obj){return typeof obj;};}else{_typeof=function(obj){return obj&&typeof Symbol==="function"&&obj.constructor===Symbol&&obj!==Symbol.prototype?"symbol":typeof obj;};}
return _typeof(obj);}
function _classCallCheck(instance,Constructor){if(!(instance instanceof Constructor)){throw new TypeError("Cannot call a class as a function");}}
function _defineProperties(target,props){for(var i=0;i<props.length;i++){var descriptor=props[i];descriptor.enumerable=descriptor.enumerable||false;descriptor.configurable=true;if("value"in descriptor)descriptor.writable=true;Object.defineProperty(target,descriptor.key,descriptor);}}
function _createClass(Constructor,protoProps,staticProps){if(protoProps)_defineProperties(Constructor.prototype,protoProps);if(staticProps)_defineProperties(Constructor,staticProps);return Constructor;}
function _toConsumableArray(arr){return _arrayWithoutHoles(arr)||_iterableToArray(arr)||_nonIterableSpread();}
function _arrayWithoutHoles(arr){if(Array.isArray(arr)){for(var i=0,arr2=new Array(arr.length);i<arr.length;i++)arr2[i]=arr[i];return arr2;}}
function _iterableToArray(iter){if(Symbol.iterator in Object(iter)||Object.prototype.toString.call(iter)==="[object Arguments]")return Array.from(iter);}
function _nonIterableSpread(){throw new TypeError("Invalid attempt to spread non-iterable instance");}
var IS_BROWSER=typeof window!=='undefined'&&typeof window.document!=='undefined';var WINDOW=IS_BROWSER?window:{};var IS_TOUCH_DEVICE=IS_BROWSER?'ontouchstart'in WINDOW.document.documentElement:false;var HAS_POINTER_EVENT=IS_BROWSER?'PointerEvent'in WINDOW:false;var NAMESPACE='cropper';var ACTION_ALL='all';var ACTION_CROP='crop';var ACTION_MOVE='move';var ACTION_ZOOM='zoom';var ACTION_EAST='e';var ACTION_WEST='w';var ACTION_SOUTH='s';var ACTION_NORTH='n';var ACTION_NORTH_EAST='ne';var ACTION_NORTH_WEST='nw';var ACTION_SOUTH_EAST='se';var ACTION_SOUTH_WEST='sw';var CLASS_CROP="".concat(NAMESPACE,"-crop");var CLASS_DISABLED="".concat(NAMESPACE,"-disabled");var CLASS_HIDDEN="".concat(NAMESPACE,"-hidden");var CLASS_HIDE="".concat(NAMESPACE,"-hide");var CLASS_INVISIBLE="".concat(NAMESPACE,"-invisible");var CLASS_MODAL="".concat(NAMESPACE,"-modal");var CLASS_MOVE="".concat(NAMESPACE,"-move");var DATA_ACTION="".concat(NAMESPACE,"Action");var DATA_PREVIEW="".concat(NAMESPACE,"Preview");var DRAG_MODE_CROP='crop';var DRAG_MODE_MOVE='move';var DRAG_MODE_NONE='none';var EVENT_CROP='crop';var EVENT_CROP_END='cropend';var EVENT_CROP_MOVE='cropmove';var EVENT_CROP_START='cropstart';var EVENT_DBLCLICK='dblclick';var EVENT_TOUCH_START=IS_TOUCH_DEVICE?'touchstart':'mousedown';var EVENT_TOUCH_MOVE=IS_TOUCH_DEVICE?'touchmove':'mousemove';var EVENT_TOUCH_END=IS_TOUCH_DEVICE?'touchend touchcancel':'mouseup';var EVENT_POINTER_DOWN=HAS_POINTER_EVENT?'pointerdown':EVENT_TOUCH_START;var EVENT_POINTER_MOVE=HAS_POINTER_EVENT?'pointermove':EVENT_TOUCH_MOVE;var EVENT_POINTER_UP=HAS_POINTER_EVENT?'pointerup pointercancel':EVENT_TOUCH_END;var EVENT_READY='ready';var EVENT_RESIZE='resize';var EVENT_WHEEL='wheel';var EVENT_ZOOM='zoom';var MIME_TYPE_JPEG='image/jpeg';var REGEXP_ACTIONS=/^e|w|s|n|se|sw|ne|nw|all|crop|move|zoom$/;var REGEXP_DATA_URL=/^data:/;var REGEXP_DATA_URL_JPEG=/^data:image\/jpeg;base64,/;var REGEXP_TAG_NAME=/^img|canvas$/i;var MIN_CONTAINER_WIDTH=200;var MIN_CONTAINER_HEIGHT=100;var DEFAULTS={viewMode:0,dragMode:DRAG_MODE_CROP,initialAspectRatio:NaN,aspectRatio:NaN,data:null,preview:'',responsive:true,restore:true,checkCrossOrigin:true,checkOrientation:true,modal:true,guides:true,center:true,highlight:true,background:true,autoCrop:true,autoCropArea:0.8,movable:true,rotatable:true,scalable:true,zoomable:true,zoomOnTouch:true,zoomOnWheel:true,wheelZoomRatio:0.1,cropBoxMovable:true,cropBoxResizable:true,toggleDragModeOnDblclick:true,minCanvasWidth:0,minCanvasHeight:0,minCropBoxWidth:0,minCropBoxHeight:0,minContainerWidth:200,minContainerHeight:100,ready:null,cropstart:null,cropmove:null,cropend:null,crop:null,zoom:null};var TEMPLATE='<div class="cropper-container" touch-action="none">'+'<div class="cropper-wrap-box">'+'<div class="cropper-canvas"></div>'+'</div>'+'<div class="cropper-drag-box"></div>'+'<div class="cropper-crop-box">'+'<span class="cropper-view-box"></span>'+'<span class="cropper-dashed dashed-h"></span>'+'<span class="cropper-dashed dashed-v"></span>'+'<span class="cropper-center"></span>'+'<span class="cropper-face"></span>'+'<span class="cropper-line line-e" data-cropper-action="e"></span>'+'<span class="cropper-line line-n" data-cropper-action="n"></span>'+'<span class="cropper-line line-w" data-cropper-action="w"></span>'+'<span class="cropper-line line-s" data-cropper-action="s"></span>'+'<span class="cropper-point point-e" data-cropper-action="e"></span>'+'<span class="cropper-point point-n" data-cropper-action="n"></span>'+'<span class="cropper-point point-w" data-cropper-action="w"></span>'+'<span class="cropper-point point-s" data-cropper-action="s"></span>'+'<span class="cropper-point point-ne" data-cropper-action="ne"></span>'+'<span class="cropper-point point-nw" data-cropper-action="nw"></span>'+'<span class="cropper-point point-sw" data-cropper-action="sw"></span>'+'<span class="cropper-point point-se" data-cropper-action="se"></span>'+'</div>'+'</div>';var isNaN=Number.isNaN||WINDOW.isNaN;function isNumber(value){return typeof value==='number'&&!isNaN(value);}
var isPositiveNumber=function isPositiveNumber(value){return value>0&&value<Infinity;};function isUndefined(value){return typeof value==='undefined';}
function isObject(value){return _typeof(value)==='object'&&value!==null;}
var hasOwnProperty=Object.prototype.hasOwnProperty;function isPlainObject(value){if(!isObject(value)){return false;}
try{var _constructor=value.constructor;var prototype=_constructor.prototype;return _constructor&&prototype&&hasOwnProperty.call(prototype,'isPrototypeOf');}catch(error){return false;}}
function isFunction(value){return typeof value==='function';}
var slice=Array.prototype.slice;function toArray(value){return Array.from?Array.from(value):slice.call(value);}
function forEach(data,callback){if(data&&isFunction(callback)){if(Array.isArray(data)||isNumber(data.length)){toArray(data).forEach(function(value,key){callback.call(data,value,key,data);});}else if(isObject(data)){Object.keys(data).forEach(function(key){callback.call(data,data[key],key,data);});}}
return data;}
var assign=Object.assign||function assign(target){for(var _len=arguments.length,args=new Array(_len>1?_len-1:0),_key=1;_key<_len;_key++){args[_key-1]=arguments[_key];}
if(isObject(target)&&args.length>0){args.forEach(function(arg){if(isObject(arg)){Object.keys(arg).forEach(function(key){target[key]=arg[key];});}});}
return target;};var REGEXP_DECIMALS=/\.\d*(?:0|9){12}\d*$/;function normalizeDecimalNumber(value){var times=arguments.length>1&&arguments[1]!==undefined?arguments[1]:100000000000;return REGEXP_DECIMALS.test(value)?Math.round(value*times)/times:value;}
var REGEXP_SUFFIX=/^width|height|left|top|marginLeft|marginTop$/;function setStyle(element,styles){var style=element.style;forEach(styles,function(value,property){if(REGEXP_SUFFIX.test(property)&&isNumber(value)){value="".concat(value,"px");}
style[property]=value;});}
function hasClass(element,value){return element.classList?element.classList.contains(value):element.className.indexOf(value)>-1;}
function addClass(element,value){if(!value){return;}
if(isNumber(element.length)){forEach(element,function(elem){addClass(elem,value);});return;}
if(element.classList){element.classList.add(value);return;}
var className=element.className.trim();if(!className){element.className=value;}else if(className.indexOf(value)<0){element.className="".concat(className," ").concat(value);}}
function removeClass(element,value){if(!value){return;}
if(isNumber(element.length)){forEach(element,function(elem){removeClass(elem,value);});return;}
if(element.classList){element.classList.remove(value);return;}
if(element.className.indexOf(value)>=0){element.className=element.className.replace(value,'');}}
function toggleClass(element,value,added){if(!value){return;}
if(isNumber(element.length)){forEach(element,function(elem){toggleClass(elem,value,added);});return;}
if(added){addClass(element,value);}else{removeClass(element,value);}}
var REGEXP_CAMEL_CASE=/([a-z\d])([A-Z])/g;function toParamCase(value){return value.replace(REGEXP_CAMEL_CASE,'$1-$2').toLowerCase();}
function getData(element,name){if(isObject(element[name])){return element[name];}
if(element.dataset){return element.dataset[name];}
return element.getAttribute("data-".concat(toParamCase(name)));}
function setData(element,name,data){if(isObject(data)){element[name]=data;}else if(element.dataset){element.dataset[name]=data;}else{element.setAttribute("data-".concat(toParamCase(name)),data);}}
function removeData(element,name){if(isObject(element[name])){try{delete element[name];}catch(error){element[name]=undefined;}}else if(element.dataset){try{delete element.dataset[name];}catch(error){element.dataset[name]=undefined;}}else{element.removeAttribute("data-".concat(toParamCase(name)));}}
var REGEXP_SPACES=/\s\s*/;var onceSupported=function(){var supported=false;if(IS_BROWSER){var once=false;var listener=function listener(){};var options=Object.defineProperty({},'once',{get:function get(){supported=true;return once;},set:function set(value){once=value;}});WINDOW.addEventListener('test',listener,options);WINDOW.removeEventListener('test',listener,options);}
return supported;}();function removeListener(element,type,listener){var options=arguments.length>3&&arguments[3]!==undefined?arguments[3]:{};var handler=listener;type.trim().split(REGEXP_SPACES).forEach(function(event){if(!onceSupported){var listeners=element.listeners;if(listeners&&listeners[event]&&listeners[event][listener]){handler=listeners[event][listener];delete listeners[event][listener];if(Object.keys(listeners[event]).length===0){delete listeners[event];}
if(Object.keys(listeners).length===0){delete element.listeners;}}}
element.removeEventListener(event,handler,options);});}
function addListener(element,type,listener){var options=arguments.length>3&&arguments[3]!==undefined?arguments[3]:{};var _handler=listener;type.trim().split(REGEXP_SPACES).forEach(function(event){if(options.once&&!onceSupported){var _element$listeners=element.listeners,listeners=_element$listeners===void 0?{}:_element$listeners;_handler=function handler(){delete listeners[event][listener];element.removeEventListener(event,_handler,options);for(var _len2=arguments.length,args=new Array(_len2),_key2=0;_key2<_len2;_key2++){args[_key2]=arguments[_key2];}
listener.apply(element,args);};if(!listeners[event]){listeners[event]={};}
if(listeners[event][listener]){element.removeEventListener(event,listeners[event][listener],options);}
listeners[event][listener]=_handler;element.listeners=listeners;}
element.addEventListener(event,_handler,options);});}
function dispatchEvent(element,type,data){var event;if(isFunction(Event)&&isFunction(CustomEvent)){event=new CustomEvent(type,{detail:data,bubbles:true,cancelable:true});}else{event=document.createEvent('CustomEvent');event.initCustomEvent(type,true,true,data);}
return element.dispatchEvent(event);}
function getOffset(element){var box=element.getBoundingClientRect();return{left:box.left+(window.pageXOffset-document.documentElement.clientLeft),top:box.top+(window.pageYOffset-document.documentElement.clientTop)};}
var location=WINDOW.location;var REGEXP_ORIGINS=/^(\w+:)\/\/([^:/?#]*):?(\d*)/i;function isCrossOriginURL(url){var parts=url.match(REGEXP_ORIGINS);return parts!==null&&(parts[1]!==location.protocol||parts[2]!==location.hostname||parts[3]!==location.port);}
function addTimestamp(url){var timestamp="timestamp=".concat(new Date().getTime());return url+(url.indexOf('?')===-1?'?':'&')+timestamp;}
function getTransforms(_ref){var rotate=_ref.rotate,scaleX=_ref.scaleX,scaleY=_ref.scaleY,translateX=_ref.translateX,translateY=_ref.translateY;var values=[];if(isNumber(translateX)&&translateX!==0){values.push("translateX(".concat(translateX,"px)"));}
if(isNumber(translateY)&&translateY!==0){values.push("translateY(".concat(translateY,"px)"));}
if(isNumber(rotate)&&rotate!==0){values.push("rotate(".concat(rotate,"deg)"));}
if(isNumber(scaleX)&&scaleX!==1){values.push("scaleX(".concat(scaleX,")"));}
if(isNumber(scaleY)&&scaleY!==1){values.push("scaleY(".concat(scaleY,")"));}
var transform=values.length?values.join(' '):'none';return{WebkitTransform:transform,msTransform:transform,transform:transform};}
function getMaxZoomRatio(pointers){var pointers2=assign({},pointers);var ratios=[];forEach(pointers,function(pointer,pointerId){delete pointers2[pointerId];forEach(pointers2,function(pointer2){var x1=Math.abs(pointer.startX-pointer2.startX);var y1=Math.abs(pointer.startY-pointer2.startY);var x2=Math.abs(pointer.endX-pointer2.endX);var y2=Math.abs(pointer.endY-pointer2.endY);var z1=Math.sqrt(x1*x1+y1*y1);var z2=Math.sqrt(x2*x2+y2*y2);var ratio=(z2-z1)/z1;ratios.push(ratio);});});ratios.sort(function(a,b){return Math.abs(a)<Math.abs(b);});return ratios[0];}
function getPointer(_ref2,endOnly){var pageX=_ref2.pageX,pageY=_ref2.pageY;var end={endX:pageX,endY:pageY};return endOnly?end:assign({startX:pageX,startY:pageY},end);}
function getPointersCenter(pointers){var pageX=0;var pageY=0;var count=0;forEach(pointers,function(_ref3){var startX=_ref3.startX,startY=_ref3.startY;pageX+=startX;pageY+=startY;count+=1;});pageX/=count;pageY/=count;return{pageX:pageX,pageY:pageY};}
function getAdjustedSizes(_ref4)
{var aspectRatio=_ref4.aspectRatio,height=_ref4.height,width=_ref4.width;var type=arguments.length>1&&arguments[1]!==undefined?arguments[1]:'contain';var isValidWidth=isPositiveNumber(width);var isValidHeight=isPositiveNumber(height);if(isValidWidth&&isValidHeight){var adjustedWidth=height*aspectRatio;if(type==='contain'&&adjustedWidth>width||type==='cover'&&adjustedWidth<width){height=width/aspectRatio;}else{width=height*aspectRatio;}}else if(isValidWidth){height=width/aspectRatio;}else if(isValidHeight){width=height*aspectRatio;}
return{width:width,height:height};}
function getRotatedSizes(_ref5){var width=_ref5.width,height=_ref5.height,degree=_ref5.degree;degree=Math.abs(degree)%180;if(degree===90){return{width:height,height:width};}
var arc=degree%90*Math.PI/180;var sinArc=Math.sin(arc);var cosArc=Math.cos(arc);var newWidth=width*cosArc+height*sinArc;var newHeight=width*sinArc+height*cosArc;return degree>90?{width:newHeight,height:newWidth}:{width:newWidth,height:newHeight};}
function getSourceCanvas(image,_ref6,_ref7,_ref8){var imageAspectRatio=_ref6.aspectRatio,imageNaturalWidth=_ref6.naturalWidth,imageNaturalHeight=_ref6.naturalHeight,_ref6$rotate=_ref6.rotate,rotate=_ref6$rotate===void 0?0:_ref6$rotate,_ref6$scaleX=_ref6.scaleX,scaleX=_ref6$scaleX===void 0?1:_ref6$scaleX,_ref6$scaleY=_ref6.scaleY,scaleY=_ref6$scaleY===void 0?1:_ref6$scaleY;var aspectRatio=_ref7.aspectRatio,naturalWidth=_ref7.naturalWidth,naturalHeight=_ref7.naturalHeight;var _ref8$fillColor=_ref8.fillColor,fillColor=_ref8$fillColor===void 0?'transparent':_ref8$fillColor,_ref8$imageSmoothingE=_ref8.imageSmoothingEnabled,imageSmoothingEnabled=_ref8$imageSmoothingE===void 0?true:_ref8$imageSmoothingE,_ref8$imageSmoothingQ=_ref8.imageSmoothingQuality,imageSmoothingQuality=_ref8$imageSmoothingQ===void 0?'low':_ref8$imageSmoothingQ,_ref8$maxWidth=_ref8.maxWidth,maxWidth=_ref8$maxWidth===void 0?Infinity:_ref8$maxWidth,_ref8$maxHeight=_ref8.maxHeight,maxHeight=_ref8$maxHeight===void 0?Infinity:_ref8$maxHeight,_ref8$minWidth=_ref8.minWidth,minWidth=_ref8$minWidth===void 0?0:_ref8$minWidth,_ref8$minHeight=_ref8.minHeight,minHeight=_ref8$minHeight===void 0?0:_ref8$minHeight;var canvas=document.createElement('canvas');var context=canvas.getContext('2d');var maxSizes=getAdjustedSizes({aspectRatio:aspectRatio,width:maxWidth,height:maxHeight});var minSizes=getAdjustedSizes({aspectRatio:aspectRatio,width:minWidth,height:minHeight},'cover');var width=Math.min(maxSizes.width,Math.max(minSizes.width,naturalWidth));var height=Math.min(maxSizes.height,Math.max(minSizes.height,naturalHeight));var destMaxSizes=getAdjustedSizes({aspectRatio:imageAspectRatio,width:maxWidth,height:maxHeight});var destMinSizes=getAdjustedSizes({aspectRatio:imageAspectRatio,width:minWidth,height:minHeight},'cover');var destWidth=Math.min(destMaxSizes.width,Math.max(destMinSizes.width,imageNaturalWidth));var destHeight=Math.min(destMaxSizes.height,Math.max(destMinSizes.height,imageNaturalHeight));var params=[-destWidth/2,-destHeight/2,destWidth,destHeight];canvas.width=normalizeDecimalNumber(width);canvas.height=normalizeDecimalNumber(height);context.fillStyle=fillColor;context.fillRect(0,0,width,height);context.save();context.translate(width/2,height/2);context.rotate(rotate*Math.PI/180);context.scale(scaleX,scaleY);context.imageSmoothingEnabled=imageSmoothingEnabled;context.imageSmoothingQuality=imageSmoothingQuality;params=params.map(normalizeDecimalNumber);context.drawImage(image,params[0],params[1],Math.floor(params[2]),Math.floor(params[3]));context.restore();return canvas;}
var fromCharCode=String.fromCharCode;function getStringFromCharCode(dataView,start,length){var str='';length+=start;for(var i=start;i<length;i+=1){str+=fromCharCode(dataView.getUint8(i));}
return str;}
var REGEXP_DATA_URL_HEAD=/^data:.*,/;function dataURLToArrayBuffer(dataURL){var base64=dataURL.replace(REGEXP_DATA_URL_HEAD,'');var binary=atob(base64);var arrayBuffer=new ArrayBuffer(binary.length);var uint8=new Uint8Array(arrayBuffer);forEach(uint8,function(value,i){uint8[i]=binary.charCodeAt(i);});return arrayBuffer;}
function arrayBufferToDataURL(arrayBuffer,mimeType){var chunks=[];var chunkSize=8192;var uint8=new Uint8Array(arrayBuffer);while(uint8.length>0){chunks.push(fromCharCode.apply(null,toArray(uint8.subarray(0,chunkSize))));uint8=uint8.subarray(chunkSize);}
return"data:".concat(mimeType,";base64,").concat(btoa(chunks.join('')));}
function resetAndGetOrientation(arrayBuffer){var dataView=new DataView(arrayBuffer);var orientation;try{var littleEndian;var app1Start;var ifdStart;if(dataView.getUint8(0)===0xFF&&dataView.getUint8(1)===0xD8){var length=dataView.byteLength;var offset=2;while(offset+1<length){if(dataView.getUint8(offset)===0xFF&&dataView.getUint8(offset+1)===0xE1){app1Start=offset;break;}
offset+=1;}}
if(app1Start){var exifIDCode=app1Start+4;var tiffOffset=app1Start+10;if(getStringFromCharCode(dataView,exifIDCode,4)==='Exif'){var endianness=dataView.getUint16(tiffOffset);littleEndian=endianness===0x4949;if(littleEndian||endianness===0x4D4D){if(dataView.getUint16(tiffOffset+2,littleEndian)===0x002A){var firstIFDOffset=dataView.getUint32(tiffOffset+4,littleEndian);if(firstIFDOffset>=0x00000008){ifdStart=tiffOffset+firstIFDOffset;}}}}}
if(ifdStart){var _length=dataView.getUint16(ifdStart,littleEndian);var _offset;var i;for(i=0;i<_length;i+=1){_offset=ifdStart+i*12+2;if(dataView.getUint16(_offset,littleEndian)===0x0112){_offset+=8;orientation=dataView.getUint16(_offset,littleEndian);dataView.setUint16(_offset,1,littleEndian);break;}}}}catch(error){orientation=1;}
return orientation;}
function parseOrientation(orientation){var rotate=0;var scaleX=1;var scaleY=1;switch(orientation){case 2:scaleX=-1;break;case 3:rotate=-180;break;case 4:scaleY=-1;break;case 5:rotate=90;scaleY=-1;break;case 6:rotate=90;break;case 7:rotate=90;scaleX=-1;break;case 8:rotate=-90;break;default:}
return{rotate:rotate,scaleX:scaleX,scaleY:scaleY};}
var render={render:function render(){this.initContainer();this.initCanvas();this.initCropBox();this.renderCanvas();if(this.cropped){this.renderCropBox();}},initContainer:function initContainer(){var element=this.element,options=this.options,container=this.container,cropper=this.cropper;addClass(cropper,CLASS_HIDDEN);removeClass(element,CLASS_HIDDEN);var containerData={width:Math.max(container.offsetWidth,Number(options.minContainerWidth)||200),height:Math.max(container.offsetHeight,Number(options.minContainerHeight)||100)};this.containerData=containerData;setStyle(cropper,{width:containerData.width,height:containerData.height});addClass(element,CLASS_HIDDEN);removeClass(cropper,CLASS_HIDDEN);},initCanvas:function initCanvas(){var containerData=this.containerData,imageData=this.imageData;var viewMode=this.options.viewMode;var rotated=Math.abs(imageData.rotate)%180===90;var naturalWidth=rotated?imageData.naturalHeight:imageData.naturalWidth;var naturalHeight=rotated?imageData.naturalWidth:imageData.naturalHeight;var aspectRatio=naturalWidth/naturalHeight;var canvasWidth=containerData.width;var canvasHeight=containerData.height;if(containerData.height*aspectRatio>containerData.width){if(viewMode===3){canvasWidth=containerData.height*aspectRatio;}else{canvasHeight=containerData.width/aspectRatio;}}else if(viewMode===3){canvasHeight=containerData.width/aspectRatio;}else{canvasWidth=containerData.height*aspectRatio;}
var canvasData={aspectRatio:aspectRatio,naturalWidth:naturalWidth,naturalHeight:naturalHeight,width:canvasWidth,height:canvasHeight};canvasData.left=(containerData.width-canvasWidth)/2;canvasData.top=(containerData.height-canvasHeight)/2;canvasData.oldLeft=canvasData.left;canvasData.oldTop=canvasData.top;this.canvasData=canvasData;this.limited=viewMode===1||viewMode===2;this.limitCanvas(true,true);this.initialImageData=assign({},imageData);this.initialCanvasData=assign({},canvasData);},limitCanvas:function limitCanvas(sizeLimited,positionLimited){var options=this.options,containerData=this.containerData,canvasData=this.canvasData,cropBoxData=this.cropBoxData;var viewMode=options.viewMode;var aspectRatio=canvasData.aspectRatio;var cropped=this.cropped&&cropBoxData;if(sizeLimited){var minCanvasWidth=Number(options.minCanvasWidth)||0;var minCanvasHeight=Number(options.minCanvasHeight)||0;if(viewMode>1){minCanvasWidth=Math.max(minCanvasWidth,containerData.width);minCanvasHeight=Math.max(minCanvasHeight,containerData.height);if(viewMode===3){if(minCanvasHeight*aspectRatio>minCanvasWidth){minCanvasWidth=minCanvasHeight*aspectRatio;}else{minCanvasHeight=minCanvasWidth/aspectRatio;}}}else if(viewMode>0){if(minCanvasWidth){minCanvasWidth=Math.max(minCanvasWidth,cropped?cropBoxData.width:0);}else if(minCanvasHeight){minCanvasHeight=Math.max(minCanvasHeight,cropped?cropBoxData.height:0);}else if(cropped){minCanvasWidth=cropBoxData.width;minCanvasHeight=cropBoxData.height;if(minCanvasHeight*aspectRatio>minCanvasWidth){minCanvasWidth=minCanvasHeight*aspectRatio;}else{minCanvasHeight=minCanvasWidth/aspectRatio;}}}
var _getAdjustedSizes=getAdjustedSizes({aspectRatio:aspectRatio,width:minCanvasWidth,height:minCanvasHeight});minCanvasWidth=_getAdjustedSizes.width;minCanvasHeight=_getAdjustedSizes.height;canvasData.minWidth=minCanvasWidth;canvasData.minHeight=minCanvasHeight;canvasData.maxWidth=Infinity;canvasData.maxHeight=Infinity;}
if(positionLimited){if(viewMode>(cropped?0:1)){var newCanvasLeft=containerData.width-canvasData.width;var newCanvasTop=containerData.height-canvasData.height;canvasData.minLeft=Math.min(0,newCanvasLeft);canvasData.minTop=Math.min(0,newCanvasTop);canvasData.maxLeft=Math.max(0,newCanvasLeft);canvasData.maxTop=Math.max(0,newCanvasTop);if(cropped&&this.limited){canvasData.minLeft=Math.min(cropBoxData.left,cropBoxData.left+(cropBoxData.width-canvasData.width));canvasData.minTop=Math.min(cropBoxData.top,cropBoxData.top+(cropBoxData.height-canvasData.height));canvasData.maxLeft=cropBoxData.left;canvasData.maxTop=cropBoxData.top;if(viewMode===2){if(canvasData.width>=containerData.width){canvasData.minLeft=Math.min(0,newCanvasLeft);canvasData.maxLeft=Math.max(0,newCanvasLeft);}
if(canvasData.height>=containerData.height){canvasData.minTop=Math.min(0,newCanvasTop);canvasData.maxTop=Math.max(0,newCanvasTop);}}}}else{canvasData.minLeft=-canvasData.width;canvasData.minTop=-canvasData.height;canvasData.maxLeft=containerData.width;canvasData.maxTop=containerData.height;}}},renderCanvas:function renderCanvas(changed,transformed){var canvasData=this.canvasData,imageData=this.imageData;if(transformed){var _getRotatedSizes=getRotatedSizes({width:imageData.naturalWidth*Math.abs(imageData.scaleX||1),height:imageData.naturalHeight*Math.abs(imageData.scaleY||1),degree:imageData.rotate||0}),naturalWidth=_getRotatedSizes.width,naturalHeight=_getRotatedSizes.height;var width=canvasData.width*(naturalWidth/canvasData.naturalWidth);var height=canvasData.height*(naturalHeight/canvasData.naturalHeight);canvasData.left-=(width-canvasData.width)/2;canvasData.top-=(height-canvasData.height)/2;canvasData.width=width;canvasData.height=height;canvasData.aspectRatio=naturalWidth/naturalHeight;canvasData.naturalWidth=naturalWidth;canvasData.naturalHeight=naturalHeight;this.limitCanvas(true,false);}
if(canvasData.width>canvasData.maxWidth||canvasData.width<canvasData.minWidth){canvasData.left=canvasData.oldLeft;}
if(canvasData.height>canvasData.maxHeight||canvasData.height<canvasData.minHeight){canvasData.top=canvasData.oldTop;}
canvasData.width=Math.min(Math.max(canvasData.width,canvasData.minWidth),canvasData.maxWidth);canvasData.height=Math.min(Math.max(canvasData.height,canvasData.minHeight),canvasData.maxHeight);this.limitCanvas(false,true);canvasData.left=Math.min(Math.max(canvasData.left,canvasData.minLeft),canvasData.maxLeft);canvasData.top=Math.min(Math.max(canvasData.top,canvasData.minTop),canvasData.maxTop);canvasData.oldLeft=canvasData.left;canvasData.oldTop=canvasData.top;setStyle(this.canvas,assign({width:canvasData.width,height:canvasData.height},getTransforms({translateX:canvasData.left,translateY:canvasData.top})));this.renderImage(changed);if(this.cropped&&this.limited){this.limitCropBox(true,true);}},renderImage:function renderImage(changed){var canvasData=this.canvasData,imageData=this.imageData;var width=imageData.naturalWidth*(canvasData.width/canvasData.naturalWidth);var height=imageData.naturalHeight*(canvasData.height/canvasData.naturalHeight);assign(imageData,{width:width,height:height,left:(canvasData.width-width)/2,top:(canvasData.height-height)/2});setStyle(this.image,assign({width:imageData.width,height:imageData.height},getTransforms(assign({translateX:imageData.left,translateY:imageData.top},imageData))));if(changed){this.output();}},initCropBox:function initCropBox(){var options=this.options,canvasData=this.canvasData;var aspectRatio=options.aspectRatio||options.initialAspectRatio;var autoCropArea=Number(options.autoCropArea)||0.8;var cropBoxData={width:canvasData.width,height:canvasData.height};if(aspectRatio){if(canvasData.height*aspectRatio>canvasData.width){cropBoxData.height=cropBoxData.width/aspectRatio;}else{cropBoxData.width=cropBoxData.height*aspectRatio;}}
this.cropBoxData=cropBoxData;this.limitCropBox(true,true);cropBoxData.width=Math.min(Math.max(cropBoxData.width,cropBoxData.minWidth),cropBoxData.maxWidth);cropBoxData.height=Math.min(Math.max(cropBoxData.height,cropBoxData.minHeight),cropBoxData.maxHeight);cropBoxData.width=Math.max(cropBoxData.minWidth,cropBoxData.width*autoCropArea);cropBoxData.height=Math.max(cropBoxData.minHeight,cropBoxData.height*autoCropArea);cropBoxData.left=canvasData.left+(canvasData.width-cropBoxData.width)/2;cropBoxData.top=canvasData.top+(canvasData.height-cropBoxData.height)/2;cropBoxData.oldLeft=cropBoxData.left;cropBoxData.oldTop=cropBoxData.top;this.initialCropBoxData=assign({},cropBoxData);},limitCropBox:function limitCropBox(sizeLimited,positionLimited){var options=this.options,containerData=this.containerData,canvasData=this.canvasData,cropBoxData=this.cropBoxData,limited=this.limited;var aspectRatio=options.aspectRatio;if(sizeLimited){var minCropBoxWidth=Number(options.minCropBoxWidth)||0;var minCropBoxHeight=Number(options.minCropBoxHeight)||0;var maxCropBoxWidth=limited?Math.min(containerData.width,canvasData.width,canvasData.width+canvasData.left,containerData.width-canvasData.left):containerData.width;var maxCropBoxHeight=limited?Math.min(containerData.height,canvasData.height,canvasData.height+canvasData.top,containerData.height-canvasData.top):containerData.height;minCropBoxWidth=Math.min(minCropBoxWidth,containerData.width);minCropBoxHeight=Math.min(minCropBoxHeight,containerData.height);if(aspectRatio){if(minCropBoxWidth&&minCropBoxHeight){if(minCropBoxHeight*aspectRatio>minCropBoxWidth){minCropBoxHeight=minCropBoxWidth/aspectRatio;}else{minCropBoxWidth=minCropBoxHeight*aspectRatio;}}else if(minCropBoxWidth){minCropBoxHeight=minCropBoxWidth/aspectRatio;}else if(minCropBoxHeight){minCropBoxWidth=minCropBoxHeight*aspectRatio;}
if(maxCropBoxHeight*aspectRatio>maxCropBoxWidth){maxCropBoxHeight=maxCropBoxWidth/aspectRatio;}else{maxCropBoxWidth=maxCropBoxHeight*aspectRatio;}}
cropBoxData.minWidth=Math.min(minCropBoxWidth,maxCropBoxWidth);cropBoxData.minHeight=Math.min(minCropBoxHeight,maxCropBoxHeight);cropBoxData.maxWidth=maxCropBoxWidth;cropBoxData.maxHeight=maxCropBoxHeight;}
if(positionLimited){if(limited){cropBoxData.minLeft=Math.max(0,canvasData.left);cropBoxData.minTop=Math.max(0,canvasData.top);cropBoxData.maxLeft=Math.min(containerData.width,canvasData.left+canvasData.width)-cropBoxData.width;cropBoxData.maxTop=Math.min(containerData.height,canvasData.top+canvasData.height)-cropBoxData.height;}else{cropBoxData.minLeft=0;cropBoxData.minTop=0;cropBoxData.maxLeft=containerData.width-cropBoxData.width;cropBoxData.maxTop=containerData.height-cropBoxData.height;}}},renderCropBox:function renderCropBox(){var options=this.options,containerData=this.containerData,cropBoxData=this.cropBoxData;if(cropBoxData.width>cropBoxData.maxWidth||cropBoxData.width<cropBoxData.minWidth){cropBoxData.left=cropBoxData.oldLeft;}
if(cropBoxData.height>cropBoxData.maxHeight||cropBoxData.height<cropBoxData.minHeight){cropBoxData.top=cropBoxData.oldTop;}
cropBoxData.width=Math.min(Math.max(cropBoxData.width,cropBoxData.minWidth),cropBoxData.maxWidth);cropBoxData.height=Math.min(Math.max(cropBoxData.height,cropBoxData.minHeight),cropBoxData.maxHeight);this.limitCropBox(false,true);cropBoxData.left=Math.min(Math.max(cropBoxData.left,cropBoxData.minLeft),cropBoxData.maxLeft);cropBoxData.top=Math.min(Math.max(cropBoxData.top,cropBoxData.minTop),cropBoxData.maxTop);cropBoxData.oldLeft=cropBoxData.left;cropBoxData.oldTop=cropBoxData.top;if(options.movable&&options.cropBoxMovable){setData(this.face,DATA_ACTION,cropBoxData.width>=containerData.width&&cropBoxData.height>=containerData.height?ACTION_MOVE:ACTION_ALL);}
setStyle(this.cropBox,assign({width:cropBoxData.width,height:cropBoxData.height},getTransforms({translateX:cropBoxData.left,translateY:cropBoxData.top})));if(this.cropped&&this.limited){this.limitCanvas(true,true);}
if(!this.disabled){this.output();}},output:function output(){this.preview();dispatchEvent(this.element,EVENT_CROP,this.getData());}};var preview={initPreview:function initPreview(){var element=this.element,crossOrigin=this.crossOrigin;var preview=this.options.preview;var url=crossOrigin?this.crossOriginUrl:this.url;var alt=element.alt||'The image to preview';var image=document.createElement('img');if(crossOrigin){image.crossOrigin=crossOrigin;}
image.src=url;image.alt=alt;this.viewBox.appendChild(image);this.viewBoxImage=image;if(!preview){return;}
var previews=preview;if(typeof preview==='string'){previews=element.ownerDocument.querySelectorAll(preview);}else if(preview.querySelector){previews=[preview];}
this.previews=previews;forEach(previews,function(el){var img=document.createElement('img');setData(el,DATA_PREVIEW,{width:el.offsetWidth,height:el.offsetHeight,html:el.innerHTML});if(crossOrigin){img.crossOrigin=crossOrigin;}
img.src=url;img.alt=alt;img.style.cssText='display:block;'+'width:100%;'+'height:auto;'+'min-width:0!important;'+'min-height:0!important;'+'max-width:none!important;'+'max-height:none!important;'+'image-orientation:0deg!important;"';el.innerHTML='';el.appendChild(img);});},resetPreview:function resetPreview(){forEach(this.previews,function(element){var data=getData(element,DATA_PREVIEW);setStyle(element,{width:data.width,height:data.height});element.innerHTML=data.html;removeData(element,DATA_PREVIEW);});},preview:function preview(){var imageData=this.imageData,canvasData=this.canvasData,cropBoxData=this.cropBoxData;var cropBoxWidth=cropBoxData.width,cropBoxHeight=cropBoxData.height;var width=imageData.width,height=imageData.height;var left=cropBoxData.left-canvasData.left-imageData.left;var top=cropBoxData.top-canvasData.top-imageData.top;if(!this.cropped||this.disabled){return;}
setStyle(this.viewBoxImage,assign({width:width,height:height},getTransforms(assign({translateX:-left,translateY:-top},imageData))));forEach(this.previews,function(element){var data=getData(element,DATA_PREVIEW);var originalWidth=data.width;var originalHeight=data.height;var newWidth=originalWidth;var newHeight=originalHeight;var ratio=1;if(cropBoxWidth){ratio=originalWidth/cropBoxWidth;newHeight=cropBoxHeight*ratio;}
if(cropBoxHeight&&newHeight>originalHeight){ratio=originalHeight/cropBoxHeight;newWidth=cropBoxWidth*ratio;newHeight=originalHeight;}
setStyle(element,{width:newWidth,height:newHeight});setStyle(element.getElementsByTagName('img')[0],assign({width:width*ratio,height:height*ratio},getTransforms(assign({translateX:-left*ratio,translateY:-top*ratio},imageData))));});}};var events={bind:function bind(){var element=this.element,options=this.options,cropper=this.cropper;if(isFunction(options.cropstart)){addListener(element,EVENT_CROP_START,options.cropstart);}
if(isFunction(options.cropmove)){addListener(element,EVENT_CROP_MOVE,options.cropmove);}
if(isFunction(options.cropend)){addListener(element,EVENT_CROP_END,options.cropend);}
if(isFunction(options.crop)){addListener(element,EVENT_CROP,options.crop);}
if(isFunction(options.zoom)){addListener(element,EVENT_ZOOM,options.zoom);}
addListener(cropper,EVENT_POINTER_DOWN,this.onCropStart=this.cropStart.bind(this));if(options.zoomable&&options.zoomOnWheel){addListener(cropper,EVENT_WHEEL,this.onWheel=this.wheel.bind(this),{passive:false,capture:true});}
if(options.toggleDragModeOnDblclick){addListener(cropper,EVENT_DBLCLICK,this.onDblclick=this.dblclick.bind(this));}
addListener(element.ownerDocument,EVENT_POINTER_MOVE,this.onCropMove=this.cropMove.bind(this));addListener(element.ownerDocument,EVENT_POINTER_UP,this.onCropEnd=this.cropEnd.bind(this));if(options.responsive){addListener(window,EVENT_RESIZE,this.onResize=this.resize.bind(this));}},unbind:function unbind(){var element=this.element,options=this.options,cropper=this.cropper;if(isFunction(options.cropstart)){removeListener(element,EVENT_CROP_START,options.cropstart);}
if(isFunction(options.cropmove)){removeListener(element,EVENT_CROP_MOVE,options.cropmove);}
if(isFunction(options.cropend)){removeListener(element,EVENT_CROP_END,options.cropend);}
if(isFunction(options.crop)){removeListener(element,EVENT_CROP,options.crop);}
if(isFunction(options.zoom)){removeListener(element,EVENT_ZOOM,options.zoom);}
removeListener(cropper,EVENT_POINTER_DOWN,this.onCropStart);if(options.zoomable&&options.zoomOnWheel){removeListener(cropper,EVENT_WHEEL,this.onWheel,{passive:false,capture:true});}
if(options.toggleDragModeOnDblclick){removeListener(cropper,EVENT_DBLCLICK,this.onDblclick);}
removeListener(element.ownerDocument,EVENT_POINTER_MOVE,this.onCropMove);removeListener(element.ownerDocument,EVENT_POINTER_UP,this.onCropEnd);if(options.responsive){removeListener(window,EVENT_RESIZE,this.onResize);}}};var handlers={resize:function resize(){var options=this.options,container=this.container,containerData=this.containerData;var minContainerWidth=Number(options.minContainerWidth)||MIN_CONTAINER_WIDTH;var minContainerHeight=Number(options.minContainerHeight)||MIN_CONTAINER_HEIGHT;if(this.disabled||containerData.width<=minContainerWidth||containerData.height<=minContainerHeight){return;}
var ratio=container.offsetWidth/containerData.width;if(ratio!==1||container.offsetHeight!==containerData.height){var canvasData;var cropBoxData;if(options.restore){canvasData=this.getCanvasData();cropBoxData=this.getCropBoxData();}
this.render();if(options.restore){this.setCanvasData(forEach(canvasData,function(n,i){canvasData[i]=n*ratio;}));this.setCropBoxData(forEach(cropBoxData,function(n,i){cropBoxData[i]=n*ratio;}));}}},dblclick:function dblclick(){if(this.disabled||this.options.dragMode===DRAG_MODE_NONE){return;}
this.setDragMode(hasClass(this.dragBox,CLASS_CROP)?DRAG_MODE_MOVE:DRAG_MODE_CROP);},wheel:function wheel(event){var _this=this;var ratio=Number(this.options.wheelZoomRatio)||0.1;var delta=1;if(this.disabled){return;}
event.preventDefault();if(this.wheeling){return;}
this.wheeling=true;setTimeout(function(){_this.wheeling=false;},50);if(event.deltaY){delta=event.deltaY>0?1:-1;}else if(event.wheelDelta){delta=-event.wheelDelta/120;}else if(event.detail){delta=event.detail>0?1:-1;}
this.zoom(-delta*ratio,event);},cropStart:function cropStart(event){var buttons=event.buttons,button=event.button;if(this.disabled||isNumber(buttons)&&buttons!==1||isNumber(button)&&button!==0||event.ctrlKey){return;}
var options=this.options,pointers=this.pointers;var action;if(event.changedTouches){forEach(event.changedTouches,function(touch){pointers[touch.identifier]=getPointer(touch);});}else{pointers[event.pointerId||0]=getPointer(event);}
if(Object.keys(pointers).length>1&&options.zoomable&&options.zoomOnTouch){action=ACTION_ZOOM;}else{action=getData(event.target,DATA_ACTION);}
if(!REGEXP_ACTIONS.test(action)){return;}
if(dispatchEvent(this.element,EVENT_CROP_START,{originalEvent:event,action:action})===false){return;}
event.preventDefault();this.action=action;this.cropping=false;if(action===ACTION_CROP){this.cropping=true;addClass(this.dragBox,CLASS_MODAL);}},cropMove:function cropMove(event){var action=this.action;if(this.disabled||!action){return;}
var pointers=this.pointers;event.preventDefault();if(dispatchEvent(this.element,EVENT_CROP_MOVE,{originalEvent:event,action:action})===false){return;}
if(event.changedTouches){forEach(event.changedTouches,function(touch){assign(pointers[touch.identifier]||{},getPointer(touch,true));});}else{assign(pointers[event.pointerId||0]||{},getPointer(event,true));}
this.change(event);},cropEnd:function cropEnd(event){if(this.disabled){return;}
var action=this.action,pointers=this.pointers;if(event.changedTouches){forEach(event.changedTouches,function(touch){delete pointers[touch.identifier];});}else{delete pointers[event.pointerId||0];}
if(!action){return;}
event.preventDefault();if(!Object.keys(pointers).length){this.action='';}
if(this.cropping){this.cropping=false;toggleClass(this.dragBox,CLASS_MODAL,this.cropped&&this.options.modal);}
dispatchEvent(this.element,EVENT_CROP_END,{originalEvent:event,action:action});}};var change={change:function change(event){var options=this.options,canvasData=this.canvasData,containerData=this.containerData,cropBoxData=this.cropBoxData,pointers=this.pointers;var action=this.action;var aspectRatio=options.aspectRatio;var left=cropBoxData.left,top=cropBoxData.top,width=cropBoxData.width,height=cropBoxData.height;var right=left+width;var bottom=top+height;var minLeft=0;var minTop=0;var maxWidth=containerData.width;var maxHeight=containerData.height;var renderable=true;var offset;if(!aspectRatio&&event.shiftKey){aspectRatio=width&&height?width/height:1;}
if(this.limited){minLeft=cropBoxData.minLeft;minTop=cropBoxData.minTop;maxWidth=minLeft+Math.min(containerData.width,canvasData.width,canvasData.left+canvasData.width);maxHeight=minTop+Math.min(containerData.height,canvasData.height,canvasData.top+canvasData.height);}
var pointer=pointers[Object.keys(pointers)[0]];var range={x:pointer.endX-pointer.startX,y:pointer.endY-pointer.startY};var check=function check(side){switch(side){case ACTION_EAST:if(right+range.x>maxWidth){range.x=maxWidth-right;}
break;case ACTION_WEST:if(left+range.x<minLeft){range.x=minLeft-left;}
break;case ACTION_NORTH:if(top+range.y<minTop){range.y=minTop-top;}
break;case ACTION_SOUTH:if(bottom+range.y>maxHeight){range.y=maxHeight-bottom;}
break;default:}};switch(action){case ACTION_ALL:left+=range.x;top+=range.y;break;case ACTION_EAST:if(range.x>=0&&(right>=maxWidth||aspectRatio&&(top<=minTop||bottom>=maxHeight))){renderable=false;break;}
check(ACTION_EAST);width+=range.x;if(width<0){action=ACTION_WEST;width=-width;left-=width;}
if(aspectRatio){height=width/aspectRatio;top+=(cropBoxData.height-height)/2;}
break;case ACTION_NORTH:if(range.y<=0&&(top<=minTop||aspectRatio&&(left<=minLeft||right>=maxWidth))){renderable=false;break;}
check(ACTION_NORTH);height-=range.y;top+=range.y;if(height<0){action=ACTION_SOUTH;height=-height;top-=height;}
if(aspectRatio){width=height*aspectRatio;left+=(cropBoxData.width-width)/2;}
break;case ACTION_WEST:if(range.x<=0&&(left<=minLeft||aspectRatio&&(top<=minTop||bottom>=maxHeight))){renderable=false;break;}
check(ACTION_WEST);width-=range.x;left+=range.x;if(width<0){action=ACTION_EAST;width=-width;left-=width;}
if(aspectRatio){height=width/aspectRatio;top+=(cropBoxData.height-height)/2;}
break;case ACTION_SOUTH:if(range.y>=0&&(bottom>=maxHeight||aspectRatio&&(left<=minLeft||right>=maxWidth))){renderable=false;break;}
check(ACTION_SOUTH);height+=range.y;if(height<0){action=ACTION_NORTH;height=-height;top-=height;}
if(aspectRatio){width=height*aspectRatio;left+=(cropBoxData.width-width)/2;}
break;case ACTION_NORTH_EAST:if(aspectRatio){if(range.y<=0&&(top<=minTop||right>=maxWidth)){renderable=false;break;}
check(ACTION_NORTH);height-=range.y;top+=range.y;width=height*aspectRatio;}else{check(ACTION_NORTH);check(ACTION_EAST);if(range.x>=0){if(right<maxWidth){width+=range.x;}else if(range.y<=0&&top<=minTop){renderable=false;}}else{width+=range.x;}
if(range.y<=0){if(top>minTop){height-=range.y;top+=range.y;}}else{height-=range.y;top+=range.y;}}
if(width<0&&height<0){action=ACTION_SOUTH_WEST;height=-height;width=-width;top-=height;left-=width;}else if(width<0){action=ACTION_NORTH_WEST;width=-width;left-=width;}else if(height<0){action=ACTION_SOUTH_EAST;height=-height;top-=height;}
break;case ACTION_NORTH_WEST:if(aspectRatio){if(range.y<=0&&(top<=minTop||left<=minLeft)){renderable=false;break;}
check(ACTION_NORTH);height-=range.y;top+=range.y;width=height*aspectRatio;left+=cropBoxData.width-width;}else{check(ACTION_NORTH);check(ACTION_WEST);if(range.x<=0){if(left>minLeft){width-=range.x;left+=range.x;}else if(range.y<=0&&top<=minTop){renderable=false;}}else{width-=range.x;left+=range.x;}
if(range.y<=0){if(top>minTop){height-=range.y;top+=range.y;}}else{height-=range.y;top+=range.y;}}
if(width<0&&height<0){action=ACTION_SOUTH_EAST;height=-height;width=-width;top-=height;left-=width;}else if(width<0){action=ACTION_NORTH_EAST;width=-width;left-=width;}else if(height<0){action=ACTION_SOUTH_WEST;height=-height;top-=height;}
break;case ACTION_SOUTH_WEST:if(aspectRatio){if(range.x<=0&&(left<=minLeft||bottom>=maxHeight)){renderable=false;break;}
check(ACTION_WEST);width-=range.x;left+=range.x;height=width/aspectRatio;}else{check(ACTION_SOUTH);check(ACTION_WEST);if(range.x<=0){if(left>minLeft){width-=range.x;left+=range.x;}else if(range.y>=0&&bottom>=maxHeight){renderable=false;}}else{width-=range.x;left+=range.x;}
if(range.y>=0){if(bottom<maxHeight){height+=range.y;}}else{height+=range.y;}}
if(width<0&&height<0){action=ACTION_NORTH_EAST;height=-height;width=-width;top-=height;left-=width;}else if(width<0){action=ACTION_SOUTH_EAST;width=-width;left-=width;}else if(height<0){action=ACTION_NORTH_WEST;height=-height;top-=height;}
break;case ACTION_SOUTH_EAST:if(aspectRatio){if(range.x>=0&&(right>=maxWidth||bottom>=maxHeight)){renderable=false;break;}
check(ACTION_EAST);width+=range.x;height=width/aspectRatio;}else{check(ACTION_SOUTH);check(ACTION_EAST);if(range.x>=0){if(right<maxWidth){width+=range.x;}else if(range.y>=0&&bottom>=maxHeight){renderable=false;}}else{width+=range.x;}
if(range.y>=0){if(bottom<maxHeight){height+=range.y;}}else{height+=range.y;}}
if(width<0&&height<0){action=ACTION_NORTH_WEST;height=-height;width=-width;top-=height;left-=width;}else if(width<0){action=ACTION_SOUTH_WEST;width=-width;left-=width;}else if(height<0){action=ACTION_NORTH_EAST;height=-height;top-=height;}
break;case ACTION_MOVE:this.move(range.x,range.y);renderable=false;break;case ACTION_ZOOM:this.zoom(getMaxZoomRatio(pointers),event);renderable=false;break;case ACTION_CROP:if(!range.x||!range.y){renderable=false;break;}
offset=getOffset(this.cropper);left=pointer.startX-offset.left;top=pointer.startY-offset.top;width=cropBoxData.minWidth;height=cropBoxData.minHeight;if(range.x>0){action=range.y>0?ACTION_SOUTH_EAST:ACTION_NORTH_EAST;}else if(range.x<0){left-=width;action=range.y>0?ACTION_SOUTH_WEST:ACTION_NORTH_WEST;}
if(range.y<0){top-=height;}
if(!this.cropped){removeClass(this.cropBox,CLASS_HIDDEN);this.cropped=true;if(this.limited){this.limitCropBox(true,true);}}
break;default:}
if(renderable){cropBoxData.width=width;cropBoxData.height=height;cropBoxData.left=left;cropBoxData.top=top;this.action=action;this.renderCropBox();}
forEach(pointers,function(p){p.startX=p.endX;p.startY=p.endY;});}};var methods={crop:function crop(){if(this.ready&&!this.cropped&&!this.disabled){this.cropped=true;this.limitCropBox(true,true);if(this.options.modal){addClass(this.dragBox,CLASS_MODAL);}
removeClass(this.cropBox,CLASS_HIDDEN);this.setCropBoxData(this.initialCropBoxData);}
return this;},reset:function reset(){if(this.ready&&!this.disabled){this.imageData=assign({},this.initialImageData);this.canvasData=assign({},this.initialCanvasData);this.cropBoxData=assign({},this.initialCropBoxData);this.renderCanvas();if(this.cropped){this.renderCropBox();}}
return this;},clear:function clear(){if(this.cropped&&!this.disabled){assign(this.cropBoxData,{left:0,top:0,width:0,height:0});this.cropped=false;this.renderCropBox();this.limitCanvas(true,true);this.renderCanvas();removeClass(this.dragBox,CLASS_MODAL);addClass(this.cropBox,CLASS_HIDDEN);}
return this;},replace:function replace(url){var hasSameSize=arguments.length>1&&arguments[1]!==undefined?arguments[1]:false;if(!this.disabled&&url){if(this.isImg){this.element.src=url;}
if(hasSameSize){this.url=url;this.image.src=url;if(this.ready){this.viewBoxImage.src=url;forEach(this.previews,function(element){element.getElementsByTagName('img')[0].src=url;});}}else{if(this.isImg){this.replaced=true;}
this.options.data=null;this.uncreate();this.load(url);}}
return this;},enable:function enable(){if(this.ready&&this.disabled){this.disabled=false;removeClass(this.cropper,CLASS_DISABLED);}
return this;},disable:function disable(){if(this.ready&&!this.disabled){this.disabled=true;addClass(this.cropper,CLASS_DISABLED);}
return this;},destroy:function destroy(){var element=this.element;if(!element[NAMESPACE]){return this;}
element[NAMESPACE]=undefined;if(this.isImg&&this.replaced){element.src=this.originalUrl;}
this.uncreate();return this;},move:function move(offsetX){var offsetY=arguments.length>1&&arguments[1]!==undefined?arguments[1]:offsetX;var _this$canvasData=this.canvasData,left=_this$canvasData.left,top=_this$canvasData.top;return this.moveTo(isUndefined(offsetX)?offsetX:left+Number(offsetX),isUndefined(offsetY)?offsetY:top+Number(offsetY));},moveTo:function moveTo(x){var y=arguments.length>1&&arguments[1]!==undefined?arguments[1]:x;var canvasData=this.canvasData;var changed=false;x=Number(x);y=Number(y);if(this.ready&&!this.disabled&&this.options.movable){if(isNumber(x)){canvasData.left=x;changed=true;}
if(isNumber(y)){canvasData.top=y;changed=true;}
if(changed){this.renderCanvas(true);}}
return this;},zoom:function zoom(ratio,_originalEvent){var canvasData=this.canvasData;ratio=Number(ratio);if(ratio<0){ratio=1/(1-ratio);}else{ratio=1+ratio;}
return this.zoomTo(canvasData.width*ratio/canvasData.naturalWidth,null,_originalEvent);},zoomTo:function zoomTo(ratio,pivot,_originalEvent){var options=this.options,canvasData=this.canvasData;var width=canvasData.width,height=canvasData.height,naturalWidth=canvasData.naturalWidth,naturalHeight=canvasData.naturalHeight;ratio=Number(ratio);if(ratio>=0&&this.ready&&!this.disabled&&options.zoomable){var newWidth=naturalWidth*ratio;var newHeight=naturalHeight*ratio;if(dispatchEvent(this.element,EVENT_ZOOM,{ratio:ratio,oldRatio:width/naturalWidth,originalEvent:_originalEvent})===false){return this;}
if(_originalEvent){var pointers=this.pointers;var offset=getOffset(this.cropper);var center=pointers&&Object.keys(pointers).length?getPointersCenter(pointers):{pageX:_originalEvent.pageX,pageY:_originalEvent.pageY};canvasData.left-=(newWidth-width)*((center.pageX-offset.left-canvasData.left)/width);canvasData.top-=(newHeight-height)*((center.pageY-offset.top-canvasData.top)/height);}else if(isPlainObject(pivot)&&isNumber(pivot.x)&&isNumber(pivot.y)){canvasData.left-=(newWidth-width)*((pivot.x-canvasData.left)/width);canvasData.top-=(newHeight-height)*((pivot.y-canvasData.top)/height);}else{canvasData.left-=(newWidth-width)/2;canvasData.top-=(newHeight-height)/2;}
canvasData.width=newWidth;canvasData.height=newHeight;this.renderCanvas(true);}
return this;},rotate:function rotate(degree){return this.rotateTo((this.imageData.rotate||0)+Number(degree));},rotateTo:function rotateTo(degree){degree=Number(degree);if(isNumber(degree)&&this.ready&&!this.disabled&&this.options.rotatable){this.imageData.rotate=degree%360;this.renderCanvas(true,true);}
return this;},scaleX:function scaleX(_scaleX){var scaleY=this.imageData.scaleY;return this.scale(_scaleX,isNumber(scaleY)?scaleY:1);},scaleY:function scaleY(_scaleY){var scaleX=this.imageData.scaleX;return this.scale(isNumber(scaleX)?scaleX:1,_scaleY);},scale:function scale(scaleX){var scaleY=arguments.length>1&&arguments[1]!==undefined?arguments[1]:scaleX;var imageData=this.imageData;var transformed=false;scaleX=Number(scaleX);scaleY=Number(scaleY);if(this.ready&&!this.disabled&&this.options.scalable){if(isNumber(scaleX)){imageData.scaleX=scaleX;transformed=true;}
if(isNumber(scaleY)){imageData.scaleY=scaleY;transformed=true;}
if(transformed){this.renderCanvas(true,true);}}
return this;},getData:function getData(){var rounded=arguments.length>0&&arguments[0]!==undefined?arguments[0]:false;var options=this.options,imageData=this.imageData,canvasData=this.canvasData,cropBoxData=this.cropBoxData;var data;if(this.ready&&this.cropped){data={x:cropBoxData.left-canvasData.left,y:cropBoxData.top-canvasData.top,width:cropBoxData.width,height:cropBoxData.height};var ratio=imageData.width/imageData.naturalWidth;forEach(data,function(n,i){data[i]=n/ratio;});if(rounded){var bottom=Math.round(data.y+data.height);var right=Math.round(data.x+data.width);data.x=Math.round(data.x);data.y=Math.round(data.y);data.width=right-data.x;data.height=bottom-data.y;}}else{data={x:0,y:0,width:0,height:0};}
if(options.rotatable){data.rotate=imageData.rotate||0;}
if(options.scalable){data.scaleX=imageData.scaleX||1;data.scaleY=imageData.scaleY||1;}
return data;},setData:function setData(data){var options=this.options,imageData=this.imageData,canvasData=this.canvasData;var cropBoxData={};if(this.ready&&!this.disabled&&isPlainObject(data)){var transformed=false;if(options.rotatable){if(isNumber(data.rotate)&&data.rotate!==imageData.rotate){imageData.rotate=data.rotate;transformed=true;}}
if(options.scalable){if(isNumber(data.scaleX)&&data.scaleX!==imageData.scaleX){imageData.scaleX=data.scaleX;transformed=true;}
if(isNumber(data.scaleY)&&data.scaleY!==imageData.scaleY){imageData.scaleY=data.scaleY;transformed=true;}}
if(transformed){this.renderCanvas(true,true);}
var ratio=imageData.width/imageData.naturalWidth;if(isNumber(data.x)){cropBoxData.left=data.x*ratio+canvasData.left;}
if(isNumber(data.y)){cropBoxData.top=data.y*ratio+canvasData.top;}
if(isNumber(data.width)){cropBoxData.width=data.width*ratio;}
if(isNumber(data.height)){cropBoxData.height=data.height*ratio;}
this.setCropBoxData(cropBoxData);}
return this;},getContainerData:function getContainerData(){return this.ready?assign({},this.containerData):{};},getImageData:function getImageData(){return this.sized?assign({},this.imageData):{};},getCanvasData:function getCanvasData(){var canvasData=this.canvasData;var data={};if(this.ready){forEach(['left','top','width','height','naturalWidth','naturalHeight'],function(n){data[n]=canvasData[n];});}
return data;},setCanvasData:function setCanvasData(data){var canvasData=this.canvasData;var aspectRatio=canvasData.aspectRatio;if(this.ready&&!this.disabled&&isPlainObject(data)){if(isNumber(data.left)){canvasData.left=data.left;}
if(isNumber(data.top)){canvasData.top=data.top;}
if(isNumber(data.width)){canvasData.width=data.width;canvasData.height=data.width/aspectRatio;}else if(isNumber(data.height)){canvasData.height=data.height;canvasData.width=data.height*aspectRatio;}
this.renderCanvas(true);}
return this;},getCropBoxData:function getCropBoxData(){var cropBoxData=this.cropBoxData;var data;if(this.ready&&this.cropped){data={left:cropBoxData.left,top:cropBoxData.top,width:cropBoxData.width,height:cropBoxData.height};}
return data||{};},setCropBoxData:function setCropBoxData(data){var cropBoxData=this.cropBoxData;var aspectRatio=this.options.aspectRatio;var widthChanged;var heightChanged;if(this.ready&&this.cropped&&!this.disabled&&isPlainObject(data)){if(isNumber(data.left)){cropBoxData.left=data.left;}
if(isNumber(data.top)){cropBoxData.top=data.top;}
if(isNumber(data.width)&&data.width!==cropBoxData.width){widthChanged=true;cropBoxData.width=data.width;}
if(isNumber(data.height)&&data.height!==cropBoxData.height){heightChanged=true;cropBoxData.height=data.height;}
if(aspectRatio){if(widthChanged){cropBoxData.height=cropBoxData.width/aspectRatio;}else if(heightChanged){cropBoxData.width=cropBoxData.height*aspectRatio;}}
this.renderCropBox();}
return this;},getCroppedCanvas:function getCroppedCanvas(){var options=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{};if(!this.ready||!window.HTMLCanvasElement){return null;}
var canvasData=this.canvasData;var source=getSourceCanvas(this.image,this.imageData,canvasData,options);if(!this.cropped){return source;}
var _this$getData=this.getData(),initialX=_this$getData.x,initialY=_this$getData.y,initialWidth=_this$getData.width,initialHeight=_this$getData.height;var ratio=source.width/Math.floor(canvasData.naturalWidth);if(ratio!==1){initialX*=ratio;initialY*=ratio;initialWidth*=ratio;initialHeight*=ratio;}
var aspectRatio=initialWidth/initialHeight;var maxSizes=getAdjustedSizes({aspectRatio:aspectRatio,width:options.maxWidth||Infinity,height:options.maxHeight||Infinity});var minSizes=getAdjustedSizes({aspectRatio:aspectRatio,width:options.minWidth||0,height:options.minHeight||0},'cover');var _getAdjustedSizes=getAdjustedSizes({aspectRatio:aspectRatio,width:options.width||(ratio!==1?source.width:initialWidth),height:options.height||(ratio!==1?source.height:initialHeight)}),width=_getAdjustedSizes.width,height=_getAdjustedSizes.height;width=Math.min(maxSizes.width,Math.max(minSizes.width,width));height=Math.min(maxSizes.height,Math.max(minSizes.height,height));var canvas=document.createElement('canvas');var context=canvas.getContext('2d');canvas.width=normalizeDecimalNumber(width);canvas.height=normalizeDecimalNumber(height);context.fillStyle=options.fillColor||'transparent';context.fillRect(0,0,width,height);var _options$imageSmoothi=options.imageSmoothingEnabled,imageSmoothingEnabled=_options$imageSmoothi===void 0?true:_options$imageSmoothi,imageSmoothingQuality=options.imageSmoothingQuality;context.imageSmoothingEnabled=imageSmoothingEnabled;if(imageSmoothingQuality){context.imageSmoothingQuality=imageSmoothingQuality;}
var sourceWidth=source.width;var sourceHeight=source.height;var srcX=initialX;var srcY=initialY;var srcWidth;var srcHeight;var dstX;var dstY;var dstWidth;var dstHeight;if(srcX<=-initialWidth||srcX>sourceWidth){srcX=0;srcWidth=0;dstX=0;dstWidth=0;}else if(srcX<=0){dstX=-srcX;srcX=0;srcWidth=Math.min(sourceWidth,initialWidth+srcX);dstWidth=srcWidth;}else if(srcX<=sourceWidth){dstX=0;srcWidth=Math.min(initialWidth,sourceWidth-srcX);dstWidth=srcWidth;}
if(srcWidth<=0||srcY<=-initialHeight||srcY>sourceHeight){srcY=0;srcHeight=0;dstY=0;dstHeight=0;}else if(srcY<=0){dstY=-srcY;srcY=0;srcHeight=Math.min(sourceHeight,initialHeight+srcY);dstHeight=srcHeight;}else if(srcY<=sourceHeight){dstY=0;srcHeight=Math.min(initialHeight,sourceHeight-srcY);dstHeight=srcHeight;}
var params=[srcX,srcY,srcWidth,srcHeight];if(dstWidth>0&&dstHeight>0){var scale=width/initialWidth;params.push(dstX*scale,dstY*scale,dstWidth*scale,dstHeight*scale);}
context.drawImage.apply(context,[source].concat(_toConsumableArray(params.map(function(param){return Math.floor(normalizeDecimalNumber(param));}))));return canvas;},setAspectRatio:function setAspectRatio(aspectRatio){var options=this.options;if(!this.disabled&&!isUndefined(aspectRatio)){options.aspectRatio=Math.max(0,aspectRatio)||NaN;if(this.ready){this.initCropBox();if(this.cropped){this.renderCropBox();}}}
return this;},setDragMode:function setDragMode(mode){var options=this.options,dragBox=this.dragBox,face=this.face;if(this.ready&&!this.disabled){var croppable=mode===DRAG_MODE_CROP;var movable=options.movable&&mode===DRAG_MODE_MOVE;mode=croppable||movable?mode:DRAG_MODE_NONE;options.dragMode=mode;setData(dragBox,DATA_ACTION,mode);toggleClass(dragBox,CLASS_CROP,croppable);toggleClass(dragBox,CLASS_MOVE,movable);if(!options.cropBoxMovable){setData(face,DATA_ACTION,mode);toggleClass(face,CLASS_CROP,croppable);toggleClass(face,CLASS_MOVE,movable);}}
return this;}};var AnotherCropper=WINDOW.Cropper;var Cropper=function(){function Cropper(element){var options=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{};_classCallCheck(this,Cropper);if(!element||!REGEXP_TAG_NAME.test(element.tagName)){throw new Error('The first argument is required and must be an <img> or <canvas> element.');}
this.element=element;this.options=assign({},DEFAULTS,isPlainObject(options)&&options);this.cropped=false;this.disabled=false;this.pointers={};this.ready=false;this.reloading=false;this.replaced=false;this.sized=false;this.sizing=false;this.init();}
_createClass(Cropper,[{key:"init",value:function init(){var element=this.element;var tagName=element.tagName.toLowerCase();var url;if(element[NAMESPACE]){return;}
element[NAMESPACE]=this;if(tagName==='img'){this.isImg=true;url=element.getAttribute('src')||'';this.originalUrl=url;if(!url){return;}
url=element.src;}else if(tagName==='canvas'&&window.HTMLCanvasElement){url=element.toDataURL();}
this.load(url);}},{key:"load",value:function load(url){var _this=this;if(!url){return;}
this.url=url;this.imageData={};var element=this.element,options=this.options;if(!options.rotatable&&!options.scalable){options.checkOrientation=false;}
if(!options.checkOrientation||!window.ArrayBuffer){this.clone();return;}
if(REGEXP_DATA_URL.test(url)){if(REGEXP_DATA_URL_JPEG.test(url)){this.read(dataURLToArrayBuffer(url));}else{this.clone();}
return;}
var xhr=new XMLHttpRequest();var clone=this.clone.bind(this);this.reloading=true;this.xhr=xhr;xhr.onabort=clone;xhr.onerror=clone;xhr.ontimeout=clone;xhr.onprogress=function(){if(xhr.getResponseHeader('content-type')!==MIME_TYPE_JPEG){xhr.abort();}};xhr.onload=function(){_this.read(xhr.response);};xhr.onloadend=function(){_this.reloading=false;_this.xhr=null;};if(options.checkCrossOrigin&&isCrossOriginURL(url)&&element.crossOrigin){url=addTimestamp(url);}
xhr.open('GET',url);xhr.responseType='arraybuffer';xhr.withCredentials=element.crossOrigin==='use-credentials';xhr.send();}},{key:"read",value:function read(arrayBuffer){var options=this.options,imageData=this.imageData;var orientation=resetAndGetOrientation(arrayBuffer);var rotate=0;var scaleX=1;var scaleY=1;if(orientation>1){this.url=arrayBufferToDataURL(arrayBuffer,MIME_TYPE_JPEG);var _parseOrientation=parseOrientation(orientation);rotate=_parseOrientation.rotate;scaleX=_parseOrientation.scaleX;scaleY=_parseOrientation.scaleY;}
if(options.rotatable){imageData.rotate=rotate;}
if(options.scalable){imageData.scaleX=scaleX;imageData.scaleY=scaleY;}
this.clone();}},{key:"clone",value:function clone(){var element=this.element,url=this.url;var crossOrigin=element.crossOrigin;var crossOriginUrl=url;if(this.options.checkCrossOrigin&&isCrossOriginURL(url)){if(!crossOrigin){crossOrigin='anonymous';}
crossOriginUrl=addTimestamp(url);}
this.crossOrigin=crossOrigin;this.crossOriginUrl=crossOriginUrl;var image=document.createElement('img');if(crossOrigin){image.crossOrigin=crossOrigin;}
image.src=crossOriginUrl||url;image.alt=element.alt||'The image to crop';this.image=image;image.onload=this.start.bind(this);image.onerror=this.stop.bind(this);addClass(image,CLASS_HIDE);element.parentNode.insertBefore(image,element.nextSibling);}},{key:"start",value:function start(){var _this2=this;var image=this.image;image.onload=null;image.onerror=null;this.sizing=true;var isIOSWebKit=WINDOW.navigator&&/(?:iPad|iPhone|iPod).*?AppleWebKit/i.test(WINDOW.navigator.userAgent);var done=function done(naturalWidth,naturalHeight){assign(_this2.imageData,{naturalWidth:naturalWidth,naturalHeight:naturalHeight,aspectRatio:naturalWidth/naturalHeight});_this2.sizing=false;_this2.sized=true;_this2.build();};if(image.naturalWidth&&!isIOSWebKit){done(image.naturalWidth,image.naturalHeight);return;}
var sizingImage=document.createElement('img');var body=document.body||document.documentElement;this.sizingImage=sizingImage;sizingImage.onload=function(){done(sizingImage.width,sizingImage.height);if(!isIOSWebKit){body.removeChild(sizingImage);}};sizingImage.src=image.src;if(!isIOSWebKit){sizingImage.style.cssText='left:0;'+'max-height:none!important;'+'max-width:none!important;'+'min-height:0!important;'+'min-width:0!important;'+'opacity:0;'+'position:absolute;'+'top:0;'+'z-index:-1;';body.appendChild(sizingImage);}}},{key:"stop",value:function stop(){var image=this.image;image.onload=null;image.onerror=null;image.parentNode.removeChild(image);this.image=null;}},{key:"build",value:function build(){if(!this.sized||this.ready){return;}
var element=this.element,options=this.options,image=this.image;var container=element.parentNode;var template=document.createElement('div');template.innerHTML=TEMPLATE;var cropper=template.querySelector(".".concat(NAMESPACE,"-container"));var canvas=cropper.querySelector(".".concat(NAMESPACE,"-canvas"));var dragBox=cropper.querySelector(".".concat(NAMESPACE,"-drag-box"));var cropBox=cropper.querySelector(".".concat(NAMESPACE,"-crop-box"));var face=cropBox.querySelector(".".concat(NAMESPACE,"-face"));this.container=container;this.cropper=cropper;this.canvas=canvas;this.dragBox=dragBox;this.cropBox=cropBox;this.viewBox=cropper.querySelector(".".concat(NAMESPACE,"-view-box"));this.face=face;canvas.appendChild(image);addClass(element,CLASS_HIDDEN);container.insertBefore(cropper,element.nextSibling);if(!this.isImg){removeClass(image,CLASS_HIDE);}
this.initPreview();this.bind();options.initialAspectRatio=Math.max(0,options.initialAspectRatio)||NaN;options.aspectRatio=Math.max(0,options.aspectRatio)||NaN;options.viewMode=Math.max(0,Math.min(3,Math.round(options.viewMode)))||0;addClass(cropBox,CLASS_HIDDEN);if(!options.guides){addClass(cropBox.getElementsByClassName("".concat(NAMESPACE,"-dashed")),CLASS_HIDDEN);}
if(!options.center){addClass(cropBox.getElementsByClassName("".concat(NAMESPACE,"-center")),CLASS_HIDDEN);}
if(options.background){addClass(cropper,"".concat(NAMESPACE,"-bg"));}
if(!options.highlight){addClass(face,CLASS_INVISIBLE);}
if(options.cropBoxMovable){addClass(face,CLASS_MOVE);setData(face,DATA_ACTION,ACTION_ALL);}
if(!options.cropBoxResizable){addClass(cropBox.getElementsByClassName("".concat(NAMESPACE,"-line")),CLASS_HIDDEN);addClass(cropBox.getElementsByClassName("".concat(NAMESPACE,"-point")),CLASS_HIDDEN);}
this.render();this.ready=true;this.setDragMode(options.dragMode);if(options.autoCrop){this.crop();}
this.setData(options.data);if(isFunction(options.ready)){addListener(element,EVENT_READY,options.ready,{once:true});}
dispatchEvent(element,EVENT_READY);}},{key:"unbuild",value:function unbuild(){if(!this.ready){return;}
this.ready=false;this.unbind();this.resetPreview();this.cropper.parentNode.removeChild(this.cropper);removeClass(this.element,CLASS_HIDDEN);}},{key:"uncreate",value:function uncreate(){if(this.ready){this.unbuild();this.ready=false;this.cropped=false;}else if(this.sizing){this.sizingImage.onload=null;this.sizing=false;this.sized=false;}else if(this.reloading){this.xhr.onabort=null;this.xhr.abort();}else if(this.image){this.stop();}}}],[{key:"noConflict",value:function noConflict(){window.Cropper=AnotherCropper;return Cropper;}},{key:"setDefaults",value:function setDefaults(options){assign(DEFAULTS,isPlainObject(options)&&options);}}]);return Cropper;}();assign(Cropper.prototype,render,preview,events,handlers,change,methods);return Cropper;}));;

/* /web_editor/static/lib/jquery-cropper/jquery-cropper.js defined in bundle 'web_editor.assets_wysiwyg' */
(function(global,factory){typeof exports==='object'&&typeof module!=='undefined'?factory(require('jquery'),require('cropperjs')):typeof define==='function'&&define.amd?define(['jquery','cropperjs'],factory):(factory(global.jQuery,global.Cropper));}(this,(function($,Cropper){'use strict';$=$&&$.hasOwnProperty('default')?$['default']:$;Cropper=Cropper&&Cropper.hasOwnProperty('default')?Cropper['default']:Cropper;if($.fn){var AnotherCropper=$.fn.cropper;var NAMESPACE='cropper';$.fn.cropper=function jQueryCropper(option){for(var _len=arguments.length,args=Array(_len>1?_len-1:0),_key=1;_key<_len;_key++){args[_key-1]=arguments[_key];}
var result=void 0;this.each(function(i,element){var $element=$(element);var isDestroy=option==='destroy';var cropper=$element.data(NAMESPACE);if(!cropper){if(isDestroy){return;}
var options=$.extend({},$element.data(),$.isPlainObject(option)&&option);cropper=new Cropper(element,options);$element.data(NAMESPACE,cropper);}
if(typeof option==='string'){var fn=cropper[option];if($.isFunction(fn)){result=fn.apply(cropper,args);if(result===cropper){result=undefined;}
if(isDestroy){$element.removeData(NAMESPACE);}}}});return result!==undefined?result:this;};$.fn.cropper.Constructor=Cropper;$.fn.cropper.setDefaults=Cropper.setDefaults;$.fn.cropper.noConflict=function noConflict(){$.fn.cropper=AnotherCropper;return this;};}})));;

/* /web_editor/static/lib/jQuery.transfo.js defined in bundle 'web_editor.assets_wysiwyg' */
(function($){'use strict';var rad=Math.PI/180;var methods={init:function(settings){return this.each(function(){var $this=$(this),transfo=$this.data('transfo');if(!transfo){_init($this,settings);}else{_overwriteOptions($this,transfo,settings);_targetCss($this,transfo);}});},destroy:function(){return this.each(function(){var $this=$(this);if($this.data('transfo')){_destroy($this);}});},reset:function(){return this.each(function(){var $this=$(this);if($this.data('transfo')){_reset($this);}});},toggle:function(){return this.each(function(){var $this=$(this);var transfo=$this.data('transfo');if(transfo){transfo.settings.hide=!transfo.settings.hide;_showHide($this,transfo);}});},hide:function(){return this.each(function(){var $this=$(this);var transfo=$this.data('transfo');if(transfo){transfo.settings.hide=true;_showHide($this,transfo);}});},show:function(){return this.each(function(){var $this=$(this);var transfo=$this.data('transfo');if(transfo){transfo.settings.hide=false;_showHide($this,transfo);}});},settings:function(){if(this.length>1){this.map(function(){var $this=$(this);return $this.data('transfo')&&$this.data('transfo').settings;});}
return this.data('transfo')&&$this.data('transfo').settings;},center:function(){if(this.length>1){this.map(function(){var $this=$(this);return $this.data('transfo')&&$this.data('transfo').$center.offset();});}
return this.data('transfo')&&this.data('transfo').$center.offset();}};$.fn.transfo=function(method){if(methods[method]){return methods[method].apply(this,Array.prototype.slice.call(arguments,1));}else if(typeof method==='object'||!method){return methods.init.apply(this,arguments);}else{$.error('Method '+method+' does not exist on jQuery.transfo');}
return false;};function _init($this,settings){var transfo={};$this.data('transfo',transfo);transfo.settings=settings;var css="box-sizing: border-box; position: absolute; background-color: #fff; border: 1px solid #ccc; width: 8px; height: 8px; margin-left: -4px; margin-top: -4px;";transfo.$markup=$(''
+'<div class="transfo-container">'
+'<div class="transfo-controls">'
+'<div style="cursor: crosshair; position: absolute; margin: -30px; top: 0; right: 0; padding: 1px 0 0 1px;" class="transfo-rotator">'
+'<span class="fa-stack fa-lg">'
+'<i class="fa fa-circle fa-stack-2x"></i>'
+'<i class="fa fa-repeat fa-stack-1x fa-inverse"></i>'
+'</span>'
+'</div>'
+'<div style="'+css+'top: 0%; left: 0%; cursor: nw-resize;" class="transfo-scaler-tl"></div>'
+'<div style="'+css+'top: 0%; left: 100%; cursor: ne-resize;" class="transfo-scaler-tr"></div>'
+'<div style="'+css+'top: 100%; left: 100%; cursor: se-resize;" class="transfo-scaler-br"></div>'
+'<div style="'+css+'top: 100%; left: 0%; cursor: sw-resize;" class="transfo-scaler-bl"></div>'
+'<div style="'+css+'top: 0%; left: 50%; cursor: n-resize;" class="transfo-scaler-tc"></div>'
+'<div style="'+css+'top: 100%; left: 50%; cursor: s-resize;" class="transfo-scaler-bc"></div>'
+'<div style="'+css+'top: 50%; left: 0%; cursor: w-resize;" class="transfo-scaler-ml"></div>'
+'<div style="'+css+'top: 50%; left: 100%; cursor: e-resize;" class="transfo-scaler-mr"></div>'
+'<div style="'+css+'border: 0; width: 0px; height: 0px; top: 50%; left: 50%;" class="transfo-scaler-mc"></div>'
+'</div>'
+'</div>');transfo.$center=transfo.$markup.find(".transfo-scaler-mc");_setOptions($this,transfo);_overwriteOptions($this,transfo,settings);$("body").append(transfo.$markup);setTimeout(function(){_targetCss($this,transfo);},0);_bind($this,transfo);_targetCss($this,transfo);_stop_animation($this[0]);}
function _overwriteOptions($this,transfo,settings){transfo.settings=$.extend(transfo.settings,settings||{});}
function _stop_animation(target){target.style.webkitAnimationPlayState="paused";target.style.animationPlayState="paused";target.style.webkitTransition="none";target.style.transition="none";}
function _setOptions($this,transfo){var style=$this.attr("style")||"";var transform=style.match(/transform\s*:([^;]+)/)?style.match(/transform\s*:([^;]+)/)[1]:"";transfo.settings={};transfo.settings.angle=transform.indexOf('rotate')!=-1?parseFloat(transform.match(/rotate\(([^)]+)deg\)/)[1]):0;transfo.settings.scalex=transform.indexOf('scaleX')!=-1?parseFloat(transform.match(/scaleX\(([^)]+)\)/)[1]):1;transfo.settings.scaley=transform.indexOf('scaleY')!=-1?parseFloat(transform.match(/scaleY\(([^)]+)\)/)[1]):1;transfo.settings.style=style.replace(/[^;]*transform[^;]+/g,'').replace(/;+/g,';');$this.attr("style",transfo.settings.style);_stop_animation($this[0]);transfo.settings.pos=$this.offset();transfo.settings.height=$this.innerHeight();transfo.settings.width=$this.innerWidth();var translatex=transform.match(/translateX\(([0-9.-]+)(%|px)\)/);var translatey=transform.match(/translateY\(([0-9.-]+)(%|px)\)/);transfo.settings.translate="%";if(translatex&&translatex[2]==="%"){transfo.settings.translatexp=parseFloat(translatex[1]);transfo.settings.translatex=transfo.settings.translatexp/100*transfo.settings.width;}else{transfo.settings.translatex=translatex?parseFloat(translatex[1]):0;}
if(translatey&&translatey[2]==="%"){transfo.settings.translateyp=parseFloat(translatey[1]);transfo.settings.translatey=transfo.settings.translateyp/100*transfo.settings.height;}else{transfo.settings.translatey=translatey?parseFloat(translatey[1]):0;}
transfo.settings.css=window.getComputedStyle($this[0],null);transfo.settings.rotationStep=5;transfo.settings.hide=false;transfo.settings.callback=function(){};}
function _bind($this,transfo){function mousedown(event){_mouseDown($this,this,transfo,event);$(document).on("mousemove",mousemove).on("mouseup",mouseup);}
function mousemove(event){_mouseMove($this,this,transfo,event);}
function mouseup(event){_mouseUp($this,this,transfo,event);$(document).off("mousemove",mousemove).off("mouseup",mouseup);}
transfo.$markup.off().on("mousedown",mousedown);transfo.$markup.find(".transfo-controls >:not(.transfo-scaler-mc)").off().on("mousedown",mousedown);}
function _mouseDown($this,div,transfo,event){event.preventDefault();if(transfo.active||event.which!==1)return;var type="position",$e=$(div);if($e.hasClass("transfo-rotator"))type="rotator";else if($e.hasClass("transfo-scaler-tl"))type="tl";else if($e.hasClass("transfo-scaler-tr"))type="tr";else if($e.hasClass("transfo-scaler-br"))type="br";else if($e.hasClass("transfo-scaler-bl"))type="bl";else if($e.hasClass("transfo-scaler-tc"))type="tc";else if($e.hasClass("transfo-scaler-bc"))type="bc";else if($e.hasClass("transfo-scaler-ml"))type="ml";else if($e.hasClass("transfo-scaler-mr"))type="mr";transfo.active={"type":type,"scalex":transfo.settings.scalex,"scaley":transfo.settings.scaley,"pageX":event.pageX,"pageY":event.pageY,"center":transfo.$center.offset(),};}
function _mouseUp($this,div,transfo,event){transfo.active=null;}
function _mouseMove($this,div,transfo,event){event.preventDefault();if(!transfo.active)return;var settings=transfo.settings;var center=transfo.active.center;var cdx=center.left-event.pageX;var cdy=center.top-event.pageY;if(transfo.active.type=="rotator"){var ang,dang=Math.atan((settings.width*settings.scalex)/(settings.height*settings.scaley))/rad;if(cdy)ang=Math.atan(-cdx/cdy)/rad;else ang=0;if(event.pageY>=center.top&&event.pageX>=center.left)ang+=180;else if(event.pageY>=center.top&&event.pageX<center.left)ang+=180;else if(event.pageY<center.top&&event.pageX<center.left)ang+=360;ang-=dang;if(settings.scaley<0&&settings.scalex<0)ang+=180;if(!event.ctrlKey){settings.angle=Math.round(ang/transfo.settings.rotationStep)*transfo.settings.rotationStep;}else{settings.angle=ang;}
_targetCss($this,transfo);var new_center=transfo.$center.offset();var x=center.left-new_center.left;var y=center.top-new_center.top;var angle=ang*rad;settings.translatex+=x*Math.cos(angle)-y*Math.sin(-angle);settings.translatey+=-x*Math.sin(angle)+y*Math.cos(-angle);}
else if(transfo.active.type=="position"){var angle=settings.angle*rad;var x=event.pageX-transfo.active.pageX;var y=event.pageY-transfo.active.pageY;transfo.active.pageX=event.pageX;transfo.active.pageY=event.pageY;var dx=x*Math.cos(angle)-y*Math.sin(-angle);var dy=-x*Math.sin(angle)+y*Math.cos(-angle);settings.translatex+=dx;settings.translatey+=dy;}
else if(transfo.active.type.length===2){var angle=settings.angle*rad;var dx=cdx*Math.cos(angle)-cdy*Math.sin(-angle);var dy=-cdx*Math.sin(angle)+cdy*Math.cos(-angle);if(transfo.active.type.indexOf("t")!=-1){settings.scaley=dy/(settings.height/2);}
if(transfo.active.type.indexOf("b")!=-1){settings.scaley=-dy/(settings.height/2);}
if(transfo.active.type.indexOf("l")!=-1){settings.scalex=dx/(settings.width/2);}
if(transfo.active.type.indexOf("r")!=-1){settings.scalex=-dx/(settings.width/2);}
if(settings.scaley>0&&settings.scaley<0.05)settings.scaley=0.05;if(settings.scalex>0&&settings.scalex<0.05)settings.scalex=0.05;if(settings.scaley<0&&settings.scaley>-0.05)settings.scaley=-0.05;if(settings.scalex<0&&settings.scalex>-0.05)settings.scalex=-0.05;if(event.shiftKey&&(transfo.active.type==="tl"||transfo.active.type==="bl"||transfo.active.type==="tr"||transfo.active.type==="br")){settings.scaley=settings.scalex;}}
settings.angle=Math.round(settings.angle);settings.translatex=Math.round(settings.translatex);settings.translatey=Math.round(settings.translatey);settings.scalex=Math.round(settings.scalex*100)/100;settings.scaley=Math.round(settings.scaley*100)/100;_targetCss($this,transfo);_stop_animation($this[0]);return false;}
function _setCss($this,css,settings){var transform="";var trans=false;if(settings.angle!==0){trans=true;transform+=" rotate("+settings.angle+"deg) ";}
if(settings.translatex){trans=true;transform+=" translateX("+(settings.translate==="%"?settings.translatexp+"%":settings.translatex+"px")+") ";}
if(settings.translatey){trans=true;transform+=" translateY("+(settings.translate==="%"?settings.translateyp+"%":settings.translatey+"px")+") ";}
if(settings.scalex!=1){trans=true;transform+=" scaleX("+settings.scalex+") ";}
if(settings.scaley!=1){trans=true;transform+=" scaleY("+settings.scaley+") ";}
if(trans){css+=";"
css+="-webkit-transform:"+transform+";"
+"-moz-transform:"+transform+";"
+"-ms-transform:"+transform+";"
+"-o-transform:"+transform+";"
+"transform:"+transform+";";}
css=css.replace(/(\s*;)+/g,';').replace(/^\s*;|;\s*$/g,'');$this.attr("style",css);}
function _targetCss($this,transfo){var settings=transfo.settings;var width=parseFloat(settings.css.width);var height=parseFloat(settings.css.height);settings.translatexp=Math.round(settings.translatex/width*1000)/10;settings.translateyp=Math.round(settings.translatey/height*1000)/10;_setCss($this,settings.style,settings);transfo.$markup.css({"position":"absolute","width":width+"px","height":height+"px","top":settings.pos.top+"px","left":settings.pos.left+"px"});var $controls=transfo.$markup.find('.transfo-controls');_setCss($controls,"width:"+width+"px;"+"height:"+height+"px;"+"cursor: move;",settings);$controls.children().css("transform","scaleX("+(1/settings.scalex)+") scaleY("+(1/settings.scaley)+")");_showHide($this,transfo);transfo.settings.callback.call($this[0],$this);}
function _showHide($this,transfo){transfo.$markup.css("z-index",transfo.settings.hide?-1:1000);if(transfo.settings.hide){transfo.$markup.find(".transfo-controls > *").hide();transfo.$markup.find(".transfo-scaler-mc").show();}else{transfo.$markup.find(".transfo-controls > *").show();}}
function _destroy($this){$this.data('transfo').$markup.remove();$this.removeData('transfo');}
function _reset($this){var transfo=$this.data('transfo');_destroy($this);$this.transfo(transfo.settings);}})(jQuery);;

/* /web/static/lib/nearest/jquery.nearest.js defined in bundle 'web_editor.assets_wysiwyg' */
;(function($,undefined){var rPerc=/^([\d.]+)%$/;function nearest(selector,options,thisObj){selector||(selector='div');var $container=$(options.container),containerOffset=$container.offset()||{left:0,top:0},containerDims=[containerOffset.left+$container.width(),containerOffset.top+$container.height()],percProps={x:0,y:1,w:0,h:1},prop,match;for(prop in percProps)if(percProps.hasOwnProperty(prop)){match=rPerc.exec(options[prop]);if(match){options[prop]=containerDims[percProps[prop]]*match[1]/100;}}
var $all=$(selector),cache=[],furthest=!!options.furthest,checkX=!!options.checkHoriz,checkY=!!options.checkVert,compDist=furthest?0:Infinity,point1x=parseFloat(options.x)||0,point1y=parseFloat(options.y)||0,point2x=parseFloat(point1x+options.w)||point1x,point2y=parseFloat(point1y+options.h)||point1y,tolerance=options.tolerance||0,hasEach2=!!$.fn.each2,min=Math.min,max=Math.max;if(!options.includeSelf&&thisObj){$all=$all.not(thisObj);}
if(tolerance<0){tolerance=0;}
$all[hasEach2?'each2':'each'](function(i,elem){var $this=hasEach2?elem:$(this),off=$this.offset(),x=off.left,y=off.top,w=$this.outerWidth(),h=$this.outerHeight(),x2=x+w,y2=y+h,maxX1=max(x,point1x),minX2=min(x2,point2x),maxY1=max(y,point1y),minY2=min(y2,point2y),intersectX=minX2>=maxX1,intersectY=minY2>=maxY1,distX,distY,distT,isValid;if((checkX&&checkY)||(!checkX&&!checkY&&intersectX&&intersectY)||(checkX&&intersectY)||(checkY&&intersectX)){distX=intersectX?0:maxX1-minX2;distY=intersectY?0:maxY1-minY2;distT=intersectX||intersectY?max(distX,distY):Math.sqrt(distX*distX+distY*distY);isValid=furthest?distT>=compDist-tolerance:distT<=compDist+tolerance;if(isValid){compDist=furthest?max(compDist,distT):min(compDist,distT);cache.push({node:this,dist:distT});}}});var len=cache.length,filtered=[],compMin,compMax,i,item;if(len){if(furthest){compMin=compDist-tolerance;compMax=compDist;}else{compMin=compDist;compMax=compDist+tolerance;}
for(i=0;i<len;i++){item=cache[i];if(item.dist>=compMin&&item.dist<=compMax){filtered.push(item.node);}}}
return filtered;}
$.each(['nearest','furthest','touching'],function(i,name){var defaults={x:0,y:0,w:0,h:0,tolerance:1,container:document,furthest:name=='furthest',includeSelf:false,checkHoriz:name!='touching',checkVert:name!='touching'};$[name]=function(point,selector,options){if(!point||point.x===undefined||point.y===undefined){return $([]);}
var opts=$.extend({},defaults,point,options||{});return $(nearest(selector,opts));};$.fn[name]=function(selector,options){var opts;if(selector&&$.isPlainObject(selector)){opts=$.extend({},defaults,selector,options||{});return this.pushStack(nearest(this,opts));}
var offset=this.offset(),dimensions={x:offset.left,y:offset.top,w:this.outerWidth(),h:this.outerHeight()};opts=$.extend({},defaults,dimensions,options||{});return this.pushStack(nearest(selector,opts,this));};});})(jQuery);;

/* /web_editor/static/lib/webgl-image-filter/webgl-image-filter.js defined in bundle 'web_editor.assets_wysiwyg' */
(function(window){var WebGLProgram=function(gl,vertexSource,fragmentSource){var _collect=function(source,prefix,collection){var r=new RegExp('\\b'+prefix+' \\w+ (\\w+)','ig');source.replace(r,function(match,name){collection[name]=0;return match;});};var _compile=function(gl,source,type){var shader=gl.createShader(type);gl.shaderSource(shader,source);gl.compileShader(shader);if(!gl.getShaderParameter(shader,gl.COMPILE_STATUS)){console.log(gl.getShaderInfoLog(shader));return null;}
return shader;};this.uniform={};this.attribute={};var _vsh=_compile(gl,vertexSource,gl.VERTEX_SHADER);var _fsh=_compile(gl,fragmentSource,gl.FRAGMENT_SHADER);this.id=gl.createProgram();gl.attachShader(this.id,_vsh);gl.attachShader(this.id,_fsh);gl.linkProgram(this.id);if(!gl.getProgramParameter(this.id,gl.LINK_STATUS)){console.log(gl.getProgramInfoLog(this.id));}
gl.useProgram(this.id);_collect(vertexSource,'attribute',this.attribute);for(var a in this.attribute){this.attribute[a]=gl.getAttribLocation(this.id,a);}
_collect(vertexSource,'uniform',this.uniform);_collect(fragmentSource,'uniform',this.uniform);for(var u in this.uniform){this.uniform[u]=gl.getUniformLocation(this.id,u);}};const identityMatrix=[1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1,0,];const weightedAvg=(a,b,w)=>a*w+b*(1-w);var WebGLImageFilter=window.WebGLImageFilter=function(params){if(!params)
params={};var
gl=null,_drawCount=0,_sourceTexture=null,_lastInChain=false,_currentFramebufferIndex=-1,_tempFramebuffers=[null,null],_filterChain=[],_width=-1,_height=-1,_vertexBuffer=null,_currentProgram=null,_canvas=params.canvas||document.createElement('canvas');var _shaderProgramCache={};var gl=_canvas.getContext("webgl")||_canvas.getContext("experimental-webgl");if(!gl){throw"Couldn't get WebGL context";}
this.addFilter=function(name){var args=Array.prototype.slice.call(arguments,1);var filter=_filter[name];_filterChain.push({func:filter,args:args});};this.reset=function(){_filterChain=[];};this.apply=function(image){_resize(image.width,image.height);_drawCount=0;if(!_sourceTexture)
_sourceTexture=gl.createTexture();gl.bindTexture(gl.TEXTURE_2D,_sourceTexture);gl.texParameteri(gl.TEXTURE_2D,gl.TEXTURE_WRAP_S,gl.CLAMP_TO_EDGE);gl.texParameteri(gl.TEXTURE_2D,gl.TEXTURE_WRAP_T,gl.CLAMP_TO_EDGE);gl.texParameteri(gl.TEXTURE_2D,gl.TEXTURE_MIN_FILTER,gl.NEAREST);gl.texParameteri(gl.TEXTURE_2D,gl.TEXTURE_MAG_FILTER,gl.NEAREST);gl.texImage2D(gl.TEXTURE_2D,0,gl.RGBA,gl.RGBA,gl.UNSIGNED_BYTE,image);if(_filterChain.length==0){var program=_compileShader(SHADER.FRAGMENT_IDENTITY);_draw();return _canvas;}
for(var i=0;i<_filterChain.length;i++){_lastInChain=(i==_filterChain.length-1);var f=_filterChain[i];f.func.apply(this,f.args||[]);}
return _canvas;};var _resize=function(width,height){if(width==_width&&height==_height){return;}
_canvas.width=_width=width;_canvas.height=_height=height;if(!_vertexBuffer){var vertices=new Float32Array([-1,-1,0,1,1,-1,1,1,-1,1,0,0,-1,1,0,0,1,-1,1,1,1,1,1,0]);_vertexBuffer=gl.createBuffer(),gl.bindBuffer(gl.ARRAY_BUFFER,_vertexBuffer);gl.bufferData(gl.ARRAY_BUFFER,vertices,gl.STATIC_DRAW);gl.pixelStorei(gl.UNPACK_PREMULTIPLY_ALPHA_WEBGL,true);}
gl.viewport(0,0,_width,_height);_tempFramebuffers=[null,null];};var _getTempFramebuffer=function(index){_tempFramebuffers[index]=_tempFramebuffers[index]||_createFramebufferTexture(_width,_height);return _tempFramebuffers[index];};var _createFramebufferTexture=function(width,height){var fbo=gl.createFramebuffer();gl.bindFramebuffer(gl.FRAMEBUFFER,fbo);var renderbuffer=gl.createRenderbuffer();gl.bindRenderbuffer(gl.RENDERBUFFER,renderbuffer);var texture=gl.createTexture();gl.bindTexture(gl.TEXTURE_2D,texture);gl.texImage2D(gl.TEXTURE_2D,0,gl.RGBA,width,height,0,gl.RGBA,gl.UNSIGNED_BYTE,null);gl.texParameteri(gl.TEXTURE_2D,gl.TEXTURE_MAG_FILTER,gl.LINEAR);gl.texParameteri(gl.TEXTURE_2D,gl.TEXTURE_MIN_FILTER,gl.LINEAR);gl.texParameteri(gl.TEXTURE_2D,gl.TEXTURE_WRAP_S,gl.CLAMP_TO_EDGE);gl.texParameteri(gl.TEXTURE_2D,gl.TEXTURE_WRAP_T,gl.CLAMP_TO_EDGE);gl.framebufferTexture2D(gl.FRAMEBUFFER,gl.COLOR_ATTACHMENT0,gl.TEXTURE_2D,texture,0);gl.bindTexture(gl.TEXTURE_2D,null);gl.bindFramebuffer(gl.FRAMEBUFFER,null);return{fbo:fbo,texture:texture};};var _draw=function(flags){var source=null,target=null,flipY=false;if(_drawCount==0){source=_sourceTexture;}
else{source=_getTempFramebuffer(_currentFramebufferIndex).texture;}
_drawCount++;if(_lastInChain&&!(flags&DRAW.INTERMEDIATE)){target=null;flipY=_drawCount%2==0;}
else{_currentFramebufferIndex=(_currentFramebufferIndex+1)%2;target=_getTempFramebuffer(_currentFramebufferIndex).fbo;}
gl.bindTexture(gl.TEXTURE_2D,source);gl.bindFramebuffer(gl.FRAMEBUFFER,target);gl.uniform1f(_currentProgram.uniform.flipY,(flipY?-1:1));gl.drawArrays(gl.TRIANGLES,0,6);};var _compileShader=function(fragmentSource){if(_shaderProgramCache[fragmentSource]){_currentProgram=_shaderProgramCache[fragmentSource];gl.useProgram(_currentProgram.id);return _currentProgram;}
_currentProgram=new WebGLProgram(gl,SHADER.VERTEX_IDENTITY,fragmentSource);var floatSize=Float32Array.BYTES_PER_ELEMENT;var vertSize=4*floatSize;gl.enableVertexAttribArray(_currentProgram.attribute.pos);gl.vertexAttribPointer(_currentProgram.attribute.pos,2,gl.FLOAT,false,vertSize,0*floatSize);gl.enableVertexAttribArray(_currentProgram.attribute.uv);gl.vertexAttribPointer(_currentProgram.attribute.uv,2,gl.FLOAT,false,vertSize,2*floatSize);_shaderProgramCache[fragmentSource]=_currentProgram;return _currentProgram;};var DRAW={INTERMEDIATE:1};var SHADER={};SHADER.VERTEX_IDENTITY=['precision highp float;','attribute vec2 pos;','attribute vec2 uv;','varying vec2 vUv;','uniform float flipY;','void main(void) {','vUv = uv;','gl_Position = vec4(pos.x, pos.y*flipY, 0.0, 1.);','}'].join('\n');SHADER.FRAGMENT_IDENTITY=['precision highp float;','varying vec2 vUv;','uniform sampler2D texture;','void main(void) {','gl_FragColor = texture2D(texture, vUv);','}',].join('\n');var _filter={};_filter.colorMatrix=function(matrix,amount=1){matrix=matrix.map((coef,index)=>weightedAvg(coef,identityMatrix[index],amount));var m=new Float32Array(matrix);m[4]/=255;m[9]/=255;m[14]/=255;m[19]/=255;var shader=(1==m[18]&&0==m[3]&&0==m[8]&&0==m[13]&&0==m[15]&&0==m[16]&&0==m[17]&&0==m[19])?_filter.colorMatrix.SHADER.WITHOUT_ALPHA:_filter.colorMatrix.SHADER.WITH_ALPHA;var program=_compileShader(shader);gl.uniform1fv(program.uniform.m,m);_draw();};_filter.colorMatrix.SHADER={};_filter.colorMatrix.SHADER.WITH_ALPHA=['precision highp float;','varying vec2 vUv;','uniform sampler2D texture;','uniform float m[20];','void main(void) {','vec4 c = texture2D(texture, vUv);','gl_FragColor.r = m[0] * c.r + m[1] * c.g + m[2] * c.b + m[3] * c.a + m[4];','gl_FragColor.g = m[5] * c.r + m[6] * c.g + m[7] * c.b + m[8] * c.a + m[9];','gl_FragColor.b = m[10] * c.r + m[11] * c.g + m[12] * c.b + m[13] * c.a + m[14];','gl_FragColor.a = m[15] * c.r + m[16] * c.g + m[17] * c.b + m[18] * c.a + m[19];','}',].join('\n');_filter.colorMatrix.SHADER.WITHOUT_ALPHA=['precision highp float;','varying vec2 vUv;','uniform sampler2D texture;','uniform float m[20];','void main(void) {','vec4 c = texture2D(texture, vUv);','gl_FragColor.r = m[0] * c.r + m[1] * c.g + m[2] * c.b + m[4];','gl_FragColor.g = m[5] * c.r + m[6] * c.g + m[7] * c.b + m[9];','gl_FragColor.b = m[10] * c.r + m[11] * c.g + m[12] * c.b + m[14];','gl_FragColor.a = c.a;','}',].join('\n');_filter.brightness=function(brightness){var b=(brightness||0)+1;_filter.colorMatrix([b,0,0,0,0,0,b,0,0,0,0,0,b,0,0,0,0,0,1,0]);};_filter.saturation=function(amount){var x=(amount||0)*2/3+1;var y=((x-1)*-0.5);_filter.colorMatrix([x,y,y,0,0,y,x,y,0,0,y,y,x,0,0,0,0,0,1,0]);};_filter.desaturate=function(){_filter.saturation(-1);};_filter.contrast=function(amount){var v=(amount||0)+1;var o=-128*(v-1);_filter.colorMatrix([v,0,0,0,o,0,v,0,0,o,0,0,v,0,o,0,0,0,1,0]);};_filter.negative=function(){_filter.contrast(-2);};_filter.hue=function(rotation){rotation=(rotation||0)/180*Math.PI;var cos=Math.cos(rotation),sin=Math.sin(rotation),lumR=0.213,lumG=0.715,lumB=0.072;_filter.colorMatrix([lumR+cos*(1-lumR)+sin*(-lumR),lumG+cos*(-lumG)+sin*(-lumG),lumB+cos*(-lumB)+sin*(1-lumB),0,0,lumR+cos*(-lumR)+sin*(0.143),lumG+cos*(1-lumG)+sin*(0.140),lumB+cos*(-lumB)+sin*(-0.283),0,0,lumR+cos*(-lumR)+sin*(-(1-lumR)),lumG+cos*(-lumG)+sin*(lumG),lumB+cos*(1-lumB)+sin*(lumB),0,0,0,0,0,1,0]);};_filter.desaturateLuminance=function(amount){_filter.colorMatrix([0.2764723,0.9297080,0.0938197,0,-37.1,0.2764723,0.9297080,0.0938197,0,-37.1,0.2764723,0.9297080,0.0938197,0,-37.1,0,0,0,1,0],amount);};_filter.sepia=function(amount){_filter.colorMatrix([0.393,0.7689999,0.18899999,0,0,0.349,0.6859999,0.16799999,0,0,0.272,0.5339999,0.13099999,0,0,0,0,0,1,0],amount);};_filter.brownie=function(amount){_filter.colorMatrix([0.5997023498159715,0.34553243048391263,-0.2708298674538042,0,47.43192855600873,-0.037703249837783157,0.8609577587992641,0.15059552388459913,0,-36.96841498319127,0.24113635128153335,-0.07441037908422492,0.44972182064877153,0,-7.562075277591283,0,0,0,1,0],amount);};_filter.vintagePinhole=function(amount){_filter.colorMatrix([0.6279345635605994,0.3202183420819367,-0.03965408211312453,0,9.651285835294123,0.02578397704808868,0.6441188644374771,0.03259127616149294,0,7.462829176470591,0.0466055556782719,-0.0851232987247891,0.5241648018700465,0,5.159190588235296,0,0,0,1,0],amount);};_filter.kodachrome=function(amount){_filter.colorMatrix([1.1285582396593525,-0.3967382283601348,-0.03992559172921793,0,63.72958762196502,-0.16404339962244616,1.0835251566291304,-0.05498805115633132,0,24.732407896706203,-0.16786010706155763,-0.5603416277695248,1.6014850761964943,0,35.62982807460946,0,0,0,1,0],amount);};_filter.technicolor=function(amount){_filter.colorMatrix([1.9125277891456083,-0.8545344976951645,-0.09155508482755585,0,11.793603434377337,-0.3087833385928097,1.7658908555458428,-0.10601743074722245,0,-70.35205161461398,-0.231103377548616,-0.7501899197440212,1.847597816108189,0,30.950940869491138,0,0,0,1,0],amount);};_filter.polaroid=function(amount){_filter.colorMatrix([1.438,-0.062,-0.062,0,0,-0.122,1.378,-0.122,0,0,-0.016,-0.016,1.483,0,0,0,0,0,1,0],amount);};_filter.shiftToBGR=function(amount){_filter.colorMatrix([0,0,1,0,0,0,1,0,0,0,1,0,0,0,0,0,0,0,1,0],amount);};_filter.convolution=function(matrix){var m=new Float32Array(matrix);var pixelSizeX=1/_width;var pixelSizeY=1/_height;var program=_compileShader(_filter.convolution.SHADER);gl.uniform1fv(program.uniform.m,m);gl.uniform2f(program.uniform.px,pixelSizeX,pixelSizeY);_draw();};_filter.convolution.SHADER=['precision highp float;','varying vec2 vUv;','uniform sampler2D texture;','uniform vec2 px;','uniform float m[9];','void main(void) {','vec4 c11 = texture2D(texture, vUv - px);','vec4 c12 = texture2D(texture, vec2(vUv.x, vUv.y - px.y));','vec4 c13 = texture2D(texture, vec2(vUv.x + px.x, vUv.y - px.y));','vec4 c21 = texture2D(texture, vec2(vUv.x - px.x, vUv.y) );','vec4 c22 = texture2D(texture, vUv);','vec4 c23 = texture2D(texture, vec2(vUv.x + px.x, vUv.y) );','vec4 c31 = texture2D(texture, vec2(vUv.x - px.x, vUv.y + px.y) );','vec4 c32 = texture2D(texture, vec2(vUv.x, vUv.y + px.y) );','vec4 c33 = texture2D(texture, vUv + px );','gl_FragColor = ','c11 * m[0] + c12 * m[1] + c22 * m[2] +','c21 * m[3] + c22 * m[4] + c23 * m[5] +','c31 * m[6] + c32 * m[7] + c33 * m[8];','gl_FragColor.a = c22.a;','}',].join('\n');_filter.detectEdges=function(){_filter.convolution.call(this,[0,1,0,1,-4,1,0,1,0]);};_filter.sobelX=function(){_filter.convolution.call(this,[-1,0,1,-2,0,2,-1,0,1]);};_filter.sobelY=function(){_filter.convolution.call(this,[-1,-2,-1,0,0,0,1,2,1]);};_filter.sharpen=function(amount){var a=amount||1;_filter.convolution.call(this,[0,-1*a,0,-1*a,1+4*a,-1*a,0,-1*a,0]);};_filter.emboss=function(size){var s=size||1;_filter.convolution.call(this,[-2*s,-1*s,0,-1*s,1,1*s,0,1*s,2*s]);};_filter.blur=function(size){var blurSizeX=(size/7)/_width;var blurSizeY=(size/7)/_height;var program=_compileShader(_filter.blur.SHADER);gl.uniform2f(program.uniform.px,0,blurSizeY);_draw(DRAW.INTERMEDIATE);gl.uniform2f(program.uniform.px,blurSizeX,0);_draw();};_filter.blur.SHADER=['precision highp float;','varying vec2 vUv;','uniform sampler2D texture;','uniform vec2 px;','void main(void) {','gl_FragColor = vec4(0.0);','gl_FragColor += texture2D(texture, vUv + vec2(-7.0*px.x, -7.0*px.y))*0.0044299121055113265;','gl_FragColor += texture2D(texture, vUv + vec2(-6.0*px.x, -6.0*px.y))*0.00895781211794;','gl_FragColor += texture2D(texture, vUv + vec2(-5.0*px.x, -5.0*px.y))*0.0215963866053;','gl_FragColor += texture2D(texture, vUv + vec2(-4.0*px.x, -4.0*px.y))*0.0443683338718;','gl_FragColor += texture2D(texture, vUv + vec2(-3.0*px.x, -3.0*px.y))*0.0776744219933;','gl_FragColor += texture2D(texture, vUv + vec2(-2.0*px.x, -2.0*px.y))*0.115876621105;','gl_FragColor += texture2D(texture, vUv + vec2(-1.0*px.x, -1.0*px.y))*0.147308056121;','gl_FragColor += texture2D(texture, vUv                             )*0.159576912161;','gl_FragColor += texture2D(texture, vUv + vec2( 1.0*px.x,  1.0*px.y))*0.147308056121;','gl_FragColor += texture2D(texture, vUv + vec2( 2.0*px.x,  2.0*px.y))*0.115876621105;','gl_FragColor += texture2D(texture, vUv + vec2( 3.0*px.x,  3.0*px.y))*0.0776744219933;','gl_FragColor += texture2D(texture, vUv + vec2( 4.0*px.x,  4.0*px.y))*0.0443683338718;','gl_FragColor += texture2D(texture, vUv + vec2( 5.0*px.x,  5.0*px.y))*0.0215963866053;','gl_FragColor += texture2D(texture, vUv + vec2( 6.0*px.x,  6.0*px.y))*0.00895781211794;','gl_FragColor += texture2D(texture, vUv + vec2( 7.0*px.x,  7.0*px.y))*0.0044299121055113265;','}',].join('\n');_filter.pixelate=function(size){var blurSizeX=(size)/_width;var blurSizeY=(size)/_height;var program=_compileShader(_filter.pixelate.SHADER);gl.uniform2f(program.uniform.size,blurSizeX,blurSizeY);_draw();};_filter.pixelate.SHADER=['precision highp float;','varying vec2 vUv;','uniform vec2 size;','uniform sampler2D texture;','vec2 pixelate(vec2 coord, vec2 size) {','return floor( coord / size ) * size;','}','void main(void) {','gl_FragColor = vec4(0.0);','vec2 coord = pixelate(vUv, size);','gl_FragColor += texture2D(texture, coord);','}',].join('\n');};})(window);;

/* /web_editor/static/src/js/wysiwyg/fonts.js defined in bundle 'web_editor.assets_wysiwyg' */
odoo.define('wysiwyg.fonts',function(require){'use strict';return{cacheCssSelectors:{},getCssSelectors:function(filter){if(this.cacheCssSelectors[filter]){return this.cacheCssSelectors[filter];}
this.cacheCssSelectors[filter]=[];var sheets=document.styleSheets;for(var i=0;i<sheets.length;i++){var rules;try{rules=sheets[i].rules||sheets[i].cssRules;}catch(e){console.warn("Can't read the css rules of: "+sheets[i].href,e);continue;}
if(!rules){continue;}
for(var r=0;r<rules.length;r++){var selectorText=rules[r].selectorText;if(!selectorText){continue;}
var selectors=selectorText.split(/\s*,\s*/);var data=null;for(var s=0;s<selectors.length;s++){var match=selectors[s].trim().match(filter);if(!match){continue;}
if(!data){data={selector:match[0],css:rules[r].cssText.replace(/(^.*\{\s*)|(\s*\}\s*$)/g,''),names:[match[1]]};}else{data.selector+=(', '+match[0]);data.names.push(match[1]);}}
if(data){this.cacheCssSelectors[filter].push(data);}}}
return this.cacheCssSelectors[filter];},fontIcons:[{base:'fa',parser:/\.(fa-(?:\w|-)+)::?before/i}],computeFonts:_.once(function(){var self=this;_.each(this.fontIcons,function(data){data.cssData=self.getCssSelectors(data.parser);data.alias=_.flatten(_.map(data.cssData,_.property('names')));});}),};});;

/* /web_editor/static/src/js/base.js defined in bundle 'web_editor.assets_wysiwyg' */
odoo.define('web_editor.base',function(require){'use strict';var ajax=require('web.ajax');var session=require('web.session');var domReady=new Promise(function(resolve){$(resolve);});return{cacheCssSelectors:{},getCssSelectors:function(filter){if(this.cacheCssSelectors[filter]){return this.cacheCssSelectors[filter];}
this.cacheCssSelectors[filter]=[];var sheets=document.styleSheets;for(var i=0;i<sheets.length;i++){var rules;try{rules=sheets[i].rules||sheets[i].cssRules;}catch(e){console.warn("Can't read the css rules of: "+sheets[i].href,e);continue;}
if(!rules){continue;}
for(var r=0;r<rules.length;r++){var selectorText=rules[r].selectorText;if(!selectorText){continue;}
var selectors=selectorText.split(/\s*,\s*/);var data=null;for(var s=0;s<selectors.length;s++){var match=selectors[s].trim().match(filter);if(!match){continue;}
if(!data){data={selector:match[0],css:rules[r].cssText.replace(/(^.*\{\s*)|(\s*\}\s*$)/g,''),names:[match[1]]};}else{data.selector+=(', '+match[0]);data.names.push(match[1]);}}
if(data){this.cacheCssSelectors[filter].push(data);}}}
return this.cacheCssSelectors[filter];},fontIcons:[{base:'fa',parser:/\.(fa-(?:\w|-)+)::?before/i}],computeFonts:_.once(function(){var self=this;_.each(this.fontIcons,function(data){data.cssData=self.getCssSelectors(data.parser);data.alias=_.flatten(_.map(data.cssData,_.property('names')));});}),ready:function(){return Promise.all([domReady,session.is_bound,ajax.loadXML()]);},};});odoo.define('web_editor.context',function(require){'use strict';function getContext(context){var html=document.documentElement;return _.extend({lang:(html.getAttribute('lang')||'en_US').replace('-','_'),'website_id':html.getAttribute('data-website-id')|0,},context||{});}
function getExtraContext(context){var html=document.documentElement;return _.extend(getContext(),{editable:!!(html.dataset.editable||$('[data-oe-model]').length),translatable:!!html.dataset.translatable,edit_translations:!!html.dataset.edit_translations,},context||{});}
return{get:getContext,getExtra:getExtraContext,};});odoo.define('web_editor.ready',function(require){'use strict';var base=require('web_editor.base');return base.ready();});;

/* /web_editor/static/src/js/editor/editor.js defined in bundle 'web_editor.assets_wysiwyg' */
odoo.define('web_editor.editor',function(require){'use strict';var Dialog=require('web.Dialog');var Widget=require('web.Widget');var core=require('web.core');var rte=require('web_editor.rte');var snippetsEditor=require('web_editor.snippet.editor');var summernoteCustomColors=require('web_editor.rte.summernote_custom_colors');var _t=core._t;var EditorMenuBar=Widget.extend({template:'web_editor.editorbar',xmlDependencies:['/web_editor/static/src/xml/editor.xml'],events:{'click button[data-action=save]':'_onSaveClick','click button[data-action=cancel]':'_onCancelClick',},custom_events:{request_editable:'_onRequestEditable',request_history_undo_record:'_onHistoryUndoRecordRequest',request_save:'_onSaveRequest',},init:function(parent,options){var self=this;var res=this._super.apply(this,arguments);var Editor=options.Editor||rte.Class;this.rte=new Editor(this,{getConfig:function($editable){var param=self._getDefaultConfig($editable);if(options.generateOptions){param=options.generateOptions(param);}
return param;},saveElement:options.saveElement,});this.rte.on('rte:start',this,function(){self.trigger('rte:start');});var $editable=this.rte.editable();window.__EditorMenuBar_$editable=$editable;if(options.snippets){this.snippetsMenu=new snippetsEditor.Class(this,Object.assign({$el:$editable,selectorEditableArea:'.o_editable',},options));}
return res;},start:function(){var self=this;var defs=[this._super.apply(this,arguments)];core.bus.on('editor_save_request',this,this.save);core.bus.on('editor_discard_request',this,this.cancel);$('.dropdown-toggle').dropdown();$(document).on('keyup',function(event){if((event.keyCode===8||event.keyCode===46)){var $target=$(event.target).closest('.o_editable');if(!$target.is(':has(*:not(p):not(br))')&&!$target.text().match(/\S/)){$target.empty();}}});$(document).on('click','.note-editable',function(ev){ev.preventDefault();});$(document).on('submit','.note-editable form .btn',function(ev){ev.preventDefault();});$(document).on('hide.bs.dropdown','.dropdown',function(ev){if(ev.originalEvent&&$(ev.target).has(ev.originalEvent.target).length&&$(ev.originalEvent.target).is('[contenteditable]')){ev.preventDefault();}});this.rte.start();var flag=false;window.onbeforeunload=function(event){if(rte.history.getEditableHasUndo().length&&!flag){flag=true;_.defer(function(){flag=false;});return _t('This document is not saved!');}};if(self.snippetsMenu){defs.push(this.snippetsMenu.insertAfter(this.$el));}
this.rte.editable().find('*').off('mousedown mouseup click');return Promise.all(defs).then(function(){self.trigger_up('edit_mode');});},destroy:function(){this._super.apply(this,arguments);core.bus.off('editor_save_request',this,this._onSaveRequest);core.bus.off('editor_discard_request',this,this._onDiscardRequest);},cancel:function(reload){var self=this;return new Promise(function(resolve,reject){if(!rte.history.getEditableHasUndo().length){resolve();}else{var confirm=Dialog.confirm(this,_t("If you discard the current edits, all unsaved changes will be lost. You can cancel to return to edit mode."),{confirm_callback:resolve,});confirm.on('closed',self,reject);}}).then(function(){if(reload!==false){window.onbeforeunload=null;return self._reload();}});},save:async function(reload){var defs=[];this.trigger_up('ready_to_save',{defs:defs});await Promise.all(defs);if(this.snippetsMenu){await this.snippetsMenu.cleanForSave();}
await this.getParent().saveModifiedImages(this.rte.editable());await this.rte.save();if(reload!==false){return this._reload();}},_getDefaultConfig:function($editable){return{'airMode':true,'focus':false,'airPopover':[['style',['style']],['font',['bold','italic','underline','clear']],['fontsize',['fontsize']],['color',['color']],['para',['ul','ol','paragraph']],['table',['table']],['insert',['link','picture']],['history',['undo','redo']],],'styleWithSpan':false,'inlinemedia':['p'],'lang':'odoo','onChange':function(html,$editable){$editable.trigger('content_changed');},'colors':summernoteCustomColors,};},_reload:function(){window.location.hash='scrollTop='+window.document.body.scrollTop;if(window.location.search.indexOf('enable_editor')>=0){window.location.href=window.location.href.replace(/&?enable_editor(=[^&]*)?/g,'');}else{window.location.reload(true);}
return new Promise(function(){});},_onCancelClick:function(){this.cancel();},_onHistoryUndoRecordRequest:function(ev){this.rte.historyRecordUndo(ev.data.$target,ev.data.event);},_onSaveClick:function(){this.save();},_onDiscardRequest:function(ev){this.cancel(ev.data.reload).then(ev.data.onSuccess,ev.data.onFailure);},_onSaveRequest:function(ev){ev.stopPropagation();this.save(ev.data.reload).then(ev.data.onSuccess,ev.data.onFailure);},_onRequestEditable:function(ev){ev.data.callback(this.rte.editable());},});return{Class:EditorMenuBar,};});;

/* /web_editor/static/src/js/editor/rte.js defined in bundle 'web_editor.assets_wysiwyg' */
odoo.define('web_editor.rte',function(require){'use strict';var fonts=require('wysiwyg.fonts');var concurrency=require('web.concurrency');var core=require('web.core');var Widget=require('web.Widget');var weContext=require('web_editor.context');var summernote=require('web_editor.summernote');var summernoteCustomColors=require('web_editor.rte.summernote_custom_colors');var _t=core._t;const{browser}=owl;var dom=summernote.core.dom;var range=summernote.core.range;var History=function History($editable){var aUndo=[];var pos=0;var toSnap;this.makeSnap=function(event,rng){rng=rng||range.create();var elEditable=$(rng&&rng.sc).closest('.o_editable')[0];if(!elEditable){return false;}
return{event:event,editable:elEditable,contents:elEditable.innerHTML,bookmark:rng&&rng.bookmark(elEditable),scrollTop:$(elEditable).scrollTop()};};this.applySnap=function(oSnap){var $editable=$(oSnap.editable);if(document.documentMode){$editable.removeAttr('contentEditable').removeProp('contentEditable');}
$editable.trigger('content_will_be_destroyed');var $tempDiv=$('<div/>',{html:oSnap.contents});_.each($tempDiv.find('.o_temp_auto_element'),function(el){var $el=$(el);var originalContent=$el.attr('data-temp-auto-element-original-content');if(originalContent){$el.after(originalContent);}
$el.remove();});$editable.html($tempDiv.html()).scrollTop(oSnap.scrollTop);$editable.trigger('content_was_recreated');$('.oe_overlay').remove();$('.note-control-selection').hide();$editable.trigger('content_changed');try{var r=oSnap.editable.innerHTML===''?range.create(oSnap.editable,0):range.createFromBookmark(oSnap.editable,oSnap.bookmark);r.select();}catch(e){console.error(e);return;}
$(document).trigger('click');$('.o_editable *').filter(function(){var $el=$(this);if($el.data('snippet-editor')){$el.removeData();}});_.defer(function(){var target=dom.isBR(r.sc)?r.sc.parentNode:dom.node(r.sc);if(!target){return;}
$editable.trigger('applySnap');var evt=document.createEvent('MouseEvents');evt.initMouseEvent('click',true,true,window,0,0,0,0,0,false,false,false,false,0,target);target.dispatchEvent(evt);$editable.trigger('keyup');});};this.undo=function(){if(!pos){return;}
var _toSnap=toSnap;if(_toSnap){this.saveSnap();}
if(!aUndo[pos]&&(!aUndo[pos]||aUndo[pos].event!=='undo')){var temp=this.makeSnap('undo');if(temp&&(!pos||temp.contents!==aUndo[pos-1].contents)){aUndo[pos]=temp;}else{pos--;}}else if(_toSnap){pos--;}
this.applySnap(aUndo[Math.max(--pos,0)]);while(pos&&(aUndo[pos].event==='blur'||(aUndo[pos+1].editable===aUndo[pos].editable&&aUndo[pos+1].contents===aUndo[pos].contents))){this.applySnap(aUndo[--pos]);}};this.hasUndo=function(){return(toSnap&&(toSnap.event!=='blur'&&toSnap.event!=='activate'&&toSnap.event!=='undo'))||!!_.find(aUndo.slice(0,pos+1),function(undo){return undo.event!=='blur'&&undo.event!=='activate'&&undo.event!=='undo';});};this.getEditableHasUndo=function(){var editable=[];if((toSnap&&(toSnap.event!=='blur'&&toSnap.event!=='activate'&&toSnap.event!=='undo'))){editable.push(toSnap.editable);}
_.each(aUndo.slice(0,pos+1),function(undo){if(undo.event!=='blur'&&undo.event!=='activate'&&undo.event!=='undo'){editable.push(undo.editable);}});return _.uniq(editable);};this.redo=function(){if(!aUndo[pos+1]){return;}
this.applySnap(aUndo[++pos]);while(aUndo[pos+1]&&aUndo[pos].event==='active'){this.applySnap(aUndo[pos++]);}};this.hasRedo=function(){return aUndo.length>pos+1;};this.recordUndo=function($editable,event,internal_history){var self=this;if(!$editable){var rng=range.create();if(!rng)return;$editable=$(rng.sc).closest('.o_editable');}
if(aUndo[pos]&&(event==='applySnap'||event==='activate')){return;}
if(!internal_history){if(!event||!toSnap||!aUndo[pos-1]||toSnap.event==='activate'){setTimeout(function(){$editable.trigger('content_changed');},0);}}
if(aUndo[pos]){pos=Math.min(pos,aUndo.length);aUndo.splice(pos,aUndo.length);}
if(toSnap&&(toSnap.split||!event||toSnap.event!==event||toSnap.editable!==$editable[0])){this.saveSnap();}
if(pos&&aUndo[pos-1].editable!==$editable[0]){var snap=this.makeSnap('blur',range.create(aUndo[pos-1].editable,0));pos++;aUndo.push(snap);}
if(range.create()){toSnap=self.makeSnap(event);}else{toSnap=false;}};this.splitNext=function(){if(toSnap){toSnap.split=true;}};this.saveSnap=function(){if(toSnap){if(!aUndo[pos]){pos++;}
aUndo.push(toSnap);delete toSnap.split;toSnap=null;}};};var history=new History();$.extend($.expr[':'],{o_editable:function(node,i,m){while(node){if(node.className&&_.isString(node.className)){if(node.className.indexOf('o_not_editable')!==-1){return false;}
if(node.className.indexOf('o_editable')!==-1){return true;}}
node=node.parentNode;}
return false;},});$.fn.extend({focusIn:function(){if(this.length){range.create(dom.firstChild(this[0]),0).select();}
return this;},focusInEnd:function(){if(this.length){var last=dom.lastChild(this[0]);range.create(last,dom.nodeLength(last)).select();}
return this;},selectContent:function(){if(this.length){var next=dom.lastChild(this[0]);range.create(dom.firstChild(this[0]),0,next,next.textContent.length).select();}
return this;},});var RTEWidget=Widget.extend({init:function(parent,params){var self=this;this._super.apply(this,arguments);this.init_bootstrap_carousel=$.fn.carousel;this.edit_bootstrap_carousel=function(){var res=self.init_bootstrap_carousel.apply(this,arguments);$(this).off('keydown.bs.carousel');return res;};this._getConfig=params&&params.getConfig||this._getDefaultConfig;this._saveElement=params&&params.saveElement||this._saveElement;fonts.computeFonts();},start:function(){var self=this;this.saving_mutex=new concurrency.Mutex();$.fn.carousel=this.edit_bootstrap_carousel;$(document).on('click.rte keyup.rte',function(){var current_range={};try{current_range=range.create()||{};}catch(e){}
var $popover=$(current_range.sc).closest('[contenteditable]');var popover_history=($popover.data()||{}).NoteHistory;if(!popover_history||popover_history===history)return;var editor=$popover.parent('.note-editor');$('button[data-event="undo"]',editor).attr('disabled',!popover_history.hasUndo());$('button[data-event="redo"]',editor).attr('disabled',!popover_history.hasRedo());});$(document).on('mousedown.rte activate.rte',this,this._onMousedown.bind(this));$(document).on('mouseup.rte',this,this._onMouseup.bind(this));$('.o_not_editable').attr('contentEditable',false);var $editable=this.editable();this.__$editable=$editable;$editable.on('content_will_be_destroyed',function(ev){self.trigger_up('content_will_be_destroyed',{$target:$(ev.currentTarget),});});$editable.on('content_was_recreated',function(ev){self.trigger_up('content_was_recreated',{$target:$(ev.currentTarget),});});$editable.addClass('o_editable').data('rte',this).each(function(){var $node=$(this);var computedStyles=window.getComputedStyle(this)||window.parent.getComputedStyle(this);if(computedStyles.display==='inline'&&$node.data('oe-type')!=='image'){$node.addClass('o_is_inline_editable');}});let pastingData=false;$(document).on('paste',function(ev){pastingData=[...self.editable()];browser.setTimeout(function(){pastingData=false;},0);});$(document).on('content_changed',function(ev){self.trigger_up('rte_change',{target:ev.target});if(pastingData){const pastedDirtyEls=ev.target.querySelectorAll('[data-oe-id]');_.difference([...pastedDirtyEls],pastingData).forEach(el=>{const dirtyAttributes=el.getAttributeNames().filter(name=>!name.indexOf("data-oe-"));dirtyAttributes.forEach(name=>el.removeAttribute(name));})
pastingData=false;}
if(!ev.__isDirtyHandled){ev.__isDirtyHandled=true;var el=ev.target;var dirty=el.closest('.o_editable')||el;dirty.classList.add('o_dirty');}});$('#wrapwrap, .o_editable').on('click.rte','*',this,this._onClick.bind(this));$('body').addClass('editor_enable');$(document.body).tooltip({selector:'[data-oe-readonly]',container:'body',trigger:'hover',delay:{'show':1000,'hide':100},placement:'bottom',title:_t("Readonly field")}).on('click',function(){$(this).tooltip('hide');});$(document).trigger('mousedown');this.trigger('rte:start');return this._super.apply(this,arguments);},destroy:function(){this.cancel();this._super.apply(this,arguments);},cancel:function(){if(this.$last){this.$last.destroy();this.$last=null;}
$.fn.carousel=this.init_bootstrap_carousel;$(document).off('.rte');$('#wrapwrap, .o_editable').off('.rte');$('.o_not_editable').removeAttr('contentEditable');$(document).off('click.rte keyup.rte mousedown.rte activate.rte mouseup.rte');$(document).off('content_changed').removeClass('o_is_inline_editable').removeData('rte');$(document).tooltip('dispose');$('body').removeClass('editor_enable');this.trigger('rte:stop');},editable:function(){return $('#wrapwrap [data-oe-model]').not('.o_not_editable').filter(function(){return!$(this).closest('.o_not_editable').length;}).not('link, script').not('[data-oe-readonly]').not('img[data-oe-field="arch"], br[data-oe-field="arch"], input[data-oe-field="arch"]').not('.oe_snippet_editor').add('.o_editable');},historyRecordUndo:function($target,event,internal_history){const initialActiveElement=document.activeElement;const initialSelectionStart=initialActiveElement&&initialActiveElement.selectionStart;const initialSelectionEnd=initialActiveElement&&initialActiveElement.selectionEnd;$target=$($target);var rng=range.create();var $editable=$(rng&&rng.sc).closest('.o_editable');if(!rng||!$editable.length){$editable=$target.closest('.o_editable');rng=range.create($target.closest('*')[0],0);}else{rng=$editable.data('range')||rng;}
try{rng.select();}catch(e){console.log('error',e);}
history.recordUndo($editable,event,internal_history);if(initialActiveElement&&initialActiveElement!==document.activeElement){initialActiveElement.focus();if(initialActiveElement.matches('input[type=range]')){return;}
try{initialActiveElement.selectionStart=initialSelectionStart;initialActiveElement.selectionEnd=initialSelectionEnd;}catch(e){console.log('error',e);}}},save:function(context){var self=this;$('.o_editable').destroy().removeClass('o_editable o_is_inline_editable o_editable_date_field_linked o_editable_date_field_format_changed');var $dirty=$('.o_dirty');$dirty.removeAttr('contentEditable').removeClass('o_dirty oe_carlos_danger o_is_inline_editable');var defs=_.map($dirty,function(el){var $el=$(el);$el.find('[class]').filter(function(){if(!this.getAttribute('class').match(/\S/)){this.removeAttribute('class');}});return self.saving_mutex.exec(function(){return self._saveElement($el,context||weContext.get()).then(function(){$el.removeClass('o_dirty');}).guardedCatch(function(response){var id=_.uniqueId('carlos_danger_');$el.addClass('o_dirty oe_carlos_danger '+id);$('.o_editable.'+id).removeClass(id).popover({trigger:'hover',content:response.message.data.message||'',placement:'auto top',}).popover('show');});});});return Promise.all(defs).then(function(){window.onbeforeunload=null;}).guardedCatch(function(failed){self.cancel();self.start();});},_enableEditableArea:function($editable){if($editable.data('oe-type')==="datetime"||$editable.data('oe-type')==="date"){var selector='[data-oe-id="'+$editable.data('oe-id')+'"]';selector+='[data-oe-field="'+$editable.data('oe-field')+'"]';selector+='[data-oe-model="'+$editable.data('oe-model')+'"]';var $linkedFieldNodes=this.editable().find(selector).addBack(selector);$linkedFieldNodes.not($editable).addClass('o_editable_date_field_linked');if(!$editable.hasClass('o_editable_date_field_format_changed')){$linkedFieldNodes.html($editable.data('oe-original-with-format'));$linkedFieldNodes.addClass('o_editable_date_field_format_changed');}}
if($editable.data('oe-type')==="monetary"){$editable.attr('contenteditable',false);$editable.find('.oe_currency_value').attr('contenteditable',true);}
if($editable.is('[data-oe-model]')&&!$editable.is('[data-oe-model="ir.ui.view"]')&&!$editable.is('[data-oe-type="html"]')){$editable.data('layoutInfo').popover().find('.btn-group:not(.note-history)').remove();}
if($editable.data('oe-type')==="image"){$editable.attr('contenteditable',false);$editable.find('img').attr('contenteditable',true);}},_getDefaultConfig:function($editable){return{'airMode':true,'focus':false,'airPopover':[['style',['style']],['font',['bold','italic','underline','clear']],['fontsize',['fontsize']],['color',['color']],['para',['ul','ol','paragraph']],['table',['table']],['insert',['link','picture']],['history',['undo','redo']],],'styleWithSpan':false,'inlinemedia':['p'],'lang':'odoo','onChange':function(html,$editable){$editable.trigger('content_changed');},'colors':summernoteCustomColors,};},_getEscapedElement:function($el){var escaped_el=$el.clone();var to_escape=escaped_el.find('*').addBack();to_escape=to_escape.not(to_escape.filter('object,iframe,script,style,[data-oe-model][data-oe-model!="ir.ui.view"]').find('*').addBack());to_escape.contents().each(function(){if(this.nodeType===3){this.nodeValue=$('<div />').text(this.nodeValue).html();}});return escaped_el;},_saveElement:function($el,context,withLang){var viewID=$el.data('oe-id');if(!viewID){return Promise.resolve();}
return this._rpc({model:'ir.ui.view',method:'save',args:[viewID,this._getEscapedElement($el).prop('outerHTML'),$el.data('oe-xpath')||null,],context:context,},withLang?undefined:{noContextKeys:'lang',});},_onClick:function(e){e.preventDefault();},_onMousedown:function(ev){var $target=$(ev.target);var $editable=$target.closest('.o_editable');var isLink=$target.is('a');if(this&&this.$last&&this.$last.length&&this.$last[0]!==$target[0]){$('.o_editable_date_field_linked').removeClass('o_editable_date_field_linked');}
if(!$editable.length||(!isLink&&$.summernote.core.dom.isContentEditableFalse($target))){return;}
_.defer(function(){$editable.find('[_moz_abspos]').removeAttr('_moz_abspos');});if(isLink&&!$target.closest('.o_not_editable').length){let hasContentEditable=$target.attr('contenteditable');$target.attr('contenteditable',true);_.defer(function(){$editable.not($target).attr('contenteditable',false);$target.focus();});$(document).on('mousedown.reactivate_contenteditable',function(e){if($target.is(e.target))return;if(!hasContentEditable){$target.removeAttr('contenteditable');}
$editable.attr('contenteditable',true);$(document).off('mousedown.reactivate_contenteditable');});}
if(this&&this.$last&&(!$editable.length||this.$last[0]!==$editable[0])){var $destroy=this.$last;history.splitNext();var lastTimerId=_.delay(function(){var id=$destroy.data('note-id');$destroy.destroy().removeData('note-id').removeAttr('data-note-id');$('#note-popover-'+id+', #note-handle-'+id+', #note-dialog-'+id+'').remove();},150);this.$last=null;if($destroy.hasClass('modal-body')){clearTimeout(lastTimerId);}}
if($editable.length&&(!this.$last||this.$last[0]!==$editable[0])){$editable.summernote(this._getConfig($editable));$editable.data('NoteHistory',history);this.$last=$editable;try{document.execCommand('enableObjectResizing',false,false);document.execCommand('enableInlineTableEditing',false,false);document.execCommand('2D-position',false,false);}catch(e){}
document.body.addEventListener('resizestart',function(evt){evt.preventDefault();return false;});document.body.addEventListener('movestart',function(evt){evt.preventDefault();return false;});document.body.addEventListener('dragstart',function(evt){evt.preventDefault();return false;});if(!range.create()){$editable.focusIn();}
if(dom.isImg($target[0])){$target.trigger('mousedown');}
this._enableEditableArea($editable);}},_onMouseup:function(ev){var $target=$(ev.target);var $editable=$target.closest('.o_editable');if(!$editable.length){return;}
var self=this;_.defer(function(){self.historyRecordUndo($target,'activate',true);});if(ev.originalEvent&&ev.originalEvent.detail===3){range.create(ev.target,0,ev.target,ev.target.childNodes.length).select();}},});return{Class:RTEWidget,history:history,};});odoo.define('web_editor.rte.summernote_custom_colors',function(require){'use strict';return[['#000000','#424242','#636363','#9C9C94','#CEC6CE','#EFEFEF','#F7F7F7','#FFFFFF'],['#FF0000','#FF9C00','#FFFF00','#00FF00','#00FFFF','#0000FF','#9C00FF','#FF00FF'],['#F7C6CE','#FFE7CE','#FFEFC6','#D6EFD6','#CEDEE7','#CEE7F7','#D6D6E7','#E7D6DE'],['#E79C9C','#FFC69C','#FFE79C','#B5D6A5','#A5C6CE','#9CC6EF','#B5A5D6','#D6A5BD'],['#E76363','#F7AD6B','#FFD663','#94BD7B','#73A5AD','#6BADDE','#8C7BC6','#C67BA5'],['#CE0000','#E79439','#EFC631','#6BA54A','#4A7B8C','#3984C6','#634AA5','#A54A7B'],['#9C0000','#B56308','#BD9400','#397B21','#104A5A','#085294','#311873','#731842'],['#630000','#7B3900','#846300','#295218','#083139','#003163','#21104A','#4A1031']];});;

/* /web_editor/static/src/js/editor/rte.summernote.js defined in bundle 'web_editor.assets_wysiwyg' */
odoo.define('web_editor.rte.summernote',function(require){'use strict';var Class=require('web.Class');const concurrency=require('web.concurrency');var core=require('web.core');const topBus=window.top.odoo.__DEBUG__.services['web.core'].bus;const{ColorpickerWidget}=require('web.Colorpicker');var ColorPaletteWidget=require('web_editor.ColorPalette').ColorPaletteWidget;var mixins=require('web.mixins');var fonts=require('wysiwyg.fonts');var rte=require('web_editor.rte');var ServicesMixin=require('web.ServicesMixin');var weWidgets=require('wysiwyg.widgets');var _t=core._t;var dom=$.summernote.core.dom;var range=$.summernote.core.range;var eventHandler=$.summernote.eventHandler;var renderer=$.summernote.renderer;function protectCommand(callback){return function(){var rng=range.create();var $sc=(rng&&rng.sc)?$(rng.sc).parents(':o_editable').last():$();var $ec=(rng&&rng.ec)?$(rng.ec).parents(':o_editable').last():$();$sc.addClass('o_we_command_protector');$ec.addClass('o_we_command_protector');var restore=function(){$sc.removeClass('o_we_command_protector');$ec.removeClass('o_we_command_protector');};var result;try{result=callback.apply(this,arguments);}catch(err){restore();throw err;}
restore();return result;};}
document.execCommand=protectCommand(document.execCommand);document.queryCommandState=protectCommand(document.queryCommandState);var tplButton=renderer.getTemplate().button;var tplIconButton=renderer.getTemplate().iconButton;var tplDropdown=renderer.getTemplate().dropdown;const processAndApplyColor=function(target,eventName,color,preview){if(!color){color='inherit';}else if(!ColorpickerWidget.isCSSColor(color)){color=(eventName==="foreColor"?'text-':'bg-')+color;}
var layoutInfo=dom.makeLayoutInfo(target);$.summernote.pluginEvents[eventName](undefined,eventHandler.modules.editor,layoutInfo,color,preview);};renderer.createPalette=function($container,options){const $dropdownContent=$container.find(".colorPalette");const parent=odoo.__DEBUG__.services['root.widget'];_.each($dropdownContent,elem=>{const eventName=elem.dataset.eventName;let colorpicker=null;const mutex=new concurrency.MutexedDropPrevious();const $dropdown=$(elem).closest('.btn-group, .dropdown');let manualOpening=false;$dropdown.on('hide.bs.dropdown',ev=>{return!(ev.clickEvent&&ev.clickEvent.originalEvent&&ev.clickEvent.originalEvent.__isColorpickerClick);});$dropdown.on('show.bs.dropdown',()=>{if(manualOpening){return true;}
mutex.exec(()=>{const oldColorpicker=colorpicker;const hookEl=oldColorpicker?oldColorpicker.el:elem;const r=range.create();const targetNode=r.sc;const targetElement=targetNode.nodeType===Node.ELEMENT_NODE?targetNode:targetNode.parentNode;colorpicker=new ColorPaletteWidget(parent,{excluded:['transparent_grayscale'],$editable:rte.Class.prototype.editable(),selectedColor:$(targetElement).css(eventName==="foreColor"?'color':'backgroundColor'),});colorpicker.on('custom_color_picked color_picked',null,ev=>{processAndApplyColor(ev.data.target,eventName,ev.data.color);});colorpicker.on('color_hover color_leave',null,ev=>{processAndApplyColor(ev.data.target,eventName,ev.data.color,true);});colorpicker.on('enter_key_color_colorpicker',null,()=>{$dropdown.children('.dropdown-toggle').dropdown('hide');});return colorpicker.replace(hookEl).then(()=>{if(oldColorpicker){oldColorpicker.destroy();}
manualOpening=true;$dropdown.children('.dropdown-toggle').dropdown('show');manualOpening=false;});});return false;});});};var fn_tplPopovers=renderer.tplPopovers;renderer.tplPopovers=function(lang,options){var $popover=$(fn_tplPopovers.call(this,lang,options));var $imagePopover=$popover.find('.note-image-popover');var $linkPopover=$popover.find('.note-link-popover');var $airPopover=$popover.find('.note-air-popover');$(tplIconButton('fa fa-align-center',{title:_t('Center'),event:'floatMe',value:'center'})).insertAfter($imagePopover.find('[data-event="floatMe"][data-value="left"]'));$imagePopover.find('button[data-event="removeMedia"]').parent().remove();$imagePopover.find('button[data-event="floatMe"][data-value="none"]').remove();var $padding=$('<div class="btn-group"/>');$padding.insertBefore($imagePopover.find('.btn-group:first'));var dropdown_content=['<li><a class="dropdown-item" data-event="padding" href="#" data-value="">'+_t('None')+'</a></li>','<li><a class="dropdown-item" data-event="padding" href="#" data-value="small">'+_t('Small')+'</a></li>','<li><a class="dropdown-item" data-event="padding" href="#" data-value="medium">'+_t('Medium')+'</a></li>','<li><a class="dropdown-item" data-event="padding" href="#" data-value="large">'+_t('Large')+'</a></li>','<li><a class="dropdown-item" data-event="padding" href="#" data-value="xl">'+_t('Xl')+'</a></li>',];$(tplIconButton('fa fa-plus-square-o',{title:_t('Padding'),dropdown:tplDropdown(dropdown_content)})).appendTo($padding);$imagePopover.find('[data-event="imageShape"]:not([data-value])').remove();var $button=$(tplIconButton('fa fa-sun-o',{title:_t('Shadow'),event:'imageShape',value:'shadow'})).insertAfter($imagePopover.find('[data-event="imageShape"][data-value="rounded-circle"]'));var $spin=$('<div class="btn-group d-none only_fa"/>').insertAfter($button.parent());$(tplIconButton('fa fa-refresh',{title:_t('Spin'),event:'imageShape',value:'fa-spin'})).appendTo($spin);var $resizefa=$('<div class="btn-group d-none only_fa"/>').insertAfter($imagePopover.find('.btn-group:has([data-event="resize"])'));for(var size=1;size<=5;size++){$(tplButton('<span class="note-fontsize-10">'+size+'x</span>',{title:size+"x",event:'resizefa',value:size+''})).appendTo($resizefa);}
var $colorfa=$airPopover.find('.note-color').clone();$colorfa.find(".dropdown-menu").css('min-width','172px');$resizefa.after($colorfa);var $imageprop=$('<div class="btn-group"/>');$imageprop.appendTo($imagePopover.find('.popover-body'));$(tplIconButton('fa fa-file-image-o',{title:_t('Edit'),event:'showImageDialog'})).appendTo($imageprop);$(tplIconButton('fa fa-trash-o',{title:_t('Remove'),event:'delete'})).appendTo($imageprop);$(tplIconButton('fa fa-crop',{title:_t('Crop Image'),event:'cropImage',})).insertAfter($imagePopover.find('[data-event="imageShape"][data-value="img-thumbnail"]'));$imagePopover.find('.popover-body').append($airPopover.find(".note-history").clone());$imagePopover.find('[data-event="showImageDialog"]').before($airPopover.find('[data-event="showLinkDialog"]').clone());var $alt=$('<div class="btn-group"/>');$alt.appendTo($imagePopover.find('.popover-body'));$alt.append('<button class="btn btn-secondary" data-event="alt"><strong>'+_t('Description')+': </strong><span class="o_image_alt"/></button>');$linkPopover.find('.popover-body').append($airPopover.find(".note-history").clone());$linkPopover.find('button[data-event="showLinkDialog"] i').attr("class","fa fa-link");$linkPopover.find('button[data-event="unlink"]').before($airPopover.find('button[data-event="showImageDialog"]').clone());$airPopover.find('.note-style .dropdown-toggle').on('mousedown',function(){var $format=$airPopover.find('[data-event="formatBlock"]');var node=range.create().sc;var formats=$format.map(function(){return $(this).data("value");}).get();while(node&&(!node.tagName||(!node.tagName||formats.indexOf(node.tagName.toLowerCase())===-1))){node=node.parentNode;}
$format.removeClass('active');$format.filter('[data-value="'+(node?node.tagName.toLowerCase():"p")+'"]').addClass("active");});setTimeout(function(){$airPopover.add($linkPopover).add($imagePopover).find("button").tooltip('dispose').tooltip({container:'body',trigger:'hover',placement:'bottom'}).on('click',function(){$(this).tooltip('hide');});});return $popover;};var fn_boutton_update=eventHandler.modules.popover.button.update;eventHandler.modules.popover.button.update=function($container,oStyle){var previous=$(".note-control-selection").data('target');if(previous){var $previous=$(previous);$previous.css({"-webkit-animation-play-state":"","animation-play-state":"","-webkit-transition":"","transition":"","-webkit-animation":"","animation":""});$previous.find('.o_we_selected_image').addBack('.o_we_selected_image').removeClass('o_we_selected_image');}
fn_boutton_update.call(this,$container,oStyle);$container.find('.note-color').removeClass('d-none');if(oStyle.image){$container.find('[data-event]').removeClass("active");$container.find('a[data-event="padding"][data-value="small"]').toggleClass("active",$(oStyle.image).hasClass("padding-small"));$container.find('a[data-event="padding"][data-value="medium"]').toggleClass("active",$(oStyle.image).hasClass("padding-medium"));$container.find('a[data-event="padding"][data-value="large"]').toggleClass("active",$(oStyle.image).hasClass("padding-large"));$container.find('a[data-event="padding"][data-value="xl"]').toggleClass("active",$(oStyle.image).hasClass("padding-xl"));$container.find('a[data-event="padding"][data-value=""]').toggleClass("active",!$container.find('li a.active[data-event="padding"]').length);$(oStyle.image).addClass('o_we_selected_image');if(dom.isImgFont(oStyle.image)){$container.find('.note-fore-color-preview > button > .caret').css('border-bottom-color',$(oStyle.image).css('color'));$container.find('.note-back-color-preview > button > .caret').css('border-bottom-color',$(oStyle.image).css('background-color'));$container.find('.btn-group:not(.only_fa):has(button[data-event="resize"],button[data-value="img-thumbnail"])').addClass('d-none');$container.find('.only_fa').removeClass('d-none');$container.find('button[data-event="resizefa"][data-value="2"]').toggleClass("active",$(oStyle.image).hasClass("fa-2x"));$container.find('button[data-event="resizefa"][data-value="3"]').toggleClass("active",$(oStyle.image).hasClass("fa-3x"));$container.find('button[data-event="resizefa"][data-value="4"]').toggleClass("active",$(oStyle.image).hasClass("fa-4x"));$container.find('button[data-event="resizefa"][data-value="5"]').toggleClass("active",$(oStyle.image).hasClass("fa-5x"));$container.find('button[data-event="resizefa"][data-value="1"]').toggleClass("active",!$container.find('.active[data-event="resizefa"]').length);$container.find('button[data-event="cropImage"]').addClass('d-none');$container.find('button[data-event="imageShape"][data-value="fa-spin"]').toggleClass("active",$(oStyle.image).hasClass("fa-spin"));$container.find('button[data-event="imageShape"][data-value="shadow"]').toggleClass("active",$(oStyle.image).hasClass("shadow"));$container.find('.btn-group:has(button[data-event="imageShape"])').removeClass("d-none");}else{$container.find('.d-none:not(.only_fa, .note-recent-color)').removeClass('d-none');$container.find('button[data-event="cropImage"]').removeClass('d-none');$container.find('.only_fa').addClass('d-none');var width=($(oStyle.image).attr('style')||'').match(/(^|;|\s)width:\s*([0-9]+%)/);if(width){width=width[2];}
$container.find('button[data-event="resize"][data-value="auto"]').toggleClass("active",width!=="100%"&&width!=="50%"&&width!=="25%");$container.find('button[data-event="resize"][data-value="1"]').toggleClass("active",width==="100%");$container.find('button[data-event="resize"][data-value="0.5"]').toggleClass("active",width==="50%");$container.find('button[data-event="resize"][data-value="0.25"]').toggleClass("active",width==="25%");$container.find('button[data-event="imageShape"][data-value="shadow"]').toggleClass("active",$(oStyle.image).hasClass("shadow"));if(!$(oStyle.image).is("img")){$container.find('.btn-group:has(button[data-event="imageShape"])').addClass('d-none');}
$container.find('.note-color').addClass('d-none');}
$container.find('button[data-event="floatMe"][data-value="left"]').toggleClass("active",$(oStyle.image).hasClass("float-left"));$container.find('button[data-event="floatMe"][data-value="center"]').toggleClass("active",$(oStyle.image).hasClass("d-block mx-auto"));$container.find('button[data-event="floatMe"][data-value="right"]').toggleClass("active",$(oStyle.image).hasClass("float-right"));$(oStyle.image).trigger('attributes_change');}else{$container.find('.note-fore-color-preview > button > .caret').css('border-bottom-color',oStyle.color);$container.find('.note-back-color-preview > button > .caret').css('border-bottom-color',oStyle['background-color']);}};var fn_toolbar_boutton_update=eventHandler.modules.toolbar.button.update;eventHandler.modules.toolbar.button.update=function($container,oStyle){fn_toolbar_boutton_update.call(this,$container,oStyle);$container.find('button[data-event="insertUnorderedList"]').toggleClass("active",$(oStyle.ancestors).is('ul:not(.o_checklist)'));$container.find('button[data-event="insertOrderedList"]').toggleClass("active",$(oStyle.ancestors).is('ol'));$container.find('button[data-event="insertCheckList"]').toggleClass("active",$(oStyle.ancestors).is('ul.o_checklist'));};var fn_popover_update=eventHandler.modules.popover.update;eventHandler.modules.popover.update=function($popover,oStyle,isAirMode){var $imagePopover=$popover.find('.note-image-popover');var $linkPopover=$popover.find('.note-link-popover');var $airPopover=$popover.find('.note-air-popover');fn_popover_update.call(this,$popover,oStyle,isAirMode);if(oStyle.image){if(oStyle.image.parentNode.className.match(/(^|\s)media_iframe_video(\s|$)/i)){oStyle.image=oStyle.image.parentNode;}
var alt=$(oStyle.image).attr("alt");$imagePopover.find('.o_image_alt').text((alt||"").replace(/&quot;/g,'"')).parent().toggle(oStyle.image.tagName==="IMG");$imagePopover.show();var target_node=oStyle.image;if(!oStyle.image.className.match(/(^|\s)media_iframe_video(\s|$)/i)){target_node=dom.firstChild(target_node);}
range.createFromNode(target_node).select();eventHandler.modules.editor.saveRange(dom.makeLayoutInfo(target_node).editable());}else{$(".note-control-selection").hide();}
if(oStyle.image||(oStyle.range&&(!oStyle.range.isCollapsed()||(oStyle.range.sc.tagName&&!dom.isAnchor(oStyle.range.sc))))||(oStyle.image&&!$(oStyle.image).closest('a').length)){$linkPopover.hide();oStyle.anchor=false;}
if(oStyle.image||oStyle.anchor||(oStyle.range&&(!$(oStyle.range.sc).closest('.note-editable').length||!$(oStyle.range.sc).parent().is(':o_editable')))){$airPopover.hide();}else{$airPopover.show();}
const $externalHistoryButtons=$('.o_we_external_history_buttons');if($externalHistoryButtons.length){const $noteHistory=$('.note-history');$noteHistory.addClass('d-none');$externalHistoryButtons.find(':first-child').prop('disabled',$noteHistory.find('[data-event=undo]').prop('disabled'));$externalHistoryButtons.find(':last-child').prop('disabled',$noteHistory.find('[data-event=redo]').prop('disabled'));}
$popover.trigger('summernote_popover_update_call');};var fn_handle_update=eventHandler.modules.handle.update;eventHandler.modules.handle.update=function($handle,oStyle,isAirMode){fn_handle_update.call(this,$handle,oStyle,isAirMode);if(oStyle.image){$handle.find('.note-control-selection').hide();}};function getImgTarget($editable){var $handle=$editable?dom.makeLayoutInfo($editable).handle():undefined;return $(".note-control-selection",$handle).data('target');}
eventHandler.modules.editor.padding=function($editable,sValue){var $target=$(getImgTarget($editable));var paddings="small medium large xl".split(/\s+/);$editable.data('NoteHistory').recordUndo();if(sValue.length){paddings.splice(paddings.indexOf(sValue),1);$target.toggleClass('padding-'+sValue);}
$target.removeClass("padding-"+paddings.join(" padding-"));};eventHandler.modules.editor.resize=function($editable,sValue){var $target=$(getImgTarget($editable));$editable.data('NoteHistory').recordUndo();var width=($target.attr('style')||'').match(/(^|;|\s)width:\s*([0-9]+)%/);if(width){width=width[2]/100;}
$target.css('width',(width!==sValue&&sValue!=="auto")?(sValue*100)+'%':'');};eventHandler.modules.editor.resizefa=function($editable,sValue){var $target=$(getImgTarget($editable));$editable.data('NoteHistory').recordUndo();$target.attr('class',$target.attr('class').replace(/\s*fa-[0-9]+x/g,''));if(+sValue>1){$target.addClass('fa-'+sValue+'x');}};eventHandler.modules.editor.floatMe=function($editable,sValue){var $target=$(getImgTarget($editable));$editable.data('NoteHistory').recordUndo();switch(sValue){case'center':$target.toggleClass('d-block mx-auto').removeClass('float-right float-left ml-auto');break;case'left':$target.toggleClass('float-left').removeClass('float-right d-block mx-auto ml-auto');break;case'right':$target.toggleClass('ml-auto float-right').removeClass('float-left d-block mx-auto');break;}};eventHandler.modules.editor.imageShape=function($editable,sValue){var $target=$(getImgTarget($editable));$editable.data('NoteHistory').recordUndo();$target.toggleClass(sValue);};eventHandler.modules.linkDialog.showLinkDialog=function($editable,$dialog,linkInfo){$editable.data('range').select();$editable.data('NoteHistory').recordUndo();var commonAncestor=linkInfo.range.commonAncestor();if(commonAncestor&&commonAncestor.closest){var link=commonAncestor.closest('a');linkInfo.className=link&&link.className;}
var def=new $.Deferred();topBus.trigger('link_dialog_demand',{$editable:$editable,linkInfo:linkInfo,onSave:function(linkInfo){linkInfo.range.select();$editable.data('range',linkInfo.range);def.resolve(linkInfo);$editable.trigger('keyup');$('.note-popover .note-link-popover').show();},onCancel:def.reject.bind(def),});return def;};var originalShowImageDialog=eventHandler.modules.imageDialog.showImageDialog;eventHandler.modules.imageDialog.showImageDialog=function($editable){var options=$editable.closest('.o_editable, .note-editor').data('options');if(options.disableFullMediaDialog){return originalShowImageDialog.apply(this,arguments);}
var r=$editable.data('range');if(r.sc.tagName&&r.sc.childNodes.length){r.sc=r.sc.childNodes[r.so];}
var media=$(r.sc).parents().addBack().filter(function(i,el){return dom.isImg(el);})[0];topBus.trigger('media_dialog_demand',{$editable:$editable,media:media,options:{onUpload:$editable.data('callbacks').onUpload,noVideos:options&&options.noVideos,},onSave:function(newMedia){if(!newMedia){return;}
if(media){$(media).replaceWith(newMedia);}else{r.insertNode(newMedia);}},});return new $.Deferred().reject();};$.summernote.pluginEvents.alt=function(event,editor,layoutInfo,sorted){var $editable=layoutInfo.editable();var $selection=layoutInfo.handle().find('.note-control-selection');topBus.trigger('alt_dialog_demand',{$editable:$editable,media:$selection.data('target'),});};$.summernote.pluginEvents.cropImage=function(event,editor,layoutInfo,sorted){var $editable=layoutInfo.editable();var $selection=layoutInfo.handle().find('.note-control-selection');topBus.trigger('crop_image_demand',{$editable:$editable,media:$selection.data('target'),});};var fn_is_void=dom.isVoid||function(){};dom.isVoid=function(node){return fn_is_void(node)||dom.isImgFont(node)||(node&&node.className&&node.className.match(/(^|\s)media_iframe_video(\s|$)/i));};var fn_is_img=dom.isImg||function(){};dom.isImg=function(node){return fn_is_img(node)||dom.isImgFont(node)||(node&&(node.nodeName==="IMG"||(node.className&&node.className.match(/(^|\s)(media_iframe_video|o_image)(\s|$)/i))));};var fn_is_forbidden_node=dom.isForbiddenNode||function(){};dom.isForbiddenNode=function(node){if(node.tagName==="BR"){return false;}
return fn_is_forbidden_node(node)||$(node).is(".media_iframe_video");};var fn_is_img_font=dom.isImgFont||function(){};dom.isImgFont=function(node){if(fn_is_img_font(node))return true;var nodeName=node&&node.nodeName.toUpperCase();var className=(node&&node.className||"");if(node&&(nodeName==="SPAN"||nodeName==="I")&&className.length){var classNames=className.split(/\s+/);for(var k=0;k<fonts.fontIcons.length;k++){if(_.intersection(fonts.fontIcons[k].alias,classNames).length){return true;}}}
return false;};var fn_is_font=dom.isFont;dom.isFont=function(node){return fn_is_font(node)||dom.isImgFont(node);};var fn_visible=$.summernote.pluginEvents.visible;$.summernote.pluginEvents.visible=function(event,editor,layoutInfo){var res=fn_visible.apply(this,arguments);var rng=range.create();if(!rng)return res;var $node=$(dom.node(rng.sc));if(($node.is('[data-oe-type="html"]')||$node.is('[data-oe-field="arch"]'))&&$node.hasClass("o_editable")&&!$node[0].children.length&&"h1 h2 h3 h4 h5 h6 p b bold i u code sup strong small pre th td span label".toUpperCase().indexOf($node[0].nodeName)===-1){var p=$('<p><br/></p>')[0];$node.append(p);range.createFromNode(p.firstChild).select();}
return res;};function prettify_html(html){html=html.trim();var result='',level=0,get_space=function(level){var i=level,space='';while(i--)space+='  ';return space;},reg=/^<\/?(a|span|font|u|em|i|strong|b)(\s|>)/i,inline_level=Infinity,tokens=_.compact(_.flatten(_.map(html.split(/</),function(value){value=value.replace(/\s+/g,' ').split(/>/);value[0]=/\S/.test(value[0])?'<'+value[0]+'>':'';return value;})));for(var i=0,l=tokens.length;i<l;i++){var token=tokens[i];var inline_tag=reg.test(token);var inline=inline_tag||inline_level<=level;if(token[0]==='<'&&token[1]==='/'){if(inline_tag&&inline_level===level){inline_level=Infinity;}
level--;}
if(!inline&&!/\S/.test(token)){continue;}
if(!inline||(token[1]!=='/'&&inline_level>level)){result+=get_space(level);}
if(token[0]==='<'&&token[1]!=='/'){level++;if(inline_tag&&inline_level>level){inline_level=level;}}
if(token.match(/^<(img|hr|br)/)){level--;}
if(!inline){token=token.trim();}
result+=token.replace(/\s+/,' ');if(inline_level>level){result+='\n';}}
return result;}
$.summernote.pluginEvents.codeview=function(event,editor,layoutInfo,enable){if(!layoutInfo){return;}
if(layoutInfo.toolbar){var is_activated=$.summernote.eventHandler.modules.codeview.isActivated(layoutInfo);if(is_activated===enable){return;}
return eventHandler.modules.codeview.toggle(layoutInfo);}else{var $editor=layoutInfo.editor();var $textarea=$editor.prev('textarea');if($textarea.is('textarea')===enable){return;}
if(!$textarea.length){var html=prettify_html($editor.prop("innerHTML"));$editor.parent().css({'position':'absolute','top':0,'bottom':0,'left':0,'right':0});$textarea=$('<textarea/>').css({'margin':'0 -4px','padding':'0 4px','border':0,'top':'51px','left':'620px','width':'100%','font-family':'sans-serif','font-size':'13px','height':'98%','white-space':'pre','word-wrap':'normal'}).val(html).data('init',html);$editor.before($textarea);$editor.hide();}else{$editor.prop('innerHTML',$textarea.val().replace(/\s*\n\s*/g,'')).trigger('content_changed');$textarea.remove();$editor.show();}}};var last_div;var last_div_change;var last_editable;var initial_data={};function reRangeSelectKey(event){initial_data.range=null;if(event.shiftKey&&event.keyCode>=37&&event.keyCode<=40&&!$(event.target).is("input, textarea, select")){var r=range.create();if(r){var rng=r.reRange(event.keyCode<=38);if(r!==rng){rng.select();}}}}
function reRangeSelect(event,dx,dy){var r=range.create();if(!r||r.isCollapsed())return;var data=r.reRange(dy<0||(dy===0&&dx<0));if(data.sc!==r.sc||data.so!==r.so||data.ec!==r.ec||data.eo!==r.eo){setTimeout(function(){data.select();$(data.sc.parentNode).closest('.note-popover');},0);}
$(data.sc).closest('.o_editable').data('range',r);return r;}
function summernote_mouseup(event){if($(event.target).closest("#web_editor-top-navbar, .note-popover").length){return;}
if(initial_data.event){var dx=event.clientX-(event.shiftKey&&initial_data.rect?initial_data.rect.left:initial_data.event.clientX);var dy=event.clientY-(event.shiftKey&&initial_data.rect?initial_data.rect.top:initial_data.event.clientY);if(10<Math.pow(dx,2)+Math.pow(dy,2)){reRangeSelect(event,dx,dy);}}
if(!$(event.target).closest(".o_editable").length){return;}
if(!initial_data.range||!event.shiftKey){setTimeout(function(){initial_data.range=range.create();},0);}}
var remember_selection;function summernote_mousedown(event){rte.history.splitNext();var $editable=$(event.target).closest(".o_editable, .note-editor");var r;if(document.documentMode){summernote_ie_fix(event,function(node){return node.tagName==="DIV"||node.tagName==="IMG"||(node.dataset&&node.dataset.oeModel);});}else if(last_div&&event.target!==last_div){if(last_div.tagName==="A"){summernote_ie_fix(event,function(node){return node.dataset&&node.dataset.oeModel;});}else if($editable.length){if(summernote_ie_fix(event,function(node){return node.tagName==="A";})){r=range.create();r.select();}}}
try{r=range.create();}catch(e){return;}
var editables=$(".o_editable[contenteditable], .note-editable[contenteditable]");var r_editable=editables.has((r||{}).sc).addBack(editables.filter((r||{}).sc));if(!r_editable.closest('.note-editor').is($editable)&&!r_editable.filter('.o_editable').is(editables)){var saved_editable=editables.has((remember_selection||{}).sc);if($editable.length&&!saved_editable.closest('.o_editable, .note-editor').is($editable)){remember_selection=range.create(dom.firstChild($editable[0]),0);}else if(!saved_editable.length){remember_selection=undefined;}
if(remember_selection){try{remember_selection.select();}catch(e){console.warn(e);}}}else if(r_editable.length){remember_selection=r;}
initial_data.event=event;if(event.shiftKey&&$editable.length){if(initial_data.range){initial_data.range.select();}
var rect=r&&r.getClientRects();initial_data.rect=rect&&rect.length?rect[0]:{top:0,left:0};}}
function summernote_ie_fix(event,pred){var editable;var div;var node=event.target;while(node.parentNode){if(!div&&pred(node)){div=node;}
if(last_div!==node&&(node.getAttribute('contentEditable')==='false'||node.className&&(node.className.indexOf('o_not_editable')!==-1))){break;}
if(node.className&&node.className.indexOf('o_editable')!==-1){if(!div){div=node;}
editable=node;break;}
node=node.parentNode;}
if(!editable){$(last_div_change).removeAttr("contentEditable").removeProp("contentEditable");$(last_editable).attr("contentEditable","true").prop("contentEditable","true");last_div_change=null;last_editable=null;return;}
if(div===last_div){return;}
last_div=div;$(last_div_change).removeAttr("contentEditable").removeProp("contentEditable");if(last_editable!==editable){if($(editable).is("[contentEditable='true']")){$(editable).removeAttr("contentEditable").removeProp("contentEditable");last_editable=editable;}else{last_editable=null;}}
if(!$(div).attr("contentEditable")&&!$(div).is("[data-oe-type='many2one'], [data-oe-type='contact']")){$(div).attr("contentEditable","true").prop("contentEditable","true");last_div_change=div;}else{last_div_change=null;}
return editable!==div?div:null;}
var fn_attach=eventHandler.attach;eventHandler.attach=function(oLayoutInfo,options){fn_attach.call(this,oLayoutInfo,options);oLayoutInfo.editor().on('dragstart','img',function(e){e.preventDefault();});$(document).on('mousedown',summernote_mousedown).on('mouseup',summernote_mouseup);oLayoutInfo.editor().off('click').on('click',function(e){e.preventDefault();});oLayoutInfo.editor().find('.note-image-dialog').on('click','.note-image-input',function(e){e.stopPropagation();});create_dblclick_feature("img, .media_iframe_video, i.fa, span.fa, a.o_image",function(){eventHandler.modules.imageDialog.show(oLayoutInfo);});create_dblclick_feature("a[href], a.btn, button.btn",function(){eventHandler.modules.linkDialog.show(oLayoutInfo);});oLayoutInfo.editable().on('mousedown',function(e){if(dom.isImg(e.target)&&dom.isContentEditable(e.target)){range.createFromNode(e.target).select();}});$(document).on("keyup",reRangeSelectKey);var clone_data=false;if(options.model){oLayoutInfo.editable().data({'oe-model':options.model,'oe-id':options.id});}
if(options.getMediaDomain){oLayoutInfo.editable().data('oe-media-domain',options.getMediaDomain);}
var $node=oLayoutInfo.editor();if($node.data('oe-model')||$node.data('oe-translation-id')){$node.on('content_changed',function(){var $nodes=$('[data-oe-model], [data-oe-translation-id]').filter(function(){return this!==$node[0];});if($node.data('oe-model')){$nodes=$nodes.filter('[data-oe-model="'+$node.data('oe-model')+'"]').filter('[data-oe-id="'+$node.data('oe-id')+'"]').filter('[data-oe-field="'+$node.data('oe-field')+'"]');}
if($node.data('oe-translation-id'))$nodes=$nodes.filter('[data-oe-translation-id="'+$node.data('oe-translation-id')+'"]');if($node.data('oe-type'))$nodes=$nodes.filter('[data-oe-type="'+$node.data('oe-type')+'"]');if($node.data('oe-expression'))$nodes=$nodes.filter('[data-oe-expression="'+$node.data('oe-expression')+'"]');if($node.data('oe-xpath'))$nodes=$nodes.filter('[data-oe-xpath="'+$node.data('oe-xpath')+'"]');if($node.data('oe-contact-options'))$nodes=$nodes.filter('[data-oe-contact-options="'+$node.data('oe-contact-options')+'"]');var nodes=$node.get();if($node.data('oe-type')==="many2one"){$nodes=$nodes.add($('[data-oe-model]').filter(function(){return this!==$node[0]&&nodes.indexOf(this)===-1;}).filter('[data-oe-many2one-model="'+$node.data('oe-many2one-model')+'"]').filter('[data-oe-many2one-id="'+$node.data('oe-many2one-id')+'"]').filter('[data-oe-type="many2one"]'));$nodes=$nodes.add($('[data-oe-model]').filter(function(){return this!==$node[0]&&nodes.indexOf(this)===-1;}).filter('[data-oe-model="'+$node.data('oe-many2one-model')+'"]').filter('[data-oe-id="'+$node.data('oe-many2one-id')+'"]').filter('[data-oe-field="name"]'));}
if(!clone_data){clone_data=true;$nodes.html(this.innerHTML);clone_data=false;}});}
var custom_toolbar=oLayoutInfo.toolbar?oLayoutInfo.toolbar():undefined;var $toolbar=$(oLayoutInfo.popover()).add(custom_toolbar);$('button[data-event="undo"], button[data-event="redo"]',$toolbar).attr('disabled',true);$(oLayoutInfo.editor()).add(oLayoutInfo.handle()).add(oLayoutInfo.popover()).add(custom_toolbar).on('click content_changed',function(){$('button[data-event="undo"]',$toolbar).attr('disabled',!oLayoutInfo.editable().data('NoteHistory').hasUndo());$('button[data-event="redo"]',$toolbar).attr('disabled',!oLayoutInfo.editable().data('NoteHistory').hasRedo());});function create_dblclick_feature(selector,callback){var show_tooltip=true;oLayoutInfo.editor().on("dblclick",selector,function(e){var $target=$(e.target);if(!dom.isContentEditable($target)){return;}
show_tooltip=false;callback();e.stopImmediatePropagation();});oLayoutInfo.editor().on("click",selector,function(e){var $target=$(e.target);if(!dom.isContentEditable($target)){return;}
show_tooltip=true;setTimeout(function(){if(!show_tooltip||$target.attr('title')!==undefined){return;}
$target.tooltip({title:_t('Double-click to edit'),trigger:'manuel',container:'body'}).tooltip('show');setTimeout(function(){$target.tooltip('dispose');},800);},400);});}};var fn_detach=eventHandler.detach;eventHandler.detach=function(oLayoutInfo,options){fn_detach.call(this,oLayoutInfo,options);oLayoutInfo.editable().off('mousedown');oLayoutInfo.editor().off("dragstart");oLayoutInfo.editor().off('click');$(document).off('mousedown',summernote_mousedown);$(document).off('mouseup',summernote_mouseup);oLayoutInfo.editor().off("dblclick");$(document).off("keyup",reRangeSelectKey);};$.summernote.lang.odoo={font:{bold:_t('Bold'),italic:_t('Italic'),underline:_t('Underline'),strikethrough:_t('Strikethrough'),subscript:_t('Subscript'),superscript:_t('Superscript'),clear:_t('Remove Font Style'),height:_t('Line Height'),name:_t('Font Family'),size:_t('Font Size')},image:{image:_t('File / Image'),insert:_t('Insert Image'),resizeFull:_t('Resize Full'),resizeHalf:_t('Resize Half'),resizeQuarter:_t('Resize Quarter'),floatLeft:_t('Float Left'),floatRight:_t('Float Right'),floatNone:_t('Float None'),dragImageHere:_t('Drag an image here'),selectFromFiles:_t('Select from files'),url:_t('Image URL'),remove:_t('Remove Image')},link:{link:_t('Link'),insert:_t('Insert Link'),unlink:_t('Unlink'),edit:_t('Edit'),textToDisplay:_t('Text to display'),url:_t('To what URL should this link go?'),openInNewWindow:_t('Open in new window')},video:{video:_t('Video'),videoLink:_t('Video Link'),insert:_t('Insert Video'),url:_t('Video URL?'),providers:_t('(YouTube, Vimeo, Vine, Instagram, DailyMotion or Youku)')},table:{table:_t('Table')},hr:{insert:_t('Insert Horizontal Rule')},style:{style:_t('Style'),normal:_t('Normal'),blockquote:_t('Quote'),pre:_t('Code'),small:_t('Small'),h1:_t('Header 1'),h2:_t('Header 2'),h3:_t('Header 3'),h4:_t('Header 4'),h5:_t('Header 5'),h6:_t('Header 6')},lists:{unordered:_t('Unordered list'),ordered:_t('Ordered list')},options:{help:_t('Help'),fullscreen:_t('Full Screen'),codeview:_t('Code View')},paragraph:{paragraph:_t('Paragraph'),outdent:_t('Outdent'),indent:_t('Indent'),left:_t('Align left'),center:_t('Align center'),right:_t('Align right'),justify:_t('Justify full')},color:{custom:_t('Custom Color'),background:_t('Background Color'),foreground:_t('Font Color'),transparent:_t('Transparent'),setTransparent:_t('None'),},shortcut:{shortcuts:_t('Keyboard shortcuts'),close:_t('Close'),textFormatting:_t('Text formatting'),action:_t('Action'),paragraphFormatting:_t('Paragraph formatting'),documentStyle:_t('Document Style')},history:{undo:_t('Undo'),redo:_t('Redo')}};var SummernoteManager=Class.extend(mixins.EventDispatcherMixin,ServicesMixin,{init:function(parent){mixins.EventDispatcherMixin.init.call(this);this.setParent(parent);topBus.on('alt_dialog_demand',this,this._onAltDialogDemand);topBus.on('crop_image_demand',this,this._onCropImageDemand);topBus.on('link_dialog_demand',this,this._onLinkDialogDemand);topBus.on('media_dialog_demand',this,this._onMediaDialogDemand);},destroy:function(){mixins.EventDispatcherMixin.destroy.call(this);topBus.off('alt_dialog_demand',this,this._onAltDialogDemand);topBus.off('crop_image_demand',this,this._onCropImageDemand);topBus.off('link_dialog_demand',this,this._onLinkDialogDemand);topBus.off('media_dialog_demand',this,this._onMediaDialogDemand);},saveModifiedImages:function($editable){const defs=_.map($editable,async editableEl=>{const{oeModel:resModel,oeId:resId}=editableEl.dataset;const proms=[...editableEl.querySelectorAll('.o_modified_image_to_save')].map(async el=>{const isBackground=!el.matches('img');el.classList.remove('o_modified_image_to_save');const newAttachmentSrc=await this._rpc({route:`/web_editor/modify_image/${el.dataset.originalId}`,params:{res_model:resModel,res_id:parseInt(resId),data:(isBackground?el.dataset.bgSrc:el.getAttribute('src')).split(',')[1],},});if(isBackground){$(el).css('background-image',`url('${newAttachmentSrc}')`);delete el.dataset.bgSrc;}else{el.setAttribute('src',newAttachmentSrc);}});return Promise.all(proms);});return Promise.all(defs);},_onAltDialogDemand:function(data){if(data.__alreadyDone){return;}
data.__alreadyDone=true;var altDialog=new weWidgets.AltDialog(this,data.options||{},data.media);if(data.onSave){altDialog.on('save',this,data.onSave);}
if(data.onCancel){altDialog.on('cancel',this,data.onCancel);}
altDialog.open();},_onCropImageDemand:function(data){if(data.__alreadyDone){return;}
data.__alreadyDone=true;new weWidgets.ImageCropWidget(this,data.media).appendTo(data.$editable.parent());},_onLinkDialogDemand:function(data){if(data.__alreadyDone){return;}
data.__alreadyDone=true;var linkDialog=new weWidgets.LinkDialog(this,data.options||{},data.$editable,data.linkInfo);if(data.onSave){linkDialog.on('save',this,data.onSave);}
if(data.onCancel){linkDialog.on('cancel',this,data.onCancel);}
linkDialog.open();},_onMediaDialogDemand:function(data){if(data.__alreadyDone){return;}
data.__alreadyDone=true;const model=data.$editable.data('oe-model');const field=data.$editable.data('oe-field');const type=data.$editable.data('oe-type');var mediaDialog=new weWidgets.MediaDialog(this,_.extend({res_model:model,res_id:data.$editable.data('oe-id'),domain:data.$editable.data('oe-media-domain'),useMediaLibrary:field&&(model==='ir.ui.view'&&field==='arch'||type==='html'),},data.options),data.media);if(data.onSave){mediaDialog.on('save',this,data.onSave);}
if(data.onCancel){mediaDialog.on('cancel',this,data.onCancel);}
mediaDialog.open();},});return SummernoteManager;});;

/* /web_editor/static/src/js/editor/image_processing.js defined in bundle 'web_editor.assets_wysiwyg' */
odoo.define('web_editor.image_processing',function(require){'use strict';const cropperDataFields=['x','y','width','height','rotate','scaleX','scaleY'];const modifierFields=['filter','quality','mimetype','glFilter','originalId','originalSrc','resizeWidth','aspectRatio',];const _applyAll=(result,filter,filters)=>{filters.forEach(f=>{if(f[0]==='blend'){const cv=f[1];const ctx=result.getContext('2d');ctx.globalCompositeOperation=f[2];ctx.globalAlpha=f[3];ctx.drawImage(cv,0,0);ctx.globalCompositeOperation='source-over';ctx.globalAlpha=1.0;}else{filter.addFilter(...f);}});};let applyAll;const glFilters={blur:filter=>filter.addFilter('blur',10),'1977':(filter,cv)=>{const ctx=cv.getContext('2d');ctx.fillStyle='rgb(243, 106, 188)';ctx.fillRect(0,0,cv.width,cv.height);applyAll(filter,[['blend',cv,'screen',.3],['brightness',.1],['contrast',.1],['saturation',.3],]);},aden:(filter,cv)=>{const ctx=cv.getContext('2d');ctx.fillStyle='rgb(66, 10, 14)';ctx.fillRect(0,0,cv.width,cv.height);applyAll(filter,[['blend',cv,'darken',.2],['brightness',.2],['contrast',-.1],['saturation',-.15],['hue',20],]);},brannan:(filter,cv)=>{const ctx=cv.getContext('2d');ctx.fillStyle='rgb(161, 44, 191)';ctx.fillRect(0,0,cv.width,cv.height);applyAll(filter,[['blend',cv,'lighten',.31],['sepia',.5],['contrast',.4],]);},earlybird:(filter,cv)=>{const ctx=cv.getContext('2d');const gradient=ctx.createRadialGradient(cv.width/2,cv.height/2,0,cv.width/2,cv.height/2,Math.hypot(cv.width,cv.height)/2);gradient.addColorStop(.2,'#D0BA8E');gradient.addColorStop(1,'#1D0210');ctx.fillStyle=gradient;ctx.fillRect(0,0,cv.width,cv.height);applyAll(filter,[['blend',cv,'overlay',.2],['sepia',.2],['contrast',-.1],]);},inkwell:(filter,cv)=>{applyAll(filter,[['sepia',.3],['brightness',.1],['contrast',-.1],['desaturateLuminance'],]);},maven:(filter,cv)=>{applyAll(filter,[['sepia',.25],['brightness',-.05],['contrast',-.05],['saturation',.5],]);},toaster:(filter,cv)=>{const ctx=cv.getContext('2d');const gradient=ctx.createRadialGradient(cv.width/2,cv.height/2,0,cv.width/2,cv.height/2,Math.hypot(cv.width,cv.height)/2);gradient.addColorStop(0,'#0F4E80');gradient.addColorStop(1,'#3B003B');ctx.fillStyle=gradient;ctx.fillRect(0,0,cv.width,cv.height);applyAll(filter,[['blend',cv,'screen',.5],['brightness',-.1],['contrast',.5],]);},walden:(filter,cv)=>{const ctx=cv.getContext('2d');ctx.fillStyle='#CC4400';ctx.fillRect(0,0,cv.width,cv.height);applyAll(filter,[['blend',cv,'screen',.3],['sepia',.3],['brightness',.1],['saturation',.6],['hue',350],]);},valencia:(filter,cv)=>{const ctx=cv.getContext('2d');ctx.fillStyle='#3A0339';ctx.fillRect(0,0,cv.width,cv.height);applyAll(filter,[['blend',cv,'exclusion',.5],['sepia',.08],['brightness',.08],['contrast',.08],]);},xpro:(filter,cv)=>{const ctx=cv.getContext('2d');const gradient=ctx.createRadialGradient(cv.width/2,cv.height/2,0,cv.width/2,cv.height/2,Math.hypot(cv.width,cv.height)/2);gradient.addColorStop(.4,'#E0E7E6');gradient.addColorStop(1,'#2B2AA1');ctx.fillStyle=gradient;ctx.fillRect(0,0,cv.width,cv.height);applyAll(filter,[['blend',cv,'color-burn',.7],['sepia',.3],]);},custom:(filter,cv,filterOptions)=>{const options=Object.assign({blend:'normal',filterColor:'',blur:'0',desaturateLuminance:'0',saturation:'0',contrast:'0',brightness:'0',sepia:'0',},JSON.parse(filterOptions||"{}"));const filters=[];if(options.filterColor){const ctx=cv.getContext('2d');ctx.fillStyle=options.filterColor;ctx.fillRect(0,0,cv.width,cv.height);filters.push(['blend',cv,options.blend,1]);}
delete options.blend;delete options.filterColor;filters.push(...Object.entries(options).map(([filter,amount])=>[filter,parseInt(amount)/100]));applyAll(filter,filters);},};async function applyModifications(img){const data=Object.assign({glFilter:'',filter:'#0000',quality:'75',},img.dataset);let{width,height,resizeWidth,quality,filter,mimetype,originalSrc,glFilter,filterOptions,}=data;[width,height,resizeWidth]=[width,height,resizeWidth].map(s=>parseFloat(s));quality=parseInt(quality);const container=document.createElement('div');const original=await loadImage(originalSrc);container.appendChild(original);await activateCropper(original,0,data);const croppedImg=$(original).cropper('getCroppedCanvas',{width,height});$(original).cropper('destroy');const result=document.createElement('canvas');result.width=resizeWidth||croppedImg.width;result.height=croppedImg.height*result.width/croppedImg.width;const ctx=result.getContext('2d');ctx.imageSmoothingQuality="high";ctx.mozImageSmoothingEnabled=true;ctx.webkitImageSmoothingEnabled=true;ctx.msImageSmoothingEnabled=true;ctx.imageSmoothingEnabled=true;ctx.drawImage(croppedImg,0,0,croppedImg.width,croppedImg.height,0,0,result.width,result.height);if(glFilter){const glf=new window.WebGLImageFilter();const cv=document.createElement('canvas');cv.width=result.width;cv.height=result.height;applyAll=_applyAll.bind(null,result);glFilters[glFilter](glf,cv,filterOptions);const filtered=glf.apply(result);ctx.drawImage(filtered,0,0,filtered.width,filtered.height,0,0,result.width,result.height);}
ctx.fillStyle=filter||'#0000';ctx.fillRect(0,0,result.width,result.height);return result.toDataURL(mimetype,quality/100);}
function loadImage(src,img=new Image()){return new Promise((resolve,reject)=>{img.addEventListener('load',()=>resolve(img),{once:true});img.addEventListener('error',reject,{once:true});img.src=src;});}
const imageCache=new Map();async function activateCropper(image,aspectRatio,dataset){const src=image.getAttribute('src');if(!imageCache.has(src)){const res=await fetch(src);imageCache.set(src,URL.createObjectURL(await res.blob()));}
image.src=imageCache.get(src);$(image).cropper({viewMode:2,dragMode:'move',autoCropArea:1.0,aspectRatio:aspectRatio,data:_.mapObject(_.pick(dataset,...cropperDataFields),value=>parseFloat(value)),minContainerWidth:1,minContainerHeight:1,});return new Promise(resolve=>image.addEventListener('ready',resolve,{once:true}));}
async function loadImageInfo(img,rpc){const src=img.getAttribute('src');if(img.dataset.originalSrc||!src){return;}
const{original}=await rpc({route:'/web_editor/get_image_info',params:{src:src.split(/[?#]/)[0]},});const isLocal=original&&new URL(original.image_src,window.location.origin).origin===window.location.origin;if(isLocal&&original.image_src){img.dataset.originalId=original.id;img.dataset.originalSrc=original.image_src;img.dataset.mimetype=original.mimetype;}}
return{applyModifications,cropperDataFields,activateCropper,loadImageInfo,loadImage,removeOnImageChangeAttrs:[...cropperDataFields,...modifierFields,'aspectRatio'],};});;

/* /web_editor/static/src/js/editor/custom_colors.js defined in bundle 'web_editor.assets_wysiwyg' */
;

/* /web_editor/static/src/js/wysiwyg/widgets/media.js defined in bundle 'web_editor.assets_wysiwyg' */
odoo.define('wysiwyg.widgets.media',function(require){'use strict';var concurrency=require('web.concurrency');const config=require('web.config');var core=require('web.core');var Dialog=require('web.Dialog');var dom=require('web.dom');var fonts=require('wysiwyg.fonts');var utils=require('web.utils');var Widget=require('web.Widget');var session=require('web.session');const{removeOnImageChangeAttrs}=require('web_editor.image_processing');const{getCSSVariableValue,DEFAULT_PALETTE}=require('web_editor.utils');var QWeb=core.qweb;var _t=core._t;var MediaWidget=Widget.extend({xmlDependencies:['/web_editor/static/src/xml/wysiwyg.xml'],init:function(parent,media,options){this._super.apply(this,arguments);this.media=media;this.$media=$(media);},clear:function(){if(!this.media){return;}
this._clear();},save:function(){},_clear:function(){},});var SearchableMediaWidget=MediaWidget.extend({events:_.extend({},MediaWidget.prototype.events||{},{'input .o_we_search':'_onSearchInput',}),init:function(){this._super.apply(this,arguments);this._onSearchInput=_.debounce(this._onSearchInput,500);},search:function(needle){},_renderThumbnails:function(){},_onSearchInput:function(ev){this.attachments=[];this.search($(ev.currentTarget).val()||'').then(()=>this._renderThumbnails());this.hasSearched=true;},});var FileWidget=SearchableMediaWidget.extend({events:_.extend({},SearchableMediaWidget.prototype.events||{},{'click .o_upload_media_button':'_onUploadButtonClick','change .o_file_input':'_onFileInputChange','click .o_upload_media_url_button':'_onUploadURLButtonClick','input .o_we_url_input':'_onURLInputChange','click .o_existing_attachment_cell':'_onAttachmentClick','click .o_existing_attachment_remove':'_onRemoveClick','click .o_load_more':'_onLoadMoreClick',}),existingAttachmentsTemplate:undefined,IMAGE_MIMETYPES:['image/gif','image/jpe','image/jpeg','image/jpg','image/gif','image/png','image/svg+xml'],NUMBER_OF_ATTACHMENTS_TO_DISPLAY:30,MAX_DB_ATTACHMENTS:5,init:function(parent,media,options){this._super.apply(this,arguments);this._mutex=new concurrency.Mutex();this.numberOfAttachmentsToDisplay=this.NUMBER_OF_ATTACHMENTS_TO_DISPLAY;this.options=_.extend({mediaWidth:media&&media.parentElement&&$(media.parentElement).width(),useMediaLibrary:true,},options||{});this.attachments=[];this.selectedAttachments=[];this.libraryMedia=[];this.selectedMedia=[];this._onUploadURLButtonClick=dom.makeAsyncHandler(this._onUploadURLButtonClick);},start:function(){var def=this._super.apply(this,arguments);var self=this;this.$urlInput=this.$('.o_we_url_input');this.$form=this.$('form');this.$fileInput=this.$('.o_file_input');this.$uploadButton=this.$('.o_upload_media_button');this.$addUrlButton=this.$('.o_upload_media_url_button');this.$urlSuccess=this.$('.o_we_url_success');this.$urlWarning=this.$('.o_we_url_warning');this.$urlError=this.$('.o_we_url_error');this.$errorText=this.$('.o_we_error_text');var o={url:null,alt:null,};if(this.$media.is('img')){o.url=this.$media.attr('src');}else if(this.$media.is('a.o_image')){o.url=this.$media.attr('href').replace(/[?].*/,'');o.id=+o.url.match(/\/web\/content\/(\d+)/,'')[1];}
return this.search('').then(async()=>{await this._renderThumbnails();if(o.url){self._selectAttachement(_.find(self.attachments,function(attachment){return o.url===attachment.image_src;})||o);}
return def;});},save:function(){return this._mutex.exec(this._save.bind(this));},search:function(needle){this.needle=needle;return this.fetchAttachments(this.NUMBER_OF_ATTACHMENTS_TO_DISPLAY,0);},fetchAttachments:function(number,offset){return this._rpc({model:'ir.attachment',method:'search_read',args:[],kwargs:{domain:this._getAttachmentsDomain(this.needle),fields:['name','mimetype','description','checksum','url','type','res_id','res_model','public','access_token','image_src','image_width','image_height','original_id'],order:[{name:'id',asc:false}],context:this.options.context,limit:number+1,offset:offset,},}).then(attachments=>{this.attachments=this.attachments.slice();Array.prototype.splice.apply(this.attachments,[offset,attachments.length].concat(attachments));});},hasContent(){return this.attachments.length;},_clear:function(){this.media.className=this.media.className&&this.media.className.replace(/(^|\s+)(o_image)(?=\s|$)/g,' ');},_getAttachmentsDomain:function(needle){var domain=this.options.attachmentIDs&&this.options.attachmentIDs.length?['|',['id','in',this.options.attachmentIDs]]:[];var attachedDocumentDomain=['&',['res_model','=',this.options.res_model],['res_id','=',this.options.res_id|0]];if(!this.options.res_id){attachedDocumentDomain.unshift('&');attachedDocumentDomain.push(['create_uid','=',this.options.user_id]);}
if(this.options.data_res_model){var relatedDomain=['&',['res_model','=',this.options.data_res_model],['res_id','=',this.options.data_res_id|0]];if(!this.options.data_res_id){relatedDomain.unshift('&');relatedDomain.push(['create_uid','=',session.uid]);}
domain=domain.concat(['|'],attachedDocumentDomain,relatedDomain);}else{domain=domain.concat(attachedDocumentDomain);}
domain=['|',['public','=',true]].concat(domain);domain=domain.concat(this.options.mimetypeDomain);if(needle&&needle.length){domain.push(['name','ilike',needle]);}
if(!this.options.useMediaLibrary){domain.push('|',['url','=',false],'!',['url','=ilike','/web_editor/shape/%']);}
domain.push('!',['name','=like','%.crop']);domain.push('|',['type','=','binary'],'!',['url','=like','/%/static/%']);return domain;},_highlightSelected:function(){var self=this;this.$('.o_existing_attachment_cell.o_we_attachment_selected').removeClass("o_we_attachment_selected");_.each(this.selectedAttachments,function(attachment){self.$('.o_existing_attachment_cell[data-id='+attachment.id+']').addClass("o_we_attachment_selected").css('display','');});},_handleNewAttachment:function(attachment){this.attachments=this.attachments.filter(att=>att.id!==attachment.id);this.attachments.unshift(attachment);this._renderThumbnails();this._selectAttachement(attachment);},_loadMoreImages:function(forceSearch){return this.fetchAttachments(10,this.numberOfAttachmentsToDisplay).then(()=>{this.numberOfAttachmentsToDisplay+=10;if(!forceSearch){this._renderThumbnails();return Promise.resolve();}else{return this.search(this.$('.o_we_search').val()||'');}});},_renderExisting:function(attachments){return QWeb.render(this.existingAttachmentsTemplate,{attachments:attachments,widget:this,});},_renderThumbnails:function(){var attachments=this.attachments.slice(0,this.numberOfAttachmentsToDisplay);this.$('.o_we_existing_attachments').replaceWith(this._renderExisting(attachments));this._highlightSelected();this.$('.o_we_load_more').toggleClass('d-none',!this.hasContent());var noLoadMoreButton=this.NUMBER_OF_ATTACHMENTS_TO_DISPLAY>=this.attachments.length;var noMoreImgToLoad=this.numberOfAttachmentsToDisplay>=this.attachments.length;this.$('.o_load_done_msg').toggleClass('d-none',noLoadMoreButton||!noMoreImgToLoad);this.$('.o_load_more').toggleClass('d-none',noMoreImgToLoad);},_save:async function(){const toSave=Object.fromEntries(this.selectedMedia.map(media=>[media.id,{query:media.query||'',is_dynamic_svg:!!media.isDynamicSVG,}]));let mediaAttachments=[];if(Object.keys(toSave).length!==0){mediaAttachments=await this._rpc({route:'/web_editor/save_library_media',params:{media:toSave,},});}
const selected=this.selectedAttachments.concat(mediaAttachments).map(attachment=>{if(attachment.image_src&&attachment.image_src.startsWith('/web_editor/shape/')){const colorCustomizedURL=new URL(attachment.image_src,window.location.origin);colorCustomizedURL.searchParams.set('c1',getCSSVariableValue('o-color-1'));attachment.image_src=colorCustomizedURL.pathname+colorCustomizedURL.search;}
return attachment;});if(this.options.multiImages){return selected;}
const img=selected[0];if(!img||!img.id||this.$media.attr('src')===img.image_src){return this.media;}
if(!img.public&&!img.access_token){await this._rpc({model:'ir.attachment',method:'generate_access_token',args:[[img.id]]}).then(function(access_token){img.access_token=access_token[0];});}
if(img.image_src){var src=img.image_src;if(!img.public&&img.access_token){src+=_.str.sprintf('?access_token=%s',encodeURIComponent(img.access_token));}
if(!this.$media.is('img')){this.$media=$('<img/>',{class:'img-fluid o_we_custom_image'});this.media=this.$media[0];}
this.$media.attr('src',src);}else{if(!this.$media.is('a')){$('.note-control-selection').hide();this.$media=$('<a/>');this.media=this.$media[0];}
var href='/web/content/'+img.id+'?';if(!img.public&&img.access_token){href+=_.str.sprintf('access_token=%s&',img.access_token);}
href+='unique='+img.checksum+'&download=true';this.$media.attr('href',href);this.$media.addClass('o_image').attr('title',img.name);}
this.$media.attr('alt',img.alt||img.description||'');var style=this.style;if(style){this.$media.css(style);}
removeOnImageChangeAttrs.forEach(attr=>{delete this.media.dataset[attr];});if(!img.image_src){this.media.dataset.mimetype=img.mimetype;}
this.media.classList.remove('o_modified_image_to_save');this.$media.trigger('image_changed');return this.media;},_selectAttachement:function(attachment,save,{type='attachment'}={}){const possibleProps={'attachment':'selectedAttachments','media':'selectedMedia'};const prop=possibleProps[type];if(this.options.multiImages){const index=this[prop].indexOf(attachment);if(index!==-1){if(!save){this[prop].splice(index,1);}}else{this[prop].push(attachment);}}else{Object.values(possibleProps).forEach(prop=>{this[prop]=[];});this[prop]=[attachment];}
this._highlightSelected();if(save){this.trigger_up('save_request');}},_updateAddUrlUi:function(emptyValue,isURL,isImage){this.$addUrlButton.toggleClass('btn-secondary',emptyValue).toggleClass('btn-primary',!emptyValue).prop('disabled',!isURL);this.$urlSuccess.toggleClass('d-none',!isURL);this.$urlError.toggleClass('d-none',emptyValue||isURL);},_onAttachmentClick:function(ev){const attachment=ev.currentTarget;const{id:attachmentID,mediaId}=attachment.dataset;if(attachmentID){const attachment=this.attachments.find(attachment=>attachment.id===parseInt(attachmentID));this._selectAttachement(attachment,!this.options.multiImages);}else if(mediaId){const media=this.libraryMedia.find(media=>media.id===parseInt(mediaId));this._selectAttachement(media,!this.options.multiImages,{type:'media'});}},_onFileInputChange:function(){return this._mutex.exec(this._addData.bind(this));},async _addData(){let files=this.$fileInput[0].files;if(!files.length){return;}
var self=this;var uploadMutex=new concurrency.Mutex();files=_.sortBy(files,'size');_.each(files,function(file){uploadMutex.exec(function(){return utils.getDataURLFromFile(file).then(function(result){return self._rpc({route:'/web_editor/attachment/add_data',params:{'name':file.name,'data':result.split(',')[1],'res_id':self.options.res_id,'res_model':self.options.res_model,'width':0,'quality':0,'generate_access_token':true,},}).then(function(attachment){self.trigger_up('wysiwyg_attachment',attachment);self._handleNewAttachment(attachment);});});});});return uploadMutex.getUnlockedDef().then(function(){if(!self.options.multiImages&&!self.noSave){self.trigger_up('save_request');}
self.noSave=false;});},_onRemoveClick:function(ev){var self=this;ev.stopPropagation();Dialog.confirm(this,_t("Are you sure you want to delete this file ?"),{confirm_callback:function(){var $a=$(ev.currentTarget).closest('.o_existing_attachment_cell');var id=parseInt($a.data('id'),10);var attachment=_.findWhere(self.attachments,{id:id});return self._rpc({route:'/web_editor/attachment/remove',params:{ids:[id],},}).then(function(prevented){if(_.isEmpty(prevented)){self.attachments=_.without(self.attachments,attachment);self.attachments.filter(at=>at.original_id[0]===attachment.id).forEach(at=>delete at.original_id);if(!self.attachments.length){self._renderThumbnails();}else{$a.closest('.o_existing_attachment_cell').remove();}
return;}
self.$errorText.replaceWith(QWeb.render('wysiwyg.widgets.image.existing.error',{views:prevented[id],widget:self,}));});}});},_onURLInputChange:function(){var inputValue=this.$urlInput.val();var emptyValue=(inputValue==='');var isURL=/^.+\..+$/.test(inputValue);var isImage=_.any(['.gif','.jpeg','.jpe','.jpg','.png'],function(format){return inputValue.endsWith(format);});this._updateAddUrlUi(emptyValue,isURL,isImage);},_onUploadButtonClick:function(){this.$fileInput.click();},_onUploadURLButtonClick:function(){if(this.$urlInput.is('.o_we_horizontal_collapse')){this.$urlInput.removeClass('o_we_horizontal_collapse');this.$addUrlButton.attr('disabled','disabled');return;}
return this._mutex.exec(this._addUrl.bind(this));},_addUrl:function(){var self=this;return this._rpc({route:'/web_editor/attachment/add_url',params:{'url':this.$urlInput.val(),'res_id':this.options.res_id,'res_model':this.options.res_model,},}).then(function(attachment){self.$urlInput.val('');self._onURLInputChange();self._handleNewAttachment(attachment);if(!self.options.multiImages){self.trigger_up('save_request');}});},_onLoadMoreClick:function(){this._loadMoreImages();},_onSearchInput:function(){this.attachments=[];this.numberOfAttachmentsToDisplay=this.NUMBER_OF_ATTACHMENTS_TO_DISPLAY;this._super.apply(this,arguments);},});var ImageWidget=FileWidget.extend({template:'wysiwyg.widgets.image',existingAttachmentsTemplate:'wysiwyg.widgets.image.existing.attachments',events:Object.assign({},FileWidget.prototype.events,{'change input.o_we_show_optimized':'_onShowOptimizedChange','change .o_we_search_select':'_onSearchSelect',}),MIN_ROW_HEIGHT:128,init:function(parent,media,options){this.searchService='all';options=_.extend({accept:'image/*',mimetypeDomain:[['mimetype','in',this.IMAGE_MIMETYPES]],},options||{});this._onAttachmentImageLoad=this._onAttachmentImageLoad.bind(this);this._super(parent,media,options);},start:async function(){await this._super(...arguments);this.el.addEventListener('load',this._onAttachmentImageLoad,true);},destroy:function(){this.el.removeEventListener('load',this._onAttachmentImageLoad,true);return this._super(...arguments);},async fetchAttachments(number,offset){if(this.needle&&this.searchService!=='database'){number=this.MAX_DB_ATTACHMENTS;offset=0;}
const result=await this._super(number,offset);const primaryColor=getCSSVariableValue('o-color-1');this.attachments.forEach(attachment=>{if(attachment.image_src.startsWith('/')){const newURL=new URL(attachment.image_src,window.location.origin);if(attachment.image_src.startsWith('/web_editor/shape/')){newURL.searchParams.set('c1',primaryColor);}else{newURL.searchParams.set('height',2*this.MIN_ROW_HEIGHT);}
attachment.thumbnail_src=newURL.pathname+newURL.search;}});if(this.needle&&this.options.useMediaLibrary){try{const response=await this._rpc({route:'/web_editor/media_library_search',params:{'query':this.needle,'offset':this.libraryMedia.length,},});const newMedia=response.media;this.nbMediaResults=response.results;this.libraryMedia.push(...newMedia);}catch(e){console.error(`Couldn't reach API endpoint.`);}}
return result;},hasContent(){if(this.searchService==='all'){return this._super(...arguments)||this.libraryMedia.length;}else if(this.searchService==='media-library'){return!!this.libraryMedia.length;}
return this._super(...arguments);},_updateAddUrlUi:function(emptyValue,isURL,isImage){this._super.apply(this,arguments);this.$addUrlButton.text((isURL&&!isImage)?_t("Add as document"):_t("Add image"));const warning=isURL&&!isImage;this.$urlWarning.toggleClass('d-none',!warning);if(warning){this.$urlSuccess.addClass('d-none');}},_renderThumbnails:function(){const alreadyLoaded=this.$('.o_existing_attachment_cell[data-loaded="true"]');this._super(...arguments);this.$('.o_existing_attachment_cell').addClass('d-none');alreadyLoaded.each((index,el)=>{const toReplace=this.$(`.o_existing_attachment_cell[data-id="${el.dataset.id}"], .o_existing_attachment_cell[data-media-id="${el.dataset.mediaId}"]`);if(toReplace.length){toReplace.replaceWith(el);}});this._toggleOptimized(this.$('input.o_we_show_optimized')[0].checked);const placeholderWidth=3/2*this.MIN_ROW_HEIGHT;this.$('.o_we_attachment_placeholder').css({flexGrow:placeholderWidth,flexBasis:placeholderWidth,});if(this.needle&&['media-library','all'].includes(this.searchService)){const noMoreImgToLoad=this.libraryMedia.length===this.nbMediaResults;const noLoadMoreButton=noMoreImgToLoad&&this.libraryMedia.length<=15;this.$('.o_load_done_msg').toggleClass('d-none',noLoadMoreButton||!noMoreImgToLoad);this.$('.o_load_more').toggleClass('d-none',noMoreImgToLoad);}},_renderExisting:function(attachments){if(this.needle&&this.searchService!=='database'){attachments=attachments.slice(0,this.MAX_DB_ATTACHMENTS);}
return QWeb.render(this.existingAttachmentsTemplate,{attachments:attachments,libraryMedia:this.libraryMedia,widget:this,});},_toggleOptimized:function(value){this.$('.o_we_attachment_optimized').each((i,cell)=>cell.style.setProperty('display',value?null:'none','important'));},_highlightSelected:function(){this._super(...arguments);this.selectedMedia.forEach(media=>{this.$(`.o_existing_attachment_cell[data-media-id=${media.id}]`).addClass("o_we_attachment_selected");});},_getAttachmentsDomain(needle){const domain=this._super(...arguments);if(!config.isDebug()){const subDomain=[false];const originalId=this.$media.length&&this.$media[0].dataset.originalId;if(originalId){subDomain.push(originalId);}
domain.push(['original_id','in',subDomain]);}
return domain;},_onAttachmentImageLoad:async function(ev){const img=ev.target;const cell=img.closest('.o_existing_attachment_cell');if(!cell){return;}
if(cell.dataset.mediaId&&!img.src.startsWith('blob')){const mediaUrl=img.src;try{const response=await fetch(mediaUrl);if(response.headers.get('content-type')==='image/svg+xml'){const svg=await response.text();const colorRegex=new RegExp(DEFAULT_PALETTE['1'],'gi');if(colorRegex.test(svg)){const fileName=mediaUrl.split('/').pop();const file=new File([svg.replace(colorRegex,getCSSVariableValue('o-color-1'))],fileName,{type:"image/svg+xml",});img.src=URL.createObjectURL(file);const media=this.libraryMedia.find(media=>media.id===parseInt(cell.dataset.mediaId));if(media){media.isDynamicSVG=true;}
return;}}}catch(e){console.error('CORS is misconfigured on the API server, image will be treated as non-dynamic.');}}
let aspectRatio=img.naturalWidth/img.naturalHeight;if(img.naturalHeight===0){img.width=1000;img.style.position='fixed';img.style.opacity='0';const originalParent=img.parentElement;document.body.appendChild(img);aspectRatio=img.width/img.height;originalParent.appendChild(img);img.removeAttribute('width');img.style.removeProperty('position');img.style.removeProperty('opacity');}
const width=aspectRatio*this.MIN_ROW_HEIGHT;cell.style.flexGrow=width;cell.style.flexBasis=`${width}px`;cell.classList.remove('d-none');cell.classList.add('d-flex');cell.dataset.loaded='true';},_onShowOptimizedChange:function(ev){this._toggleOptimized(ev.target.checked);},_onSearchSelect:function(ev){const{value}=ev.target;this.searchService=value;this.$('.o_we_search').trigger('input');},_onSearchInput:function(ev){this.libraryMedia=[];this._super(...arguments);},_clear:function(type){var allImgClasses=/(^|\s+)(img|img-\S*|o_we_custom_image|rounded-circle|rounded|thumbnail|shadow|w-25|w-50|w-75|w-100|o_modified_image_to_save)(?=\s|$)/g;this.media.className=this.media.className&&this.media.className.replace(allImgClasses,' ');},});var DocumentWidget=FileWidget.extend({template:'wysiwyg.widgets.document',existingAttachmentsTemplate:'wysiwyg.widgets.document.existing.attachments',init:function(parent,media,options){options=_.extend({accept:'*/*',mimetypeDomain:[['mimetype','not in',this.IMAGE_MIMETYPES]],},options||{});this._super(parent,media,options);},_updateAddUrlUi:function(emptyValue,isURL,isImage){this._super.apply(this,arguments);this.$addUrlButton.text((isURL&&isImage)?_t("Add as image"):_t("Add document"));const warning=isURL&&isImage;this.$urlWarning.toggleClass('d-none',!warning);if(warning){this.$urlSuccess.addClass('d-none');}},_getAttachmentsDomain:function(needle){var domain=this._super.apply(this,arguments);return domain.concat('!',utils.assetsDomain());},});var IconWidget=SearchableMediaWidget.extend({template:'wysiwyg.widgets.font-icons',events:_.extend({},SearchableMediaWidget.prototype.events||{},{'click .font-icons-icon':'_onIconClick',}),init:function(parent,media){this._super.apply(this,arguments);fonts.computeFonts();this.iconsParser=fonts.fontIcons;this.alias=_.flatten(_.map(this.iconsParser,function(data){return data.alias;}));},start:function(){this.$icons=this.$('.font-icons-icon');var classes=(this.media&&this.media.className||'').split(/\s+/);for(var i=0;i<classes.length;i++){var cls=classes[i];if(_.contains(this.alias,cls)){this.selectedIcon=cls;this.initialIcon=cls;this._highlightSelectedIcon();}}
this.nonIconClasses=_.without(classes,'media_iframe_video',this.selectedIcon);return this._super.apply(this,arguments);},save:function(){var style=this.$media.attr('style')||'';var iconFont=this._getFont(this.selectedIcon)||{base:'fa',font:''};if(!this.$media.is('span, i')){var $span=$('<span/>');$span.data(this.$media.data());this.$media=$span;this.media=this.$media[0];style=style.replace(/\s*width:[^;]+/,'');}
this.$media.removeClass(this.initialIcon).addClass([iconFont.base,iconFont.font]);this.$media.attr('style',style||null);return Promise.resolve(this.media);},search:function(needle){var iconsParser=this.iconsParser;if(needle&&needle.length){iconsParser=[];_.filter(this.iconsParser,function(data){var cssData=_.filter(data.cssData,function(cssData){return _.find(cssData.names,function(alias){return alias.indexOf(needle)>=0;});});if(cssData.length){iconsParser.push({base:data.base,cssData:cssData,});}});}
this.$('div.font-icons-icons').html(QWeb.render('wysiwyg.widgets.font-icons.icons',{iconsParser:iconsParser,widget:this}));return Promise.resolve();},_clear:function(){var allFaClasses=/(^|\s)(fa|(text-|bg-|fa-)\S*|rounded-circle|rounded|thumbnail|img-thumbnail|shadow)(?=\s|$)/g;this.media.className=this.media.className&&this.media.className.replace(allFaClasses,' ');},_getFont:function(classNames){if(!(classNames instanceof Array)){classNames=(classNames||"").split(/\s+/);}
var fontIcon,cssData;for(var k=0;k<this.iconsParser.length;k++){fontIcon=this.iconsParser[k];for(var s=0;s<fontIcon.cssData.length;s++){cssData=fontIcon.cssData[s];if(_.intersection(classNames,cssData.names).length){return{base:fontIcon.base,parser:fontIcon.parser,font:cssData.names[0],};}}}
return null;},_highlightSelectedIcon:function(){var self=this;this.$icons.removeClass('o_we_attachment_selected');this.$icons.filter(function(i,el){return _.contains($(el).data('alias').split(','),self.selectedIcon);}).addClass('o_we_attachment_selected');},_onIconClick:function(ev){ev.preventDefault();ev.stopPropagation();this.selectedIcon=$(ev.currentTarget).data('id');this._highlightSelectedIcon();this.trigger_up('save_request');},});var VideoWidget=MediaWidget.extend({template:'wysiwyg.widgets.video',events:_.extend({},MediaWidget.prototype.events||{},{'change .o_video_dialog_options input':'_onUpdateVideoOption','input textarea#o_video_text':'_onVideoCodeInput','change textarea#o_video_text':'_onVideoCodeChange',}),init:function(parent,media,options){this._super.apply(this,arguments);this.isForBgVideo=!!options.isForBgVideo;this._onVideoCodeInput=_.debounce(this._onVideoCodeInput,1000);},start:function(){this.$content=this.$('.o_video_dialog_iframe');if(this.media){var $media=$(this.media);var src=$media.data('oe-expression')||$media.data('src')||($media.is('iframe')?$media.attr('src'):'')||'';this.$('textarea#o_video_text').val(src);this.$('input#o_video_autoplay').prop('checked',src.indexOf('autoplay=1')>=0);this.$('input#o_video_hide_controls').prop('checked',src.indexOf('controls=0')>=0);this.$('input#o_video_loop').prop('checked',src.indexOf('loop=1')>=0);this.$('input#o_video_hide_fullscreen').prop('checked',src.indexOf('fs=0')>=0);this.$('input#o_video_hide_yt_logo').prop('checked',src.indexOf('modestbranding=1')>=0);this.$('input#o_video_hide_dm_logo').prop('checked',src.indexOf('ui-logo=0')>=0);this.$('input#o_video_hide_dm_share').prop('checked',src.indexOf('sharing-enable=0')>=0);this._updateVideo();}
return this._super.apply(this,arguments);},save:function(){this._updateVideo();if(this.isForBgVideo){return Promise.resolve({bgVideoSrc:this.$content.attr('src')});}
if(this.$('.o_video_dialog_iframe').is('iframe')){this.$media=$('<div class="media_iframe_video" data-oe-expression="'+this.$content.attr('src')+'">'+'<div class="css_editable_mode_display">&nbsp;</div>'+'<div class="media_iframe_video_size" contenteditable="false">&nbsp;</div>'+'<iframe src="'+this.$content.attr('src')+'" frameborder="0" contenteditable="false" allowfullscreen="allowfullscreen"></iframe>'+'</div>');this.media=this.$media[0];}
return Promise.resolve(this.media);},_clear:function(){if(this.media.dataset.src){try{delete this.media.dataset.src;}catch(e){this.media.dataset.src=undefined;}}
var allVideoClasses=/(^|\s)media_iframe_video(\s|$)/g;var isVideo=this.media.className&&this.media.className.match(allVideoClasses);if(isVideo){this.media.className=this.media.className.replace(allVideoClasses,' ');this.media.innerHTML='';}},_createVideoNode:function(url,options){options=options||{};const videoData=this._getVideoURLData(url,options);if(videoData.error){return{errorCode:0};}
if(!videoData.type){return{errorCode:1};}
const $video=$('<iframe>').width(1280).height(720).attr('frameborder',0).attr('src',videoData.embedURL).addClass('o_video_dialog_iframe');return{$video:$video,type:videoData.type};},_updateVideo:function(){this.$content.empty();this.$('#o_video_form_group').removeClass('o_has_error o_has_success').find('.form-control, .custom-select').removeClass('is-invalid is-valid');this.$('.o_video_dialog_options div').addClass('d-none');var $textarea=this.$('textarea#o_video_text');var code=$textarea.val().trim();if(!code){return;}
var embedMatch=code.match(/(src|href)=["']?([^"']+)?/);if(embedMatch&&embedMatch[2].length>0&&embedMatch[2].indexOf('instagram')){embedMatch[1]=embedMatch[2];}
var url=embedMatch?embedMatch[1]:code;var query=this._createVideoNode(url,{'autoplay':this.isForBgVideo||this.$('input#o_video_autoplay').is(':checked'),'hide_controls':this.isForBgVideo||this.$('input#o_video_hide_controls').is(':checked'),'loop':this.isForBgVideo||this.$('input#o_video_loop').is(':checked'),'hide_fullscreen':this.isForBgVideo||this.$('input#o_video_hide_fullscreen').is(':checked'),'hide_yt_logo':this.isForBgVideo||this.$('input#o_video_hide_yt_logo').is(':checked'),'hide_dm_logo':this.isForBgVideo||this.$('input#o_video_hide_dm_logo').is(':checked'),'hide_dm_share':this.isForBgVideo||this.$('input#o_video_hide_dm_share').is(':checked'),});var $optBox=this.$('.o_video_dialog_options');this.$el.find('.o_video_dialog_preview_text, .media_iframe_video_size').add($optBox).toggleClass('d-none',!query.$video);this.$el.find('#o_video_form_group').toggleClass('o_has_error',!query.$video).find('.form-control, .custom-select').toggleClass('is-invalid',!query.$video).end().toggleClass('o_has_success',!!query.$video).find('.form-control, .custom-select').toggleClass('is-valid',!!query.$video);$optBox.find('div.o_'+query.type+'_option').removeClass('d-none');$optBox.toggleClass('d-none',this.isForBgVideo||$optBox.find('div:not(.d-none)').length===0);if(query.type==='youtube'){this.$('input#o_video_hide_fullscreen, input#o_video_hide_yt_logo').closest('div').toggleClass('d-none',this.$('input#o_video_hide_controls').is(':checked'));}
this.error=false;var $content=query.$video;if(!$content){switch(query.errorCode){case 0:this.error=_t("The provided url is not valid");$content=$('<div/>',{class:'alert alert-danger o_video_dialog_iframe mb-2 mt-2',text:this.error,});break;case 1:this.error=_t("The provided url does not reference any supported video");$content=$('<div/>',{class:'alert alert-warning o_video_dialog_iframe mb-2 mt-2',text:this.error,});break;}}
this.$content.replaceWith($content);this.$content=$content;},_onUpdateVideoOption:function(){this._updateVideo();},_onVideoCodeChange:function(){this._updateVideo();},_onVideoCodeInput:function(){this._updateVideo();},_getVideoURLData:function(url,options){if(!url.match(/^(http:\/\/|https:\/\/|\/\/)[a-z0-9]+([-.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$/i)){return{error:true,message:'The provided url is invalid',};}
const regexes={youtube:/^(?:(?:https?:)?\/\/)?(?:www\.)?(?:youtu\.be\/|youtube(-nocookie)?\.com\/(?:embed\/|v\/|watch\?v=|watch\?.+&v=))((?:\w|-){11})(?:\S+)?$/,instagram:/(.*)instagram.com\/p\/(.[a-zA-Z0-9]*)/,vine:/\/\/vine.co\/v\/(.[a-zA-Z0-9]*)/,vimeo:/\/\/(player.)?vimeo.com\/([a-z]*\/)*([0-9]{6,11})[?]?.*/,dailymotion:/.+dailymotion.com\/(video|hub|embed)\/([^_?]+)[^#]*(#video=([^_&]+))?/,youku:/(.*).youku\.com\/(v_show\/id_|embed\/)(.+)/,};const matches=_.mapObject(regexes,regex=>url.match(regex));const autoplay=options.autoplay?'?autoplay=1&mute=1':'?autoplay=0';const controls=options.hide_controls?'&controls=0':'';const loop=options.loop?'&loop=1':'';let embedURL;let type;if(matches.youtube&&matches.youtube[2].length===11){const fullscreen=options.hide_fullscreen?'&fs=0':'';const ytLoop=loop?loop+`&playlist=${encodeURIComponent(matches.youtube[2])}`:'';const logo=options.hide_yt_logo?'&modestbranding=1':'';const enablejsapi=options.autoplay?'&enablejsapi=1':'';embedURL=`//www.youtube${matches.youtube[1] || ''}.com/embed/${matches.youtube[2]}${autoplay}${enablejsapi}&rel=0${ytLoop}${controls}${fullscreen}${logo}`;type='youtube';}else if(matches.instagram&&matches.instagram[2].length){embedURL=`//www.instagram.com/p/${matches.instagram[2]}/embed/`;type='instagram';}else if(matches.vine&&matches.vine[0].length){embedURL=`${matches.vine[0]}/embed/simple`;type='vine';}else if(matches.vimeo&&matches.vimeo[3].length){const vimeoAutoplay=autoplay.replace('mute','muted').replace('autoplay=1','autoplay=1&autopause=0');embedURL=`//player.vimeo.com/video/${matches.vimeo[3]}${vimeoAutoplay}${loop}${controls}`;type='vimeo';}else if(matches.dailymotion&&matches.dailymotion[2].length){const videoId=matches.dailymotion[2].replace('video/','');const logo=options.hide_dm_logo?'&ui-logo=0':'';const share=options.hide_dm_share?'&sharing-enable=0':'';embedURL=`//www.dailymotion.com/embed/video/${videoId}${autoplay}${controls}${logo}${share}`;type='dailymotion';}else if(matches.youku&&matches.youku[3].length){const videoId=matches.youku[3].indexOf('.html?')>=0?matches.youku[3].substring(0,matches.youku[3].indexOf('.html?')):matches.youku[3];embedURL=`//player.youku.com/embed/${videoId}`;type='youku';}
return{type:type,embedURL:embedURL};},});return{MediaWidget:MediaWidget,SearchableMediaWidget:SearchableMediaWidget,FileWidget:FileWidget,ImageWidget:ImageWidget,DocumentWidget:DocumentWidget,IconWidget:IconWidget,VideoWidget:VideoWidget,};});;

/* /web_editor/static/src/js/wysiwyg/widgets/dialog.js defined in bundle 'web_editor.assets_wysiwyg' */
odoo.define('wysiwyg.widgets.Dialog',function(require){'use strict';var config=require('web.config');var core=require('web.core');var Dialog=require('web.Dialog');var _t=core._t;var SummernoteDialog=Dialog.extend({init:function(parent,options){this.options=options||{};if(config.device.isMobile){options.fullscreen=true;}
this._super(parent,_.extend({},{buttons:[{text:this.options.save_text||_t("Save"),classes:'btn-primary',click:this.save,},{text:_t("Discard"),close:true,}]},this.options));this.destroyAction='cancel';var self=this;this.opened(function(){self.$('input:visible:first').focus();self.$el.closest('.modal').addClass('o_web_editor_dialog');self.$el.closest('.modal').on('hidden.bs.modal',self.options.onClose);});this.on('closed',this,function(){self._toggleFullScreen();this.trigger(this.destroyAction,this.final_data||null);});},_toggleFullScreen:function(){if(config.device.isMobile&&!this.hasFullScreen){$('#iframe_target[isMobile="true"] #web_editor-top-edit .o_fullscreen').click();}},save:function(){this.destroyAction="save";this.close();},open:function(){this.hasFullScreen=$(window.top.document.body).hasClass('o_field_widgetTextHtml_fullscreen');this._toggleFullScreen();return this._super.apply(this,arguments);},});return SummernoteDialog;});;

/* /web_editor/static/src/js/wysiwyg/widgets/alt_dialog.js defined in bundle 'web_editor.assets_wysiwyg' */
odoo.define('wysiwyg.widgets.AltDialog',function(require){'use strict';var core=require('web.core');var Dialog=require('wysiwyg.widgets.Dialog');var _t=core._t;var AltDialog=Dialog.extend({template:'wysiwyg.widgets.alt',xmlDependencies:Dialog.prototype.xmlDependencies.concat(['/web_editor/static/src/xml/wysiwyg.xml']),init:function(parent,options,media){options=options||{};this._super(parent,_.extend({},{title:_t("Change media description and tooltip")},options));this.trigger_up('getRecordInfo',{recordInfo:options,callback:function(recordInfo){_.defaults(options,recordInfo);},});this.media=media;var allEscQuots=/&quot;/g;this.alt=($(this.media).attr('alt')||"").replace(allEscQuots,'"');var title=$(this.media).attr('title')||$(this.media).data('original-title')||"";this.tag_title=(title).replace(allEscQuots,'"');},save:function(){var alt=this.$('#alt').val();var title=this.$('#title').val();var allNonEscQuots=/"/g;$(this.media).attr('alt',alt?alt.replace(allNonEscQuots,"&quot;"):null).attr('title',title?title.replace(allNonEscQuots,"&quot;"):null);$(this.media).trigger('content_changed');this.final_data=this.media;return this._super.apply(this,arguments);},});return AltDialog;});;

/* /web_editor/static/src/js/wysiwyg/widgets/color_palette.js defined in bundle 'web_editor.assets_wysiwyg' */
odoo.define('web_editor.ColorPalette',function(require){'use strict';const ajax=require('web.ajax');const core=require('web.core');const session=require('web.session');const{ColorpickerWidget}=require('web.Colorpicker');const Widget=require('web.Widget');const summernoteCustomColors=require('web_editor.rte.summernote_custom_colors');const weUtils=require('web_editor.utils');const qweb=core.qweb;const ColorPaletteWidget=Widget.extend({template:'web_editor.snippet.option.colorpicker',events:{'click .o_we_color_btn':'_onColorButtonClick','mouseenter .o_we_color_btn':'_onColorButtonEnter','mouseleave .o_we_color_btn':'_onColorButtonLeave','click .o_we_colorpicker_switch_pane_btn':'_onSwitchPaneButtonClick',},custom_events:{'colorpicker_select':'_onColorPickerSelect','colorpicker_preview':'_onColorPickerPreview',},init:function(parent,options){this._super.apply(this,arguments);this.summernoteCustomColorsArray=[].concat(...summernoteCustomColors);this.style=window.getComputedStyle(document.documentElement);this.options=_.extend({selectedColor:false,resetButton:true,excluded:[],excludeSectionOf:null,$editable:$(),withCombinations:false,},options||{});this.selectedColor='';this.resetButton=this.options.resetButton;this.withCombinations=this.options.withCombinations;this.trigger_up('request_editable',{callback:val=>this.options.$editable=val});},willStart:async function(){await this._super(...arguments);await ColorPaletteWidget.loadDependencies(this);},start:async function(){const res=this._super.apply(this,arguments);const $colorSection=this.$('.o_colorpicker_sections[data-color-tab="theme-colors"]');const $clpicker=qweb.has_template('web_editor.colorpicker')?$(qweb.render('web_editor.colorpicker')):$(`<colorpicker><div class="o_colorpicker_section" data-name="common"></div></colorpicker>`);$clpicker.find('button').addClass('o_we_color_btn');$clpicker.appendTo($colorSection);_.each(this.options.excluded,exc=>{this.$('[data-name="'+exc+'"]').addClass('d-none');});if(this.options.excludeSectionOf){this.$('[data-name]:has([data-color="'+this.options.excludeSectionOf+'"])').addClass('d-none');}
this.el.querySelectorAll('.o_colorpicker_section').forEach(elem=>{$(elem).prepend('<div>'+(elem.dataset.display||'')+'</div>');});if(!this.options.excluded.includes('common')){const $commonColorSection=this.$('[data-name="common"]');summernoteCustomColors.forEach((colorRow,i)=>{if(i===0){return;}
const $div=$('<div/>',{class:'clearfix'}).appendTo($commonColorSection);colorRow.forEach(color=>{$div.append(this._createColorButton(color,['o_common_color']));});});}
const compatibilityColorNames=['primary','secondary','alpha','beta','gamma','delta','epsilon','success','info','warning','danger'];this.colorNames=[...compatibilityColorNames];this.colorToColorNames={};this.el.querySelectorAll('button[data-color]').forEach(elem=>{const colorName=elem.dataset.color;const $color=$(elem);const isCCName=weUtils.isColorCombinationName(colorName);if(isCCName){$color.find('.o_we_cc_preview_wrapper').addClass(`o_cc o_cc${colorName}`);}else{$color.addClass(`bg-${colorName}`);}
this.colorNames.push(colorName);if(!isCCName&&!elem.classList.contains('d-none')){const color=weUtils.getCSSVariableValue(colorName,this.style);this.colorToColorNames[color]=colorName;}});if(this.options.selectedColor){let selectedColor=this.options.selectedColor;if(compatibilityColorNames.includes(selectedColor)){selectedColor=weUtils.getCSSVariableValue(selectedColor,this.style)||selectedColor;}
selectedColor=ColorpickerWidget.normalizeCSSColor(selectedColor);if(selectedColor!=='rgba(0, 0, 0, 0)'){this.selectedColor=this.colorToColorNames[selectedColor]||selectedColor;}}
this._buildCustomColors();this._markSelectedColor();let defaultColor=this.selectedColor;if(defaultColor&&!ColorpickerWidget.isCSSColor(defaultColor)){defaultColor=weUtils.getCSSVariableValue(defaultColor,this.style);}
this.colorPicker=new ColorpickerWidget(this,{defaultColor:defaultColor,});await this.colorPicker.prependTo($colorSection);if(this.options.excluded.includes('custom')){this.colorPicker.$el.addClass('d-none');}
return res;},getColorNames:function(){return this.colorNames;},setSelectedColor:function(color){this._selectColor({color:color});},_buildCustomColors:function(){if(this.options.excluded.includes('custom')){return;}
this.el.querySelectorAll('.o_custom_color').forEach(el=>el.remove());const existingColors=new Set(this.summernoteCustomColorsArray.concat(Object.keys(this.colorToColorNames)));this.trigger_up('get_custom_colors',{onSuccess:(colors)=>{colors.forEach(color=>{this._addCustomColor(existingColors,color);});},});weUtils.getCSSVariableValue('custom-colors',this.style).split(' ').forEach(v=>{const color=weUtils.getCSSVariableValue(v.substring(1,v.length-1),this.style);if(ColorpickerWidget.isCSSColor(color)){this._addCustomColor(existingColors,color);}});_.each(this.options.$editable.find('[style*="color"]'),el=>{for(const colorProp of['color','backgroundColor']){this._addCustomColor(existingColors,el.style[colorProp]);}});if(this.selectedColor){this._addCustomColor(existingColors,this.selectedColor);}},_addCustomColor:function(existingColors,color){if(!color){return;}
if(!ColorpickerWidget.isCSSColor(color)){color=weUtils.getCSSVariableValue(color,this.style);}
const normColor=ColorpickerWidget.normalizeCSSColor(color);if(!existingColors.has(normColor)){this._addCustomColorButton(normColor);existingColors.add(normColor);}},_addCustomColorButton:function(color,classes=[]){classes.push('o_custom_color');const $themeSection=this.$('.o_colorpicker_section[data-name="theme"]');const $button=this._createColorButton(color,classes);return $button.appendTo($themeSection);},_createColorButton:function(color,classes){return $('<button/>',{class:'o_we_color_btn '+classes.join(' '),style:'background-color:'+color+';',});},_getButtonInfo:function(buttonEl){const bgColor=buttonEl.style.backgroundColor;return{color:bgColor?ColorpickerWidget.normalizeCSSColor(bgColor):buttonEl.dataset.color||'',target:buttonEl,};},_selectColor:function(colorInfo,eventName){this.selectedColor=colorInfo.color=this.colorToColorNames[colorInfo.color]||colorInfo.color;if(eventName){this.trigger_up(eventName,colorInfo);}
this._buildCustomColors();this._markSelectedColor();this.colorPicker.setSelectedColor(colorInfo.color);},_markSelectedColor:function(){this.el.querySelectorAll('button.selected').forEach(el=>el.classList.remove('selected'));const selectedButton=this.el.querySelector(`button[data-color="${this.selectedColor}"], button[style*="background-color:${this.selectedColor};"]`);if(selectedButton){selectedButton.classList.add('selected');}},_onColorButtonClick:function(ev){const buttonEl=ev.currentTarget;const colorInfo=this._getButtonInfo(buttonEl);this._selectColor(colorInfo,'color_picked');},_onColorButtonEnter:function(ev){ev.stopPropagation();this.trigger_up('color_hover',this._getButtonInfo(ev.currentTarget));},_onColorButtonLeave:function(ev){ev.stopPropagation();this.trigger_up('color_leave',{color:this.selectedColor,target:ev.target,});},_onColorPickerPreview:function(ev){this.trigger_up('color_hover',{color:ev.data.cssColor,target:this.colorPicker.el,});},_onColorPickerSelect:function(ev){this._selectColor({color:ev.data.cssColor,target:this.colorPicker.el,},'custom_color_picked');},_onSwitchPaneButtonClick(ev){ev.stopPropagation();this.el.querySelectorAll('.o_we_colorpicker_switch_pane_btn').forEach(el=>{el.classList.remove('active');});ev.currentTarget.classList.add('active');this.el.querySelectorAll('.o_colorpicker_sections').forEach(el=>{el.classList.toggle('d-none',el.dataset.colorTab!==ev.currentTarget.dataset.target);});},});let colorpickerTemplateProm;ColorPaletteWidget.loadDependencies=async function(rpcCapableObj){const proms=[ajax.loadXML('/web_editor/static/src/xml/snippets.xml',qweb)];if(!session.is_website_user){if(!colorpickerTemplateProm&&!qweb.has_template('web_editor.colorpicker')){colorpickerTemplateProm=rpcCapableObj._rpc({model:'ir.ui.view',method:'read_template',args:['web_editor.colorpicker'],}).then(template=>{return qweb.add_template('<templates>'+template+'</templates>');});}
proms.push(colorpickerTemplateProm);}
return Promise.all(proms);};return{ColorPaletteWidget:ColorPaletteWidget,};});;

/* /web_editor/static/src/js/wysiwyg/widgets/image_crop_widget.js defined in bundle 'web_editor.assets_wysiwyg' */
odoo.define('wysiwyg.widgets.ImageCropWidget',function(require){'use strict';const core=require('web.core');const Widget=require('web.Widget');const{applyModifications,cropperDataFields,activateCropper,loadImage,loadImageInfo}=require('web_editor.image_processing');const _t=core._t;const ImageCropWidget=Widget.extend({template:['wysiwyg.widgets.crop'],xmlDependencies:['/web_editor/static/src/xml/wysiwyg.xml'],events:{'click.crop_options [data-action]':'_onCropOptionClick','zoom':'_onCropZoom',},init(parent,media){this._super(...arguments);this.media=media;this.$media=$(media);this.document=media.ownerDocument;this.aspectRatios={"0/0":{label:_t("Free"),value:0},"16/9":{label:"16:9",value:16/9},"4/3":{label:"4:3",value:4/3},"1/1":{label:"1:1",value:1},"2/3":{label:"2:3",value:2/3},};const src=this.media.getAttribute('src');const data=Object.assign({},media.dataset);this.initialSrc=src;this.aspectRatio=data.aspectRatio||"0/0";this.mimetype=data.mimetype||src.endsWith('.png')?'image/png':'image/jpeg';},async willStart(){await this._super.apply(this,arguments);await loadImageInfo(this.media,this._rpc.bind(this));const isIllustration=/^\/web_editor\/shape\/illustration\//.test(this.media.dataset.originalSrc);if(this.media.dataset.originalSrc&&!isIllustration){this.originalSrc=this.media.dataset.originalSrc;this.originalId=this.media.dataset.originalId;return;}
this.uncroppable=true;},async start(){if(this.uncroppable){this.displayNotification({type:'warning',title:_t("This image is an external image"),message:_t("This type of image is not supported for cropping.<br/>If you want to crop it, please first download it from the original source and upload it in Odoo."),});return this.destroy();}
const _super=this._super.bind(this);const $cropperWrapper=this.$('.o_we_cropper_wrapper');await loadImage(this.originalSrc,this.media);this.$cropperImage=this.$('.o_we_cropper_img');const cropperImage=this.$cropperImage[0];[cropperImage.style.width,cropperImage.style.height]=[this.$media.width()+'px',this.$media.height()+'px'];const offset=this.$media.offset();offset.left+=parseInt(this.$media.css('padding-left'));offset.top+=parseInt(this.$media.css('padding-right'));$cropperWrapper.offset(offset);await loadImage(this.originalSrc,cropperImage);await activateCropper(cropperImage,this.aspectRatios[this.aspectRatio].value,this.media.dataset);core.bus.trigger('deactivate_snippet');this._onDocumentMousedown=this._onDocumentMousedown.bind(this);this.document.addEventListener('mousedown',this._onDocumentMousedown,{capture:true});return _super(...arguments);},destroy(){if(this.$cropperImage){this.$cropperImage.cropper('destroy');this.document.removeEventListener('mousedown',this._onDocumentMousedown,{capture:true});}
this.media.setAttribute('src',this.initialSrc);return this._super(...arguments);},async _save(){this.media.classList.add('o_modified_image_to_save');[...cropperDataFields,'aspectRatio'].forEach(attr=>{delete this.media.dataset[attr];const value=this._getAttributeValue(attr);if(value){this.media.dataset[attr]=value;}});delete this.media.dataset.resizeWidth;this.initialSrc=await applyModifications(this.media);this.$media.trigger('image_cropped');this.destroy();},_getAttributeValue(attr){if(cropperDataFields.includes(attr)){return this.$cropperImage.cropper('getData')[attr];}
return this[attr];},_resetCropBox(){this.$cropperImage.cropper('clear');this.$cropperImage.cropper('crop');},_onCropOptionClick(ev){const{action,value,scaleDirection}=ev.currentTarget.dataset;switch(action){case'ratio':this.$cropperImage.cropper('reset');this.aspectRatio=value;this.$cropperImage.cropper('setAspectRatio',this.aspectRatios[this.aspectRatio].value);break;case'zoom':case'reset':this.$cropperImage.cropper(action,value);break;case'rotate':this.$cropperImage.cropper(action,value);this._resetCropBox();break;case'flip':{const amount=this.$cropperImage.cropper('getData')[scaleDirection]*-1;return this.$cropperImage.cropper(scaleDirection,amount);}
case'apply':return this._save();case'discard':return this.destroy();}},_onDocumentMousedown(ev){if(document.body.contains(ev.target)&&this.$(ev.target).length===0){return this.destroy();}},async _onCropZoom(){await new Promise(res=>setTimeout(res,0));this._resetCropBox();},});return ImageCropWidget;});;

/* /web_editor/static/src/js/wysiwyg/widgets/link_dialog.js defined in bundle 'web_editor.assets_wysiwyg' */
odoo.define('wysiwyg.widgets.LinkDialog',function(require){'use strict';var core=require('web.core');var Dialog=require('wysiwyg.widgets.Dialog');var dom=$.summernote.core.dom;var range=$.summernote.core.range;var _t=core._t;var LinkDialog=Dialog.extend({template:'wysiwyg.widgets.link',xmlDependencies:(Dialog.prototype.xmlDependencies||[]).concat(['/web_editor/static/src/xml/wysiwyg.xml']),events:_.extend({},Dialog.prototype.events||{},{'input':'_onAnyChange','change [name="link_style_color"]':'_onTypeChange','change':'_onAnyChange','input input[name="url"]':'_onURLInput',}),init:function(parent,options,editable,linkInfo){this.options=options||{};this._super(parent,_.extend({title:_t("Link to"),},this.options));this.trigger_up('getRecordInfo',{recordInfo:this.options,callback:recordInfo=>{_.defaults(this.options,recordInfo);},});this.data=linkInfo||{};this.isButton=this.data.isButton;const isButtonLink=this.isButton||(this.data.range&&this.data.range.isOnAnchor()&&this.data.className.includes("btn-link"));this.colorsData=[{type:isButtonLink?'link':'',label:_t("Link"),btnPreview:'link'},{type:'primary',label:_t("Primary"),btnPreview:'primary'},{type:'secondary',label:_t("Secondary"),btnPreview:'secondary'},];this.editable=editable;this.data.className="";this.data.iniClassName="";var r=this.data.range;this.needLabel=!r||(r.sc===r.ec&&r.so===r.eo);if(this.data.range){const $el=$(this.data.range.sc).filter(this.isButton?"button":"a");this.data.iniClassName=$el.attr("class")||"";this.colorCombinationClass=false;let $node=$el;while($node.length&&!$node.is('body')){const className=$node.attr('class')||'';const m=className.match(/\b(o_cc\d+)\b/g);if(m){this.colorCombinationClass=m[0];break;}
$node=$node.parent();}
this.data.className=this.data.iniClassName.replace(/(^|\s+)btn(-[a-z0-9_-]*)?/gi,' ');var is_link=this.data.range.isOnAnchor();var sc=r.sc;var so=r.so;var ec=r.ec;var eo=r.eo;var nodes;if(!is_link){if(sc.tagName){sc=dom.firstChild(so?sc.childNodes[so]:sc);so=0;}else if(so!==sc.textContent.length){if(sc===ec){ec=sc=sc.splitText(so);eo-=so;}else{sc=sc.splitText(so);}
so=0;}
if(ec.tagName){ec=dom.lastChild(eo?ec.childNodes[eo-1]:ec);eo=ec.textContent.length;}else if(eo!==ec.textContent.length){ec.splitText(eo);}
nodes=dom.listBetween(sc,ec);if(dom.isVoid(sc)||dom.isImg(sc)){so=dom.listPrev(sc).length-1;sc=sc.parentNode;}
if(dom.isBR(ec)){eo=dom.listPrev(ec).length-1;ec=ec.parentNode;}else if(dom.isVoid(ec)||dom.isImg(sc)){eo=dom.listPrev(ec).length;ec=ec.parentNode;}
this.data.range=range.create(sc,so,ec,eo);$(editable).data("range",this.data.range);this.data.range.select();}else{nodes=dom.ancestor(sc,dom.isAnchor).childNodes;}
if(dom.isImg(sc)&&nodes.indexOf(sc)===-1){nodes.push(sc);}
if(nodes.length>1||dom.ancestor(nodes[0],dom.isImg)){var text="";this.data.images=[];for(var i=0;i<nodes.length;i++){if(dom.ancestor(nodes[i],dom.isImg)){this.data.images.push(dom.ancestor(nodes[i],dom.isImg));text+='[IMG]';}else if(!is_link&&nodes[i].nodeType===1){}else if(!is_link&&i===0){text+=nodes[i].textContent.slice(so,Infinity);}else if(!is_link&&i===nodes.length-1){text+=nodes[i].textContent.slice(0,eo);}else{text+=nodes[i].textContent;}}
this.data.text=text;}}
this.data.text=this.data.text.replace(/[ \t\r\n]+/g,' ');var allBtnClassSuffixes=/(^|\s+)btn(-[a-z0-9_-]*)?/gi;var allBtnShapes=/\s*(rounded-circle|flat)\s*/gi;this.data.className=this.data.iniClassName.replace(allBtnClassSuffixes,' ').replace(allBtnShapes,' ');if(/(?:s_website_form_send|o_submit)/.test(this.data.className)){this.isButton=true;}},start:function(){this.buttonOptsCollapseEl=this.el.querySelector('#o_link_dialog_button_opts_collapse');this.$styleInputs=this.$('input.link-style');this.$styleInputs.prop('checked',false).filter('[value=""]').prop('checked',true);if(this.data.iniClassName){_.each(this.$('input[name="link_style_color"], select[name="link_style_size"] > option, select[name="link_style_shape"] > option'),el=>{const $option=$(el);const value=$option.val();if(!value){return;}
const subValues=value.split(',');let active=true;for(let i=0;i<subValues.length;i++){const classPrefix=new RegExp('(^|btn-| |btn-outline-|btn-fill-)'+subValues[i]);active=active&&classPrefix.test(this.data.iniClassName);}
if(active){if($option.is("input")){$option.prop("checked",true);}else{$option.parent().find('option').removeAttr('selected').removeProp('selected');$option.parent().val($option.val());$option.attr('selected','selected').prop('selected','selected');}}});}
if(this.data.url){var match=/mailto:(.+)/.exec(this.data.url);this.$('input[name="url"]').val(match?match[1]:this.data.url);this._onURLInput();}
this._updateOptionsUI();this._adaptPreview();this.$('input:visible:first').focus();return this._super.apply(this,arguments);},save:function(){var data=this._getData();if(data===null){var $url=this.$('input[name="url"]');$url.closest('.form-group').addClass('o_has_error').find('.form-control, .custom-select').addClass('is-invalid');$url.focus();return Promise.reject();}
this.data.text=data.label;this.data.url=data.url;var allWhitespace=/\s+/gi;var allStartAndEndSpace=/^\s+|\s+$/gi;var allBtnTypes=/(^|[ ])(btn-secondary|btn-success|btn-primary|btn-info|btn-warning|btn-danger)([ ]|$)/gi;this.data.className=data.classes.replace(allWhitespace,' ').replace(allStartAndEndSpace,'');if(data.classes.replace(allBtnTypes,' ')){this.data.style={'background-color':'','color':'',};}
this.data.isNewWindow=data.isNewWindow;this.final_data=this.data;return this._super.apply(this,arguments);},_adaptPreview:function(){var data=this._getData();if(data===null){return;}
const attrs={target:data.isNewWindow?'_blank':'',href:data.url&&data.url.length?data.url:'#',class:`${data.classes.replace(/float-\w+/, '')} o_btn_preview`,};this.$("#link-preview").attr(attrs).html((data.label&&data.label.length)?data.label:data.url);},_getData:function(){var $url=this.$('input[name="url"]');var url=$url.val();var label=_.escape(this.$('input[name="label"]').val()||url);if(label&&this.data.images){for(var i=0;i<this.data.images.length;i++){label=label.replace(/\[IMG\]/,this.data.images[i].outerHTML);}}
if(!this.isButton&&$url.prop('required')&&(!url||!$url[0].checkValidity())){return null;}
const type=this.$('input[name="link_style_color"]:checked').val()||'';const size=this.$('select[name="link_style_size"]').val()||'';const shape=this.$('select[name="link_style_shape"]').val()||'';const shapes=shape?shape.split(','):[];const style=['outline','fill'].includes(shapes[0])?`${shapes[0]}-`:'';const shapeClasses=shapes.slice(style?1:0).join(' ');const classes=(this.data.className||'')+
(type?(` btn btn-${style}${type}`):'')+
(shapeClasses?(` ${shapeClasses}`):'')+
(size?(' btn-'+size):'');var isNewWindow=this.$('input[name="is_new_window"]').prop('checked');if(url.indexOf('@')>=0&&url.indexOf('mailto:')<0&&!url.match(/^http[s]?/i)){url=('mailto:'+url);}else if(url.indexOf(location.origin)===0&&this.$('#o_link_dialog_url_strip_domain').prop("checked")){url=url.slice(location.origin.length);}
var allWhitespace=/\s+/gi;var allStartAndEndSpace=/^\s+|\s+$/gi;return{label:label,url:url,classes:classes.replace(allWhitespace,' ').replace(allStartAndEndSpace,''),isNewWindow:isNewWindow,};},_updateOptionsUI:function(){const el=this.el.querySelector('[name="link_style_color"]:checked');$(this.buttonOptsCollapseEl).collapse(el&&el.value?'show':'hide');},_onAnyChange:function(){this._adaptPreview();},_onTypeChange(){this._updateOptionsUI();},_onURLInput:function(){var $linkUrlInput=this.$('#o_link_dialog_url_input');$linkUrlInput.closest('.form-group').removeClass('o_has_error').find('.form-control, .custom-select').removeClass('is-invalid');let value=$linkUrlInput.val();let isLink=value.indexOf('@')<0;this.$('input[name="is_new_window"]').closest('.form-group').toggleClass('d-none',!isLink);this.$('.o_strip_domain').toggleClass('d-none',value.indexOf(window.location.origin)!==0);},});return LinkDialog;});;

/* /web_editor/static/src/js/wysiwyg/widgets/media_dialog.js defined in bundle 'web_editor.assets_wysiwyg' */
odoo.define('wysiwyg.widgets.MediaDialog',function(require){'use strict';var core=require('web.core');var MediaModules=require('wysiwyg.widgets.media');var Dialog=require('wysiwyg.widgets.Dialog');var _t=core._t;var MediaDialog=Dialog.extend({template:'wysiwyg.widgets.media',xmlDependencies:Dialog.prototype.xmlDependencies.concat(['/web_editor/static/src/xml/wysiwyg.xml']),events:_.extend({},Dialog.prototype.events,{'click #editor-media-image-tab':'_onClickImageTab','click #editor-media-document-tab':'_onClickDocumentTab','click #editor-media-icon-tab':'_onClickIconTab','click #editor-media-video-tab':'_onClickVideoTab',}),custom_events:_.extend({},Dialog.prototype.custom_events||{},{save_request:'_onSaveRequest',show_parent_dialog_request:'_onShowRequest',hide_parent_dialog_request:'_onHideRequest',}),init:function(parent,options,media){var $media=$(media);media=$media[0];this.media=media;options=_.extend({},options);var onlyImages=options.onlyImages||this.multiImages||(media&&($media.parent().data('oeField')==='image'||$media.parent().data('oeType')==='image'));options.noDocuments=onlyImages||options.noDocuments;options.noIcons=onlyImages||options.noIcons;options.noVideos=onlyImages||options.noVideos;this._super(parent,_.extend({},{title:_t("Select a Media"),save_text:_t("Add"),},options));this.trigger_up('getRecordInfo',{recordInfo:options,type:'media',callback:function(recordInfo){_.defaults(options,recordInfo);},});if(!options.noImages){this.imageWidget=new MediaModules.ImageWidget(this,media,options);}
if(!options.noDocuments){this.documentWidget=new MediaModules.DocumentWidget(this,media,options);}
if(!options.noIcons){this.iconWidget=new MediaModules.IconWidget(this,media,options);}
if(!options.noVideos){this.videoWidget=new MediaModules.VideoWidget(this,media,options);}
if(this.imageWidget&&$media.is('img')){this.activeWidget=this.imageWidget;}else if(this.documentWidget&&$media.is('a.o_image')){this.activeWidget=this.documentWidget;}else if(this.videoWidget&&$media.is('.media_iframe_video, .o_bg_video_iframe')){this.activeWidget=this.videoWidget;}else if(this.iconWidget&&$media.is('span, i')){this.activeWidget=this.iconWidget;}else{this.activeWidget=[this.imageWidget,this.documentWidget,this.videoWidget,this.iconWidget].find(w=>!!w);}
this.initiallyActiveWidget=this.activeWidget;},start:function(){var promises=[this._super.apply(this,arguments)];this.$modal.find('.modal-dialog').addClass('o_select_media_dialog');if(this.imageWidget){promises.push(this.imageWidget.appendTo(this.$("#editor-media-image")));}
if(this.documentWidget){promises.push(this.documentWidget.appendTo(this.$("#editor-media-document")));}
if(this.iconWidget){promises.push(this.iconWidget.appendTo(this.$("#editor-media-icon")));}
if(this.videoWidget){promises.push(this.videoWidget.appendTo(this.$("#editor-media-video")));}
this.opened(()=>this.$('input.o_we_search:visible:first').focus());return Promise.all(promises);},isDocumentActive:function(){return this.activeWidget===this.documentWidget;},isIconActive:function(){return this.activeWidget===this.iconWidget;},isImageActive:function(){return this.activeWidget===this.imageWidget;},isVideoActive:function(){return this.activeWidget===this.videoWidget;},save:function(){var self=this;var _super=this._super;var args=arguments;return this.activeWidget.save().then(function(data){if(self.activeWidget.error){self.displayNotification({type:'danger',message:self.activeWidget.error,});return;}
if(self.activeWidget!==self.initiallyActiveWidget){self._clearWidgets();}
if(self.media!==data){var oldClasses=self.media&&_.toArray(self.media.classList);if(oldClasses){data.className=_.union(_.toArray(data.classList),oldClasses).join(' ');}}
self.final_data=data;_super.apply(self,args);$(data).trigger('content_changed');});},_clearWidgets:function(){[this.imageWidget,this.documentWidget,this.iconWidget,this.videoWidget].forEach((widget)=>{if(widget!==this.activeWidget){widget&&widget.clear();}});},_onClickDocumentTab:function(){this.activeWidget=this.documentWidget;},_onClickIconTab:function(){this.activeWidget=this.iconWidget;},_onClickImageTab:function(){this.activeWidget=this.imageWidget;},_onClickVideoTab:function(){this.activeWidget=this.videoWidget;},_onHideRequest:function(ev){this.$modal.addClass('d-none');},_onSaveRequest:function(ev){ev.stopPropagation();this.save();},_onShowRequest:function(ev){this.$modal.removeClass('d-none');},});return MediaDialog;});;

/* /web_editor/static/src/js/wysiwyg/widgets/widgets.js defined in bundle 'web_editor.assets_wysiwyg' */
odoo.define('wysiwyg.widgets',function(require){'use strict';var Dialog=require('wysiwyg.widgets.Dialog');var AltDialog=require('wysiwyg.widgets.AltDialog');var MediaDialog=require('wysiwyg.widgets.MediaDialog');var LinkDialog=require('wysiwyg.widgets.LinkDialog');var ImageCropWidget=require('wysiwyg.widgets.ImageCropWidget');const{ColorpickerDialog}=require('web.Colorpicker');var media=require('wysiwyg.widgets.media');return{Dialog:Dialog,AltDialog:AltDialog,MediaDialog:MediaDialog,LinkDialog:LinkDialog,ImageCropWidget:ImageCropWidget,ColorpickerDialog:ColorpickerDialog,MediaWidget:media.MediaWidget,SearchableMediaWidget:media.SearchableMediaWidget,FileWidget:media.FileWidget,ImageWidget:media.ImageWidget,DocumentWidget:media.DocumentWidget,IconWidget:media.IconWidget,VideoWidget:media.VideoWidget,};});;

/* /web_editor/static/src/js/editor/snippets.editor.js defined in bundle 'web_editor.assets_wysiwyg' */
odoo.define('web_editor.snippet.editor',function(require){'use strict';var concurrency=require('web.concurrency');var core=require('web.core');var Dialog=require('web.Dialog');var dom=require('web.dom');var Widget=require('web.Widget');var options=require('web_editor.snippets.options');var Wysiwyg=require('web_editor.wysiwyg');const{ColorPaletteWidget}=require('web_editor.ColorPalette');const SmoothScrollOnDrag=require('web/static/src/js/core/smooth_scroll_on_drag.js');const{getCSSVariableValue}=require('web_editor.utils');var _t=core._t;var globalSelector={closest:()=>$(),all:()=>$(),is:()=>false,};var SnippetEditor=Widget.extend({template:'web_editor.snippet_overlay',xmlDependencies:['/web_editor/static/src/xml/snippets.xml'],events:{'click .oe_snippet_remove':'_onRemoveClick','wheel':'_onMouseWheel',},custom_events:{'option_update':'_onOptionUpdate','user_value_widget_request':'_onUserValueWidgetRequest','snippet_option_update':'_onSnippetOptionUpdate','snippet_option_visibility_update':'_onSnippetOptionVisibilityUpdate',},layoutElementsSelector:['.o_we_shape','.o_we_bg_filter',].join(','),init:function(parent,target,templateOptions,$editable,options){this._super.apply(this,arguments);this.options=options;this.$editable=$editable&&$editable.length?$editable:$(document.body);this.ownerDocument=this.$editable[0].ownerDocument;this.$body=$(this.ownerDocument.body);this.$target=$(target);this.$target.data('snippet-editor',this);this.templateOptions=templateOptions;this.isTargetParentEditable=false;this.isTargetMovable=false;this.$scrollingElement=$().getScrollingElement();this.__isStarted=new Promise(resolve=>{this.__isStartedResolveFunc=resolve;});},start:function(){var defs=[this._super.apply(this,arguments)];defs.push(this._initializeOptions());var $customize=this._customize$Elements[this._customize$Elements.length-1];this.isTargetParentEditable=this.$target.parent().is(':o_editable');this.isTargetMovable=this.isTargetParentEditable&&this.isTargetMovable;this.isTargetRemovable=this.isTargetParentEditable&&!this.$target.parent().is('[data-oe-type="image"]');if(this.isTargetMovable){this.dropped=false;const smoothScrollOptions=this.options.getScrollOptions({jQueryDraggableOptions:{cursorAt:{left:10,top:10},handle:'.o_move_handle',helper:()=>{var $clone=this.$el.clone().css({width:'24px',height:'24px',border:0});$clone.appendTo(this.$body).removeClass('d-none');return $clone;},start:this._onDragAndDropStart.bind(this),stop:(...args)=>{setTimeout(()=>{this._onDragAndDropStop(...args);},0);},},});const modalAncestorEl=this.$target[0].closest('.modal');const $scrollTarget=modalAncestorEl&&$(modalAncestorEl)||$().getScrollingElement();this.draggableComponent=new SmoothScrollOnDrag(this,this.$el,$scrollTarget,smoothScrollOptions);}else{this.$('.o_overlay_move_options').addClass('d-none');$customize.find('.oe_snippet_clone').addClass('d-none');}
if(!this.isTargetRemovable){this.$el.add($customize).find('.oe_snippet_remove').addClass('d-none');}
var _animationsCount=0;var postAnimationCover=_.throttle(()=>{this.trigger_up('cover_update',{overlayVisible:true,});},100);this.$target.on('transitionstart.snippet_editor, animationstart.snippet_editor',()=>{_animationsCount++;setTimeout(()=>{if(!--_animationsCount){postAnimationCover();}},500);});this.$target.on('transitionend.snippet_editor, animationend.snippet_editor',postAnimationCover);return Promise.all(defs).then(()=>{this.__isStartedResolveFunc(this);});},destroy:function(){this.trigger_up('snippet_editor_destroyed');this._super(...arguments);this.$target.removeData('snippet-editor');this.$target.off('.snippet_editor');},areOptionsShown:function(){const lastIndex=this._customize$Elements.length-1;return!!this._customize$Elements[lastIndex].parent().length;},buildSnippet:async function(){for(var i in this.styles){this.styles[i].onBuilt();}
await this.toggleTargetVisibility(true);},cleanForSave:async function(){if(this.isDestroyed()){return;}
this.willDestroyEditors=true;await this.toggleTargetVisibility(!this.$target.hasClass('o_snippet_invisible'));const proms=_.map(this.styles,option=>{return option.cleanForSave();});await Promise.all(proms);},closeWidgets:function(){if(!this.styles||!this.areOptionsShown()){return;}
Object.keys(this.styles).forEach(key=>{this.styles[key].closeWidgets();});},cover:function(){if(!this.isShown()||!this.$target.length){return;}
const $modal=this.$target.find('.modal:visible');const $target=$modal.length?$modal:this.$target;const targetEl=$target[0];const rect=targetEl.getBoundingClientRect();const vpWidth=window.innerWidth||document.documentElement.clientWidth;const vpHeight=window.innerHeight||document.documentElement.clientHeight;const isInViewport=(rect.bottom>-0.1&&rect.right>-0.1&&(vpHeight-rect.top)>-0.1&&(vpWidth-rect.left)>-0.1);const hasSize=(Math.abs(rect.bottom-rect.top)>0.01&&Math.abs(rect.right-rect.left)>0.01);if(!isInViewport||!hasSize||!this.$target.is(`:visible`)){this.toggleOverlayVisibility(false);return;}
const offset=$target.offset();var manipulatorOffset=this.$el.parent().offset();offset.top-=manipulatorOffset.top;offset.left-=manipulatorOffset.left;this.$el.css({width:$target.outerWidth(),left:offset.left,top:offset.top,});this.$('.o_handles').css('height',$target.outerHeight());const editableOffsetTop=this.$editable.offset().top-manipulatorOffset.top;this.$el.toggleClass('o_top_cover',offset.top-editableOffsetTop<25);},getName:function(){if(this.$target.data('name')!==undefined){return this.$target.data('name');}
if(this.$target.is('img')){return _t("Image");}
if(this.$target.parent('.row').length){return _t("Column");}
return _t("Block");},isShown:function(){return this.$el&&this.$el.parent().length&&this.$el.hasClass('oe_active');},isSticky:function(){return this.$el&&this.$el.hasClass('o_we_overlay_sticky');},isTargetVisible:function(){return(this.$target[0].dataset.invisible!=='1');},removeSnippet:async function(){this.toggleOverlay(false);await this.toggleOptions(false);await this.toggleTargetVisibility(!this.$target.hasClass('o_snippet_invisible'));this.trigger_up('will_remove_snippet',{$target:this.$target});await new Promise(resolve=>{this.trigger_up('call_for_each_child_snippet',{$snippet:this.$target,callback:function(editor,$snippet){for(var i in editor.styles){editor.styles[i].onRemove();}},onSuccess:resolve,});});this.trigger_up('go_to_parent',{$snippet:this.$target});var $parent=this.$target.parent();this.$target.find('*').addBack().tooltip('dispose');this.$target.remove();this.$el.remove();var node=$parent[0];if(node&&node.firstChild){if(!node.firstChild.tagName&&node.firstChild.textContent===' '){node.removeChild(node.firstChild);}}
if($parent.closest(':data("snippet-editor")').length){const isEmptyAndRemovable=($el,editor)=>{editor=editor||$el.data('snippet-editor');const isEmpty=$el.text().trim()===''&&$el.children().toArray().every(el=>{return el.matches(this.layoutElementsSelector);});return isEmpty&&!$el.hasClass('oe_structure')&&!$el.parent().hasClass('carousel-item')&&(!editor||editor.isTargetParentEditable);};var editor=$parent.data('snippet-editor');while(!editor){var $nextParent=$parent.parent();if(isEmptyAndRemovable($parent)){$parent.remove();}
$parent=$nextParent;editor=$parent.data('snippet-editor');}
if(isEmptyAndRemovable($parent,editor)){setTimeout(()=>editor.removeSnippet());}}
this.$body.find('.note-control-selection').hide();this.$body.find('.o_table_handler').remove();this.trigger_up('snippet_removed');this.destroy();$parent.trigger('content_changed');$(window).trigger('resize');},toggleOverlay:function(show,previewMode){if(!this.$el){return;}
if(previewMode){this.$el.toggleClass('o_we_overlay_preview',show);}else{this.$el.removeClass('o_we_overlay_preview');this.$el.toggleClass('o_we_overlay_sticky',show);}
this.$el.toggleClass('oe_active',show);this.cover();},async toggleOptions(show){if(!this.$el){return;}
if(this.areOptionsShown()===show){return;}
this.trigger_up('update_customize_elements',{customize$Elements:show?this._customize$Elements:[],});const editorUIsToUpdate=[];const focusOrBlur=show?(editor,options)=>{for(const opt of options){opt.onFocus();}
editorUIsToUpdate.push(editor);}:(editor,options)=>{for(const opt of options){opt.onBlur();}};for(const $el of this._customize$Elements){const editor=$el.data('editor');const styles=_.chain(editor.styles).values().sortBy('__order').value();focusOrBlur(editor,styles);}
await Promise.all(editorUIsToUpdate.map(editor=>editor.updateOptionsUI()));await Promise.all(editorUIsToUpdate.map(editor=>editor.updateOptionsUIVisibility()));},toggleTargetVisibility:async function(show){show=this._toggleVisibilityStatus(show);var styles=_.values(this.styles);const proms=_.sortBy(styles,'__order').map(style=>{return show?style.onTargetShow():style.onTargetHide();});await Promise.all(proms);return show;},toggleOverlayVisibility:function(show){if(this.$el&&!this.scrollingTimeout){this.$el.toggleClass('o_overlay_hidden',!show&&this.isShown());}},async updateOptionsUI(){const proms=Object.values(this.styles).map(opt=>{return opt.updateUI({noVisibility:true});});return Promise.all(proms);},async updateOptionsUIVisibility(){const proms=Object.values(this.styles).map(opt=>{return opt.updateUIVisibility();});return Promise.all(proms);},clone:async function(recordUndo){this.trigger_up('snippet_will_be_cloned',{$target:this.$target});var $clone=this.$target.clone(false);if(recordUndo){this.trigger_up('request_history_undo_record',{$target:this.$target});}
this.$target.after($clone);await new Promise(resolve=>{this.trigger_up('call_for_each_child_snippet',{$snippet:$clone,callback:function(editor,$snippet){for(var i in editor.styles){editor.styles[i].onClone({isCurrent:($snippet.is($clone)),});}},onSuccess:resolve,});});this.trigger_up('snippet_cloned',{$target:$clone,$origin:this.$target});$clone.trigger('content_changed');},_initializeOptions:function(){this._customize$Elements=[];this.styles={};this.selectorSiblings=[];this.selectorChildren=[];var $element=this.$target.parent();while($element.length){var parentEditor=$element.data('snippet-editor');if(parentEditor){this._customize$Elements=this._customize$Elements.concat(parentEditor._customize$Elements);break;}
$element=$element.parent();}
var $optionsSection=$(core.qweb.render('web_editor.customize_block_options_section',{name:this.getName(),})).data('editor',this);const $optionsSectionBtnGroup=$optionsSection.find('we-top-button-group');$optionsSectionBtnGroup.contents().each((i,node)=>{if(node.nodeType===Node.TEXT_NODE){node.parentNode.removeChild(node);}});$optionsSection.on('mouseenter',this._onOptionsSectionMouseEnter.bind(this));$optionsSection.on('mouseleave',this._onOptionsSectionMouseLeave.bind(this));$optionsSection.on('click','we-title > span',this._onOptionsSectionClick.bind(this));$optionsSection.on('click','.oe_snippet_clone',this._onCloneClick.bind(this));$optionsSection.on('click','.oe_snippet_remove',this._onRemoveClick.bind(this));this._customize$Elements.push($optionsSection);this.$el.data('$optionsSection',$optionsSection);var i=0;var defs=_.map(this.templateOptions,val=>{if(!val.selector.is(this.$target)){return;}
if(val['drop-near']){this.selectorSiblings.push(val['drop-near']);}
if(val['drop-in']){this.selectorChildren.push(val['drop-in']);}
var optionName=val.option;var option=new(options.registry[optionName]||options.Class)(this,val.$el.children(),val.base_target?this.$target.find(val.base_target).eq(0):this.$target,this.$el,_.extend({optionName:optionName,snippetName:this.getName(),},val.data),this.options);var key=optionName||_.uniqueId('option');if(this.styles[key]){key=_.uniqueId(key);}
this.styles[key]=option;option.__order=i++;if(option.forceNoDeleteButton){this.$el.add($optionsSection).find('.oe_snippet_remove').addClass('d-none');}
return option.appendTo(document.createDocumentFragment());});this.isTargetMovable=(this.selectorSiblings.length>0||this.selectorChildren.length>0);this.$el.find('[data-toggle="dropdown"]').dropdown();return Promise.all(defs).then(()=>{const options=_.sortBy(this.styles,'__order');options.forEach(option=>{if(option.isTopOption){$optionsSectionBtnGroup.prepend(option.$el);}else{$optionsSection.append(option.$el);}});$optionsSection.toggleClass('d-none',options.length===0);});},_toggleVisibilityStatus:function(show){if(show===undefined){show=!this.isTargetVisible();}
if(show){delete this.$target[0].dataset.invisible;}else{this.$target[0].dataset.invisible='1';}
return show;},_canBeSanitizedUnless(el){let result=true;for(const snippetEl of[el,...el.querySelectorAll('[data-snippet]')]){this.trigger_up('find_snippet_template',{snippet:snippetEl,callback:function(snippetTemplate){const forbidSanitize=snippetTemplate.dataset.oeForbidSanitize;if(forbidSanitize){result=forbidSanitize==='form'?'form':false;}},});if(!result){break;}}
return result;},_onCloneClick:function(ev){ev.preventDefault();this.clone(true);},_onDragAndDropStart:function(){var self=this;this.dropped=false;self.size={width:self.$target.width(),height:self.$target.height()};const closestFormEl=this.$target[0].closest('form');self.$target.after('<div class="oe_drop_clone" style="display: none;"/>');self.$target.detach();self.$el.addClass('d-none');var $selectorSiblings;for(var i=0;i<self.selectorSiblings.length;i++){if(!$selectorSiblings){$selectorSiblings=self.selectorSiblings[i].all();}else{$selectorSiblings=$selectorSiblings.add(self.selectorSiblings[i].all());}}
var $selectorChildren;for(i=0;i<self.selectorChildren.length;i++){if(!$selectorChildren){$selectorChildren=self.selectorChildren[i].all();}else{$selectorChildren=$selectorChildren.add(self.selectorChildren[i].all());}}
if(this.$target[0].classList.contains('s_table_of_content')){$selectorChildren=$selectorChildren.filter((i,el)=>!el.closest('.s_table_of_content'));}
if(this.$target[0].matches('.form-group')){$selectorSiblings=$selectorSiblings.filter((i,el)=>closestFormEl===el.closest('form'));}
const canBeSanitizedUnless=this._canBeSanitizedUnless(this.$target[0]);this.trigger_up('go_to_parent',{$snippet:this.$target});this.trigger_up('activate_insertion_zones',{$selectorSiblings:$selectorSiblings,$selectorChildren:$selectorChildren,canBeSanitizedUnless:canBeSanitizedUnless,});this.$body.addClass('move-important');this.$dropZones=this.$editable.find('.oe_drop_zone');if(!canBeSanitizedUnless){this.$dropZones=this.$dropZones.not('[data-oe-sanitize] .oe_drop_zone');}else if(canBeSanitizedUnless==='form'){this.$dropZones=this.$dropZones.not('[data-oe-sanitize][data-oe-sanitize!="allow_form"] .oe_drop_zone');}
this.$dropZones.droppable({over:function(){if(self.dropped){self.$target.detach();$('.oe_drop_zone').removeClass('invisible');}
self.dropped=true;$(this).first().after(self.$target).addClass('invisible');},out:function(){var prev=self.$target.prev();if(this===prev[0]){self.dropped=false;self.$target.detach();$(this).removeClass('invisible');}},});self.draggableComponent.$scrollTarget.on('scroll.scrolling_element',function(){self.$el.trigger('scroll');});},_onDragAndDropStop:function(ev,ui){if(!this.dropped){var $el=$.nearest({x:ui.position.left,y:ui.position.top},'.oe_drop_zone',{container:document.body}).first();$el=$el.filter(this.$dropZones);if($el.length){$el.after(this.$target);this.dropped=true;}}
this.$dropZones.droppable('destroy');this.$editable.find('.oe_drop_zone').remove();var prev=this.$target.first()[0].previousSibling;var next=this.$target.last()[0].nextSibling;var $parent=this.$target.parent();var $clone=this.$editable.find('.oe_drop_clone');if(prev===$clone[0]){prev=$clone[0].previousSibling;}else if(next===$clone[0]){next=$clone[0].nextSibling;}
$clone.after(this.$target);var $from=$clone.parent();this.$el.removeClass('d-none');this.$body.removeClass('move-important');$clone.remove();if(this.dropped){this.trigger_up('request_history_undo_record',{$target:this.$target});if(prev){this.$target.insertAfter(prev);}else if(next){this.$target.insertBefore(next);}else{$parent.prepend(this.$target);}
for(var i in this.styles){this.styles[i].onMove();}
this.$target.trigger('content_changed');$from.trigger('content_changed');}
this.trigger_up('drag_and_drop_stop',{$snippet:this.$target,});this.draggableComponent.$scrollTarget.off('scroll.scrolling_element');delete this.$dropZones;},_onOptionsSectionMouseEnter:function(ev){if(!this.$target.is(':visible')){return;}
this.trigger_up('activate_snippet',{$snippet:this.$target,previewMode:true,});},_onOptionsSectionMouseLeave:function(ev){this.trigger_up('activate_snippet',{$snippet:false,previewMode:true,});},_onOptionsSectionClick:function(ev){this.trigger_up('activate_snippet',{$snippet:this.$target,previewMode:false,});},_onOptionUpdate:function(ev){var self=this;if(ev.data.optionNames){ev.stopPropagation();_.each(ev.data.optionNames,function(name){notifyForEachMatchedOption(name);});}
if(ev.data.optionName){if(notifyForEachMatchedOption(ev.data.optionName)){ev.stopPropagation();}}
function notifyForEachMatchedOption(name){var regex=new RegExp('^'+name+'\\d+$');var hasOption=false;for(var key in self.styles){if(key===name||regex.test(key)){self.styles[key].notify(ev.data.name,ev.data.data);hasOption=true;}}
return hasOption;}},_onRemoveClick:function(ev){ev.preventDefault();ev.stopPropagation();this.trigger_up('request_history_undo_record',{$target:this.$target});this.trigger_up('snippet_edition_request',{exec:this.removeSnippet.bind(this)});},_onSnippetOptionUpdate:async function(ev){},_onSnippetOptionVisibilityUpdate:function(ev){if(this.willDestroyEditors){return;}
ev.data.show=this._toggleVisibilityStatus(ev.data.show);},_onUserValueWidgetRequest:function(ev){ev.stopPropagation();for(const key of Object.keys(this.styles)){const widget=this.styles[key].findWidget(ev.data.name);if(widget){ev.data.onSuccess(widget);return;}}},_onMouseWheel:function(ev){ev.stopPropagation();this.$el.css('pointer-events','none');clearTimeout(this.wheelTimeout);this.wheelTimeout=setTimeout(()=>{this.$el.css('pointer-events','');},250);},});var SnippetsMenu=Widget.extend({id:'oe_snippets',cacheSnippetTemplate:{},events:{'click .oe_snippet':'_onSnippetClick','click .o_install_btn':'_onInstallBtnClick','click .o_we_add_snippet_btn':'_onBlocksTabClick','click .o_we_invisible_entry':'_onInvisibleEntryClick','click #snippet_custom .o_delete_btn':'_onDeleteBtnClick','mousedown':'_onMouseDown','input .o_snippet_search_filter_input':'_onSnippetSearchInput','click .o_snippet_search_filter_reset':'_onSnippetSearchResetClick','summernote_popover_update_call .o_we_snippet_text_tools':'_onSummernoteToolsUpdate',},custom_events:{'activate_insertion_zones':'_onActivateInsertionZones','activate_snippet':'_onActivateSnippet','call_for_each_child_snippet':'_onCallForEachChildSnippet','clone_snippet':'_onCloneSnippet','cover_update':'_onOverlaysCoverUpdate','deactivate_snippet':'_onDeactivateSnippet','drag_and_drop_stop':'_onDragAndDropStop','get_snippet_versions':'_onGetSnippetVersions','go_to_parent':'_onGoToParent','find_snippet_template':'_onFindSnippetTemplate','remove_snippet':'_onRemoveSnippet','snippet_edition_request':'_onSnippetEditionRequest','snippet_editor_destroyed':'_onSnippetEditorDestroyed','snippet_removed':'_onSnippetRemoved','snippet_cloned':'_onSnippetCloned','snippet_option_update':'_onSnippetOptionUpdate','snippet_option_visibility_update':'_onSnippetOptionVisibilityUpdate','snippet_thumbnail_url_request':'_onSnippetThumbnailURLRequest','reload_snippet_dropzones':'_disableUndroppableSnippets','request_save':'_onSaveRequest','update_customize_elements':'_onUpdateCustomizeElements','hide_overlay':'_onHideOverlay','block_preview_overlays':'_onBlockPreviewOverlays','unblock_preview_overlays':'_onUnblockPreviewOverlays','user_value_widget_opening':'_onUserValueWidgetOpening','user_value_widget_closing':'_onUserValueWidgetClosing','reload_snippet_template':'_onReloadSnippetTemplate',},tabs:{BLOCKS:'blocks',OPTIONS:'options',},init:function(parent,options){this._super.apply(this,arguments);options=options||{};this.trigger_up('getRecordInfo',{recordInfo:options,callback:function(recordInfo){_.defaults(options,recordInfo);},});this.options=options;if(!this.options.snippets){this.options.snippets='web_editor.snippets';}
this.snippetEditors=[];this._enabledEditorHierarchy=[];this._mutex=new concurrency.Mutex();this.setSelectorEditableArea(options.$el,options.selectorEditableArea);this._notActivableElementsSelector=['#web_editor-top-edit','.o_we_website_top_actions','#oe_snippets','#oe_manipulators','.o_technical_modal','.oe_drop_zone','.o_notification_manager','.o_we_no_overlay','.ui-autocomplete','.modal .close','.o_we_crop_widget',].join(', ');this.loadingTimers={};this.loadingElements={};},willStart:function(){ColorPaletteWidget.loadDependencies(this);return this._super(...arguments);},async start(){var defs=[this._super.apply(this,arguments)];this.ownerDocument=this.$el[0].ownerDocument;this.$document=$(this.ownerDocument);this.window=this.ownerDocument.defaultView;this.$window=$(this.window);this.customizePanel=document.createElement('div');this.customizePanel.classList.add('o_we_customize_panel','d-none');this.textEditorPanelEl=document.createElement('div');this.textEditorPanelEl.classList.add('o_we_snippet_text_tools','d-none');this.invisibleDOMPanelEl=document.createElement('div');this.invisibleDOMPanelEl.classList.add('o_we_invisible_el_panel');this.invisibleDOMPanelEl.appendChild($('<div/>',{text:_t('Invisible Elements'),class:'o_panel_header',})[0]);this.options.getScrollOptions=this._getScrollOptions.bind(this);defs.push((async()=>{await this._loadSnippetsTemplates();await this._updateInvisibleDOM();})());this.$snippetEditorArea=$('<div/>',{id:'oe_manipulators',}).insertAfter(this.$el);var lastElement;const onClick=ev=>{var srcElement=ev.target||(ev.originalEvent&&(ev.originalEvent.target||ev.originalEvent.originalTarget))||ev.srcElement;if(!srcElement||lastElement===srcElement){return;}
lastElement=srcElement;_.defer(function(){lastElement=false;});var $target=$(srcElement);if(!$target.closest('we-button, we-toggler, we-select, .o_we_color_preview').length){this._closeWidgets();}
if(!$target.closest('body > *').length){return;}
if($target.closest(this._notActivableElementsSelector).length){return;}
const $oeStructure=$target.closest('.oe_structure');if($oeStructure.length&&!$oeStructure.children().length&&this.$snippets){this._activateSnippet(false);this.$snippets.odooBounce();return;}
this._activateSnippet($target);};this.$document.on('click.snippets_menu','*',onClick);this.$document.on('mouseup.snippets_menu','.dropdown-toggle',onClick);core.bus.on('deactivate_snippet',this,this._onDeactivateSnippet);var debouncedCoverUpdate=_.throttle(()=>{this.updateCurrentSnippetEditorOverlay();},50);this.$window.on('resize.snippets_menu',debouncedCoverUpdate);this.$window.on('content_changed.snippets_menu',debouncedCoverUpdate);this.$document.on('keydown.snippets_menu',()=>{this.__overlayKeyWasDown=true;this.snippetEditors.forEach(editor=>{editor.toggleOverlayVisibility(false);});});this.$document.on('mousemove.snippets_menu, mousedown.snippets_menu',_.throttle(()=>{if(!this.__overlayKeyWasDown){return;}
this.__overlayKeyWasDown=false;this.snippetEditors.forEach(editor=>{editor.toggleOverlayVisibility(true);editor.cover();});},250));this.$scrollingElement=$().getScrollingElement();this.$scrollingTarget=this.$scrollingElement.is(this.ownerDocument.scrollingElement)?$(this.ownerDocument.defaultView):this.$scrollingElement;this._onScrollingElementScroll=_.throttle(()=>{for(const editor of this.snippetEditors){editor.toggleOverlayVisibility(false);}
clearTimeout(this.scrollingTimeout);this.scrollingTimeout=setTimeout(()=>{this._scrollingTimeout=null;for(const editor of this.snippetEditors){editor.toggleOverlayVisibility(true);editor.cover();}},250);},50);this.$scrollingTarget[0].addEventListener('scroll',this._onScrollingElementScroll,{capture:true});this.$document.on('click.snippets_menu','.o_default_snippet_text',function(ev){$(ev.target).closest('.o_default_snippet_text').removeClass('o_default_snippet_text');$(ev.target).selectContent();$(ev.target).removeClass('o_default_snippet_text');});this.$document.on('keyup.snippets_menu',function(){var range=Wysiwyg.getRange(this);$(range&&range.sc).closest('.o_default_snippet_text').removeClass('o_default_snippet_text');});const $autoFocusEls=$('.o_we_snippet_autofocus');this._activateSnippet($autoFocusEls.length?$autoFocusEls.first():false);this.$el.tooltip({selector:'we-title',placement:'bottom',delay:100,title:function(){const el=this;el.style.setProperty('overflow','scroll','important');const tipContent=el.scrollWidth>el.clientWidth?el.innerHTML:'';el.style.removeProperty('overflow');return tipContent;},});return Promise.all(defs).then(()=>{this.$('[data-title]').tooltip({delay:100,title:function(){return this.classList.contains('active')?false:this.dataset.title;},});setTimeout(()=>{this.$window.trigger('resize');this._mutex.exec(()=>this._textToolsSwitchingEnabled=true);},1000);});},destroy:function(){this._super.apply(this,arguments);if(this.$window){this.$snippetEditorArea.remove();this.$window.off('.snippets_menu');this.$document.off('.snippets_menu');if(this.$scrollingTarget){this.$scrollingTarget[0].removeEventListener('scroll',this._onScrollingElementScroll,{capture:true});}}
core.bus.off('deactivate_snippet',this,this._onDeactivateSnippet);delete this.cacheSnippetTemplate[this.options.snippets];},cleanForSave:async function(){window.document.querySelectorAll("span[data-snippet='s_cover'][data-name='Cover']").forEach(el=>{delete el.dataset["snippet"];delete el.dataset["name"];const dirty=el.closest(".o_editable")||el;dirty.classList.add("o_dirty");});await this._activateSnippet(false);await this._mutex.getUnlockedDef();this.trigger_up('ready_to_clean_for_save');this.willDestroyEditors=true;await this._destroyEditors();this.getEditableArea().find('[contentEditable]').removeAttr('contentEditable').removeProp('contentEditable');this.getEditableArea().find('.o_we_selected_image').removeClass('o_we_selected_image');},loadSnippets:function(invalidateCache){if(!invalidateCache&&this.cacheSnippetTemplate[this.options.snippets]){this._defLoadSnippets=this.cacheSnippetTemplate[this.options.snippets];return this._defLoadSnippets;}
this._defLoadSnippets=this._rpc({model:'ir.ui.view',method:'render_public_asset',args:[this.options.snippets,{}],kwargs:{context:this.options.context,},});this.cacheSnippetTemplate[this.options.snippets]=this._defLoadSnippets;return this._defLoadSnippets;},setSelectorEditableArea:function($editor,selectorEditableArea){this.selectorEditableArea=selectorEditableArea;this.$editor=$editor;this.$body=$editor.closest('body');},getEditableArea:function(){return this.$editor.find(this.selectorEditableArea).add(this.$editor.filter(this.selectorEditableArea));},updateCurrentSnippetEditorOverlay:function(){for(const snippetEditor of this.snippetEditors){if(snippetEditor.$target.closest('body').length){snippetEditor.cover();continue;}
this._mutex.exec(()=>snippetEditor.destroy());}},_activateInsertionZones:function($selectorSiblings,$selectorChildren){var self=this;const $editableArea=self.getEditableArea();let $open=$editableArea.find('.modal:visible');if(!$open.length){$open=$editableArea.find('.dropdown-menu.show').addBack('.dropdown-menu.show').parent();}
if($open.length){$selectorSiblings=$open.find($selectorSiblings);$selectorChildren=$open.find($selectorChildren);}
function setDropZoneDirection($elem,$parent,$sibling){var vertical=false;var style={};$sibling=$sibling||$elem;var css=window.getComputedStyle($elem[0]);var parentCss=window.getComputedStyle($parent[0]);var float=css.float||css.cssFloat;var display=parentCss.display;var flex=parentCss.flexDirection;if(float==='left'||float==='right'||(display==='flex'&&flex==='row')){style['float']=float;if($sibling.parent().width()!==$sibling.outerWidth(true)){vertical=true;style['height']=Math.max($sibling.outerHeight(),30)+'px';}}
return{vertical:vertical,style:style,};}
function testPreviousSibling(node,$zone){if(!node||((node.tagName||!node.textContent.match(/\S/))&&node.tagName!=='BR')){return false;}
return{vertical:true,style:{'float':'none','display':'inline-block','height':parseInt(self.window.getComputedStyle($zone[0]).lineHeight)+'px',},};}
var $clone=$('.oe_drop_clone');if($clone.length){var $neighbor=$clone.prev();if(!$neighbor.length){$neighbor=$clone.next();}
var data;if($neighbor.length){data=setDropZoneDirection($neighbor,$neighbor.parent());}else{data={vertical:false,style:{},};}
self._insertDropzone($('<we-hook/>').insertAfter($clone),data.vertical,data.style);}
if($selectorChildren){$selectorChildren.each(function(){var data;var $zone=$(this);var $children=$zone.find('> :not(.oe_drop_zone, .oe_drop_clone)');if(!$zone.children().last().is('.oe_drop_zone')){data=testPreviousSibling($zone[0].lastChild,$zone)||setDropZoneDirection($zone,$zone,$children.last());self._insertDropzone($('<we-hook/>').appendTo($zone),data.vertical,data.style);}
if(!$zone.children().first().is('.oe_drop_clone')){data=testPreviousSibling($zone[0].firstChild,$zone)||setDropZoneDirection($zone,$zone,$children.first());self._insertDropzone($('<we-hook/>').prependTo($zone),data.vertical,data.style);}});$selectorSiblings=$(_.uniq(($selectorSiblings||$()).add($selectorChildren.children()).get()));}
var noDropZonesSelector='[data-invisible="1"], .o_we_no_overlay, :not(:visible), :not(:o_editable)';if($selectorSiblings){$selectorSiblings.not(`.oe_drop_zone, .oe_drop_clone, ${noDropZonesSelector}`).each(function(){var data;var $zone=$(this);var $zoneToCheck=$zone;while($zoneToCheck.prev(noDropZonesSelector).length){$zoneToCheck=$zoneToCheck.prev();}
if(!$zoneToCheck.prev('.oe_drop_zone:visible, .oe_drop_clone').length){data=setDropZoneDirection($zone,$zone.parent());self._insertDropzone($('<we-hook/>').insertBefore($zone),data.vertical,data.style);}
$zoneToCheck=$zone;while($zoneToCheck.next(noDropZonesSelector).length){$zoneToCheck=$zoneToCheck.next();}
if(!$zoneToCheck.next('.oe_drop_zone:visible, .oe_drop_clone').length){data=setDropZoneDirection($zone,$zone.parent());self._insertDropzone($('<we-hook/>').insertAfter($zone),data.vertical,data.style);}});}
var count;var $zones;do{count=0;$zones=this.getEditableArea().find('.oe_drop_zone > .oe_drop_zone').remove();count+=$zones.length;$zones.remove();}while(count>0);$zones=this.getEditableArea().find('.oe_drop_zone:not(.oe_vertical)');$zones.each(function(){var zone=$(this);var prev=zone.prev();var next=zone.next();if(prev.is('.oe_drop_zone')||next.is('.oe_drop_zone')){zone.remove();return;}
var floatPrev=prev.css('float')||'none';var floatNext=next.css('float')||'none';var dispPrev=prev.css('display')||null;var dispNext=next.css('display')||null;if((floatPrev==='left'||floatPrev==='right')&&(floatNext==='left'||floatNext==='right')){zone.remove();}else if(dispPrev!==null&&dispNext!==null&&dispPrev.indexOf('inline')>=0&&dispNext.indexOf('inline')>=0){zone.remove();}});},_updateInvisibleDOM:function(){return this._execWithLoadingEffect(()=>{this.invisibleDOMMap=new Map();const $invisibleDOMPanelEl=$(this.invisibleDOMPanelEl);$invisibleDOMPanelEl.find('.o_we_invisible_entry').remove();const $invisibleSnippets=globalSelector.all().find('.o_snippet_invisible').addBack('.o_snippet_invisible');$invisibleDOMPanelEl.toggleClass('d-none',!$invisibleSnippets.length);const proms=_.map($invisibleSnippets,async el=>{const editor=await this._createSnippetEditor($(el));const $invisEntry=$('<div/>',{class:'o_we_invisible_entry d-flex align-items-center justify-content-between',text:editor.getName(),}).append($('<i/>',{class:`fa ${editor.isTargetVisible() ? 'fa-eye' : 'fa-eye-slash'} ml-2`}));$invisibleDOMPanelEl.append($invisEntry);this.invisibleDOMMap.set($invisEntry[0],el);});return Promise.all(proms);},false);},_activateSnippet:async function($snippet,previewMode,ifInactiveOptions){if(this._blockPreviewOverlays&&previewMode){return;}
if($snippet&&!$snippet.is(':visible')){return;}
if($snippet&&$snippet.length){$snippet=globalSelector.closest($snippet);}
const exec=previewMode?action=>this._mutex.exec(action):action=>this._execWithLoadingEffect(action,false);return exec(()=>{return new Promise(resolve=>{if($snippet&&$snippet.length){return this._createSnippetEditor($snippet).then(resolve);}
resolve(null);}).then(async editorToEnable=>{if(!previewMode&&this._enabledEditorHierarchy[0]===editorToEnable||ifInactiveOptions&&this._enabledEditorHierarchy.includes(editorToEnable)){return editorToEnable;}
if(!previewMode){this._enabledEditorHierarchy=[];let current=editorToEnable;while(current&&current.$target){this._enabledEditorHierarchy.push(current);current=current.getParent();}}
for(let i=this.snippetEditors.length;i--;){const editor=this.snippetEditors[i];editor.toggleOverlay(false,previewMode);if(!previewMode){await editor.toggleOptions(false);}}
if(!editorToEnable){editorToEnable=this.snippetEditors.find(editor=>editor.isSticky());previewMode=false;}
if(editorToEnable){editorToEnable.toggleOverlay(true,previewMode);await editorToEnable.toggleOptions(true);}
return editorToEnable;});});},_loadSnippetsTemplates:async function(invalidateCache){return this._execWithLoadingEffect(async()=>{await this._destroyEditors();const html=await this.loadSnippets(invalidateCache);await this._computeSnippetTemplates(html);},false);},_destroyEditors:async function($el){const proms=_.map(this.snippetEditors,async function(snippetEditor){if($el&&!$el.has(snippetEditor.$target).length){return;}
await snippetEditor.cleanForSave();snippetEditor.destroy();});await Promise.all(proms);},_callForEachChildSnippet:function($snippet,callback){var self=this;var defs=_.map($snippet.add(globalSelector.all($snippet)),function(el){var $snippet=$(el);return self._createSnippetEditor($snippet).then(function(editor){if(editor){return callback.call(self,editor,$snippet);}});});return Promise.all(defs);},_closeWidgets:function(){this.snippetEditors.forEach(editor=>editor.closeWidgets());},_computeSelectorFunctions:function(selector,exclude,target,noCheck,isChildren,excludeParent){var self=this;exclude+=`${exclude && ', '}.o_snippet_not_selectable`;let filterFunc=function(){return!$(this).is(exclude);};if(target){const oldFilter=filterFunc;filterFunc=function(){return oldFilter.apply(this)&&$(this).find(target).length!==0;};}
if(excludeParent){const oldFilter=filterFunc;filterFunc=function(){return oldFilter.apply(this)&&!$(this).parent().is(excludeParent);};}
var functions={is:function($from){return $from.is(selector)&&$from.filter(filterFunc).length!==0;},};if(noCheck){functions.closest=function($from,parentNode){return $from.closest(selector,parentNode).filter(filterFunc);};functions.all=function($from){return($from?dom.cssFind($from,selector):$(selector)).filter(filterFunc);};}else{functions.closest=function($from,parentNode){var parents=self.getEditableArea().get();return $from.closest(selector,parentNode).filter(function(){var node=this;while(node.parentNode){if(parents.indexOf(node)!==-1){return true;}
node=node.parentNode;}
return false;}).filter(filterFunc);};functions.all=isChildren?function($from){return dom.cssFind($from||self.getEditableArea(),selector).filter(filterFunc);}:function($from){$from=$from||self.getEditableArea();return $from.filter(selector).add(dom.cssFind($from,selector)).filter(filterFunc);};}
return functions;},_computeSnippetTemplates:function(html){var self=this;var $html=$(html);var $scroll=$html.siblings('#o_scroll');const $headerNavFix=$html.find('[data-js="HeaderNavbar"][data-selector="#wrapwrap > header > nav"]');if($headerNavFix.length){$headerNavFix[0].dataset.selector='#wrapwrap > header nav.navbar';}
this.templateOptions=[];var selectors=[];var $styles=$html.find('[data-selector]');const snippetAdditionDropIn=$styles.filter('#so_snippet_addition').data('drop-in');const oldFooterSnippetsSelector='footer .oe_structure > *';const newFooterSnippetsSelector='footer #footer.oe_structure > *:not(.s_popup)';$styles.each(function(){var $style=$(this);var selector=$style.data('selector');var exclude=$style.data('exclude')||'';const excludeParent=$style.attr('id')==="so_content_addition"?snippetAdditionDropIn:'';if($style[0].getAttribute('id')==='so_content_addition'){let dropInPatch=$style[0].dataset.dropIn.split(', ');dropInPatch=dropInPatch.map(selector=>selector==='.content'?'.content:not(.row)':selector);$style[0].dataset.dropIn=dropInPatch.join(', ');}
if(($style[0].dataset.js==='ColoredLevelBackground')&&exclude){exclude=exclude.split(', ').map(selector=>selector===oldFooterSnippetsSelector?newFooterSnippetsSelector:selector).join(', ');}
if(($style[0].dataset.js==='BackgroundToggler')){selector=selector.split(', ').map(selector=>selector===oldFooterSnippetsSelector?newFooterSnippetsSelector:selector).join(', ');}
if($style[0].dataset.optionName==="colorFilter"){const weColorPickerEl=$style[0].querySelector('we-colorpicker');weColorPickerEl.dataset.applyTo=weColorPickerEl.dataset.applyTo.split(', ').map(selector=>selector==='.o_we_bg_filter'?'> .o_we_bg_filter':selector).join(', ');}
var target=$style.data('target');var noCheck=$style.data('no-check');var optionID=$style.data('js')||$style.data('option-name');var option={'option':optionID,'base_selector':selector,'base_exclude':exclude,'base_target':target,'selector':self._computeSelectorFunctions(selector,exclude,target,noCheck),'$el':$style,'drop-near':$style.data('drop-near')&&self._computeSelectorFunctions($style.data('drop-near'),'',false,noCheck,true,excludeParent),'drop-in':$style.data('drop-in')&&self._computeSelectorFunctions($style.data('drop-in'),'',false,noCheck),'data':_.extend({string:$style.attr('string')},$style.data()),};self.templateOptions.push(option);selectors.push(option.selector);});$styles.addClass('d-none');globalSelector.closest=function($from){var $temp;var $target;for(var i=0,len=selectors.length;i<len;i++){$temp=selectors[i].closest($from,$target&&$target[0]);if($temp.length){$target=$temp;}}
return $target||$();};globalSelector.all=function($from){var $target=$();for(var i=0,len=selectors.length;i<len;i++){$target=$target.add(selectors[i].all($from));}
return $target;};globalSelector.is=function($from){for(var i=0,len=selectors.length;i<len;i++){if(selectors[i].is($from)){return true;}}
return false;};this.$snippets=$scroll.find('.o_panel_body').children().addClass('oe_snippet').each((i,el)=>{const $snippet=$(el);const name=_.escape(el.getAttribute('name'));const thumbnailSrc=_.escape(el.dataset.oeThumbnail);const $sbody=$snippet.children().addClass('oe_snippet_body');const isCustomSnippet=!!el.closest('#snippet_custom');let snippetClasses=$sbody.attr('class').match(/s_[^ ]+/g);if(snippetClasses&&snippetClasses.length){snippetClasses='.'+snippetClasses.join('.');}
const $els=$(snippetClasses).not('[data-name]').add($sbody);$els.attr('data-name',name).data('name',name);if($snippet[0].querySelector('form')&&!$snippet.data('oeForbidSanitize')){$snippet[0].dataset.oeForbidSanitize='form';}
const $thumbnail=$(`
                    <div class="oe_snippet_thumbnail">
                        <div class="oe_snippet_thumbnail_img" style="background-image: url(${thumbnailSrc});"/>
                        <span class="oe_snippet_thumbnail_title">${name}</span>
                    </div>
                `);$snippet.prepend($thumbnail);const moduleID=$snippet.data('moduleId');if(moduleID){el.classList.add('o_snippet_install');$thumbnail.append($('<button/>',{class:'btn btn-primary o_install_btn w-100',type:'button',text:_t("Install"),}));}
if(isCustomSnippet){const btnEl=document.createElement('we-button');btnEl.dataset.snippetId=$snippet.data('oeSnippetId');btnEl.classList.add('o_delete_btn','fa','fa-trash','btn','o_we_hover_danger');btnEl.title=_.str.sprintf(_t("Delete %s"),name);$snippet.append(btnEl);}}).not('[data-module-id]');if(!this.$snippets.length){this.$el.detach();}
this._registerDefaultTexts();$html.find('.o_not_editable').attr('contentEditable',false);for(const customTabPaneEl of $html.find('#snippet_custom_body .tab-pane')){customTabPaneEl.removeAttribute('id');}
this.$el.html($html);this.$el.append(this.customizePanel);this.$el.append(this.textEditorPanelEl);this.$el.append(this.invisibleDOMPanelEl);this._makeSnippetDraggable(this.$snippets);this._disableUndroppableSnippets();this.$el.addClass('o_loaded');$('body.editor_enable').addClass('editor_has_snippets');this.trigger_up('snippets_loaded',self.$el);},_createSnippetEditor:function($snippet){var self=this;var snippetEditor=$snippet.data('snippet-editor');if(snippetEditor){return snippetEditor.__isStarted;}
var def;var $parent=globalSelector.closest($snippet.parent());if($parent.length){def=this._createSnippetEditor($parent);}
return Promise.resolve(def).then(function(parentEditor){snippetEditor=$snippet.data('snippet-editor');if(snippetEditor){return snippetEditor.__isStarted;}
let editableArea=self.getEditableArea();snippetEditor=new SnippetEditor(parentEditor||self,$snippet,self.templateOptions,$snippet.closest('[data-oe-type="html"], .oe_structure').add(editableArea),self.options);self.snippetEditors.push(snippetEditor);return snippetEditor.appendTo(self.$snippetEditorArea);}).then(function(){return snippetEditor;});},_disableUndroppableSnippets:function(){var self=this;var cache={};this.$snippets.each(function(){var $snippet=$(this);var $snippetBody=$snippet.find('.oe_snippet_body');const isSanitizeForbidden=$snippet.data('oeForbidSanitize');const filterSanitize=isSanitizeForbidden==='form'?$els=>$els.filter((i,el)=>!el.closest('[data-oe-sanitize]:not([data-oe-sanitize="allow_form"])')):isSanitizeForbidden?$els=>$els.filter((i,el)=>!el.closest('[data-oe-sanitize]')):$els=>$els;var check=false;_.each(self.templateOptions,function(option,k){if(check||!($snippetBody.is(option.base_selector)&&!$snippetBody.is(option.base_exclude))){return;}
k=isSanitizeForbidden?'forbidden/'+k:k;cache[k]=cache[k]||{'drop-near':option['drop-near']?filterSanitize(option['drop-near'].all()).length:0,'drop-in':option['drop-in']?filterSanitize(option['drop-in'].all()).length:0,};check=(cache[k]['drop-near']||cache[k]['drop-in']);});$snippet.toggleClass('o_disabled',!check);$snippet.attr('title',check?'':_t("No location to drop in"));const $icon=$snippet.find('.o_snippet_undroppable').remove();if(check){$icon.remove();}else if(!$icon.length){const imgEl=document.createElement('img');imgEl.classList.add('o_snippet_undroppable');imgEl.src='/web_editor/static/src/img/snippet_disabled.svg';$snippet.append(imgEl);}});},_filterSnippets(search){const searchInputEl=this.el.querySelector('.o_snippet_search_filter_input');const searchInputReset=this.el.querySelector('.o_snippet_search_filter_reset');if(search!==undefined){searchInputEl.value=search;}else{search=searchInputEl.value;}
search=search.toLowerCase();searchInputReset.classList.toggle('d-none',!search);const strMatches=str=>!search||str.toLowerCase().includes(search);for(const panelEl of this.el.querySelectorAll('.o_panel')){let hasVisibleSnippet=false;const panelTitle=panelEl.querySelector('.o_panel_header').textContent;const isPanelTitleMatch=strMatches(panelTitle);for(const snippetEl of panelEl.querySelectorAll('.oe_snippet')){const matches=(isPanelTitleMatch||strMatches(snippetEl.getAttribute('name'))||strMatches(snippetEl.dataset.oeKeywords||''));if(matches){hasVisibleSnippet=true;}
snippetEl.classList.toggle('d-none',!matches);}
panelEl.classList.toggle('d-none',!hasVisibleSnippet);}},_getScrollOptions(options={}){return Object.assign({},options,{scrollBoundaries:Object.assign({right:false,},options.scrollBoundaries),jQueryDraggableOptions:Object.assign({appendTo:this.$body,cursor:'move',greedy:true,scroll:false,},options.jQueryDraggableOptions),disableHorizontalScroll:true,});},_insertDropzone:function($hook,vertical,style){let forbidSanitize;if(this._insertDropzoneCanBeSanitizedUnless==='form'){forbidSanitize=$hook.closest('[data-oe-sanitize]:not([data-oe-sanitize="allow_form"])').length;}else{forbidSanitize=!this._insertDropzoneCanBeSanitizedUnless&&$hook.closest('[data-oe-sanitize]').length;}
var $dropzone=$('<div/>',{'class':'oe_drop_zone oe_insert'+(vertical?' oe_vertical':'')+
(forbidSanitize?' text-center oe_drop_zone_danger':''),});if(style){$dropzone.css(style);}
if(forbidSanitize){$dropzone[0].appendChild(document.createTextNode(_t("For technical reasons, this block cannot be dropped here")));}
$hook.replaceWith($dropzone);return $dropzone;},_makeSnippetDraggable:function($snippets){var self=this;var $toInsert,dropped,$snippet;let $dropZones;let dragAndDropResolve;const $scrollingElement=$().getScrollingElement();const smoothScrollOptions=this._getScrollOptions({jQueryDraggableOptions:{handle:'.oe_snippet_thumbnail:not(.o_we_already_dragging)',cancel:'.oe_snippet.o_disabled',helper:function(){const dragSnip=this.cloneNode(true);dragSnip.querySelectorAll('.o_delete_btn').forEach(el=>el.remove());return dragSnip;},start:function(){const prom=new Promise(resolve=>dragAndDropResolve=()=>resolve());self._mutex.exec(()=>prom);self.$el.find('.oe_snippet_thumbnail').addClass('o_we_already_dragging');dropped=false;$snippet=$(this);var $baseBody=$snippet.find('.oe_snippet_body');var $selectorSiblings=$();var $selectorChildren=$();var temp=self.templateOptions;for(var k in temp){if($baseBody.is(temp[k].base_selector)&&!$baseBody.is(temp[k].base_exclude)){if(temp[k]['drop-near']){$selectorSiblings=$selectorSiblings.add(temp[k]['drop-near'].all());}
if(temp[k]['drop-in']){$selectorChildren=$selectorChildren.add(temp[k]['drop-in'].all());}}}
if($baseBody[0].matches('.s_popup, .o_newsletter_popup')){$selectorChildren=$selectorChildren.not('[data-snippet] *');}
if($baseBody[0].classList.contains('s_table_of_content')){$selectorChildren=$selectorChildren.filter((i,el)=>!el.closest('.s_table_of_content'));}
$toInsert=$baseBody.clone();[...$toInsert.find('img[src^="/web_editor/shape/"]')].forEach(dynamicSvg=>{const colorCustomizedURL=new URL(dynamicSvg.getAttribute('src'),window.location.origin);colorCustomizedURL.searchParams.set('c1',getCSSVariableValue('o-color-1'));dynamicSvg.src=colorCustomizedURL.pathname+colorCustomizedURL.search;});if(!$selectorSiblings.length&&!$selectorChildren.length){console.warn($snippet.find('.oe_snippet_thumbnail_title').text()+" have not insert action: data-drop-near or data-drop-in");return;}
const forbidSanitize=$snippet.data('oeForbidSanitize');self._insertDropzoneCanBeSanitizedUnless=forbidSanitize==='form'?'form':!forbidSanitize;self._activateInsertionZones($selectorSiblings,$selectorChildren);delete self._insertDropzoneCanBeSanitizedUnless;$dropZones=self.getEditableArea().find('.oe_drop_zone');if(forbidSanitize==='form'){$dropZones=$dropZones.filter((i,el)=>!el.closest('[data-oe-sanitize]:not([data-oe-sanitize="allow_form"]) .oe_drop_zone'));}else if(forbidSanitize){$dropZones=$dropZones.filter((i,el)=>!el.closest('[data-oe-sanitize] .oe_drop_zone'));}
$dropZones.droppable({over:function(){if(dropped){$toInsert.detach();$toInsert.addClass('oe_snippet_body');$('.oe_drop_zone').removeClass('invisible');}
dropped=true;$(this).first().after($toInsert).addClass('invisible');$toInsert.removeClass('oe_snippet_body');},out:function(){var prev=$toInsert.prev();if(this===prev[0]){dropped=false;$toInsert.detach();$(this).removeClass('invisible');$toInsert.addClass('oe_snippet_body');}},});const $openModal=self.getEditableArea().find('.modal:visible');self.draggableComponent.$scrollTarget=$openModal.length?$openModal:$scrollingElement;self.draggableComponent.$scrollTarget.on('scroll.scrolling_element',function(){self.$el.trigger('scroll');});},stop:async function(ev,ui){$toInsert.removeClass('oe_snippet_body');self.draggableComponent.$scrollTarget.off('scroll.scrolling_element');if(!dropped&&ui.position.top>3&&ui.position.left+ui.helper.outerHeight()<self.el.getBoundingClientRect().left){var $el=$.nearest({x:ui.position.left,y:ui.position.top},'.oe_drop_zone',{container:document.body}).first();$el=$el.filter($dropZones);if($el.length){$el.after($toInsert);dropped=true;}}
$dropZones.droppable('destroy');self.getEditableArea().find('.oe_drop_zone').remove();if(dropped){var prev=$toInsert.first()[0].previousSibling;var next=$toInsert.last()[0].nextSibling;if(prev){$toInsert.detach();self.trigger_up('request_history_undo_record',{$target:$(prev)});$toInsert.insertAfter(prev);}else if(next){$toInsert.detach();self.trigger_up('request_history_undo_record',{$target:$(next)});$toInsert.insertBefore(next);}else{var $parent=$toInsert.parent();$toInsert.detach();self.trigger_up('request_history_undo_record',{$target:$parent});$parent.prepend($toInsert);}
var $target=$toInsert;await self._scrollToSnippet($target);_.defer(async function(){self.trigger_up('snippet_dropped',{$target:$target});self._disableUndroppableSnippets();dragAndDropResolve();await self._callForEachChildSnippet($target,function(editor,$snippet){return editor.buildSnippet();});$target.trigger('content_changed');await self._updateInvisibleDOM();self.$el.find('.oe_snippet_thumbnail').removeClass('o_we_already_dragging');});}else{$toInsert.remove();dragAndDropResolve();self.$el.find('.oe_snippet_thumbnail').removeClass('o_we_already_dragging');}},},});this.draggableComponent=new SmoothScrollOnDrag(this,$snippets,$scrollingElement,smoothScrollOptions);},_registerDefaultTexts:function($in){if($in===undefined){$in=this.$snippets.find('.oe_snippet_body');}
$in.find('*').addBack().contents().filter(function(){return this.nodeType===3&&this.textContent.match(/\S/);}).parent().addClass('o_default_snippet_text');},_updateLeftPanelContent:function({content,tab}){clearTimeout(this._textToolsSwitchingTimeout);this._closeWidgets();tab=tab||this.tabs.BLOCKS;if(content){while(this.customizePanel.firstChild){this.customizePanel.removeChild(this.customizePanel.firstChild);}
$(this.customizePanel).append(content);}
this.$('.o_snippet_search_filter').toggleClass('d-none',tab!==this.tabs.BLOCKS);this.$('#o_scroll').toggleClass('d-none',tab!==this.tabs.BLOCKS);this.customizePanel.classList.toggle('d-none',tab===this.tabs.BLOCKS);this.textEditorPanelEl.classList.toggle('d-none',tab!==this.tabs.OPTIONS);this.$('.o_we_add_snippet_btn').toggleClass('active',tab===this.tabs.BLOCKS);this.$('.o_we_customize_snippet_btn').toggleClass('active',tab===this.tabs.OPTIONS).prop('disabled',tab!==this.tabs.OPTIONS);},async _scrollToSnippet($el){const modalEl=$el[0].closest('.modal');if(modalEl&&!dom.hasScrollableContent(modalEl)){return;}
return dom.scrollTo($el[0],{extraOffset:50});},_createLoadingElement(){const loaderContainer=document.createElement('div');const loader=document.createElement('i');const loaderContainerClassList=['o_we_ui_loading','d-flex','justify-content-center','align-items-center',];const loaderClassList=['fa','fa-circle-o-notch','fa-spin','fa-4x',];loaderContainer.classList.add(...loaderContainerClassList);loader.classList.add(...loaderClassList);loaderContainer.appendChild(loader);return loaderContainer;},async _execWithLoadingEffect(action,contentLoading=true,delay=500){const mutexExecResult=this._mutex.exec(action);if(!this.loadingTimers[contentLoading]){const addLoader=()=>{if(this.loadingElements[contentLoading]){return;}
this.loadingElements[contentLoading]=this._createLoadingElement();if(contentLoading){this.$snippetEditorArea.append(this.loadingElements[contentLoading]);}else{this.el.appendChild(this.loadingElements[contentLoading]);}};if(delay){this.loadingTimers[contentLoading]=setTimeout(addLoader,delay);}else{addLoader();}
this._mutex.getUnlockedDef().then(()=>{if(delay){clearTimeout(this.loadingTimers[contentLoading]);this.loadingTimers[contentLoading]=undefined;}
if(this.loadingElements[contentLoading]){this.loadingElements[contentLoading].remove();this.loadingElements[contentLoading]=null;}});}
return mutexExecResult;},_onActivateInsertionZones:function(ev){this._insertDropzoneCanBeSanitizedUnless=ev.data.canBeSanitizedUnless;this._activateInsertionZones(ev.data.$selectorSiblings,ev.data.$selectorChildren);delete this._insertDropzoneCanBeSanitizedUnless;},_onActivateSnippet:function(ev){this._activateSnippet(ev.data.$snippet,ev.data.previewMode,ev.data.ifInactiveOptions);},_onCallForEachChildSnippet:function(ev){const prom=this._callForEachChildSnippet(ev.data.$snippet,ev.data.callback);if(ev.data.onSuccess){prom.then(()=>ev.data.onSuccess());}},_onOverlaysCoverUpdate:function(ev){this.snippetEditors.forEach(editor=>{if(ev.data.overlayVisible){editor.toggleOverlayVisibility(true);}
editor.cover();});},_onCloneSnippet:async function(ev){ev.stopPropagation();const editor=await this._createSnippetEditor(ev.data.$snippet);await editor.clone();if(ev.data.onSuccess){ev.data.onSuccess();}},_onDeactivateSnippet:function(){this._activateSnippet(false);},_onDragAndDropStop:async function(ev){const modalEl=ev.data.$snippet[0].closest('.modal');const carouselItemEl=ev.data.$snippet[0].closest('.carousel-item');await this._destroyEditors(carouselItemEl?$(carouselItemEl):modalEl?$(modalEl):null);await this._activateSnippet(ev.data.$snippet);},_onFindSnippetTemplate(ev){this.$snippets.each(function(){const snippetBody=this.querySelector(`.oe_snippet_body[data-snippet=${ev.data.snippet.dataset.snippet}]`);if(snippetBody){ev.data.callback(snippetBody.parentElement);return false;}});},_onGoToParent:function(ev){ev.stopPropagation();this._activateSnippet(ev.data.$snippet.parent());},_onHideOverlay:function(){for(const editor of this.snippetEditors){editor.toggleOverlay(false);}},_onInstallBtnClick:function(ev){var self=this;var $snippet=$(ev.currentTarget).closest('[data-module-id]');var moduleID=$snippet.data('moduleId');var name=$snippet.attr('name');new Dialog(this,{title:_.str.sprintf(_t("Install %s"),name),size:'medium',$content:$('<div/>',{text:_.str.sprintf(_t("Do you want to install the %s App?"),name)}).append($('<a/>',{target:'_blank',href:'/web#id='+encodeURIComponent(moduleID)+'&view_type=form&model=ir.module.module&action=base.open_module_tree',text:_t("More info about this app."),class:'ml4',})),buttons:[{text:_t("Save and Install"),classes:'btn-primary',click:function(){this.$footer.find('.btn').toggleClass('o_hidden');this._rpc({model:'ir.module.module',method:'button_immediate_install',args:[[moduleID]],}).then(()=>{self.trigger_up('request_save',{reloadEditor:true,_toMutex:true,});}).guardedCatch(reason=>{reason.event.preventDefault();this.close();self.displayNotification({message:_.str.sprintf(_t("Could not install module <strong>%s</strong>"),name),type:'danger',sticky:true,});});},},{text:_t("Install in progress"),icon:'fa-spin fa-spinner fa-pulse mr8',classes:'btn-primary disabled o_hidden',},{text:_t("Cancel"),close:true,}],}).open();},_onInvisibleEntryClick:async function(ev){ev.preventDefault();const $snippet=$(this.invisibleDOMMap.get(ev.currentTarget));const isVisible=await this._execWithLoadingEffect(async()=>{const editor=await this._createSnippetEditor($snippet);return editor.toggleTargetVisibility();},true);$(ev.currentTarget).find('.fa').toggleClass('fa-eye',isVisible).toggleClass('fa-eye-slash',!isVisible);return this._activateSnippet(isVisible?$snippet:false);},_onBlocksTabClick:function(ev){this._activateSnippet(false).then(()=>{this._updateLeftPanelContent({content:[],tab:this.tabs.BLOCKS,});});},_onDeleteBtnClick:function(ev){const $snippet=$(ev.target).closest('.oe_snippet');const snippetId=parseInt(ev.currentTarget.dataset.snippetId);ev.stopPropagation();new Dialog(this,{size:'medium',title:_t('Confirmation'),$content:$('<div><p>'+_.str.sprintf(_t("Are you sure you want to delete the snippet: %s ?"),$snippet.attr('name'))+'</p></div>'),buttons:[{text:_t("Yes"),close:true,classes:'btn-primary',click:async()=>{await this._rpc({model:'ir.ui.view',method:'delete_snippet',kwargs:{'view_id':snippetId,'template_key':this.options.snippets,},});await this._loadSnippetsTemplates(true);},},{text:_t("No"),close:true,}],}).open();},_onMouseDown:function(){const $blockedArea=$('#wrapwrap');$blockedArea.addClass('o_we_no_pointer_events');const reenable=()=>$blockedArea.removeClass('o_we_no_pointer_events');const enableTimeoutID=setTimeout(()=>reenable(),5000);$(document).one('mouseup',()=>{clearTimeout(enableTimeoutID);reenable();});},_onGetSnippetVersions:function(ev){const snippet=this.el.querySelector(`.oe_snippet > [data-snippet="${ev.data.snippetName}"]`);ev.data.onSuccess(snippet&&{vcss:snippet.dataset.vcss,vjs:snippet.dataset.vjs,vxml:snippet.dataset.vxml,});},_onReloadSnippetTemplate:async function(ev){await this._activateSnippet(false);await this._loadSnippetsTemplates(true);},_onBlockPreviewOverlays:function(ev){this._blockPreviewOverlays=true;},_onUnblockPreviewOverlays:function(ev){this._blockPreviewOverlays=false;},_onRemoveSnippet:async function(ev){ev.stopPropagation();const editor=await this._createSnippetEditor(ev.data.$snippet);await editor.removeSnippet();if(ev.data.onSuccess){ev.data.onSuccess();}},_onSaveRequest:function(ev){const data=ev.data;if(ev.target===this&&!data._toMutex){return;}
delete data._toMutex;ev.stopPropagation();this._execWithLoadingEffect(()=>{if(data.reloadEditor){data.reload=false;const oldOnSuccess=data.onSuccess;data.onSuccess=async function(){if(oldOnSuccess){await oldOnSuccess.call(this,...arguments);}
window.location.href=window.location.origin+window.location.pathname+'?enable_editor=1';};}
this.trigger_up('request_save',data);},true);},_onSnippetClick(){const $els=this.getEditableArea().find('.oe_structure.oe_empty').addBack('.oe_structure.oe_empty');for(const el of $els){if(!el.children.length){$(el).odooBounce('o_we_snippet_area_animation');}}},_onSnippetEditionRequest:function(ev){this._execWithLoadingEffect(ev.data.exec,true);},_onSnippetEditorDestroyed(ev){ev.stopPropagation();const index=this.snippetEditors.indexOf(ev.target);this.snippetEditors.splice(index,1);},_onSnippetCloned:function(ev){this._updateInvisibleDOM();},_onSnippetRemoved:function(){this._disableUndroppableSnippets();this._updateInvisibleDOM();},_onSnippetOptionUpdate(ev){ev.stopPropagation();(async()=>{const editors=this._enabledEditorHierarchy;await Promise.all(editors.map(editor=>editor.updateOptionsUI()));await Promise.all(editors.map(editor=>editor.updateOptionsUIVisibility()));ev.data.onSuccess();})();},_onSnippetOptionVisibilityUpdate:async function(ev){if(this.willDestroyEditors){return;}
if(!ev.data.show){await this._activateSnippet(false);}
await this._updateInvisibleDOM();},_onSnippetThumbnailURLRequest(ev){const $snippet=this.$snippets.has(`[data-snippet="${ev.data.key}"]`);ev.data.onSuccess($snippet.length?$snippet[0].dataset.oeThumbnail:'');},_onSummernoteToolsUpdate(ev){if(!this._textToolsSwitchingEnabled){return;}
const range=$.summernote.core.range.create();if(!range){return;}
if(range.sc===range.ec&&range.sc.nodeType===Node.ELEMENT_NODE&&range.sc.classList.contains('oe_structure')&&range.sc.children.length===0){return;}
this.textEditorPanelEl.classList.add('d-block');const hasVisibleButtons=!!$(this.textEditorPanelEl).find('.btn:visible').length;this.textEditorPanelEl.classList.remove('d-block');if(!hasVisibleButtons){return;}
clearTimeout(this._textToolsSwitchingTimeout);if(!this.$('#o_scroll').hasClass('d-none')){this._textToolsSwitchingTimeout=setTimeout(()=>{this._updateLeftPanelContent({tab:this.tabs.OPTIONS});},250);}},_onUpdateCustomizeElements:function(ev){this._updateLeftPanelContent({content:ev.data.customize$Elements,tab:ev.data.customize$Elements.length?this.tabs.OPTIONS:this.tabs.BLOCKS,});},_onUserValueWidgetOpening:function(){this._closeWidgets();this.el.classList.add('o_we_backdrop');},_onUserValueWidgetClosing:function(){this.el.classList.remove('o_we_backdrop');},_onSnippetSearchInput:function(){this._filterSnippets();},_onSnippetSearchResetClick:function(){this._filterSnippets('');},});return{Class:SnippetsMenu,Editor:SnippetEditor,globalSelector:globalSelector,};});;

/* /web_editor/static/src/js/editor/snippets.options.js defined in bundle 'web_editor.assets_wysiwyg' */
odoo.define('web_editor.snippets.options',function(require){'use strict';var core=require('web.core');const{ColorpickerWidget}=require('web.Colorpicker');const Dialog=require('web.Dialog');const{scrollTo}=require('web.dom');const rpc=require('web.rpc');const time=require('web.time');const utils=require('web.utils');var Widget=require('web.Widget');var ColorPaletteWidget=require('web_editor.ColorPalette').ColorPaletteWidget;const weUtils=require('web_editor.utils');const{normalizeColor,getBgImageURL,isBackgroundImageAttribute,}=weUtils;var weWidgets=require('wysiwyg.widgets');const{loadImage,loadImageInfo,applyModifications,removeOnImageChangeAttrs,}=require('web_editor.image_processing');var qweb=core.qweb;var _t=core._t;function _addTitleAndAllowedAttributes(el,title,options){let tooltipEl=el;if(title){const titleEl=_buildTitleElement(title);tooltipEl=titleEl;el.appendChild(titleEl);}
if(options&&options.classes){el.classList.add(...options.classes);}
if(options&&options.tooltip){tooltipEl.title=options.tooltip;}
if(options&&options.placeholder){el.setAttribute('placeholder',options.placeholder);}
if(options&&options.dataAttributes){for(const key in options.dataAttributes){el.dataset[key]=options.dataAttributes[key];}}
return el;}
function _buildElement(tagName,title,options){const el=document.createElement(tagName);return _addTitleAndAllowedAttributes(el,title,options);}
function _buildTitleElement(title){const titleEl=document.createElement('we-title');titleEl.textContent=title.replace(/⌙/g,'└');return titleEl;}
const _buildImgElementCache={};async function _buildImgElement(src){if(!(src in _buildImgElementCache)){_buildImgElementCache[src]=(async()=>{if(src.split('.').pop()==='svg'){const response=await window.fetch(src);const text=await response.text();const parser=new window.DOMParser();const xmlDoc=parser.parseFromString(text,'text/xml');return xmlDoc.getElementsByTagName('svg')[0];}else{const imgEl=document.createElement('img');imgEl.src=src;return imgEl;}})();}
const node=await _buildImgElementCache[src];return node.cloneNode(true);}
function _buildRowElement(title,options){const groupEl=_buildElement('we-row',title,options);const rowEl=document.createElement('div');groupEl.appendChild(rowEl);if(options&&options.childNodes){options.childNodes.forEach(node=>rowEl.appendChild(node));}
return groupEl;}
function _buildCollapseElement(title,options){const groupEl=_buildElement('we-collapse',title,options);const titleEl=groupEl.querySelector('we-title');const children=options&&options.childNodes||[];if(titleEl){titleEl.remove();children.unshift(titleEl);}
let i=0;for(i=0;i<children.length;i++){groupEl.appendChild(children[i]);if(children[i].nodeType===Node.ELEMENT_NODE){break;}}
const togglerEl=document.createElement('we-toggler');togglerEl.classList.add('o_we_collapse_toggler');if(_t.database.parameters.direction==='rtl'){togglerEl.classList.add('o_we_collapse_toggler_rtl');}
groupEl.appendChild(togglerEl);const containerEl=document.createElement('div');children.slice(i+1).forEach(node=>containerEl.appendChild(node));groupEl.appendChild(containerEl);return groupEl;}
function createPropertyProxy(obj,propertyName,value){return new Proxy(obj,{get:function(obj,prop){if(prop===propertyName){return value;}
return obj[prop];},set:function(obj,prop,val){if(prop===propertyName){return(value=val);}
return Reflect.set(...arguments);},});}
const NULL_ID='__NULL__';const UserValueWidget=Widget.extend({className:'o_we_user_value_widget',custom_events:{'user_value_update':'_onUserValueNotification',},init:function(parent,title,options,$target){this._super(...arguments);this.title=title;this.options=options;this._userValueWidgets=[];this._value='';this.$target=$target;},async willStart(){await this._super(...arguments);if(this.options.dataAttributes.img){this.imgEl=await _buildImgElement(this.options.dataAttributes.img);}},_makeDescriptive:function(){const $el=this._super(...arguments);const el=$el[0];_addTitleAndAllowedAttributes(el,this.title,this.options);this.containerEl=document.createElement('div');if(this.imgEl){this.containerEl.appendChild(this.imgEl);}
el.appendChild(this.containerEl);return $el;},async start(){await this._super(...arguments);if(this.el.classList.contains('o_we_img_animate')){const buildImgExtensionSwitcher=(from,to)=>{const regex=new RegExp(`${from}$`,'i');return ev=>{const img=ev.currentTarget.getElementsByTagName("img")[0];img.src=img.src.replace(regex,to);};};this.$el.on('mouseenter.img_animate',buildImgExtensionSwitcher('png','gif'));this.$el.on('mouseleave.img_animate',buildImgExtensionSwitcher('gif','png'));}},destroy(){if(this.$el){this.$el.off('.img_animate');}
this._super(...arguments);},close:function(){if(!this.el){return;}
if(!this.el.classList.contains('o_we_widget_opened')){return;}
this.trigger_up('user_value_widget_closing');this.el.classList.remove('o_we_widget_opened');this._userValueWidgets.forEach(widget=>widget.close());},enable(){this.$el.click();},findWidget:function(name){for(const widget of this._userValueWidgets){if(widget.getName()===name){return widget;}
const depWidget=widget.findWidget(name);if(depWidget){return depWidget;}}
return null;},getActiveValue:function(methodName){return this._value;},getDefaultValue:function(methodName){const possibleValues=this._methodsParams.optionsPossibleValues[methodName];return possibleValues&&possibleValues[0]||'';},getDependencies:function(){return this._dependencies;},getMethodsNames:function(){return this._methodsNames;},getMethodsParams:function(methodName){const params=_.extend({},this._methodsParams);if(methodName){params.possibleValues=params.optionsPossibleValues[methodName]||[];params.activeValue=this.getActiveValue(methodName);params.defaultValue=this.getDefaultValue(methodName);}
return params;},getName:function(){return this._methodsParams.name||'';},getValue:function(methodName){const isActive=this.isActive();if(!methodName||!this._methodsNames.includes(methodName)){return isActive?'true':'';}
if(isActive){return this.getActiveValue(methodName);}
return this.getDefaultValue(methodName);},isActive:function(){return this._value&&this._value!==NULL_ID;},isContainer:function(){return false;},isPreviewed:function(){const focusEl=document.activeElement;if(focusEl&&focusEl.tagName==='INPUT'&&(this.el===focusEl||this.el.contains(focusEl))){return true;}
return this.el.classList.contains('o_we_preview');},loadMethodsData:function(validMethodNames,extraParams){this._methodsNames=[];this._methodsParams=_.extend({},extraParams);this._methodsParams.optionsPossibleValues={};this._dependencies=[];this._triggerWidgetsNames=[];this._triggerWidgetsValues=[];for(const key in this.el.dataset){const dataValue=this.el.dataset[key].trim();if(key==='dependencies'){this._dependencies.push(...dataValue.split(/\s*,\s*/g));}else if(key==='trigger'){this._triggerWidgetsNames.push(...dataValue.split(/\s*,\s*/g));}else if(key==='triggerValue'){this._triggerWidgetsValues.push(...dataValue.split(/\s*,\s*/g));}else if(validMethodNames.includes(key)){this._methodsNames.push(key);this._methodsParams.optionsPossibleValues[key]=dataValue.split(/\s*\|\s*/g);}else{this._methodsParams[key]=dataValue;}}
this._userValueWidgets.forEach(widget=>{const inheritedParams=_.extend({},this._methodsParams);inheritedParams.optionsPossibleValues=null;widget.loadMethodsData(validMethodNames,inheritedParams);const subMethodsNames=widget.getMethodsNames();const subMethodsParams=widget.getMethodsParams();for(const methodName of subMethodsNames){if(!this._methodsNames.includes(methodName)){this._methodsNames.push(methodName);this._methodsParams.optionsPossibleValues[methodName]=[];}
for(const subPossibleValue of subMethodsParams.optionsPossibleValues[methodName]){this._methodsParams.optionsPossibleValues[methodName].push(subPossibleValue);}}});for(const methodName of this._methodsNames){const arr=this._methodsParams.optionsPossibleValues[methodName];const uniqArr=arr.filter((v,i,arr)=>i===arr.indexOf(v));this._methodsParams.optionsPossibleValues[methodName]=uniqArr;}},notifyValueChange:function(previewMode,isSimulatedEvent){if(!this._methodsNames.length){return;}
const isPreviewed=this.isPreviewed();if(!previewMode&&!isPreviewed){this.notifyValueChange(true);}
const data={previewMode:previewMode||false,isSimulatedEvent:!!isSimulatedEvent,};if(previewMode===true||previewMode===false){data.prepare=()=>this.el.classList.add('o_we_preview');}else if(previewMode==='reset'){data.prepare=()=>this.el.classList.remove('o_we_preview');}
this.trigger_up('user_value_update',data);},open(){this.trigger_up('user_value_widget_opening');this.el.classList.add('o_we_widget_opened');},registerSubWidget:function(widget){this._userValueWidgets.push(widget);},async setValue(value,methodName){this._value=value;this.el.classList.remove('o_we_preview');},toggleVisibility:function(show){this.el.classList.toggle('d-none',!show);},_handleNotifierEvent:function(ev){if(!ev){return true;}
if(ev._seen){return false;}
ev._seen=true;if(ev.preventDefault){ev.preventDefault();}
return true;},_onUserValueChange:function(ev){if(this._handleNotifierEvent(ev)){this.notifyValueChange(false);}},_onUserValueNotification:function(ev){ev.data.widget=this;if(!ev.data.triggerWidgetsNames){ev.data.triggerWidgetsNames=[];}
ev.data.triggerWidgetsNames.push(...this._triggerWidgetsNames);if(!ev.data.triggerWidgetsValues){ev.data.triggerWidgetsValues=[];}
ev.data.triggerWidgetsValues.push(...this._triggerWidgetsValues);},_onUserValuePreview:function(ev){if(this._handleNotifierEvent(ev)){this.notifyValueChange(true);}},_onUserValueReset:function(ev){if(this._handleNotifierEvent(ev)){this.notifyValueChange('reset');}},});const ButtonUserValueWidget=UserValueWidget.extend({tagName:'we-button',events:{'click':'_onButtonClick','click [role="button"]':'_onInnerButtonClick','mouseenter':'_onUserValuePreview','mouseleave':'_onUserValueReset',},start:function(parent,title,options){if(this.options&&this.options.childNodes){this.options.childNodes.forEach(node=>this.containerEl.appendChild(node));}
return this._super(...arguments);},getActiveValue:function(methodName){const possibleValues=this._methodsParams.optionsPossibleValues[methodName];return possibleValues&&possibleValues[possibleValues.length-1]||'';},isActive:function(){return(this.isPreviewed()!==this.el.classList.contains('active'));},loadMethodsData:function(validMethodNames){this._super.apply(this,arguments);for(const methodName of this._methodsNames){const possibleValues=this._methodsParams.optionsPossibleValues[methodName];if(possibleValues.length<=1){possibleValues.unshift('');}}},async setValue(value,methodName){await this._super(...arguments);let active=!!value;if(methodName){if(!this._methodsNames.includes(methodName)){return;}
active=(this.getActiveValue(methodName)===value);}
this.el.classList.toggle('active',active);},_onButtonClick:function(ev){if(!ev._innerButtonClicked){this._onUserValueChange(ev);}},_onInnerButtonClick:function(ev){ev._innerButtonClicked=true;},});const CheckboxUserValueWidget=ButtonUserValueWidget.extend({className:(ButtonUserValueWidget.prototype.className||'')+' o_we_checkbox_wrapper',start:function(){const checkboxEl=document.createElement('we-checkbox');this.containerEl.appendChild(checkboxEl);return this._super(...arguments);},enable(){this.$('we-checkbox').click();},_onButtonClick(ev){if(!ev.target.closest('we-title, we-checkbox')){return;}
return this._super(...arguments);},});const BaseSelectionUserValueWidget=UserValueWidget.extend({async start(){await this._super(...arguments);this.menuEl=document.createElement('we-selection-items');if(this.options&&this.options.childNodes){this.options.childNodes.forEach(node=>this.menuEl.appendChild(node));}
this.containerEl.appendChild(this.menuEl);},getMethodsParams(methodName){const params=this._super(...arguments);const activeWidget=this._getActiveSubWidget();if(!activeWidget){return params;}
return Object.assign(activeWidget.getMethodsParams(...arguments),params);},getValue(methodName){const activeWidget=this._getActiveSubWidget();if(activeWidget){return activeWidget.getActiveValue(methodName);}
return this._super(...arguments);},isContainer(){return true;},async setValue(value,methodName){const _super=this._super.bind(this);for(const widget of this._userValueWidgets){await widget.setValue(NULL_ID,methodName);}
for(const widget of[...this._userValueWidgets].reverse()){await widget.setValue(value,methodName);if(widget.isActive()){return;}}
await _super(...arguments);},_getActiveSubWidget(){const previewedWidget=this._userValueWidgets.find(widget=>widget.isPreviewed());if(previewedWidget){return previewedWidget;}
return this._userValueWidgets.find(widget=>widget.isActive());},});const SelectUserValueWidget=BaseSelectionUserValueWidget.extend({tagName:'we-select',events:{'click':'_onClick',},async start(){await this._super(...arguments);if(!this.menuEl.children.length){while(this.menuEl.firstChild&&!this.menuEl.firstChild.data.trim().length){this.menuEl.firstChild.remove();}}
if(this.options&&this.options.valueEl){this.containerEl.insertBefore(this.options.valueEl,this.menuEl);}
this.menuTogglerEl=document.createElement('we-toggler');this.icon=this.el.dataset.icon||false;if(this.icon){this.el.classList.add('o_we_icon_select');const iconEl=document.createElement('i');iconEl.classList.add('fa','fa-fw',this.icon);this.menuTogglerEl.appendChild(iconEl);}
this.containerEl.insertBefore(this.menuTogglerEl,this.menuEl);const dropdownCaretEl=document.createElement('span');dropdownCaretEl.classList.add('o_we_dropdown_caret');this.containerEl.appendChild(dropdownCaretEl);},close:function(){this._super(...arguments);if(this.menuTogglerEl){this.menuTogglerEl.classList.remove('active');}},isPreviewed:function(){return this._super(...arguments)||this.menuTogglerEl.classList.contains('active');},open(){this._super(...arguments);this.menuTogglerEl.classList.add('active');},async setValue(){await this._super(...arguments);if(this.icon){return;}
if(this.menuTogglerItemEl){this.menuTogglerItemEl.remove();this.menuTogglerItemEl=null;}
let textContent='';const activeWidget=this._userValueWidgets.find(widget=>!widget.isPreviewed()&&widget.isActive());if(activeWidget){const svgTag=activeWidget.el.querySelector('svg');const value=(activeWidget.el.dataset.selectLabel||(!svgTag&&activeWidget.el.textContent.trim()));const imgSrc=activeWidget.el.dataset.img;if(value){textContent=value;}else if(imgSrc){this.menuTogglerItemEl=document.createElement('img');this.menuTogglerItemEl.src=imgSrc;}else{const fakeImgEl=activeWidget.el.querySelector('.o_we_fake_img_item');if(fakeImgEl){this.menuTogglerItemEl=fakeImgEl.cloneNode(true);}}}else{textContent="/";}
this.menuTogglerEl.textContent=textContent;if(this.menuTogglerItemEl){this.menuTogglerEl.appendChild(this.menuTogglerItemEl);}},_shouldIgnoreClick(ev){return!!ev.target.closest('[role="button"]');},_onClick:function(ev){if(this._shouldIgnoreClick(ev)){return;}
if(!this.menuTogglerEl.classList.contains('active')){this.open();}else{this.close();}
const activeButton=this._userValueWidgets.find(widget=>widget.isActive());if(activeButton){this.menuEl.scrollTop=activeButton.el.offsetTop-(this.menuEl.offsetHeight/2);}},});const ButtonGroupUserValueWidget=BaseSelectionUserValueWidget.extend({tagName:'we-button-group',});const UnitUserValueWidget=UserValueWidget.extend({start:async function(){const unit=this.el.dataset.unit||'';this.el.dataset.unit=unit;if(this.el.dataset.saveUnit===undefined){this.el.dataset.saveUnit=unit;}
return this._super(...arguments);},getActiveValue:function(methodName){const activeValue=this._super(...arguments);const params=this._methodsParams;if(!params.unit){return activeValue;}
const defaultValue=this.getDefaultValue(methodName,false);return activeValue.split(/\s+/g).map(v=>{const numValue=parseFloat(v);if(isNaN(numValue)){return defaultValue;}else{const value=weUtils.convertNumericToUnit(numValue,params.unit,params.saveUnit,params.cssProperty,this.$target);return`${this._floatToStr(value)}${params.saveUnit}`;}}).join(' ');},getDefaultValue:function(methodName,useInputUnit){const defaultValue=this._super(...arguments);const params=this._methodsParams;if(!params.unit){return defaultValue;}
const unit=useInputUnit?params.unit:params.saveUnit;const numValue=weUtils.convertValueToUnit(defaultValue||'0',unit,params.cssProperty,this.$target);if(isNaN(numValue)){return defaultValue;}
return`${this._floatToStr(numValue)}${unit}`;},isActive:function(){const isSuperActive=this._super(...arguments);const params=this._methodsParams;if(!params.unit){return isSuperActive;}
return isSuperActive&&this._floatToStr(parseFloat(this._value))!=='0';},async setValue(value,methodName){const params=this._methodsParams;if(params.unit){value=value.split(' ').map(v=>{const numValue=weUtils.convertValueToUnit(v,params.unit,params.cssProperty,this.$target);if(isNaN(numValue)){return'';}
return this._floatToStr(numValue);}).join(' ');}
return this._super(value,methodName);},_floatToStr:function(value){return`${parseFloat(value.toFixed(5))}`;},});const InputUserValueWidget=UnitUserValueWidget.extend({tagName:'we-input',events:{'input input':'_onInputInput','blur input':'_onInputBlur','keydown input':'_onInputKeydown',},start:async function(){await this._super(...arguments);const unit=this.el.dataset.unit;this.inputEl=document.createElement('input');this.inputEl.setAttribute('type','text');this.inputEl.setAttribute('autocomplete','chrome-off');this.inputEl.setAttribute('placeholder',this.el.getAttribute('placeholder')||'');this.inputEl.classList.toggle('text-left',!unit);this.inputEl.classList.toggle('text-right',!!unit);this.containerEl.appendChild(this.inputEl);var unitEl=document.createElement('span');unitEl.textContent=unit;this.containerEl.appendChild(unitEl);if(unit.length>3){this.el.classList.add('o_we_large_input');}},async setValue(){await this._super(...arguments);this.inputEl.value=this._value;},_onInputInput:function(ev){this._value=this.inputEl.value;this._onUserValuePreview(ev);},_onInputBlur:function(ev){setTimeout(()=>{if(ev.currentTarget===document.activeElement){return;}
this._onUserValueChange(ev);});},_onInputKeydown:function(ev){switch(ev.which){case $.ui.keyCode.ENTER:{this._onUserValueChange(ev);break;}
case $.ui.keyCode.UP:case $.ui.keyCode.DOWN:{const input=ev.currentTarget;const params=this._methodsParams;if(!params.unit&&!params.step){break;}
let value=parseFloat(input.value||input.placeholder);if(isNaN(value)){value=0.0;}
let step=parseFloat(params.step);if(isNaN(step)){step=1.0;}
value+=(ev.which===$.ui.keyCode.UP?step:-step);input.value=this._floatToStr(value);$(input).trigger('input');break;}}},});const MultiUserValueWidget=UserValueWidget.extend({tagName:'we-multi',start:function(){if(this.options&&this.options.childNodes){this.options.childNodes.forEach(node=>this.containerEl.appendChild(node));}
return this._super(...arguments);},getValue:function(methodName){const value=this._userValueWidgets.map(widget=>{return widget.getValue(methodName);}).join(' ').trim();return value||this._super(...arguments);},isContainer:function(){return true;},async setValue(value,methodName){let values=value.split(/\s*\|\s*/g);if(values.length===1){values=value.split(/\s+/g);}
for(let i=0;i<this._userValueWidgets.length-1;i++){await this._userValueWidgets[i].setValue(values.shift()||'',methodName);}
await this._userValueWidgets[this._userValueWidgets.length-1].setValue(values.join(' '),methodName);},});const ColorpickerUserValueWidget=SelectUserValueWidget.extend({className:(SelectUserValueWidget.prototype.className||'')+' o_we_so_color_palette',custom_events:_.extend({},SelectUserValueWidget.prototype.custom_events,{'custom_color_picked':'_onCustomColorPicked','color_picked':'_onColorPicked','color_hover':'_onColorHovered','color_leave':'_onColorLeft','enter_key_color_colorpicker':'_onEnterKey'}),start:async function(){const _super=this._super.bind(this);const args=arguments;if(this.options.dataAttributes.lazyPalette==='true'){this.colorPalette=new Widget(this);this.colorPalette.getColorNames=()=>[];await this.colorPalette.appendTo(document.createDocumentFragment());}else{await this._renderColorPalette();}
this.colorPreviewEl=document.createElement('span');this.colorPreviewEl.classList.add('o_we_color_preview');this.options.childNodes=[this.colorPalette.el];this.options.valueEl=this.colorPreviewEl;return _super(...args);},open:function(){if(this.colorPalette.setSelectedColor){this.colorPalette.setSelectedColor(this._value);}else{this._colorPaletteRenderPromise=this._renderColorPalette();}
this._super(...arguments);},close:function(){this._super(...arguments);if(this._customColorValue&&this._customColorValue!==this._value){this._value=this._customColorValue;this._customColorValue=false;this._onUserValueChange();}},getMethodsParams:function(){return _.extend(this._super(...arguments),{colorNames:this.colorPalette.getColorNames(),});},getValue:function(methodName){if(typeof this._previewColor==='string'){return this._previewColor;}
if(typeof this._customColorValue==='string'){return this._customColorValue;}
let value=this._super(...arguments);if(value){const useCssColor=this.options.dataAttributes.hasOwnProperty('useCssColor');const cssCompatible=this.options.dataAttributes.hasOwnProperty('cssCompatible');if((useCssColor||cssCompatible)&&!ColorpickerWidget.isCSSColor(value)){if(useCssColor){value=weUtils.getCSSVariableValue(value);}else{value=`var(--${value})`;}}}
return value;},isContainer:function(){return false;},isActive:function(){return!weUtils.areCssValuesEqual(this._value,'rgba(0, 0, 0, 0)');},async setValue(color){await this._super(...arguments);await this._colorPaletteRenderPromise;const classes=weUtils.computeColorClasses(this.colorPalette.getColorNames());this.colorPreviewEl.classList.remove(...classes);this.colorPreviewEl.style.removeProperty('background-color');if(this._value){if(ColorpickerWidget.isCSSColor(this._value)){this.colorPreviewEl.style.backgroundColor=this._value;}else if(weUtils.isColorCombinationName(this._value)){this.colorPreviewEl.classList.add('o_cc',`o_cc${this._value}`);}else{const className=`bg-${this._value}`;if(classes.includes(className)){this.colorPreviewEl.classList.add(className);}}}},_renderColorPalette:function(){const options={selectedColor:this._value,};if(this.options.dataAttributes.excluded){options.excluded=this.options.dataAttributes.excluded.replace(/ /g,'').split(',');}
if(this.options.dataAttributes.withCombinations){options.withCombinations=!!this.options.dataAttributes.withCombinations;}
const oldColorPalette=this.colorPalette;this.colorPalette=new ColorPaletteWidget(this,options);if(oldColorPalette){return this.colorPalette.insertAfter(oldColorPalette.el).then(()=>{oldColorPalette.destroy();});}
return this.colorPalette.appendTo(document.createDocumentFragment());},_shouldIgnoreClick(ev){return ev.originalEvent.__isColorpickerClick||this._super(...arguments);},_onCustomColorPicked:function(ev){this._customColorValue=ev.data.color;},_onColorPicked:function(ev){this._previewColor=false;this._customColorValue=false;this._value=ev.data.color;this._onUserValueChange(ev);},_onColorHovered:function(ev){this._previewColor=ev.data.color;this._onUserValuePreview(ev);},_onColorLeft:function(ev){this._previewColor=false;this._onUserValueReset(ev);},_onEnterKey:function(){this.close();},});const MediapickerUserValueWidget=UserValueWidget.extend({tagName:'we-button',events:{'click':'_onEditMedia',},async start(){await this._super(...arguments);const iconEl=document.createElement('i');if(this.options.dataAttributes.buttonStyle){iconEl.classList.add('fa','fa-fw','fa-camera');}else{iconEl.classList.add('fa','fa-fw','fa-refresh','mr-1');this.el.classList.add('o_we_no_toggle');this.containerEl.textContent=_t("Replace media");}
$(this.containerEl).prepend(iconEl);},_openDialog(el,{images=false,videos=false}){el.src=this._value;const $editable=this.$target.closest('.o_editable');const mediaDialog=new weWidgets.MediaDialog(this,{noImages:!images,noVideos:!videos,noIcons:true,noDocuments:true,isForBgVideo:true,'res_model':$editable.data('oe-model'),'res_id':$editable.data('oe-id'),},el).open();return mediaDialog;},async setValue(){await this._super(...arguments);this.el.classList.toggle('active',this.isActive());},_onEditMedia:function(ev){},});const ImagepickerUserValueWidget=MediapickerUserValueWidget.extend({_onEditMedia(ev){const dummyEl=document.createElement('img');const dialog=this._openDialog(dummyEl,{images:true});dialog.on('save',this,data=>{this._value=dummyEl.getAttribute('src');this._onUserValueChange();});},});const VideopickerUserValueWidget=MediapickerUserValueWidget.extend({_onEditMedia(ev){const dummyEl=document.createElement('iframe');const dialog=this._openDialog(dummyEl,{videos:true});dialog.on('save',this,data=>{this._value=data.bgVideoSrc;this._onUserValueChange();});},});const DatetimePickerUserValueWidget=InputUserValueWidget.extend({events:{'blur input':'_onInputBlur','change.datetimepicker':'_onDateTimePickerChange','error.datetimepicker':'_onDateTimePickerError',},init:function(){this._super(...arguments);this._value=moment().unix().toString();this.__libInput=0;},start:async function(){await this._super(...arguments);const datetimePickerId=_.uniqueId('datetimepicker');this.el.classList.add('o_we_large_input');this.inputEl.classList.add('datetimepicker-input','mx-0','text-left');this.inputEl.setAttribute('id',datetimePickerId);this.inputEl.setAttribute('data-target','#'+datetimePickerId);const datepickersOptions={minDate:moment({y:1000}),maxDate:moment().add(200,'y'),calendarWeeks:true,defaultDate:moment().format(),icons:{close:'fa fa-check primary',},locale:moment.locale(),format:time.getLangDatetimeFormat(),sideBySide:true,buttons:{showClose:true,showToday:true,},widgetParent:'body',allowInputToggle:true,};this.__libInput++;const $input=$(this.inputEl);$input.datetimepicker(datepickersOptions);this.__libInput--;const libObject=$input.data('datetimepicker');const oldFunc=libObject._getTemplate;libObject._getTemplate=function(){const $template=oldFunc.call(this,...arguments);$template.addClass('o_we_no_overlay o_we_datetimepicker');return $template;};},isPreviewed:function(){return this._super(...arguments)||!!$(this.inputEl).data('datetimepicker').widget;},async setValue(){await this._super(...arguments);let momentObj=moment.unix(this._value);if(!momentObj.isValid()){momentObj=moment();}
this.__libInput++;$(this.inputEl).datetimepicker('date',momentObj);this.__libInput--;},_onDateTimePickerChange:function(ev){if(this.__libInput>0){return;}
if(!ev.date||!ev.date.isValid()){return;}
this._value=ev.date.unix().toString();this._onUserValuePreview(ev);},_onDateTimePickerError:function(ev){ev.stopPropagation();},});const RangeUserValueWidget=UnitUserValueWidget.extend({tagName:'we-range',events:{'change input':'_onInputChange',},async start(){await this._super(...arguments);this.input=document.createElement('input');this.input.type="range";let min=this.el.dataset.min&&parseFloat(this.el.dataset.min)||0;let max=this.el.dataset.max&&parseFloat(this.el.dataset.max)||100;const step=this.el.dataset.step&&parseFloat(this.el.dataset.step)||1;if(min>max){[min,max]=[max,min];this.input.classList.add('o_we_inverted_range');}
this.input.setAttribute('min',min);this.input.setAttribute('max',max);this.input.setAttribute('step',step);this.containerEl.appendChild(this.input);this._onInputChange=_.debounce(this._onInputChange,100);},async setValue(value,methodName){await this._super(...arguments);this.input.value=this._value;},_onInputChange(ev){this._value=ev.target.value;this._onUserValueChange(ev);},});const SelectPagerUserValueWidget=SelectUserValueWidget.extend({className:(SelectUserValueWidget.prototype.className||'')+' o_we_select_pager',events:Object.assign({},SelectUserValueWidget.prototype.events,{'click .o_we_pager_next, .o_we_pager_prev':'_onPageChange',}),async start(){const _super=this._super.bind(this);this.pages=this.options.childNodes.filter(node=>node.matches&&node.matches('we-select-page'));this.numPages=this.pages.length;const prev=document.createElement('i');prev.classList.add('o_we_pager_prev','fa','fa-chevron-left');this.pageNum=document.createElement('span');this.currentPage=0;const next=document.createElement('i');next.classList.add('o_we_pager_next','fa','fa-chevron-right');const pagerControls=document.createElement('div');pagerControls.classList.add('o_we_pager_controls');pagerControls.appendChild(prev);pagerControls.appendChild(this.pageNum);pagerControls.appendChild(next);this.pageName=document.createElement('b');const pagerHeader=document.createElement('div');pagerHeader.classList.add('o_we_pager_header');pagerHeader.appendChild(this.pageName);pagerHeader.appendChild(pagerControls);await _super(...arguments);this.menuEl.classList.add('o_we_has_pager');$(this.menuEl).prepend(pagerHeader);this._updatePage();},_shouldIgnoreClick(ev){return!!ev.target.closest('.o_we_pager_header')||this._super(...arguments);},_updatePage(){this.pages.forEach((page,i)=>page.classList.toggle('active',i===this.currentPage));this.pageNum.textContent=`${this.currentPage + 1}/${this.numPages}`;const activePage=this.pages.find((page,i)=>i===this.currentPage);this.pageName.textContent=activePage.getAttribute('string');},_onPageChange(ev){ev.preventDefault();ev.stopPropagation();const delta=ev.target.matches('.o_we_pager_next')?1:-1;this.currentPage=(this.currentPage+this.numPages+delta)%this.numPages;this._updatePage();},_onClick(ev){const activeButton=this._getActiveSubWidget();if(activeButton){const currentPage=this.pages.indexOf(activeButton.el.closest('we-select-page'));if(currentPage!==-1){this.currentPage=currentPage;this._updatePage();}}
return this._super(...arguments);},});const userValueWidgetsRegistry={'we-button':ButtonUserValueWidget,'we-checkbox':CheckboxUserValueWidget,'we-select':SelectUserValueWidget,'we-button-group':ButtonGroupUserValueWidget,'we-input':InputUserValueWidget,'we-multi':MultiUserValueWidget,'we-colorpicker':ColorpickerUserValueWidget,'we-datetimepicker':DatetimePickerUserValueWidget,'we-imagepicker':ImagepickerUserValueWidget,'we-videopicker':VideopickerUserValueWidget,'we-range':RangeUserValueWidget,'we-select-pager':SelectPagerUserValueWidget,};const SnippetOptionWidget=Widget.extend({tagName:'we-customizeblock-option',events:{'click .o_we_collapse_toggler':'_onCollapseTogglerClick',},custom_events:{'user_value_update':'_onUserValueUpdate','user_value_widget_critical':'_onUserValueWidgetCritical',},isTopOption:false,forceNoDeleteButton:false,init:function(parent,$uiElements,$target,$overlay,data,options){this._super.apply(this,arguments);this.$originalUIElements=$uiElements;this.$target=$target;this.$overlay=$overlay;this.data=data;this.options=options;this.className='snippet-option-'+this.data.optionName;this.ownerDocument=this.$target[0].ownerDocument;this._userValueWidgets=[];this._actionQueues=new Map();},willStart:async function(){await this._super(...arguments);return this._renderOriginalXML().then(uiFragment=>{this.uiFragment=uiFragment;});},renderElement:function(){this._super(...arguments);this.el.appendChild(this.uiFragment);this.uiFragment=null;},onFocus:function(){},onBuilt:function(){},onBlur:function(){},onClone:function(options){},onMove:function(){},onRemove:function(){},onTargetShow:async function(){},onTargetHide:async function(){},cleanForSave:async function(){},selectClass:function(previewMode,widgetValue,params){for(const classNames of params.possibleValues){if(classNames){this.$target[0].classList.remove(...classNames.trim().split(/\s+/g));}}
if(widgetValue){this.$target[0].classList.add(...widgetValue.trim().split(/\s+/g));}},selectDataAttribute:function(previewMode,widgetValue,params){const value=this._selectAttributeHelper(widgetValue,params);this.$target[0].dataset[params.attributeName]=value;},selectAttribute:function(previewMode,widgetValue,params){const value=this._selectAttributeHelper(widgetValue,params);this.$target[0].setAttribute(params.attributeName,value);},selectStyle:function(previewMode,widgetValue,params){this.$target[0].classList.add('o_we_force_no_transition');const _restoreTransitions=()=>this.$target[0].classList.remove('o_we_force_no_transition');if(params.cssProperty==='background-color'){this.$target.trigger('background-color-event',previewMode);}
const cssProps=weUtils.CSS_SHORTHANDS[params.cssProperty]||[params.cssProperty];for(const cssProp of cssProps){this.$target[0].style.setProperty(cssProp,'');}
if(params.extraClass){this.$target.removeClass(params.extraClass);}
if(params.colorNames&&params.colorPrefix){const classes=weUtils.computeColorClasses(params.colorNames,params.colorPrefix);this.$target[0].classList.remove(...classes);if(weUtils.isColorCombinationName(widgetValue)){this.$target[0].classList.add('o_cc',`o_cc${widgetValue}`,params.extraClass);_restoreTransitions();return;}
if(params.colorNames.includes(widgetValue)){const originalCSSValue=window.getComputedStyle(this.$target[0])[cssProps[0]];const className=params.colorPrefix+widgetValue;this.$target[0].classList.add(className);if(originalCSSValue!==window.getComputedStyle(this.$target[0])[cssProps[0]]){this.$target.addClass(params.extraClass);_restoreTransitions();return;}
this.$target[0].classList.remove(className);}}
const htmlPropValue=weUtils.getCSSVariableValue(widgetValue);if(htmlPropValue){widgetValue=`var(--${widgetValue})`;}
const values=widgetValue.replace(/,\s/g,',').split(/\s+/g);while(values.length<cssProps.length){switch(values.length){case 1:case 2:{values.push(values[0]);break;}
case 3:{values.push(values[1]);break;}
default:{values.push(values[values.length-1]);}}}
const styles=window.getComputedStyle(this.$target[0]);let hasUserValue=false;for(let i=cssProps.length-1;i>0;i--){hasUserValue=applyCSS.call(this,cssProps[i],values.pop(),styles)||hasUserValue;}
hasUserValue=applyCSS.call(this,cssProps[0],values.join(' '),styles)||hasUserValue;function applyCSS(cssProp,cssValue,styles){const forceStyle=(typeof params.forceStyle!=='undefined');if(forceStyle||!weUtils.areCssValuesEqual(styles[cssProp],cssValue,cssProp,this.$target[0])){const priority=forceStyle?params.forceStyle:'important';this.$target[0].style.setProperty(cssProp,cssValue,priority);return true;}
return false;}
if(params.extraClass){this.$target.toggleClass(params.extraClass,hasUserValue);}
_restoreTransitions();},$:function(){return this.$target.find.apply(this.$target,arguments);},closeWidgets:function(){this._userValueWidgets.forEach(widget=>widget.close());},findWidget:function(name){for(const widget of this._userValueWidgets){if(widget.getName()===name){return widget;}
const depWidget=widget.findWidget(name);if(depWidget){return depWidget;}}
return null;},notify:function(name,data){if(name==='target'){this.setTarget(data);}},setTarget:function($target){this.$target=$target;},updateUI:async function({noVisibility}={}){const proms=this._userValueWidgets.map(async widget=>{const methodsNames=widget.getMethodsNames();for(const methodName of methodsNames){const params=widget.getMethodsParams(methodName);let obj=this;if(params.applyTo){const $firstSubTarget=this.$(params.applyTo).eq(0);if(!$firstSubTarget.length){continue;}
obj=createPropertyProxy(this,'$target',$firstSubTarget);}
const value=await this._computeWidgetState.call(obj,methodName,params);if(value===undefined){continue;}
const normalizedValue=this._normalizeWidgetValue(value);await widget.setValue(normalizedValue,methodName);}});await Promise.all(proms);if(!noVisibility){await this.updateUIVisibility();}},updateUIVisibility:async function(){const proms=this._userValueWidgets.map(async widget=>{const params=widget.getMethodsParams();let obj=this;if(params.applyTo){const $firstSubTarget=this.$(params.applyTo).eq(0);if(!$firstSubTarget.length){widget.toggleVisibility(false);return;}
obj=createPropertyProxy(this,'$target',$firstSubTarget);}
const allSubWidgets=[widget];let i=0;while(i<allSubWidgets.length){allSubWidgets.push(...allSubWidgets[i]._userValueWidgets);i++;}
const proms=allSubWidgets.map(async widget=>{const show=await this._computeWidgetVisibility.call(obj,widget.getName(),params);if(!show){widget.toggleVisibility(false);return;}
const dependencies=widget.getDependencies();const dependenciesData=[];dependencies.forEach(depName=>{const toBeActive=(depName[0]!=='!');if(!toBeActive){depName=depName.substr(1);}
const widget=this._requestUserValueWidgets(depName)[0];if(widget){dependenciesData.push({widget:widget,toBeActive:toBeActive,});}});const dependenciesOK=!dependenciesData.length||dependenciesData.some(depData=>{return(depData.widget.isActive()===depData.toBeActive);});widget.toggleVisibility(dependenciesOK);});return Promise.all(proms);});const showUI=await this._computeVisibility();this.el.classList.toggle('d-none',!showUI);await Promise.all(proms);for(const el of this.$el.find('we-row')){el.classList.toggle('d-none',!$(el).find('> div > .o_we_user_value_widget').not('.d-none').length);}
for(const el of this.$el.find('we-collapse')){const $el=$(el);el.classList.toggle('d-none',$el.children().first().hasClass('d-none'));const hasNoVisibleElInCollapseMenu=!$el.children().last().children().not('.d-none').length;if(hasNoVisibleElInCollapseMenu){this._toggleCollapseEl(el,false);}
el.querySelector('.o_we_collapse_toggler').classList.toggle('d-none',hasNoVisibleElInCollapseMenu);}},async _checkIfWidgetsUpdateNeedWarning(widgets){const messages=[];for(const widget of widgets){const message=widget.getMethodsParams().warnMessage;if(message){messages.push(message);}}
return messages.join(' ');},async _checkIfWidgetsUpdateNeedReload(widgets){return false;},_computeVisibility:async function(){return true;},_computeWidgetState:async function(methodName,params){switch(methodName){case'selectClass':{let maxNbClasses=0;let activeClassNames='';params.possibleValues.forEach(classNames=>{if(!classNames){return;}
const classes=classNames.split(/\s+/g);if(classes.length>=maxNbClasses&&classes.every(className=>this.$target[0].classList.contains(className))){maxNbClasses=classes.length;activeClassNames=classNames;}});return activeClassNames;}
case'selectAttribute':case'selectDataAttribute':{const attrName=params.attributeName;let attrValue;if(methodName==='selectAttribute'){attrValue=this.$target[0].getAttribute(attrName);}else if(methodName==='selectDataAttribute'){attrValue=this.$target[0].dataset[attrName];}
attrValue=(attrValue||'').trim();if(params.saveUnit&&!params.withUnit){attrValue=attrValue.split(/\s+/g).map(v=>v+params.saveUnit).join(' ');}
return attrValue||params.attributeDefaultValue||'';}
case'selectStyle':{if(params.colorPrefix&&params.colorNames){for(const c of params.colorNames){const className=weUtils.computeColorClasses([c],params.colorPrefix)[0];if(this.$target[0].classList.contains(className)){return c;}}}
this.$target[0].classList.add('o_we_force_no_transition');const _restoreTransitions=()=>this.$target[0].classList.remove('o_we_force_no_transition');const styles=window.getComputedStyle(this.$target[0]);const cssProps=weUtils.CSS_SHORTHANDS[params.cssProperty]||[params.cssProperty];const borderWidthCssProps=weUtils.CSS_SHORTHANDS['border-width'];const cssValues=cssProps.map(cssProp=>{let value=styles[cssProp].trim();if(cssProp==='box-shadow'){const inset=value.includes('inset');let values=value.replace(/,\s/g,',').replace('inset','').trim().split(/\s+/g);const color=values.find(s=>!s.match(/^\d/));values=values.join(' ').replace(color,'').trim();value=`${color} ${values}${inset ? ' inset' : ''}`;}
if(borderWidthCssProps.includes(cssProp)&&value.endsWith('px')){value=`${Math.ceil(parseFloat(value))}px`;}
return value;});if(cssValues.length===4&&weUtils.areCssValuesEqual(cssValues[3],cssValues[1],params.cssProperty,this.$target)){cssValues.pop();}
if(cssValues.length===3&&weUtils.areCssValuesEqual(cssValues[2],cssValues[0],params.cssProperty,this.$target)){cssValues.pop();}
if(cssValues.length===2&&weUtils.areCssValuesEqual(cssValues[1],cssValues[0],params.cssProperty,this.$target)){cssValues.pop();}
_restoreTransitions();return cssValues.join(' ');}}},_computeWidgetVisibility:async function(widgetName,params){if(widgetName==='move_up_opt'||widgetName==='move_left_opt'){return!this.$target.is(':first-child');}
if(widgetName==='move_down_opt'||widgetName==='move_right_opt'){return!this.$target.is(':last-child');}
return true;},_extraInfoFromDescriptionElement:function(el){return{title:el.getAttribute('string'),options:{classes:el.classList,dataAttributes:el.dataset,tooltip:el.title,placeholder:el.getAttribute('placeholder'),childNodes:[...el.childNodes],},};},_normalizeWidgetValue:function(value){value=`${value}`.trim();value=ColorpickerWidget.normalizeCSSColor(value);return value;},_registerUserValueWidget:function(widgetName,parent,title,options){const widget=new userValueWidgetsRegistry[widgetName](parent,title,options,this.$target);if(!parent||parent===this){this._userValueWidgets.push(widget);}else{parent.registerSubWidget(widget);}
return widget;},_renderCustomWidgets:function(uiFragment){return Promise.resolve();},_renderCustomXML:function(uiFragment){return Promise.resolve();},_renderOriginalXML:async function($xml){const uiFragment=document.createDocumentFragment();($xml||this.$originalUIElements).clone(true).appendTo(uiFragment);await this._renderCustomXML(uiFragment);for(const[itemName,build]of[['we-row',_buildRowElement],['we-collapse',_buildCollapseElement]]){uiFragment.querySelectorAll(itemName).forEach(el=>{const infos=this._extraInfoFromDescriptionElement(el);const groupEl=build(infos.title,infos.options);el.parentNode.insertBefore(groupEl,el);el.parentNode.removeChild(el);});}
await this._renderXMLWidgets(uiFragment);await this._renderCustomWidgets(uiFragment);if(this.isDestroyed()){return uiFragment;}
const validMethodNames=[];for(const key in this){validMethodNames.push(key);}
this._userValueWidgets.forEach(widget=>{widget.loadMethodsData(validMethodNames);});return uiFragment;},_renderXMLWidgets:function(parentEl,parentWidget){const proms=[...parentEl.children].map(el=>{const widgetName=el.tagName.toLowerCase();if(!userValueWidgetsRegistry.hasOwnProperty(widgetName)){return this._renderXMLWidgets(el,parentWidget);}
const infos=this._extraInfoFromDescriptionElement(el);const widget=this._registerUserValueWidget(widgetName,parentWidget||this,infos.title,infos.options);return widget.insertAfter(el).then(()=>{parentEl.removeChild(el);if(widget.isContainer()){return this._renderXMLWidgets(widget.el,widget);}});});return Promise.all(proms);},_requestUserValueWidgets:function(...widgetNames){const widgets=[];for(const widgetName of widgetNames){let widget=null;this.trigger_up('user_value_widget_request',{name:widgetName,onSuccess:_widget=>widget=_widget,});if(widget){widgets.push(widget);}}
return widgets;},_rerenderXML:async function(callback){this._userValueWidgets.forEach(widget=>widget.destroy());this._userValueWidgets=[];this.$el.empty();let $xml=undefined;if(callback){$xml=await callback.call(this);}
return this._renderOriginalXML($xml).then(uiFragment=>{this.$el.append(uiFragment);return this.updateUI();});},_select:async function(previewMode,widget){let $applyTo=null;for(const methodName of widget.getMethodsNames()){const widgetValue=widget.getValue(methodName);const params=widget.getMethodsParams(methodName);if(params.applyTo){if(!$applyTo){$applyTo=this.$(params.applyTo);}
const proms=_.map($applyTo,subTargetEl=>{const proxy=createPropertyProxy(this,'$target',$(subTargetEl));return this[methodName].call(proxy,previewMode,widgetValue,params);});await Promise.all(proms);}else{await this[methodName](previewMode,widgetValue,params);}}
($applyTo||this.$target).trigger('content_changed');},_selectAttributeHelper(value,params){if(!params.attributeName){throw new Error('Attribute name missing');}
if(params.saveUnit&&!params.withUnit){value=value.split(params.saveUnit).join('');}
if(params.extraClass){this.$target.toggleClass(params.extraClass,params.defaultValue!==value);}
return value;},_toggleCollapseEl(collapseEl,show){collapseEl.classList.toggle('active',show);collapseEl.querySelector('.o_we_collapse_toggler').classList.toggle('active',show);},_onCollapseTogglerClick(ev){const currentCollapseEl=ev.currentTarget.parentNode;this._toggleCollapseEl(currentCollapseEl);for(const collapseEl of currentCollapseEl.querySelectorAll('we-collapse')){this._toggleCollapseEl(collapseEl,false);}},_onUserValueUpdate:async function(ev){ev.stopPropagation();const widget=ev.data.widget;const previewMode=ev.data.previewMode;let requiresReload=false;if(!ev.data.previewMode&&!ev.data.isSimulatedEvent){const linkedWidgets=this._requestUserValueWidgets(...ev.data.triggerWidgetsNames);const widgets=[ev.data.widget].concat(linkedWidgets);const warnMessage=await this._checkIfWidgetsUpdateNeedWarning(widgets);if(warnMessage){const okWarning=await new Promise(resolve=>{Dialog.confirm(this,warnMessage,{confirm_callback:()=>resolve(true),cancel_callback:()=>resolve(false),});});if(!okWarning){return;}}
const reloadMessage=await this._checkIfWidgetsUpdateNeedReload(widgets);requiresReload=!!reloadMessage;if(requiresReload){const save=await new Promise(resolve=>{Dialog.confirm(this,_t("This change needs to reload the page, this will save all your changes and reload the page, are you sure you want to proceed?")+' '
+(typeof reloadMessage==='string'?reloadMessage:''),{confirm_callback:()=>resolve(true),cancel_callback:()=>resolve(false),});});if(!save){return;}}}
if(!this._actionQueues.get(widget)){this._actionQueues.set(widget,[]);}
const currentAction={previewMode};this._actionQueues.get(widget).push(currentAction);const shouldRecordUndo=(!previewMode&&!ev.data.isSimulatedEvent);this.trigger_up('snippet_edition_request',{exec:async()=>{if(this.isDestroyed()){return;}
const actionQueue=this._actionQueues.get(widget).filter(({previewMode},i,actions)=>{const prev=actions[i-1];const next=actions[i+1];if(previewMode===true&&next&&next.previewMode){return false;}else if(previewMode==='reset'&&prev&&prev.previewMode){return false;}
return true;});if(!actionQueue.includes(currentAction)){this._actionQueues.set(widget,actionQueue);return;}
this._actionQueues.set(widget,actionQueue.filter(action=>action!==currentAction));if(ev.data.prepare){ev.data.prepare();}
if(previewMode&&(widget.$el.closest('[data-no-preview="true"]').length)){return;}
if(shouldRecordUndo){this.trigger_up('request_history_undo_record',{$target:this.$target});}
await this._select(previewMode,widget);if(previewMode){return;}
await new Promise(resolve=>setTimeout(()=>{this.trigger_up('snippet_option_update',{onSuccess:()=>resolve(),});}));}});if(ev.data.isSimulatedEvent){return;}
const linkedWidgets=this._requestUserValueWidgets(...ev.data.triggerWidgetsNames);if(linkedWidgets.length!==ev.data.triggerWidgetsNames.length){console.warn('Missing widget to trigger');return;}
let i=0;const triggerWidgetsValues=ev.data.triggerWidgetsValues;for(const linkedWidget of linkedWidgets){const widgetValue=triggerWidgetsValues[i];if(widgetValue!==undefined){const normValue=this._normalizeWidgetValue(widgetValue);if(previewMode===true){linkedWidget._previewColor=normValue;}else if(previewMode===false){linkedWidget._previewColor=false;linkedWidget._value=normValue;}else{linkedWidget._previewColor=false;}}
linkedWidget.notifyValueChange(previewMode,true);i++;}
if(requiresReload){this.trigger_up('request_save',{reloadEditor:true,});}},_onUserValueWidgetCritical(){this.trigger_up('remove_snippet',{$snippet:this.$target,});},});const registry={};registry.sizing=SnippetOptionWidget.extend({start:function(){var self=this;var def=this._super.apply(this,arguments);this.$handles=this.$overlay.find('.o_handle');var resizeValues=this._getSize();this.$handles.on('mousedown',function(ev){ev.preventDefault();resizeValues=self._getSize();var $handle=$(ev.currentTarget);var compass=false;var XY=false;if($handle.hasClass('n')){compass='n';XY='Y';}else if($handle.hasClass('s')){compass='s';XY='Y';}else if($handle.hasClass('e')){compass='e';XY='X';}else if($handle.hasClass('w')){compass='w';XY='X';}
var resize=resizeValues[compass];if(!resize){return;}
var current=0;var cssProperty=resize[2];var cssPropertyValue=parseInt(self.$target.css(cssProperty));_.each(resize[0],function(val,key){if(self.$target.hasClass(val)){current=key;}else if(resize[1][key]===cssPropertyValue){current=key;}});var begin=current;var beginClass=self.$target.attr('class');var regClass=new RegExp('\\s*'+resize[0][begin].replace(/[-]*[0-9]+/,'[-]*[0-9]+'),'g');var cursor=$handle.css('cursor')+'-important';var $body=$(this.ownerDocument.body);$body.addClass(cursor);var xy=ev['page'+XY];var bodyMouseMove=function(ev){ev.preventDefault();var dd=ev['page'+XY]-xy+resize[1][begin];var next=current+(current+1===resize[1].length?0:1);var prev=current?(current-1):0;var change=false;if(dd>(2*resize[1][next]+resize[1][current])/3){self.$target.attr('class',(self.$target.attr('class')||'').replace(regClass,''));self.$target.addClass(resize[0][next]);current=next;change=true;}
if(prev!==current&&dd<(2*resize[1][prev]+resize[1][current])/3){self.$target.attr('class',(self.$target.attr('class')||'').replace(regClass,''));self.$target.addClass(resize[0][prev]);current=prev;change=true;}
if(change){self._onResize(compass,beginClass,current);self.trigger_up('cover_update');$handle.addClass('o_active');}};var bodyMouseUp=function(){$body.off('mousemove',bodyMouseMove);$(window).off('mouseup',bodyMouseUp);$body.removeClass(cursor);$handle.removeClass('o_active');var $handlers=self.$overlay.find('.o_handle');$handlers.addClass('o_active').delay(300).queue(function(){$handlers.removeClass('o_active').dequeue();});if(begin===current){return;}
setTimeout(function(){self.trigger_up('request_history_undo_record',{$target:self.$target,event:'resize_'+XY,});},0);};$body.on('mousemove',bodyMouseMove);$(window).on('mouseup',bodyMouseUp);});return def;},onFocus:function(){this._onResize();},onBlur:function(){this.$handles.addClass('readonly');},setTarget:function(){this._super(...arguments);this._onResize();},updateUI:async function(){await this._super(...arguments);const resizeValues=this._getSize();_.each(resizeValues,(value,key)=>{this.$handles.filter('.'+key).toggleClass('readonly',!value);});},_getSize:function(){},_onResize:function(compass,beginClass,current){var self=this;var resizeValues=this._getSize();var $handles=this.$overlay.find('.o_handle');_.each(resizeValues,function(resizeValue,direction){var classes=resizeValue[0];var values=resizeValue[1];var cssProperty=resizeValue[2];var $handle=$handles.filter('.'+direction);var current=0;var cssPropertyValue=parseInt(self.$target.css(cssProperty));_.each(classes,function(className,key){if(self.$target.hasClass(className)){current=key;}else if(values[key]===cssPropertyValue){current=key;}});$handle.toggleClass('o_handle_start',current===0);$handle.toggleClass('o_handle_end',current===classes.length-1);});var ml=this.$target.css('margin-left');this.$overlay.find('.o_handle.w').css({width:ml,left:'-'+ml,});this.$overlay.find('.o_handle.e').css({width:0,});_.each(this.$overlay.find(".o_handle.n, .o_handle.s"),function(handle){var $handle=$(handle);var direction=$handle.hasClass('n')?'top':'bottom';$handle.height(self.$target.css('padding-'+direction));});this.$target.trigger('content_changed');},});registry['sizing_y']=registry.sizing.extend({_getSize:function(){var nClass='pt';var nProp='padding-top';var sClass='pb';var sProp='padding-bottom';if(this.$target.is('hr')){nClass='mt';nProp='margin-top';sClass='mb';sProp='margin-bottom';}
var grid=[];for(var i=0;i<=(256/8);i++){grid.push(i*8);}
grid.splice(1,0,4);this.grid={n:[grid.map(v=>nClass+v),grid,nProp],s:[grid.map(v=>sClass+v),grid,sProp],};return this.grid;},});const ImageHandlerOption=SnippetOptionWidget.extend({async willStart(){const _super=this._super.bind(this);await this._loadImageInfo();return _super(...arguments);},selectWidth(previewMode,widgetValue,params){this._getImg().dataset.resizeWidth=widgetValue;return this._applyOptions();},setQuality(previewMode,widgetValue,params){this._getImg().dataset.quality=widgetValue;return this._applyOptions();},glFilter(previewMode,widgetValue,params){const dataset=this._getImg().dataset;if(widgetValue){dataset.glFilter=widgetValue;}else{delete dataset.glFilter;}
return this._applyOptions();},customFilter(previewMode,widgetValue,params){const img=this._getImg();const{filterOptions}=img.dataset;const{filterProperty}=params;if(filterProperty==='filterColor'){widgetValue=normalizeColor(widgetValue);}
const newOptions=Object.assign(JSON.parse(filterOptions||"{}"),{[filterProperty]:widgetValue});img.dataset.filterOptions=JSON.stringify(newOptions);return this._applyOptions();},_computeVisibility(){const src=this._getImg().getAttribute('src');return src&&src!=='/';},async _computeWidgetState(methodName,params){const img=this._getImg();await new Promise((resolve,reject)=>{if(img.complete){resolve();return;}
img.addEventListener('load',resolve,{once:true});img.addEventListener('error',resolve,{once:true});});switch(methodName){case'selectWidth':return img.naturalWidth;case'setFilter':return img.dataset.filter;case'glFilter':return img.dataset.glFilter||"";case'setQuality':return img.dataset.quality||75;case'customFilter':{const{filterProperty}=params;const options=JSON.parse(img.dataset.filterOptions||"{}");const defaultValue=filterProperty==='blend'?'normal':0;return options[filterProperty]||defaultValue;}}
return this._super(...arguments);},async _renderCustomXML(uiFragment){const isLocalURL=href=>new URL(href,window.location.origin).origin===window.location.origin;const img=this._getImg();if(!this.originalSrc||!['image/png','image/jpeg'].includes(img.dataset.mimetype)){return[...uiFragment.childNodes].forEach(node=>{if(node.matches('.o_we_external_warning')){node.classList.remove('d-none');if(isLocalURL(img.getAttribute('src'))){const title=node.querySelector('we-title');title.textContent=` ${_t("Quality options unavailable")}`;$(title).prepend('<i class="fa fa-warning" />');if(img.dataset.mimetype){title.setAttribute('title',_t("Only PNG and JPEG images support quality options and image filtering"));}else{title.setAttribute('title',_t("Due to technical limitations, you can only change optimization settings on this image by choosing it again in the media-dialog or reuploading it (double click on the image)"));}}}else{node.remove();}});}
const $select=$(uiFragment).find('we-select[data-name=width_select_opt]');(await this._computeAvailableWidths()).forEach(([value,label])=>{$select.append(`<we-button data-select-width="${value}">${label}</we-button>`);});if(img.dataset.mimetype!=='image/jpeg'){uiFragment.querySelector('we-range[data-set-quality]').remove();}},async _computeAvailableWidths(){const img=this._getImg();const original=await loadImage(this.originalSrc);const maxWidth=img.dataset.width?img.naturalWidth:original.naturalWidth;const optimizedWidth=Math.min(maxWidth,this._computeMaxDisplayWidth());this.optimizedWidth=optimizedWidth;const widths={128:'128px',256:'256px',512:'512px',1024:'1024px',1920:'1920px',};widths[img.naturalWidth]=_.str.sprintf(_t("%spx"),img.naturalWidth);widths[optimizedWidth]=_.str.sprintf(_t("%dpx (Suggested)"),optimizedWidth);widths[maxWidth]=_.str.sprintf(_t("%dpx (Original)"),maxWidth);return Object.entries(widths).filter(([width])=>width<=maxWidth).sort(([v1],[v2])=>v1-v2);},async _applyOptions(){const img=this._getImg();if(!['image/jpeg','image/png'].includes(img.dataset.mimetype)){this.originalId=null;return;}
const dataURL=await applyModifications(img);const weight=dataURL.split(',')[1].length/4*3;const $weight=this.$el.find('.o_we_image_weight');$weight.find('> small').text(_t("New size"));$weight.find('b').text(`${(weight / 1024).toFixed(1)} kb`);$weight.removeClass('d-none');img.classList.add('o_modified_image_to_save');const loadedImg=await loadImage(dataURL,img);this._applyImage(loadedImg);return loadedImg;},async _loadImageInfo(){const img=this._getImg();await loadImageInfo(img,this._rpc.bind(this));if(!img.dataset.originalId){this.originalId=null;this.originalSrc=null;return;}
this.originalId=img.dataset.originalId;this.originalSrc=img.dataset.originalSrc;},async _autoOptimizeImage(){await this._loadImageInfo();await this._rerenderXML();this._getImg().dataset.resizeWidth=this.optimizedWidth;await this._applyOptions();await this.updateUI();},_getImg(){},_computeMaxDisplayWidth(){},_applyImage(img){},});registry.ImageOptimize=ImageHandlerOption.extend({MAX_SUGGESTED_WIDTH:1920,start(){this.$target.on('image_changed.ImageOptimization',this._onImageChanged.bind(this));this.$target.on('image_cropped.ImageOptimization',this._onImageCropped.bind(this));return this._super(...arguments);},destroy(){this.$target.off('.ImageOptimization');return this._super(...arguments);},_computeMaxDisplayWidth(){const img=this._getImg();const computedStyles=window.getComputedStyle(img);const displayWidth=parseFloat(computedStyles.getPropertyValue('width'));const gutterWidth=parseFloat(computedStyles.getPropertyValue('--o-grid-gutter-width'))||30;if(this.$target[0].closest('nav')){return Math.round(Math.min(displayWidth*3,this.MAX_SUGGESTED_WIDTH));}else if(img.closest('.container, .o_container_small')){const mdContainerMaxWidth=parseFloat(computedStyles.getPropertyValue('--o-md-container-max-width'))||720;const mdContainerInnerWidth=mdContainerMaxWidth-gutterWidth;return Math.round(utils.confine(displayWidth,mdContainerInnerWidth,this.MAX_SUGGESTED_WIDTH));}else if(img.closest('.container-fluid')){const lgBp=parseFloat(computedStyles.getPropertyValue('--breakpoint-lg'))||992;const mdContainerFluidMaxInnerWidth=lgBp-gutterWidth;return Math.round(utils.confine(displayWidth,mdContainerFluidMaxInnerWidth,this.MAX_SUGGESTED_WIDTH));}
return Math.round(Math.min(displayWidth*1.5,this.MAX_SUGGESTED_WIDTH));},_getImg(){return this.$target[0];},async _onImageChanged(ev){this.trigger_up('snippet_edition_request',{exec:async()=>{await this._autoOptimizeImage();this.trigger_up('cover_update');}});},async _onImageCropped(ev){await this._rerenderXML();},});registry.BackgroundOptimize=ImageHandlerOption.extend({start(){this.$target.on('background_changed.BackgroundOptimize',this._onBackgroundChanged.bind(this));return this._super(...arguments);},destroy(){this.$target.off('.BackgroundOptimize');return this._super(...arguments);},_getImg(){return this.img;},_computeMaxDisplayWidth(){return 1920;},async _loadImageInfo(){this.img=new Image();const targetEl=this.$target[0].classList.contains("oe_img_bg")?this.$target[0]:this.$target[0].querySelector(":scope > .s_parallax_bg.oe_img_bg");if(targetEl){Object.entries(targetEl.dataset).filter(([key])=>isBackgroundImageAttribute(key)).forEach(([key,value])=>{this.img.dataset[key]=value;});const src=getBgImageURL(targetEl);this.img.src=src.startsWith("/")?src:"";}
return await this._super(...arguments);},_applyImage(img){this.$target.css('background-image',`url('${img.getAttribute('src')}')`);this.$target[0].classList.add("o_modified_image_to_save");for(const attribute in this.$target[0].dataset){if(isBackgroundImageAttribute(attribute)){delete this.$target[0].dataset[attribute];}}
Object.entries(img.dataset).forEach(([key,value])=>{this.$target[0].dataset[key]=value;});this.$target[0].dataset.bgSrc=img.getAttribute("src");},async _onBackgroundChanged(ev,previewMode){ev.stopPropagation();if(!previewMode){this.trigger_up('snippet_edition_request',{exec:async()=>{await this._autoOptimizeImage();}});}},});registry.BackgroundToggler=SnippetOptionWidget.extend({start(){this.$target.on('content_changed.BackgroundToggler',this._onExternalUpdate.bind(this));return this._super(...arguments);},destroy(){this._super(...arguments);this.$target.off('.BackgroundToggler');},toggleBgImage(previewMode,widgetValue,params){if(!widgetValue){const[bgImageWidget]=this._requestUserValueWidgets('bg_image_opt');const bgImageOpt=bgImageWidget.getParent();return bgImageOpt.background(false,'',bgImageWidget.getMethodsParams('background'));}else{this._requestUserValueWidgets('bg_image_opt')[0].el.click();}},toggleBgShape(previewMode,widgetValue,params){const[shapeWidget]=this._requestUserValueWidgets('bg_shape_opt');const shapeOption=shapeWidget.getParent();return shapeOption._toggleShape();},toggleBgFilter(previewMode,widgetValue,params){if(widgetValue){const bgFilterEl=document.createElement('div');bgFilterEl.classList.add('o_we_bg_filter','bg-black-50');const lastBackgroundEl=this._getLastPreFilterLayerElement();if(lastBackgroundEl){$(lastBackgroundEl).after(bgFilterEl);}else{this.$target.prepend(bgFilterEl);}}else{this.$target.find('> .o_we_bg_filter').remove();}},_computeWidgetState(methodName,params){switch(methodName){case'toggleBgImage':{const[bgImageWidget]=this._requestUserValueWidgets('bg_image_opt');const bgImageOpt=bgImageWidget.getParent();return!!bgImageOpt._computeWidgetState('background',bgImageWidget.getMethodsParams('background'));}
case'toggleBgFilter':{return this._hasBgFilter();}
case'toggleBgShape':{const[shapeWidget]=this._requestUserValueWidgets('bg_shape_opt');const shapeOption=shapeWidget.getParent();return!!shapeOption._computeWidgetState('shape',shapeWidget.getMethodsParams('shape'));}}
return this._super(...arguments);},_getLastPreFilterLayerElement(){return null;},_hasBgFilter(){return!!this.$target.find('> .o_we_bg_filter').length;},_onExternalUpdate(){if(this._hasBgFilter()&&!this._getLastPreFilterLayerElement()&&!getBgImageURL(this.$target)){const widget=this._requestUserValueWidgets('bg_filter_toggle_opt')[0];widget.enable();}},});registry.BackgroundImage=SnippetOptionWidget.extend({start:function(){this.__customImageSrc=getBgImageURL(this.$target[0]);return this._super(...arguments);},background:async function(previewMode,widgetValue,params){if(previewMode===true){this.__customImageSrc=getBgImageURL(this.$target[0]);}else if(previewMode==='reset'){widgetValue=this.__customImageSrc;}else{this.__customImageSrc=widgetValue;}
this._setBackground(widgetValue);if(previewMode!=='reset'){removeOnImageChangeAttrs.forEach(attr=>delete this.$target[0].dataset[attr]);this.$target.trigger('background_changed',[previewMode]);}},async dynamicColor(previewMode,widgetValue,params){const currentSrc=getBgImageURL(this.$target[0]);switch(previewMode){case true:this.previousSrc=currentSrc;break;case'reset':this.$target.css('background-image',`url('${this.previousSrc}')`);return;}
const newURL=new URL(currentSrc,window.location.origin);newURL.searchParams.set('c1',normalizeColor(widgetValue));const src=newURL.pathname+newURL.search;await loadImage(src);this.$target.css('background-image',`url('${src}')`);if(!previewMode){this.previousSrc=src;}},setTarget:function(){const oldBgURL=getBgImageURL(this.$target);const isModifiedImage=this.$target[0].classList.contains("o_modified_image_to_save");const filteredOldDataset=Object.entries(this.$target[0].dataset).filter(([key])=>{return isBackgroundImageAttribute(key);});filteredOldDataset.forEach(([key])=>{delete this.$target[0].dataset[key];});this.$target[0].classList.remove("o_modified_image_to_save");this._setBackground('');this._super(...arguments);if(oldBgURL){this._setBackground(oldBgURL);filteredOldDataset.forEach(([key,value])=>{this.$target[0].dataset[key]=value;});this.$target[0].classList.toggle("o_modified_image_to_save",isModifiedImage);}
this.__customImageSrc=getBgImageURL(this.$target[0]);},_computeWidgetState:function(methodName){switch(methodName){case'background':return getBgImageURL(this.$target[0]);case'dynamicColor':return new URL(getBgImageURL(this.$target[0]),window.location.origin).searchParams.get('c1');}
return this._super(...arguments);},_computeWidgetVisibility(widgetName,params){if(widgetName==='dynamic_color_opt'){const src=new URL(getBgImageURL(this.$target[0]),window.location.origin);return src.origin===window.location.origin&&src.pathname.startsWith('/web_editor/shape/');}
return this._super(...arguments);},_setBackground(backgroundURL){if(backgroundURL){this.$target.css('background-image',`url('${backgroundURL}')`);this.$target.addClass('oe_img_bg');}else{this.$target.css('background-image','');this.$target.removeClass('oe_img_bg');}},});registry.BackgroundShape=SnippetOptionWidget.extend({updateUI(){if(this.rerender){this.rerender=false;return this._rerenderXML();}
return this._super.apply(this,arguments);},onBuilt(){if(this.$target[0].querySelector('.o_we_flip_x, .o_we_flip_y')){this._handlePreviewState(false,()=>{return{flip:this._getShapeData().flip};});}},shape(previewMode,widgetValue,params){this._handlePreviewState(previewMode,()=>{return{shape:widgetValue,colors:this._getDefaultColors(),flip:[]};});},color(previewMode,widgetValue,params){this._handlePreviewState(previewMode,()=>{const{colorName}=params;const{colors:previousColors}=this._getShapeData();const newColor=normalizeColor(widgetValue)||this._getDefaultColors()[colorName];const newColors=Object.assign(previousColors,{[colorName]:newColor});return{colors:newColors};});},flipX(previewMode,widgetValue,params){this._flipShape(previewMode,'x');},flipY(previewMode,widgetValue,params){this._flipShape(previewMode,'y');},_computeWidgetState(methodName,params){switch(methodName){case'shape':{return this._getShapeData().shape;}
case'color':{const{shape,colors:customColors}=this._getShapeData();const colors=Object.assign(this._getDefaultColors(),customColors);const color=shape&&colors[params.colorName];return color||'';}
case'flipX':{const hasFlipClass=this.$target.find('> .o_we_shape.o_we_flip_x').length!==0;return hasFlipClass||this._getShapeData().flip.includes('x');}
case'flipY':{const hasFlipClass=this.$target.find('> .o_we_shape.o_we_flip_y').length!==0;return hasFlipClass||this._getShapeData().flip.includes('y');}}
return this._super(...arguments);},_renderCustomXML(uiFragment){Object.keys(this._getDefaultColors()).map(colorName=>{uiFragment.querySelector('[data-name="colors"]').prepend($(`<we-colorpicker data-color="true" data-color-name="${colorName}">`)[0]);});uiFragment.querySelectorAll('we-select-pager we-button[data-shape]').forEach(btn=>{const btnContent=document.createElement('div');btnContent.classList.add('o_we_shape_btn_content','position-relative','border-dark');const btnContentInnerDiv=document.createElement('div');btnContentInnerDiv.classList.add('o_we_shape');btnContent.appendChild(btnContentInnerDiv);const{shape}=btn.dataset;const shapeEl=btnContent.querySelector('.o_we_shape');shapeEl.classList.add(`o_${shape.replace(/\//g, '_')}`);btn.append(btnContent);});return uiFragment;},async _computeWidgetVisibility(widgetName,params){if(widgetName==='shape_none_opt'){return false;}
return this._super(...arguments);},_flipShape(previewMode,axis){this._handlePreviewState(previewMode,()=>{const flip=new Set(this._getShapeData().flip);if(flip.has(axis)){flip.delete(axis);}else{flip.add(axis);}
return{flip:[...flip]};});},_handlePreviewState(previewMode,computeShapeData){const target=this.$target[0];const insertShapeContainer=newContainer=>{const shapeContainer=target.querySelector(':scope > .o_we_shape');if(shapeContainer){this._removeShapeEl(shapeContainer);}
if(newContainer){const preShapeLayerElement=this._getLastPreShapeLayerElement();if(preShapeLayerElement){$(preShapeLayerElement).after(newContainer);}else{this.$target.prepend(newContainer);}}
return newContainer;};let changedShape=false;if(previewMode==='reset'){insertShapeContainer(this.prevShapeContainer);if(this.prevShape){target.dataset.oeShapeData=this.prevShape;}else{delete target.dataset.oeShapeData;}
return;}else{if(previewMode===true){const shapeContainer=target.querySelector(':scope > .o_we_shape');this.prevShapeContainer=shapeContainer&&shapeContainer.cloneNode(true);this.prevShape=target.dataset.oeShapeData;}
const curShapeData=target.dataset.oeShapeData||{};const newShapeData=computeShapeData();const{shape:curShape}=curShapeData;changedShape=newShapeData.shape!==curShape;this._markShape(newShapeData);if(previewMode===false&&changedShape){this.rerender=true;}}
const json=target.dataset.oeShapeData;const{shape,colors,flip=[]}=json?JSON.parse(json):{};let shapeContainer=target.querySelector(':scope > .o_we_shape');if(!shape){return insertShapeContainer(null);}
if(changedShape){shapeContainer=insertShapeContainer(null);}
if(!shapeContainer){shapeContainer=insertShapeContainer(document.createElement('div'));target.style.position='relative';shapeContainer.className=`o_we_shape o_${shape.replace(/\//g, '_')}`;}
shapeContainer.classList.remove('o_we_flip_x','o_we_flip_y');if(colors||flip.length){$(shapeContainer).css('background-image',`url("${this._getShapeSrc()}")`);shapeContainer.style.backgroundPosition='';if(flip.length){let[xPos,yPos]=$(shapeContainer).css('background-position').split(' ').map(p=>parseFloat(p));xPos=flip.includes('x')?-xPos+100:xPos;yPos=flip.includes('y')?-yPos+100:yPos;shapeContainer.style.backgroundPosition=`${xPos}% ${yPos}%`;}}else{$(shapeContainer).css('background-image','');$(shapeContainer).css('background-position','');}
if(previewMode===false){this.prevShapeContainer=shapeContainer.cloneNode(true);this.prevShape=target.dataset.oeShapeData;}},_removeShapeEl(shapeEl){shapeEl.remove();},_markShape(newData){const defaultColors=this._getDefaultColors();const shapeData=Object.assign(this._getShapeData(),newData);const areColorsDefault=Object.entries(shapeData.colors).every(([colorName,colorValue])=>{return colorValue.toLowerCase()===defaultColors[colorName].toLowerCase();});if(areColorsDefault){delete shapeData.colors;}
if(!shapeData.shape){delete this.$target[0].dataset.oeShapeData;}else{this.$target[0].dataset.oeShapeData=JSON.stringify(shapeData);}},_getLastPreShapeLayerElement(){const $filterEl=this.$target.find('> .o_we_bg_filter');if($filterEl.length){return $filterEl[0];}
return null;},_getShapeSrc(){const{shape,colors,flip}=this._getShapeData();if(!shape){return'';}
const searchParams=Object.entries(colors).map(([colorName,colorValue])=>{const encodedCol=encodeURIComponent(normalizeColor(colorValue));return`${colorName}=${encodedCol}`;});if(flip.length){searchParams.push(`flip=${flip.sort().join('')}`);}
return`/web_editor/shape/${shape}.svg?${searchParams.join('&')}`;},_getShapeData(target=this.$target[0]){const defaultData={shape:'',colors:this._getDefaultColors(),flip:[],};const json=target.dataset.oeShapeData;return json?Object.assign(defaultData,JSON.parse(json.replace(/'/g,'"'))):defaultData;},_getDefaultColors(){const $shapeContainer=this.$target.find('> .o_we_shape').clone().addClass('d-none').appendTo(document.body);const shapeContainer=$shapeContainer[0];$shapeContainer.css('background-image','');const shapeSrc=shapeContainer&&getBgImageURL(shapeContainer);$shapeContainer.remove();if(!shapeSrc){return{};}
const url=new URL(shapeSrc,window.location.origin);return Object.fromEntries(url.searchParams.entries());},_toggleShape(){if(this._getShapeData().shape){return this._handlePreviewState(false,()=>({shape:''}));}else{const target=this.$target[0];const previousSibling=target.previousElementSibling;const[shapeWidget]=this._requestUserValueWidgets('bg_shape_opt');const possibleShapes=shapeWidget.getMethodsParams('shape').possibleValues;let shapeToSelect;if(previousSibling){const previousShape=this._getShapeData(previousSibling).shape;shapeToSelect=possibleShapes.find((shape,i)=>{return possibleShapes[i-1]===previousShape;});}
if(!shapeToSelect){shapeToSelect=possibleShapes[1];}
return this._handlePreviewState(false,()=>({shape:shapeToSelect}));}},});registry.BackgroundPosition=SnippetOptionWidget.extend({xmlDependencies:['/web_editor/static/src/xml/editor.xml'],start:function(){this._super.apply(this,arguments);this._initOverlay();$(window).on('resize.bgposition',()=>this._dimensionOverlay());},destroy:function(){this._toggleBgOverlay(false);$(window).off('.bgposition');this._super.apply(this,arguments);},backgroundType:function(previewMode,widgetValue,params){this.$target.toggleClass('o_bg_img_opt_repeat',widgetValue==='repeat-pattern');this.$target.css('background-position','');this.$target.css('background-size','');},backgroundPositionOverlay:async function(previewMode,widgetValue,params){await new Promise(resolve=>{this.img=document.createElement('img');this.img.addEventListener('load',()=>resolve());this.img.src=getBgImageURL(this.$target[0]);});const position=this.$target.css('background-position').split(' ').map(v=>parseInt(v));const delta=this._getBackgroundDelta();this.originalPosition={left:position[0],top:position[1],};this.currentPosition={left:position[0]/100*delta.x||0,top:position[1]/100*delta.y||0,};const rect=this.$target[0].getBoundingClientRect();const viewportTop=$(window).scrollTop();const viewportBottom=viewportTop+$(window).height();const visibleHeight=rect.top<viewportTop?Math.max(0,Math.min(viewportBottom,rect.bottom)-viewportTop):rect.top<viewportBottom?Math.min(viewportBottom,rect.bottom)-rect.top:0;if(visibleHeight<200){await scrollTo(this.$target[0],{extraOffset:50});}
this._toggleBgOverlay(true);},selectStyle:function(previewMode,widgetValue,params){if(params.cssProperty==='background-size'&&!this.$target.hasClass('o_bg_img_opt_repeat')){return;}
this._super(...arguments);},_computeVisibility:function(){return this._super(...arguments)&&!!getBgImageURL(this.$target[0]);},_computeWidgetState:function(methodName,params){if(methodName==='backgroundType'){return this.$target.css('background-repeat')==='repeat'?'repeat-pattern':'cover';}
return this._super(...arguments);},_initOverlay:function(){this.$backgroundOverlay=$(qweb.render('web_editor.background_position_overlay'));this.$overlayContent=this.$backgroundOverlay.find('.o_we_overlay_content');this.$overlayBackground=this.$overlayContent.find('.o_overlay_background');this.$backgroundOverlay.on('click','.o_btn_apply',()=>{this.$target.css('background-position',this.$bgDragger.css('background-position'));this._toggleBgOverlay(false);});this.$backgroundOverlay.on('click','.o_btn_discard',()=>{this._toggleBgOverlay(false);});this.$backgroundOverlay.insertAfter(this.$overlay);},_dimensionOverlay:function(){if(!this.$backgroundOverlay.is('.oe_active')){return;}
const $wrapwrap=$('#wrapwrap');const targetOffset=this.$target.offset();this.$backgroundOverlay.css({width:$wrapwrap.innerWidth(),height:$wrapwrap.innerHeight(),});this.$overlayContent.offset(targetOffset);this.$bgDragger.css({width:`${this.$target.innerWidth()}px`,height:`${this.$target.innerHeight()}px`,});const topPos=Math.max(0,$(window).scrollTop()-this.$target.offset().top);this.$overlayContent.find('.o_we_overlay_buttons').css('top',`${topPos}px`);},_toggleBgOverlay:function(activate){if(!this.$backgroundOverlay||this.$backgroundOverlay.is('.oe_active')===activate){return;}
if(!activate){this.$backgroundOverlay.removeClass('oe_active');this.trigger_up('unblock_preview_overlays');this.trigger_up('activate_snippet',{$snippet:this.$target});$(document).off('click.bgposition');return;}
this.trigger_up('hide_overlay');this.trigger_up('activate_snippet',{$snippet:this.$target,previewMode:true,});this.trigger_up('block_preview_overlays');this.$bgDragger=this.$target.clone().empty();this.$bgDragger.removeClass('o_editable');this.$bgDragger.css('background-attachment',this.$target.css('background-attachment'));this.$bgDragger.on('mousedown',this._onDragBackgroundStart.bind(this));this.$bgDragger.tooltip({title:'Click and drag the background to adjust its position!',trigger:'manual',container:this.$backgroundOverlay});this.$overlayBackground.empty().append(this.$bgDragger);this.$backgroundOverlay.addClass('oe_active');this._dimensionOverlay();this.$bgDragger.tooltip('show');window.setTimeout(()=>$(document).on('click.bgposition',this._onDocumentClicked.bind(this)),0);},_getBackgroundDelta:function(){const bgSize=this.$target.css('background-size');if(bgSize!=='cover'){let[width,height]=bgSize.split(' ');if(width==='auto'&&(height==='auto'||!height)){return{x:this.$target.outerWidth()-this.img.naturalWidth,y:this.$target.outerHeight()-this.img.naturalHeight,};}
[width,height]=[parseInt(width),parseInt(height)];return{x:this.$target.outerWidth()-(width||(height*this.img.naturalWidth/this.img.naturalHeight)),y:this.$target.outerHeight()-(height||(width*this.img.naturalHeight/this.img.naturalWidth)),};}
const renderRatio=Math.max(this.$target.outerWidth()/this.img.naturalWidth,this.$target.outerHeight()/this.img.naturalHeight);return{x:this.$target.outerWidth()-Math.round(renderRatio*this.img.naturalWidth),y:this.$target.outerHeight()-Math.round(renderRatio*this.img.naturalHeight),};},_onDragBackgroundStart:function(ev){ev.preventDefault();this.$bgDragger.addClass('o_we_grabbing');const $document=$(this.ownerDocument);$document.on('mousemove.bgposition',this._onDragBackgroundMove.bind(this));$document.one('mouseup',()=>{this.$bgDragger.removeClass('o_we_grabbing');$document.off('mousemove.bgposition');});},_onDragBackgroundMove:function(ev){ev.preventDefault();const delta=this._getBackgroundDelta();this.currentPosition.left=clamp(this.currentPosition.left+ev.originalEvent.movementX,[0,delta.x]);this.currentPosition.top=clamp(this.currentPosition.top+ev.originalEvent.movementY,[0,delta.y]);const percentPosition={left:this.currentPosition.left/delta.x*100,top:this.currentPosition.top/delta.y*100,};percentPosition.left=isFinite(percentPosition.left)?percentPosition.left:this.originalPosition.left;percentPosition.top=isFinite(percentPosition.top)?percentPosition.top:this.originalPosition.top;this.$bgDragger.css('background-position',`${percentPosition.left}% ${percentPosition.top}%`);function clamp(val,bounds){bounds=bounds.sort();return Math.max(bounds[0],Math.min(val,bounds[1]));}},_onDocumentClicked:function(ev){if(!$(ev.target).closest('.o_we_background_position_overlay').length){this._toggleBgOverlay(false);}},});registry.ColoredLevelBackground=registry.BackgroundToggler.extend({start:function(){this._markColorLevel();return this._super(...arguments);},onBuilt:function(){this._markColorLevel();},_markColorLevel:function(){this.$target.addClass('o_colored_level');},});registry.many2one=SnippetOptionWidget.extend({xmlDependencies:['/web_editor/static/src/xml/snippets.xml'],start:function(){var self=this;this.trigger_up('getRecordInfo',_.extend(this.options,{callback:function(recordInfo){_.defaults(self.options,recordInfo);},}));this.Model=this.$target.data('oe-many2one-model');this.ID=+this.$target.data('oe-many2one-id');this.$btn=$(qweb.render('web_editor.many2one.button')).prependTo(this.$el);this.$ul=this.$btn.find('ul');this.$search=this.$ul.find('li:first');this.$search.find('input').on('mousedown click mouseup keyup keydown',function(e){e.stopPropagation();});setTimeout(function(){self.$btn.find('a').on('click',function(e){self._clear();});},0);this.$search.find('input').focus().on('keyup',function(e){self.$overlay.removeClass('o_overlay_hidden');self._findExisting($(this).val());});this.$ul.on('click','li:not(:first) a',function(e){self._selectRecord($(e.currentTarget));});return this._super.apply(this,arguments);},onFocus:function(){this.$target.attr('contentEditable','false');this._clear();},_clear:function(){var self=this;this.$search.siblings().remove();self.$search.find('input').val('');setTimeout(function(){self.$search.find('input').focus();},0);},_findExisting:function(name){var self=this;var domain=[];if(!name||!name.length){self.$search.siblings().remove();return;}
if(isNaN(+name)){if(this.Model!=='res.partner'){domain.push(['name','ilike',name]);}else{domain.push('|',['name','ilike',name],['email','ilike',name]);}}else{domain.push(['id','=',name]);}
return this._rpc({model:this.Model,method:'search_read',args:[domain,this.Model==='res.partner'?['name','display_name','city','country_id']:['name','display_name']],kwargs:{order:[{name:'name',asc:false}],limit:5,context:this.options.context,},}).then(function(result){self.$search.siblings().remove();self.$search.after(qweb.render('web_editor.many2one.search',{contacts:result}));});},_selectRecord:function($li){var self=this;this.ID=+$li.data('id');this.$target.attr('data-oe-many2one-id',this.ID).data('oe-many2one-id',this.ID);this.trigger_up('request_history_undo_record',{$target:this.$target});this.$target.trigger('content_changed');if(self.$target.data('oe-type')==='contact'){$('[data-oe-contact-options]').filter('[data-oe-model="'+self.$target.data('oe-model')+'"]').filter('[data-oe-id="'+self.$target.data('oe-id')+'"]').filter('[data-oe-field="'+self.$target.data('oe-field')+'"]').filter('[data-oe-contact-options!="'+self.$target.data('oe-contact-options')+'"]').add(self.$target).attr('data-oe-many2one-id',self.ID).data('oe-many2one-id',self.ID).each(function(){var $node=$(this);var options=$node.data('oe-contact-options');self._rpc({model:'ir.qweb.field.contact',method:'get_record_to_html',args:[[self.ID]],kwargs:{options:options,context:self.options.context,},}).then(function(html){$node.html(html);});});}else{self.$target.text($li.data('name'));}
this._clear();}});registry.VersionControl=SnippetOptionWidget.extend({xmlDependencies:['/web_editor/static/src/xml/snippets.xml'],start:function(){this.trigger_up('get_snippet_versions',{snippetName:this.$target[0].dataset.snippet,onSuccess:snippetVersions=>{const isUpToDate=snippetVersions&&['vjs','vcss','vxml'].every(key=>this.$target[0].dataset[key]===snippetVersions[key]);if(!isUpToDate){this.$el.prepend(qweb.render('web_editor.outdated_block_message'));}},});return this._super(...arguments);},});registry.SnippetSave=SnippetOptionWidget.extend({xmlDependencies:['/web_editor/static/src/xml/editor.xml'],isTopOption:true,saveSnippet:function(previewMode,widgetValue,params){return new Promise(resolve=>{const dialog=new Dialog(this,{title:_t("Save Your Block"),size:'small',$content:$(qweb.render('web_editor.dialog.save_snippet',{currentSnippetName:_.str.sprintf(_t("Custom %s"),this.data.snippetName),})),buttons:[{text:_t("Save"),classes:'btn-primary',close:true,click:async()=>{const save=await new Promise(resolve=>{Dialog.confirm(this,_t("To save a snippet, we need to save all your previous modifications and reload the page."),{buttons:[{text:_t("Save and Reload"),classes:'btn-primary',close:true,click:()=>resolve(true),},{text:_t("Cancel"),close:true,click:()=>resolve(false),}]});});if(!save){return;}
const snippetKey=this.$target[0].dataset.snippet;let thumbnailURL;this.trigger_up('snippet_thumbnail_url_request',{key:snippetKey,onSuccess:url=>thumbnailURL=url,});let context;this.trigger_up('context_get',{callback:ctx=>context=ctx,});this.trigger_up('request_save',{reloadEditor:true,onSuccess:async()=>{const snippetName=dialog.el.querySelector('.o_we_snippet_name_input').value;const targetCopyEl=this.$target[0].cloneNode(true);delete targetCopyEl.dataset.name;await rpc.query({model:'ir.ui.view',method:'save_snippet',kwargs:{'name':snippetName,'arch':targetCopyEl.outerHTML,'template_key':this.options.snippets,'snippet_key':snippetKey,'thumbnail_url':thumbnailURL,'context':context,},});},});},},{text:_t("Discard"),close:true,}],}).open();dialog.on('closed',this,()=>resolve());});},_computeVisibility(){return this.$target[0].hasAttribute('data-snippet');},});registry.DynamicSvg=SnippetOptionWidget.extend({start(){this.$target.on('image_changed.DynamicSvg',this._onImageChanged.bind(this));return this._super(...arguments);},destroy(){this.$target.off('.DynamicSvg');return this._super(...arguments);},async color(previewMode,widgetValue,params){const target=this.$target[0];switch(previewMode){case true:this.previousSrc=target.getAttribute('src');break;case'reset':target.src=this.previousSrc;return;}
const newURL=new URL(target.src,window.location.origin);newURL.searchParams.set('c1',normalizeColor(widgetValue));const src=newURL.pathname+newURL.search;await loadImage(src);target.src=src;if(!previewMode){this.previousSrc=src;}},_computeWidgetState(methodName,params){switch(methodName){case'color':return new URL(this.$target[0].src,window.location.origin).searchParams.get('c1');}
return this._super(...arguments);},_computeVisibility(methodName,params){return this.$target.is("img[src^='/web_editor/shape/']");},_onImageChanged(methodName,params){return this.updateUI();},});return{SnippetOptionWidget:SnippetOptionWidget,snippetOptionRegistry:registry,NULL_ID:NULL_ID,UserValueWidget:UserValueWidget,userValueWidgetsRegistry:userValueWidgetsRegistry,UnitUserValueWidget:UnitUserValueWidget,addTitleAndAllowedAttributes:_addTitleAndAllowedAttributes,buildElement:_buildElement,buildTitleElement:_buildTitleElement,buildRowElement:_buildRowElement,buildCollapseElement:_buildCollapseElement,Class:SnippetOptionWidget,registry:registry,};});;

/* /web_editor/static/lib/jabberwock/jabberwock.js defined in bundle 'web_editor.assets_wysiwyg' */
;

/* /web_editor/static/src/js/wysiwyg/wysiwyg_translate_attributes.js defined in bundle 'web_editor.assets_wysiwyg' */
;

/* /web_editor/static/src/js/wysiwyg/wysiwyg.js defined in bundle 'web_editor.assets_wysiwyg' */
odoo.define('web_editor.wysiwyg',function(require){'use strict';var Widget=require('web.Widget');var SummernoteManager=require('web_editor.rte.summernote');var summernoteCustomColors=require('web_editor.rte.summernote_custom_colors');var id=0;var Wysiwyg=Widget.extend({xmlDependencies:[],defaultOptions:{'focus':false,'toolbar':[['style',['style']],['font',['bold','italic','underline','clear']],['fontsize',['fontsize']],['color',['color']],['para',['ul','ol','paragraph']],['table',['table']],['insert',['link','picture']],['history',['undo','redo']],],'styleWithSpan':false,'inlinemedia':['p'],'lang':'odoo','colors':summernoteCustomColors,recordInfo:{context:{},},},init:function(parent,options){this._super.apply(this,arguments);this.id=++id;this.options=options;},willStart:function(){this._summernoteManager=new SummernoteManager(this);this.$target=this.$el;return this._super();},start:function(){this.$target.wrap('<odoo-wysiwyg-container>');this.$el=this.$target.parent();var options=this._editorOptions();this.$target.summernote(options);this.$editor=this.$('.note-editable:first');this.$editor.data('wysiwyg',this);this.$editor.data('oe-model',options.recordInfo.res_model);this.$editor.data('oe-id',options.recordInfo.res_id);$(document).on('mousedown',this._blur);this._value=this.$target.html()||this.$target.val();return this._super.apply(this,arguments);},destroy:function(){$(document).off('mousedown',this._blur);if(this.$target&&this.$target.is('textarea')&&this.$target.next('.note-editor').length){this.$target.summernote('destroy');}
this._super();},getEditable:function(){return this.$editor;},isDirty:function(){return this._value!==(this.$editor.html()||this.$editor.val());},focus:function(){console.log('focus');},getValue:function(options){var $editable=options&&options.$layout||this.$editor.clone();$editable.find('[contenteditable]').removeAttr('contenteditable');$editable.find('[class=""]').removeAttr('class');$editable.find('[style=""]').removeAttr('style');$editable.find('[title=""]').removeAttr('title');$editable.find('[alt=""]').removeAttr('alt');$editable.find('[data-original-title=""]').removeAttr('data-original-title');if(!options||!options['style-inline']){$editable.find('a.o_image, span.fa, i.fa').html('');}
$editable.find('[aria-describedby]').removeAttr('aria-describedby').removeAttr('data-original-title');return $editable.html();},save:function(options){var isDirty=this.isDirty();var html=this.getValue(options);if(this.$target.is('textarea')){this.$target.val(html);}else{this.$target.html(html);}
return Promise.resolve({isDirty:isDirty,html:html});},saveModifiedImages:function($editable){return this._summernoteManager.saveModifiedImages($editable);},setValue:function(value,options){if(this.$editor.is('textarea')){this.$target.val(value);}else{this.$target.html(value);}
this.$editor.html(value);},_editorOptions:function(){var self=this;var options=Object.assign({},$.summernote.options,this.defaultOptions,this.options);if(this.options.generateOptions){options=this.options.generateOptions(options);}
options.airPopover=options.toolbar;options.onChange=function(html,$editable){$editable.trigger('content_changed');self.trigger_up('wysiwyg_change');};options.onUpload=function(attachments){self.trigger_up('wysiwyg_attachment',attachments);};options.onFocus=function(){self.trigger_up('wysiwyg_focus');};options.onBlur=function(){self.trigger_up('wysiwyg_blur');};return options;},});Wysiwyg.getRange=function(node){var range=$.summernote.core.range.create();return range&&{sc:range.sc,so:range.so,ec:range.ec,eo:range.eo,};};Wysiwyg.setRange=function(startNode,startOffset,endNode,endOffset){$(startNode).focus();if(endNode){$.summernote.core.range.create(startNode,startOffset,endNode,endOffset).select();}else{$.summernote.core.range.create(startNode,startOffset).select();}
$(startNode.tagName?startNode:startNode.parentNode).trigger('wysiwyg.range');};Wysiwyg.setRangeFromNode=function(node,options){var last=node;while(last.lastChild){last=last.lastChild;}
var first=node;while(first.firstChild){first=first.firstChild;}
if(options&&options.begin&&!options.end){Wysiwyg.setRange(first,0);}else if(options&&!options.begin&&options.end){Wysiwyg.setRange(last,last.textContent.length);}else{Wysiwyg.setRange(first,0,last,last.tagName?last.childNodes.length:last.textContent.length);}};return Wysiwyg;});odoo.define('web_editor.widget',function(require){'use strict';return{Dialog:require('wysiwyg.widgets.Dialog'),MediaDialog:require('wysiwyg.widgets.MediaDialog'),LinkDialog:require('wysiwyg.widgets.LinkDialog'),};});;

/* /web_editor/static/src/js/wysiwyg/wysiwyg_snippets.js defined in bundle 'web_editor.assets_wysiwyg' */
odoo.define('web_editor.wysiwyg.snippets',function(require){'use strict';var editor=require('web_editor.editor');var Wysiwyg=require('web_editor.wysiwyg');Wysiwyg.include({init:function(parent,options){this._super.apply(this,arguments);this.Editor=editor.Class;if(!this.options.toolbarHandler){this.options.toolbarHandler=$('#web_editor-top-edit');}},start:async function(){if(this.options.snippets){var self=this;this.editor=new(this.Editor)(this,this.options);this.$editor=this.editor.rte.editable();const $body=this.$editor[0]?this.$editor[0].ownerDocument.body:document.body;await this.editor.prependTo($body);this._relocateEditorBar();this.$el.on('content_changed',function(e){self.trigger_up('wysiwyg_change');});}else{return this._super();}},_relocateEditorBar:function(){if(!this.options.toolbarHandler.length){this.options.toolbarHandler=$('.o_we_snippet_text_tools');}
this.options.toolbarHandler.append(this.editor.$el);if(this.editor.snippetsMenu&&!this.editor.snippetsMenu.$el.has(this.options.toolbarHandler).length){this.editor.snippetsMenu.$el.insertAfter(this.options.toolbarHandler);this.editor.snippetsMenu.$snippetEditorArea.insertAfter(this.editor.snippetsMenu.$el);}},});});;

/* /web_editor/static/src/js/wysiwyg/wysiwyg_iframe.js defined in bundle 'web_editor.assets_wysiwyg' */
odoo.define('web_editor.wysiwyg.iframe',function(require){'use strict';var Wysiwyg=require('web_editor.wysiwyg');var ajax=require('web.ajax');var core=require('web.core');var config=require('web.config');var qweb=core.qweb;var promiseCommon;var promiseWysiwyg;Wysiwyg.include({init:function(parent,options){this._super.apply(this,arguments);if(this.options.inIframe){this._onUpdateIframeId='onLoad_'+this.id;}
this.__extraAssetsForIframe=[];},willStart:function(){if(!this.options.inIframe){return this._super();}
var defAsset;if(this.options.iframeCssAssets){defAsset=ajax.loadAsset(this.options.iframeCssAssets);}else{defAsset=Promise.resolve({cssLibs:[],cssContents:[]});}
promiseWysiwyg=promiseWysiwyg||ajax.loadAsset('web_editor.wysiwyg_iframe_editor_assets');this.defAsset=Promise.all([promiseWysiwyg,defAsset]);this.$target=this.$el;return this.defAsset.then(this._loadIframe.bind(this)).then(this._super.bind(this));},_loadIframe:function(){var self=this;this.$iframe=$('<iframe class="wysiwyg_iframe">').css({'min-height':'55vh',width:'100%'});var avoidDoubleLoad=0;var def=new Promise(function(resolve){window.top[self._onUpdateIframeId]=function(Editor,_avoidDoubleLoad){if(_avoidDoubleLoad!==avoidDoubleLoad){console.warn('Wysiwyg iframe double load detected');return;}
delete window.top[self._onUpdateIframeId];var $iframeTarget=self.$iframe.contents().find('#iframe_target');$iframeTarget.attr("isMobile",config.device.isMobile);$iframeTarget.find('.o_editable').html(self.$target.val());self.options.toolbarHandler=$('#web_editor-top-edit',self.$iframe[0].contentWindow.document);$(qweb.render('web_editor.FieldTextHtml.fullscreen')).appendTo(self.options.toolbarHandler).on('click','.o_fullscreen',function(){$("body").toggleClass("o_field_widgetTextHtml_fullscreen");var full=$("body").hasClass("o_field_widgetTextHtml_fullscreen");self.$iframe.parents().toggleClass('o_form_fullscreen_ancestor',full);$(window).trigger("resize");});self.Editor=Editor;resolve();};});this.$iframe.data('loadDef',def);this.$iframe.on('load',function onLoad(ev){var _avoidDoubleLoad=++avoidDoubleLoad;self.defAsset.then(function(assets){if(_avoidDoubleLoad!==avoidDoubleLoad){console.warn('Wysiwyg immediate iframe double load detected');return;}
var iframeContent=qweb.render('wysiwyg.iframeContent',{assets:assets.concat(self.__extraAssetsForIframe),updateIframeId:self._onUpdateIframeId,avoidDoubleLoad:_avoidDoubleLoad});self.$iframe[0].contentWindow.document.open("text/html","replace").write(iframeContent);});});this.$iframe.insertAfter(this.$target);return def;},});});;

/* /web_unsplash/static/src/js/unsplashapi.js defined in bundle 'web_editor.assets_wysiwyg' */
odoo.define('unsplash.api',function(require){'use strict';var Class=require('web.Class');var rpc=require('web.rpc');var Mixins=require('web.mixins');var ServicesMixin=require('web.ServicesMixin');var UnsplashCore=Class.extend(Mixins.EventDispatcherMixin,ServicesMixin,{init:function(parent){Mixins.EventDispatcherMixin.init.call(this,arguments);this.setParent(parent);this._cache={};this.clientId=false;},getImages:function(query,pageSize){var from=0;var to=pageSize;var cachedData=this._cache[query];if(cachedData&&(cachedData.images.length>=to||(cachedData.totalImages!==0&&cachedData.totalImages<to))){return Promise.resolve({images:cachedData.images.slice(from,to),isMaxed:to>cachedData.totalImages});}
return this._fetchImages(query).then(function(cachedData){return{images:cachedData.images.slice(from,to),isMaxed:to>cachedData.totalImages};});},_fetchImages:function(query){if(!this._cache[query]){this._cache[query]={images:[],maxPages:0,totalImages:0,pageCached:0};}
var cachedData=this._cache[query];var payload={query:query,page:cachedData.pageCached+1,per_page:30,};return this._rpc({route:'/web_unsplash/fetch_images',params:payload,}).then(function(result){if(result.error){return Promise.reject(result.error);}
cachedData.pageCached++;cachedData.images.push.apply(cachedData.images,result.results);cachedData.maxPages=result.total_pages;cachedData.totalImages=result.total;return cachedData;});},});return UnsplashCore;});;

/* /web_unsplash/static/src/js/unsplash_image_widget.js defined in bundle 'web_editor.assets_wysiwyg' */
odoo.define('web_unsplash.image_widgets',function(require){'use strict';var core=require('web.core');var UnsplashAPI=require('unsplash.api');var widgetsMedia=require('wysiwyg.widgets.media');var unsplashAPI=null;const originalEvents=widgetsMedia.ImageWidget.prototype.events;const clickHandler=originalEvents['click .o_existing_attachment_cell'];if(!clickHandler){throw new Error(`Couldn't find a handler for o_existing_attachment_cell clicks.
The unsplash image widget needs to prevent this handler from executing on unsplash attachments.`);}
_.extend(originalEvents,{'click .o_existing_attachment_cell:not(.o_unsplash_attachment_cell)':clickHandler,});delete originalEvents['click .o_existing_attachment_cell'];widgetsMedia.ImageWidget.include({xmlDependencies:widgetsMedia.ImageWidget.prototype.xmlDependencies.concat(['/web_unsplash/static/src/xml/unsplash_image_widget.xml']),events:_.extend({},widgetsMedia.ImageWidget.prototype.events,{'click .o_unsplash_attachment_cell[data-imgid]':'_onUnsplashImgClick','click button.save_unsplash':'_onSaveUnsplashCredentials',}),init:function(){this._super.apply(this,arguments);this._unsplash={selectedImages:{},isMaxed:false,query:false,error:false,records:[],};if(unsplashAPI===null){this.unsplashAPI=new UnsplashAPI(this);unsplashAPI=this.unsplashAPI;}else{this.unsplashAPI=unsplashAPI;this.unsplashAPI.setParent(this);}},destroy:function(){this.unsplashAPI.setParent(undefined);this._super.apply(this,arguments);},_save:async function(){const _super=this._super;if(Object.keys(this._unsplash.selectedImages).length){this.saved=true;const images=await this._rpc({route:'/web_unsplash/attachment/add',params:{unsplashurls:this._unsplash.selectedImages,res_model:this.options.res_model,res_id:this.options.res_id,query:this._unsplash.query,},});this.attachments.push(...images);this.selectedAttachments.push(...images);}
return _super.apply(this,arguments);},search:async function(needle){var self=this;await this._super(...arguments);this._unsplash.query=needle;if(!needle){this._unsplash.records=[];return;}
await this.unsplashAPI.getImages(needle,this.numberOfAttachmentsToDisplay).then(function(res){self._unsplash.isMaxed=res.isMaxed;self._unsplash.records=res.images;self._unsplash.error=false;},function(err){self._unsplash.error=err;});},hasContent(){if(this.searchService==='all'){return this._super(...arguments)||(this.unsplashRecords&&this.unsplashRecords.length);}else if(this.searchService==='unsplash'){return(this.unsplashRecords&&this.unsplashRecords.length);}
return this._super(...arguments);},_highlightSelected:function(){this._super.apply(this,arguments);const $select=this.$('.o_unsplash_attachment_cell[data-imgid]').filter((i,el)=>{return $(el).data('imgid')in this._unsplash.selectedImages;}).addClass('o_we_attachment_selected');return $select;},_loadMoreImages:function(forceSearch){if(!this.$('.o_we_search').val()){return this._super(forceSearch);}
this.numberOfAttachmentsToDisplay+=10;this.search(this.$('.o_we_search').val()).then(()=>this._renderThumbnails());},_renderThumbnails:function(){this._super(...arguments);this.$('.unsplash_error').empty();if(!['all','unsplash'].includes(this.searchService)){return;}
if(this._unsplash.query&&this._unsplash.error){this.$('.unsplash_error').html(core.qweb.render('web_unsplash.dialog.error.content',{status:this._unsplash.error,}));return;}
if(['all','unsplash'].includes(this.searchService)&&this._unsplash.query&&!this._unsplash.isMaxed){this.$('.o_load_more').removeClass('d-none');this.$('.o_load_done_msg').addClass('d-none');}},_renderExisting:function(attachments){this.unsplashRecords=this._unsplash.records.map(record=>{const url=new URL(record.urls.regular);url.searchParams.set('h',2*this.MIN_ROW_HEIGHT);url.searchParams.delete('w');return Object.assign({},record,{url:url.toString(),});});return this._super(...arguments);},_selectAttachement:function(attachment,save){if(!this.options.multiImages){this._unsplash.selectedImages={};}
this._super(...arguments);},_onSaveUnsplashCredentials:function(){var self=this;var key=this.$('#accessKeyInput').val().trim();var appId=this.$('#appIdInput').val().trim();this.$('#accessKeyInput').toggleClass('is-invalid',!key);this.$('#appIdInput').toggleClass('is-invalid',!appId);if(key&&appId){if(!this.$el.find('.is-invalid').length){this._rpc({route:'/web_unsplash/save_unsplash',params:{key:key,appId:appId},}).then(function(){self.unsplashAPI.clientId=key;self._unsplash.error=false;self.search(self._unsplash.query).then(()=>self._renderThumbnails());});}}},_onUnsplashImgClick:function(ev){if(this.saved){return;}
const{imgid,url,downloadUrl,description}=ev.currentTarget.dataset;if(!this.options.multiImages){this._unsplash.selectedImages={};this.selectedAttachments=[];}
if(imgid in this._unsplash.selectedImages){delete this._unsplash.selectedImages[imgid];}else{const _1920Url=new URL(url);_1920Url.searchParams.set('w','1920');this._unsplash.selectedImages[imgid]={url:_1920Url.href,download_url:downloadUrl,description:description};}
this._highlightSelected();if(!this.options.multiImages){this.trigger_up('save_request');}},});});;

/* /odex25_web_editor/static/src/js/wysiwyg.js defined in bundle 'web_editor.assets_wysiwyg' */
odoo.define("odex25_web_editor.summernote",function(require){"use strict";require("summernote/summernote");const Wysiwyg=require("web_editor.wysiwyg");var tmpl=$.summernote.renderer.getTemplate();$.summernote.addPlugin({buttons:{text_ltr:function(lang,options){return tmpl.iconButton("fa fa-angle-double-right",{event:"text_ltr",value:"text_ltr",title:"Change Text direction to Left-To-Right",});},text_rtl:function(lang,options){return tmpl.iconButton("fa fa-angle-double-left",{event:"text_rtl",value:"text_rtl",title:"Change Text direction to Right-To-Left",});},},events:{text_rtl:function(ev,editor,layoutInfo){const editable=layoutInfo.editable();editable.attr("dir","rtl");},text_ltr:function(ev,editor,layoutInfo){const editable=layoutInfo.editable();editable.attr("dir","ltr");},},});Wysiwyg.include({init:function(parent,options){this._super.apply(this,arguments);if(this.id===1)this.defaultOptions["toolbar"].push(["direction",["text_rtl","text_ltr"]]);},});return $.summernote;});;

/* /odex25_web_editor/static/src/js/editor.js defined in bundle 'web_editor.assets_wysiwyg' */
odoo.define("odex25_web_editor.editor",function(require){const EditorMenuBar=require("web_editor.editor");EditorMenuBar.Class.include({_getDefaultConfig:function($editable){let res=this._super.apply(this,arguments);res["airPopover"].push(["direction",["text_rtl","text_ltr"]]);return res;},});});