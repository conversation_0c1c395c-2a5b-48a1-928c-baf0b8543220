
/* <inline asset> defined in bundle 'web_editor.assets_wysiwyg' */
@charset "UTF-8"; 

/* /web_editor/static/lib/cropperjs/cropper.css defined in bundle 'web_editor.assets_wysiwyg' */
 .cropper-container{direction: ltr; font-size: 0; line-height: 0; position: relative; -ms-touch-action: none; touch-action: none; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none; user-select: none;}.cropper-container img{display: block; height: 100%; image-orientation: 0deg; max-height: none !important; max-width: none !important; min-height: 0 !important; min-width: 0 !important; width: 100%;}.cropper-wrap-box, .cropper-canvas, .cropper-drag-box, .cropper-crop-box, .cropper-modal{bottom: 0; left: 0; position: absolute; right: 0; top: 0;}.cropper-wrap-box, .cropper-canvas{overflow: hidden;}.cropper-drag-box{background-color: #fff; opacity: 0;}.cropper-modal{background-color: #000; opacity: 0.5;}.cropper-view-box{display: block; height: 100%; outline: 1px solid #39f; outline-color: rgba(51, 153, 255, 0.75); overflow: hidden; width: 100%;}.cropper-dashed{border: 0 dashed #eee; display: block; opacity: 0.5; position: absolute;}.cropper-dashed.dashed-h{border-bottom-width: 1px; border-top-width: 1px; height: calc(100% / 3); left: 0; top: calc(100% / 3); width: 100%;}.cropper-dashed.dashed-v{border-left-width: 1px; border-right-width: 1px; height: 100%; left: calc(100% / 3); top: 0; width: calc(100% / 3);}.cropper-center{display: block; height: 0; left: 50%; opacity: 0.75; position: absolute; top: 50%; width: 0;}.cropper-center::before, .cropper-center::after{background-color: #eee; content: ' '; display: block; position: absolute;}.cropper-center::before{height: 1px; left: -3px; top: 0; width: 7px;}.cropper-center::after{height: 7px; left: 0; top: -3px; width: 1px;}.cropper-face, .cropper-line, .cropper-point{display: block; height: 100%; opacity: 0.1; position: absolute; width: 100%;}.cropper-face{background-color: #fff; left: 0; top: 0;}.cropper-line{background-color: #39f;}.cropper-line.line-e{cursor: ew-resize; right: -3px; top: 0; width: 5px;}.cropper-line.line-n{cursor: ns-resize; height: 5px; left: 0; top: -3px;}.cropper-line.line-w{cursor: ew-resize; left: -3px; top: 0; width: 5px;}.cropper-line.line-s{bottom: -3px; cursor: ns-resize; height: 5px; left: 0;}.cropper-point{background-color: #39f; height: 5px; opacity: 0.75; width: 5px;}.cropper-point.point-e{cursor: ew-resize; margin-top: -3px; right: -3px; top: 50%;}.cropper-point.point-n{cursor: ns-resize; left: 50%; margin-left: -3px; top: -3px;}.cropper-point.point-w{cursor: ew-resize; left: -3px; margin-top: -3px; top: 50%;}.cropper-point.point-s{bottom: -3px; cursor: s-resize; left: 50%; margin-left: -3px;}.cropper-point.point-ne{cursor: nesw-resize; right: -3px; top: -3px;}.cropper-point.point-nw{cursor: nwse-resize; left: -3px; top: -3px;}.cropper-point.point-sw{bottom: -3px; cursor: nesw-resize; left: -3px;}.cropper-point.point-se{bottom: -3px; cursor: nwse-resize; height: 20px; opacity: 1; right: -3px; width: 20px;}@media (min-width: 768px){.cropper-point.point-se{height: 15px; width: 15px;}}@media (min-width: 992px){.cropper-point.point-se{height: 10px; width: 10px;}}@media (min-width: 1200px){.cropper-point.point-se{height: 5px; opacity: 0.75; width: 5px;}}.cropper-point.point-se::before{background-color: #39f; bottom: -50%; content: ' '; display: block; height: 200%; opacity: 0; position: absolute; right: -50%; width: 200%;}.cropper-invisible{opacity: 0;}.cropper-bg{background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQAQMAAAAlPW0iAAAAA3NCSVQICAjb4U/gAAAABlBMVEXMzMz////TjRV2AAAACXBIWXMAAArrAAAK6wGCiw1aAAAAHHRFWHRTb2Z0d2FyZQBBZG9iZSBGaXJld29ya3MgQ1M26LyyjAAAABFJREFUCJlj+M/AgBVhF/0PAH6/D/HkDxOGAAAAAElFTkSuQmCC');}.cropper-hide{display: block; height: 0; position: absolute; width: 0;}.cropper-hidden{display: none !important;}.cropper-move{cursor: move;}.cropper-crop{cursor: crosshair;}.cropper-disabled .cropper-drag-box, .cropper-disabled .cropper-face, .cropper-disabled .cropper-line, .cropper-disabled .cropper-point{cursor: not-allowed;}

/* /web/static/lib/bootstrap/scss/_functions.scss defined in bundle 'web_editor.assets_wysiwyg' */
 

/* /web/static/lib/bootstrap/scss/_mixins.scss defined in bundle 'web_editor.assets_wysiwyg' */
 

/* /web/static/src/scss/bs_mixins_overrides.scss defined in bundle 'web_editor.assets_wysiwyg' */
 

/* /web/static/src/scss/utils.scss defined in bundle 'web_editor.assets_wysiwyg' */
 body .modal .o_select_media_dialog .modal-body .o_we_images > .o_existing_attachment_cell .o_we_media_dialog_img_wrapper, #oe_snippets > .o_we_customize_panel .o_we_so_color_palette.o_we_user_value_widget .o_we_color_preview, .o_we_cc_preview_wrapper, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name="transparent_grayscale"] .o_we_color_btn, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name="theme"] .o_we_color_btn, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name="reset"] .o_we_color_btn, we-select.o_we_shape_menu we-button[data-shape] .o_we_shape_btn_content{position: relative; z-index: 0;}body .modal .o_select_media_dialog .modal-body .o_we_images > .o_existing_attachment_cell .o_we_media_dialog_img_wrapper::before, #oe_snippets > .o_we_customize_panel .o_we_so_color_palette.o_we_user_value_widget .o_we_color_preview::before, .o_we_cc_preview_wrapper::before, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name="transparent_grayscale"] .o_we_color_btn::before, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name="theme"] .o_we_color_btn::before, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name="reset"] .o_we_color_btn::before, we-select.o_we_shape_menu we-button[data-shape] .o_we_shape_btn_content::before{content: ""; position: absolute; top: 0; left: 0; bottom: 0; right: 0; z-index: -1; background-image: url("/web/static/src/img/transparent.png"); background-size: 10px auto; border-radius: inherit;}body .modal .o_select_media_dialog .modal-body .o_we_images > .o_existing_attachment_cell .o_we_media_dialog_img_wrapper::after, #oe_snippets > .o_we_customize_panel .o_we_so_color_palette.o_we_user_value_widget .o_we_color_preview::after, .o_we_cc_preview_wrapper::after, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name="transparent_grayscale"] .o_we_color_btn::after, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name="theme"] .o_we_color_btn::after, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name="reset"] .o_we_color_btn::after, we-select.o_we_shape_menu we-button[data-shape] .o_we_shape_btn_content::after{content: ""; position: absolute; top: 0; left: 0; bottom: 0; right: 0; z-index: -1; background: inherit; border-radius: inherit;}

/* /web/static/src/scss/primary_variables.scss defined in bundle 'web_editor.assets_wysiwyg' */
 

/* /odex25_web/static/src/scss/primary_variables.scss defined in bundle 'web_editor.assets_wysiwyg' */
 

/* /odex_backend_theme/static/src/scss/primary_variables.scss defined in bundle 'web_editor.assets_wysiwyg' */
 

/* /web_editor/static/src/scss/web_editor.variables.scss defined in bundle 'web_editor.assets_wysiwyg' */
 

/* /mail/static/src/scss/variables.scss defined in bundle 'web_editor.assets_wysiwyg' */
 

/* /hr_org_chart/static/src/scss/variables.scss defined in bundle 'web_editor.assets_wysiwyg' */
 

/* /portal/static/src/scss/primary_variables.scss defined in bundle 'web_editor.assets_wysiwyg' */
 

/* /website/static/src/scss/primary_variables.scss defined in bundle 'web_editor.assets_wysiwyg' */
 

/* /website/static/src/scss/options/user_values.scss defined in bundle 'web_editor.assets_wysiwyg' */
 

/* /website/static/src/scss/options/colors/user_color_palette.custom.web.assets_common.scss defined in bundle 'web_editor.assets_wysiwyg' */
 

/* /website/static/src/scss/options/colors/user_theme_color_palette.scss defined in bundle 'web_editor.assets_wysiwyg' */
 

/* /website/static/src/snippets/s_badge/000_variables.scss defined in bundle 'web_editor.assets_wysiwyg' */
 

/* /website/static/src/snippets/s_product_list/000_variables.scss defined in bundle 'web_editor.assets_wysiwyg' */
 

/* /website_sale/static/src/scss/primary_variables.scss defined in bundle 'web_editor.assets_wysiwyg' */
 

/* /account/static/src/scss/variables.scss defined in bundle 'web_editor.assets_wysiwyg' */
 @keyframes animate-red{0%{color: red;}100%{color: inherit;}}.animate{animation: animate-red 1s ease;}

/* /website/static/src/scss/secondary_variables.scss defined in bundle 'web_editor.assets_wysiwyg' */
 

/* /web_editor/static/src/scss/secondary_variables.scss defined in bundle 'web_editor.assets_wysiwyg' */
 

/* /odex25_web/static/src/scss/secondary_variables.scss defined in bundle 'web_editor.assets_wysiwyg' */
 

/* /web/static/src/scss/secondary_variables.scss defined in bundle 'web_editor.assets_wysiwyg' */
 

/* /web_editor/static/src/scss/bootstrap_overridden.scss defined in bundle 'web_editor.assets_wysiwyg' */
 

/* /web/static/lib/bootstrap/scss/_variables.scss defined in bundle 'web_editor.assets_wysiwyg' */
 

/* /web_editor/static/src/scss/wysiwyg.scss defined in bundle 'web_editor.assets_wysiwyg' */
 :root{--o-we-toolbar-height: 32px;}.o_we_command_protector{font-weight: 400 !important;}.o_we_command_protector b, .o_we_command_protector strong{font-weight: 700 !important;}.o_we_command_protector *{font-weight: inherit !important;}.o_we_command_protector .btn{text-align: unset !important;}.note-popover .popover{max-width: 350px; left: 50% !important; transform: translate(-50%, 0);}.note-popover .popover .popover-body{white-space: normal;}#web_editor-top-edit{position: absolute; top: 0; left: 0; bottom: auto; right: 0; position: fixed; z-index: 1037; height: var(--o-we-toolbar-height); background-color: #2b2b33;}#web_editor-top-edit .note-popover .popover{top: 0 !important; left: 0 !important; right: 0 !important; border: none !important; max-width: none; transform: none;}#web_editor-top-edit .note-popover .popover .popover-body{height: var(--o-we-toolbar-height);}.wysiwyg_iframe, .note-editor{border: 1px solid #2b2b33; margin: 0; padding: 0;}.note-popover .popover{z-index: 1035;}.note-popover .popover .popover-body, .panel-heading.note-toolbar{padding-bottom: 0; border-bottom: 1px solid #2b2b33; background-color: #2b2b33; color: #D9D9D9; font-family: Roboto, "Montserrat", "Segoe UI", "Helvetica Neue", Helvetica, Arial, sans-serif;}.note-popover .popover .popover-body .btn-group, .note-popover .popover .popover-body .btn, .panel-heading.note-toolbar .btn-group, .panel-heading.note-toolbar .btn{width: auto !important; height: 100% !important; margin-top: 0; margin-bottom: 0; background: transparent; border: none; border-radius: 0;}.note-popover .popover .popover-body .btn-secondary, .panel-heading.note-toolbar .btn-secondary{color: inherit;}.note-popover .popover .popover-body .btn, .panel-heading.note-toolbar .btn{padding: 0.5em 0.75em !important; border-left: 1px solid #000000; border-right: 1px solid #000000; background: #595964; color: inherit; font-size: 13px !important;}.note-popover .popover .popover-body .btn > .caret, .panel-heading.note-toolbar .btn > .caret{display: block; position: absolute; top: auto; left: 0; bottom: 0; right: 0; border-bottom: 2px solid transparent;}.note-popover .popover .popover-body .btn-group.show::after, .panel-heading.note-toolbar .btn-group.show::after{content: ''; position: absolute; top: 100%; left: 1px; bottom: auto; right: 1px; height: 1px;}.note-popover .popover .popover-body .btn.active, .note-popover .popover .popover-body .btn:focus, .note-popover .popover .popover-body .btn:active, .note-popover .popover .popover-body .btn:focus:active, .note-popover .popover .popover-body .btn-group.show > .btn, .note-popover .popover .popover-body .panel-heading.note-toolbar .btn-group.show > .btn, .panel-heading.note-toolbar .note-popover .popover .popover-body .btn.active, .note-popover .popover .popover-body .panel-heading.note-toolbar .btn.active, .panel-heading.note-toolbar .note-popover .popover .popover-body .btn:focus, .note-popover .popover .popover-body .panel-heading.note-toolbar .btn:focus, .panel-heading.note-toolbar .note-popover .popover .popover-body .btn:active, .note-popover .popover .popover-body .panel-heading.note-toolbar .btn:active, .panel-heading.note-toolbar .btn.active, .panel-heading.note-toolbar .btn:focus, .panel-heading.note-toolbar .btn:active, .panel-heading.note-toolbar .btn:focus:active, .panel-heading.note-toolbar .note-popover .popover .popover-body .btn-group.show > .btn, .note-popover .popover .popover-body .panel-heading.note-toolbar .btn-group.show > .btn, .panel-heading.note-toolbar .btn-group.show > .btn{background: #2b2b33; color: #FFFFFF; box-shadow: none !important; outline: none !important;}.note-popover .popover .popover-body .dropdown-menu, .panel-heading.note-toolbar .dropdown-menu{transform: none !important; margin-top: 8px; padding: 0; margin-top: 32px; border: 1px solid #000000; background-color: #141217; box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.5);}.note-popover .popover .popover-body .dropdown-menu.show, .panel-heading.note-toolbar .dropdown-menu.show{min-width: 0;}.note-popover .popover .popover-body .dropdown-item, .panel-heading.note-toolbar .dropdown-item{display: block; max-width: none; overflow: visible; margin-top: 0; padding: 0 1em; border: none; background: none; background-clip: padding-box; background-color: #595964; color: #C6C6C6; line-height: 34px;}.note-popover .popover .popover-body .dropdown-item:not(.d-none) ~ .dropdown-item, .panel-heading.note-toolbar .dropdown-item:not(.d-none) ~ .dropdown-item{border-top: 1px solid transparent;}.note-popover .popover .popover-body .dropdown-item.active, .panel-heading.note-toolbar .dropdown-item.active{color: #FFFFFF;}.note-popover .popover .popover-body li > .dropdown-item, .panel-heading.note-toolbar li > .dropdown-item{border-top: 1px solid transparent;}.note-popover .popover .popover-body .note-style .dropdown-item > *, .panel-heading.note-toolbar .note-style .dropdown-item > *{display: inline;}.note-popover .popover .popover-body .note-style .dropdown-item, .note-popover .popover .popover-body .note-style .dropdown-item > *, .panel-heading.note-toolbar .note-style .dropdown-item, .panel-heading.note-toolbar .note-style .dropdown-item > *{line-height: 2;}.note-popover .popover .popover-body .note-style .dropdown-item[data-value="blockquote"], .panel-heading.note-toolbar .note-style .dropdown-item[data-value="blockquote"]{padding-top: 0.5em; padding-bottom: 0.5em;}.note-popover .popover .popover-body .note-style .dropdown-item[data-value="blockquote"] > *, .panel-heading.note-toolbar .note-style .dropdown-item[data-value="blockquote"] > *{display: block;}.note-popover .popover .popover-body .o_image_alt, .panel-heading.note-toolbar .o_image_alt{display: inline-block; max-width: 100%; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; vertical-align: top; max-width: 150px;}.note-popover .popover .popover-body .note-color-palette div .note-color-btn, .panel-heading.note-toolbar .note-color-palette div .note-color-btn{border-color: #141217;}.note-popover .popover .popover-body .note-custom-color-palette .note-color-row, .panel-heading.note-toolbar .note-custom-color-palette .note-color-row{height: auto !important;}.note-popover .popover .popover-body .note-custom-color-palette .note-color-row .note-color-btn, .panel-heading.note-toolbar .note-custom-color-palette .note-color-row .note-color-btn{float: left; height: 20px; width: 20px; padding: 0; margin: 0; border: 1px solid #141217;}.note-color ul.show{min-width: 216px !important;}@keyframes fadeInDownSmall{0%{opacity: 0; transform: translate(0, -5px);}100%{opacity: 1; transform: translate(0, 0);}}@keyframes inputHighlighter{from{background: #00A09D;}to{width: 0; background: transparent;}}.o_we_horizontal_collapse{width: 0 !important; padding: 0 !important; border: none !important;}.o_we_transition_ease{transition: all ease 0.35s;}body .modal .o_select_media_dialog{max-width: 80%;}body .modal .o_select_media_dialog .modal-body .tab-pane{min-height: 300px;}body .modal .o_select_media_dialog .modal-body .o_we_images > .o_existing_attachment_cell .o_we_media_dialog_img_wrapper, body .modal .o_select_media_dialog .modal-body .o_we_images > .o_existing_attachment_cell .o_we_media_dialog_img_wrapper > img{width: 100%;}body .modal .o_select_media_dialog .modal-body .o_existing_attachment_cell{cursor: pointer; margin: 1px;}body .modal .o_select_media_dialog .modal-body .o_existing_attachment_cell .o_existing_attachment_optimize, body .modal .o_select_media_dialog .modal-body .o_existing_attachment_cell .o_existing_attachment_remove{background-color: rgba(255, 255, 255, 0.4); opacity: 0; cursor: pointer; transition: color 0.2s ease;}body .modal .o_select_media_dialog .modal-body .o_existing_attachment_cell .o_existing_attachment_optimize{position: absolute; top: 0; left: 0; bottom: auto; right: auto; border-radius: 0 0 2px 0;}body .modal .o_select_media_dialog .modal-body .o_existing_attachment_cell .o_existing_attachment_remove{position: absolute; top: 0; left: auto; bottom: auto; right: 0; z-index: 1; border-radius: 0 0 0 2px;}body .modal .o_select_media_dialog .modal-body .o_existing_attachment_cell .o_existing_attachment_remove:hover{color: #e6586c;}body .modal .o_select_media_dialog .modal-body .o_existing_attachment_cell .o_file_name{display: inline-block; max-width: 100%; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; vertical-align: top;}body .modal .o_select_media_dialog .modal-body .o_existing_attachment_cell:hover .o_existing_attachment_optimize, body .modal .o_select_media_dialog .modal-body .o_existing_attachment_cell:hover .o_existing_attachment_remove{opacity: 1;}body .modal .o_select_media_dialog .modal-body .o_existing_attachment_cell:hover.o_we_attachment_highlight, body .modal .o_select_media_dialog .modal-body .o_existing_attachment_cell:hover .o_we_attachment_highlight{border-color: rgba(0, 0, 0, 0.125); box-shadow: 0px 0px 2px 2px rgba(0, 0, 0, 0.125);}body .modal .o_select_media_dialog .modal-body .o_we_attachment_selected{border-color: #00A09D; box-shadow: 0px 0px 2px 2px #00A09D;}body .modal .o_select_media_dialog .modal-body .o_we_attachment_optimized .badge{position: absolute; bottom: 0; right: 0; margin: 2px;}body .modal .o_select_media_dialog .modal-body .font-icons-icons > span{text-align: center; font-size: 22px; margin: 5px; width: 50px; height: 50px; padding: 15px; cursor: pointer;}body .modal .o_select_media_dialog .modal-body #editor-media-image .o_we_url_input, body .modal .o_select_media_dialog .modal-body #editor-media-document .o_we_url_input{width: 300px;}body .modal .o_select_media_dialog .modal-body #editor-media-video .o_video_dialog_form #o_video_form_group{position: relative; width: 100%;}body .modal .o_select_media_dialog .modal-body #editor-media-video .o_video_dialog_form #o_video_form_group > textarea{width: 100%; min-height: 95px; padding-bottom: 25px; overflow-y: scroll;}body .modal .o_select_media_dialog .modal-body #editor-media-video #video-preview{position: relative; border-top: 1px solid black; border-bottom: 1px solid white; background-image: linear-gradient(-150deg, #2b2b33, #191922); color: white; border: none;}body .modal .o_select_media_dialog .modal-body #editor-media-video #video-preview .media_iframe_video{width: 100%;}body .modal .o_select_media_dialog .modal-body #editor-media-video #video-preview .o_video_dialog_iframe{display: inline-block; max-width: 100%; overflow: hidden; box-shadow: 0 0 15px 2px #000; max-width: 100%; max-height: 100%;}body .modal .o_select_media_dialog .modal-body #editor-media-video #video-preview .o_video_dialog_iframe.alert{animation: fadeInDownSmall 700ms forwards; margin: 0 auto;}body .modal .o_link_dialog input.link-style:checked + span::after{content: "\f00c"; display: inline-block; font-family: FontAwesome; margin-left: 2px;}body .modal .o_link_dialog .o_link_dialog_preview{border-left: 1px solid #E9ECEF;}body .modal .o_we_image_optimize_dialog .o_we_title_label{font-size: 13px;}body .modal .o_we_image_optimize_dialog .o_we_preview_area{max-height: 400px; overflow: auto;}img.o_we_selected_image, .fa.o_we_selected_image::before{outline: 3px solid rgba(150, 150, 220, 0.3);}img::selection{background: transparent;}.o_we_media_author{font-size: 11px; position: absolute; top: auto; left: 0; bottom: 0; right: 0; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; text-align: center; background-color: rgba(255, 255, 255, 0.7);}@media (max-width: 991.98px){#web_editor-top-edit{position: initial !important; height: initial !important; top: initial !important; left: initial !important;}#web_editor-top-edit #web_editor-toolbars .popover-body{display: -webkit-box; display: -webkit-flex; display: flex; width: 100%; overflow-x: auto;}#web_editor-top-edit #web_editor-toolbars .popover-body .btn-group{position: static;}}.editor_enable .modal:not(.o_technical_modal), .note-editable .modal:not(.o_technical_modal){top: 40px; right: 0; bottom: 0; right: 291px; width: auto; height: auto;}.editor_enable .modal:not(.o_technical_modal) .modal-dialog, .note-editable .modal:not(.o_technical_modal) .modal-dialog{padding: 0.5rem 0;}.o_we_no_pointer_events{pointer-events: none;}.o_we_crop_widget{background-color: rgba(128, 128, 128, 0.5); position: absolute; top: 0; left: 0; bottom: 0; right: 0; z-index: 1024;}.o_we_crop_widget .o_we_cropper_wrapper{position: absolute;}.o_we_crop_widget .o_we_crop_buttons{margin-top: 0.5rem; display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-wrap: wrap; flex-wrap: wrap;}.o_we_crop_widget .o_we_crop_buttons input[type=radio]{display: none;}.o_we_crop_widget .o_we_crop_buttons .btn-group{border-radius: 0.25rem; margin: 0.1rem;}.o_we_crop_widget .o_we_crop_buttons button, .o_we_crop_widget .o_we_crop_buttons label{cursor: pointer !important; padding: 0.2rem 0.3rem;}.o_we_crop_widget .o_we_crop_buttons label{display: -webkit-box; display: -webkit-flex; display: flex; align-items: center;}.o_we_crop_widget .o_we_crop_buttons label.active{background-color: #000000;}.o_we_crop_widget .o_we_crop_buttons button:not(.btn), .o_we_crop_widget .o_we_crop_buttons label{margin: 0; border: none; border-right: 1px solid #2b2b33; background-color: #2b2b33; color: #D9D9D9;}.o_we_crop_widget .o_we_crop_buttons button:not(.btn):first-child, .o_we_crop_widget .o_we_crop_buttons label:first-child{border-top-left-radius: 0.25rem; border-bottom-left-radius: 0.25rem;}.o_we_crop_widget .o_we_crop_buttons button:not(.btn):last-child, .o_we_crop_widget .o_we_crop_buttons label:last-child{border-top-right-radius: 0.25rem; border-bottom-right-radius: 0.25rem; border-right: none;}

/* /web_editor/static/src/scss/wysiwyg_iframe.scss defined in bundle 'web_editor.assets_wysiwyg' */
 iframe.wysiwyg_iframe.o_fullscreen{position: fixed !important; left: 0 !important; right: 0 !important; top: 0 !important; bottom: 0 !important; width: 100% !important; min-height: 100% !important; z-index: 1001 !important; border: 0;}.o_wysiwyg_no_transform{transform: none !important;}body.o_in_iframe{background-color: white;}body.o_in_iframe.editor_enable{padding-top: var(--o-we-toolbar-height) !important;}body.o_in_iframe .note-statusbar{display: none;}

/* /web_editor/static/src/scss/wysiwyg_snippets.scss defined in bundle 'web_editor.assets_wysiwyg' */
 body.editor_enable.editor_has_snippets{padding-right: 291px !important;}body.editor_enable.editor_has_snippets #web_editor-top-edit .note-popover .popover{right: 291px !important;}body.editor_enable.editor_has_snippets .modal:not(.o_technical_modal){top: 0 !important; z-index: 1034; background-color: rgba(66, 66, 66, 0.4);}body.editor_enable.editor_has_snippets > .oe_overlay.ui-draggable .o_handles{display: none;}@media (max-width: 991.98px){body.editor_enable.editor_has_snippets #web_editor-top-edit{position: initial !important; height: initial !important; top: initial !important; left: initial !important;}body.editor_enable.editor_has_snippets #web_editor-top-edit .note-popover .popover{right: 0 !important;}}#oe_snippets#oe_snippets .o_we_snippet_text_tools{z-index: 1036; -webkit-box-flex: 1; -webkit-flex: 1 0 auto; flex: 1 0 auto; display: -webkit-box; display: -webkit-flex; display: flex; padding: 35px 10px 30px 15px; box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2); overflow-y: auto;}#oe_snippets#oe_snippets .o_we_snippet_text_tools .popover{position: static !important; top: 0 !important; left: 0 !important; transform: none !important; border: none !important;}#oe_snippets#oe_snippets .o_we_snippet_text_tools .popover-body{padding: 0 !important; border: none !important; display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-wrap: wrap; flex-wrap: wrap; justify-content: space-between; gap: 3px;}#oe_snippets#oe_snippets .o_we_snippet_text_tools .popover-body > *{margin: 0 0 4px 0;}#oe_snippets#oe_snippets .o_we_snippet_text_tools .btn{border-radius: 0; padding: 0 6px !important; line-height: 24px !important;}#oe_snippets#oe_snippets .o_we_snippet_text_tools .popover-body > .btn-group > .btn{border-radius: 0;}#oe_snippets#oe_snippets .o_we_snippet_text_tools .popover-body > .btn-group > .btn:first-of-type, #oe_snippets#oe_snippets .o_we_snippet_text_tools .popover-body > .btn-group > div:first-of-type .btn{border-top-left-radius: 2px; border-bottom-left-radius: 2px;}#oe_snippets#oe_snippets .o_we_snippet_text_tools .popover-body > .btn-group > .btn:last-of-type, #oe_snippets#oe_snippets .o_we_snippet_text_tools .popover-body > .btn-group > div:last-of-type .btn{border-top-right-radius: 2px; border-bottom-right-radius: 2px;}#oe_snippets#oe_snippets .o_we_snippet_text_tools .btn-group.note-color{order: -3;}#oe_snippets#oe_snippets .o_we_snippet_text_tools .btn-group.note-color .note-back-color-preview{margin-left: 3px;}#oe_snippets#oe_snippets .o_we_snippet_text_tools .btn-group.note-color .btn::after{display: none;}#oe_snippets#oe_snippets .o_we_snippet_text_tools .btn-group.note-style{order: -2;}#oe_snippets#oe_snippets .o_we_snippet_text_tools .btn-group.note-style, #oe_snippets#oe_snippets .o_we_snippet_text_tools .btn-group.note-style > div{flex-grow: 1;}#oe_snippets#oe_snippets .o_we_snippet_text_tools .btn-group.note-fontsize{order: -1;}#oe_snippets#oe_snippets .o_we_snippet_text_tools .btn-group.note-font [data-name="clear"]{position: absolute; top: -30px; left: auto; bottom: auto; right: 0; background: 0; border: 0;}#oe_snippets#oe_snippets .o_we_snippet_text_tools .btn-group.note-para{flex-grow: 1; gap: 3px; justify-content: space-between;}#oe_snippets#oe_snippets .o_we_snippet_text_tools .btn-group.note-para > *{flex: 1 1 33%;}#oe_snippets#oe_snippets .o_we_snippet_text_tools .btn-group > .d-none + *{margin-left: 0 !important;}#oe_snippets#oe_snippets .o_we_snippet_text_tools .note-color .btn-group{position: static;}#oe_snippets#oe_snippets .o_we_snippet_text_tools .note-color .dropdown-menu{margin-top: 8px;}#oe_snippets#oe_snippets .o_we_snippet_text_tools .note-popover ~ .note-popover, #oe_snippets#oe_snippets .o_we_snippet_text_tools .note-handle ~ .note-handle, #oe_snippets#oe_snippets .o_we_snippet_text_tools .note-dialog ~ .note-dialog{display: none;}.oe_snippet{position: relative; z-index: 1036; width: 77px; background-color: #3e3e46;}.oe_snippet.ui-draggable-dragging{transform: rotate(-3deg) scale(1.2); box-shadow: 0 5px 25px -10px black; transition: transform 0.3s, box-shadow 0.3s;}.oe_snippet > .oe_snippet_body{display: none !important;}.oe_snippet .oe_snippet_thumbnail{width: 100%;}.oe_snippet .oe_snippet_thumbnail .oe_snippet_thumbnail_img{width: 100%; padding-top: 75%; background-repeat: no-repeat; background-size: contain; background-position: top center; overflow: hidden;}.oe_snippet .oe_snippet_thumbnail_title{display: none;}.oe_snippet:not(:hover):not(.o_disabled):not(.o_snippet_install){background-color: rgba(62, 62, 70, 0.9);}.oe_snippet:not(:hover):not(.o_disabled):not(.o_snippet_install) .oe_snippet_thumbnail{filter: saturate(0.7); opacity: .9;}#oe_snippets#oe_snippets .o_we_snippet_text_tools .btn-group.note-font [data-name="clear"], #oe_snippets #snippets_menu > button, .colorpicker .o_we_colorpicker_switch_pane_btn, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .o_delete_btn, #oe_snippets > .o_we_customize_panel we-button.o_we_link, #oe_snippets > .o_we_customize_panel .o_we_fold_icon, #oe_snippets#oe_snippets .o_we_snippet_text_tools .btn, #oe_snippets > .o_we_customize_panel we-button, #oe_snippets > .o_we_customize_panel we-toggler, .colorpicker .o_colorpicker_reset, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > .o_overlay_move_options > *, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > .o_overlay_move_options + *{outline: none; text-decoration: none; line-height: 20px; cursor: pointer;}#oe_snippets#oe_snippets .o_we_snippet_text_tools .btn-group.note-font [disabled][data-name="clear"], #oe_snippets #snippets_menu > button[disabled], .colorpicker .o_we_colorpicker_switch_pane_btn[disabled], #oe_snippets > #o_scroll #snippet_custom .oe_snippet .o_delete_btn[disabled], #oe_snippets > .o_we_customize_panel .o_we_fold_icon[disabled], #oe_snippets#oe_snippets .o_we_snippet_text_tools .btn[disabled], #oe_snippets > .o_we_customize_panel we-button[disabled], #oe_snippets > .o_we_customize_panel we-toggler[disabled], .colorpicker .o_colorpicker_reset[disabled], #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > .o_overlay_move_options > [disabled], #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > .o_overlay_move_options + [disabled]{opacity: .5;}#oe_snippets#oe_snippets .o_we_snippet_text_tools .btn-group.note-font :not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper)[data-name="clear"], #oe_snippets #snippets_menu > button:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_snippets > #o_scroll #snippet_custom .oe_snippet .o_delete_btn:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_snippets#oe_snippets .o_we_snippet_text_tools .btn:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_snippets > .o_we_customize_panel we-button:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), .colorpicker .o_colorpicker_reset:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > .o_overlay_move_options > :not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > .o_overlay_move_options + :not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_snippets#oe_snippets .o_we_snippet_text_tools .btn-group.note-font :not([disabled]):hover[data-name="clear"], #oe_snippets #snippets_menu > button:not([disabled]):hover, .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]):hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .o_delete_btn:not([disabled]):hover, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]):hover, #oe_snippets#oe_snippets .o_we_snippet_text_tools .btn:not([disabled]):hover, #oe_snippets > .o_we_customize_panel we-button:not([disabled]):hover, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]):hover, .colorpicker .o_colorpicker_reset:not([disabled]):hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > .o_overlay_move_options > :not([disabled]):hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > .o_overlay_move_options + :not([disabled]):hover{color: #FFFFFF;}#oe_snippets#oe_snippets .o_we_snippet_text_tools .btn-group.note-font :not([disabled]).o_we_text_success[data-name="clear"], #oe_snippets #snippets_menu > button:not([disabled]).o_we_text_success, .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_text_success, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .o_delete_btn:not([disabled]).o_we_text_success, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_text_success, #oe_snippets#oe_snippets .o_we_snippet_text_tools .btn:not([disabled]).o_we_text_success, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_text_success, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_text_success, .colorpicker .o_colorpicker_reset:not([disabled]).o_we_text_success, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > .o_overlay_move_options > :not([disabled]).o_we_text_success, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > .o_overlay_move_options + :not([disabled]).o_we_text_success{color: #40ad67;}#oe_snippets#oe_snippets .o_we_snippet_text_tools .btn-group.note-font :not([disabled]).o_we_hover_success:hover[data-name="clear"], #oe_snippets #snippets_menu > button:not([disabled]).o_we_hover_success:hover, .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_hover_success:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .o_delete_btn:not([disabled]).o_we_hover_success:hover, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_hover_success:hover, #oe_snippets#oe_snippets .o_we_snippet_text_tools .btn:not([disabled]).o_we_hover_success:hover, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_hover_success:hover, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_hover_success:hover, .colorpicker .o_colorpicker_reset:not([disabled]).o_we_hover_success:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > .o_overlay_move_options > :not([disabled]).o_we_hover_success:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > .o_overlay_move_options + :not([disabled]).o_we_hover_success:hover{color: #40ad67;}#oe_snippets#oe_snippets .o_we_snippet_text_tools .btn-group.note-font :not([disabled]).o_we_text_info[data-name="clear"], #oe_snippets #snippets_menu > button:not([disabled]).o_we_text_info, .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_text_info, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .o_delete_btn:not([disabled]).o_we_text_info, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_text_info, #oe_snippets#oe_snippets .o_we_snippet_text_tools .btn:not([disabled]).o_we_text_info, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_text_info, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_text_info, .colorpicker .o_colorpicker_reset:not([disabled]).o_we_text_info, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > .o_overlay_move_options > :not([disabled]).o_we_text_info, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > .o_overlay_move_options + :not([disabled]).o_we_text_info{color: #6999a8;}#oe_snippets#oe_snippets .o_we_snippet_text_tools .btn-group.note-font :not([disabled]).o_we_hover_info:hover[data-name="clear"], #oe_snippets #snippets_menu > button:not([disabled]).o_we_hover_info:hover, .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_hover_info:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .o_delete_btn:not([disabled]).o_we_hover_info:hover, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_hover_info:hover, #oe_snippets#oe_snippets .o_we_snippet_text_tools .btn:not([disabled]).o_we_hover_info:hover, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_hover_info:hover, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_hover_info:hover, .colorpicker .o_colorpicker_reset:not([disabled]).o_we_hover_info:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > .o_overlay_move_options > :not([disabled]).o_we_hover_info:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > .o_overlay_move_options + :not([disabled]).o_we_hover_info:hover{color: #6999a8;}#oe_snippets#oe_snippets .o_we_snippet_text_tools .btn-group.note-font :not([disabled]).o_we_text_warning[data-name="clear"], #oe_snippets #snippets_menu > button:not([disabled]).o_we_text_warning, .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_text_warning, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .o_delete_btn:not([disabled]).o_we_text_warning, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_text_warning, #oe_snippets#oe_snippets .o_we_snippet_text_tools .btn:not([disabled]).o_we_text_warning, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_text_warning, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_text_warning, .colorpicker .o_colorpicker_reset:not([disabled]).o_we_text_warning, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > .o_overlay_move_options > :not([disabled]).o_we_text_warning, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > .o_overlay_move_options + :not([disabled]).o_we_text_warning{color: #f0ad4e;}#oe_snippets#oe_snippets .o_we_snippet_text_tools .btn-group.note-font :not([disabled]).o_we_hover_warning:hover[data-name="clear"], #oe_snippets #snippets_menu > button:not([disabled]).o_we_hover_warning:hover, .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_hover_warning:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .o_delete_btn:not([disabled]).o_we_hover_warning:hover, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_hover_warning:hover, #oe_snippets#oe_snippets .o_we_snippet_text_tools .btn:not([disabled]).o_we_hover_warning:hover, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_hover_warning:hover, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_hover_warning:hover, .colorpicker .o_colorpicker_reset:not([disabled]).o_we_hover_warning:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > .o_overlay_move_options > :not([disabled]).o_we_hover_warning:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > .o_overlay_move_options + :not([disabled]).o_we_hover_warning:hover{color: #f0ad4e;}#oe_snippets#oe_snippets .o_we_snippet_text_tools .btn-group.note-font :not([disabled]).o_we_text_danger[data-name="clear"], #oe_snippets #snippets_menu > button:not([disabled]).o_we_text_danger, .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_text_danger, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .o_delete_btn:not([disabled]).o_we_text_danger, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_text_danger, #oe_snippets#oe_snippets .o_we_snippet_text_tools .btn:not([disabled]).o_we_text_danger, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_text_danger, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_text_danger, .colorpicker .o_colorpicker_reset:not([disabled]).o_we_text_danger, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > .o_overlay_move_options > :not([disabled]).o_we_text_danger, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > .o_overlay_move_options + :not([disabled]).o_we_text_danger{color: #e6586c;}#oe_snippets#oe_snippets .o_we_snippet_text_tools .btn-group.note-font :not([disabled]).o_we_hover_danger:hover[data-name="clear"], #oe_snippets #snippets_menu > button:not([disabled]).o_we_hover_danger:hover, .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_hover_danger:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .o_delete_btn:not([disabled]).o_we_hover_danger:hover, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_hover_danger:hover, #oe_snippets#oe_snippets .o_we_snippet_text_tools .btn:not([disabled]).o_we_hover_danger:hover, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_hover_danger:hover, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_hover_danger:hover, .colorpicker .o_colorpicker_reset:not([disabled]).o_we_hover_danger:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > .o_overlay_move_options > :not([disabled]).o_we_hover_danger:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > .o_overlay_move_options + :not([disabled]).o_we_hover_danger:hover{color: #e6586c;}#oe_snippets#oe_snippets .o_we_snippet_text_tools .btn-group.note-font [data-name="clear"], #oe_snippets #snippets_menu > button, .colorpicker .o_we_colorpicker_switch_pane_btn, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .o_delete_btn, #oe_snippets > .o_we_customize_panel we-button.o_we_link, #oe_snippets > .o_we_customize_panel .o_we_fold_icon{color: #9d9d9d;}#oe_snippets#oe_snippets .o_we_snippet_text_tools .btn-group.note-font [data-name="clear"] svg .o_graphic, #oe_snippets #snippets_menu > button svg .o_graphic, .colorpicker .o_we_colorpicker_switch_pane_btn svg .o_graphic, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .o_delete_btn svg .o_graphic, #oe_snippets > .o_we_customize_panel we-button.o_we_link svg .o_graphic, #oe_snippets > .o_we_customize_panel .o_we_fold_icon svg .o_graphic{fill: #9d9d9d;}#oe_snippets#oe_snippets .o_we_snippet_text_tools .btn-group.note-font [data-name="clear"] svg .o_subdle, #oe_snippets #snippets_menu > button svg .o_subdle, .colorpicker .o_we_colorpicker_switch_pane_btn svg .o_subdle, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .o_delete_btn svg .o_subdle, #oe_snippets > .o_we_customize_panel we-button.o_we_link svg .o_subdle, #oe_snippets > .o_we_customize_panel .o_we_fold_icon svg .o_subdle{fill: rgba(157, 157, 157, 0.5);}#oe_snippets#oe_snippets .o_we_snippet_text_tools .btn-group.note-font :not([disabled]).active[data-name="clear"] svg .o_graphic, #oe_snippets #snippets_menu > button:not([disabled]).active svg .o_graphic, .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).active svg .o_graphic, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .o_delete_btn:not([disabled]).active svg .o_graphic, #oe_snippets > .o_we_customize_panel we-button.o_we_link:not([disabled]).active svg .o_graphic, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).active svg .o_graphic, #oe_snippets#oe_snippets .o_we_snippet_text_tools .btn-group.note-font :not([disabled]):hover[data-name="clear"] svg .o_graphic, #oe_snippets #snippets_menu > button:not([disabled]):hover svg .o_graphic, .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]):hover svg .o_graphic, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .o_delete_btn:not([disabled]):hover svg .o_graphic, #oe_snippets > .o_we_customize_panel we-button.o_we_link:not([disabled]):hover svg .o_graphic, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]):hover svg .o_graphic{fill: #FFFFFF;}#oe_snippets#oe_snippets .o_we_snippet_text_tools .btn-group.note-font :not([disabled]).active[data-name="clear"] svg .o_subdle, #oe_snippets #snippets_menu > button:not([disabled]).active svg .o_subdle, .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).active svg .o_subdle, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .o_delete_btn:not([disabled]).active svg .o_subdle, #oe_snippets > .o_we_customize_panel we-button.o_we_link:not([disabled]).active svg .o_subdle, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).active svg .o_subdle, #oe_snippets#oe_snippets .o_we_snippet_text_tools .btn-group.note-font :not([disabled]):hover[data-name="clear"] svg .o_subdle, #oe_snippets #snippets_menu > button:not([disabled]):hover svg .o_subdle, .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]):hover svg .o_subdle, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .o_delete_btn:not([disabled]):hover svg .o_subdle, #oe_snippets > .o_we_customize_panel we-button.o_we_link:not([disabled]):hover svg .o_subdle, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]):hover svg .o_subdle{fill: rgba(157, 157, 157, 0.75);}#oe_snippets#oe_snippets .o_we_snippet_text_tools .btn, #oe_snippets > .o_we_customize_panel we-button, #oe_snippets > .o_we_customize_panel we-toggler, .colorpicker .o_colorpicker_reset, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > .o_overlay_move_options > *, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > .o_overlay_move_options + *{display: block; max-width: 100%; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; vertical-align: top; padding: 0 6px; border: 1px solid #000000; border-radius: 2px; background-color: #595964; color: #D9D9D9;}#oe_snippets#oe_snippets .o_we_snippet_text_tools .btn svg .o_graphic, #oe_snippets > .o_we_customize_panel we-button svg .o_graphic, #oe_snippets > .o_we_customize_panel we-toggler svg .o_graphic, .colorpicker .o_colorpicker_reset svg .o_graphic, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > .o_overlay_move_options > * svg .o_graphic, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > .o_overlay_move_options + * svg .o_graphic{fill: #D9D9D9;}#oe_snippets#oe_snippets .o_we_snippet_text_tools .btn svg .o_subdle, #oe_snippets > .o_we_customize_panel we-button svg .o_subdle, #oe_snippets > .o_we_customize_panel we-toggler svg .o_subdle, .colorpicker .o_colorpicker_reset svg .o_subdle, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > .o_overlay_move_options > * svg .o_subdle, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > .o_overlay_move_options + * svg .o_subdle{fill: rgba(217, 217, 217, 0.5);}#oe_snippets#oe_snippets .o_we_snippet_text_tools .btn:not([disabled]):hover svg .o_graphic, #oe_snippets > .o_we_customize_panel we-button:not([disabled]):hover svg .o_graphic, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]):hover svg .o_graphic, .colorpicker .o_colorpicker_reset:not([disabled]):hover svg .o_graphic, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > .o_overlay_move_options > :not([disabled]):hover svg .o_graphic, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > .o_overlay_move_options + :not([disabled]):hover svg .o_graphic, #oe_snippets#oe_snippets .o_we_snippet_text_tools .active.btn:not(.o_we_no_toggle) svg .o_graphic, #oe_snippets > .o_we_customize_panel we-button.active:not(.o_we_no_toggle) svg .o_graphic, #oe_snippets > .o_we_customize_panel we-toggler.active:not(.o_we_no_toggle) svg .o_graphic, .colorpicker .active.o_colorpicker_reset:not(.o_we_no_toggle) svg .o_graphic, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > .o_overlay_move_options > .active:not(.o_we_no_toggle) svg .o_graphic, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > .o_overlay_move_options + .active:not(.o_we_no_toggle) svg .o_graphic{fill: #FFFFFF;}#oe_snippets#oe_snippets .o_we_snippet_text_tools .btn:not([disabled]):hover svg .o_subdle, #oe_snippets > .o_we_customize_panel we-button:not([disabled]):hover svg .o_subdle, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]):hover svg .o_subdle, .colorpicker .o_colorpicker_reset:not([disabled]):hover svg .o_subdle, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > .o_overlay_move_options > :not([disabled]):hover svg .o_subdle, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > .o_overlay_move_options + :not([disabled]):hover svg .o_subdle, #oe_snippets#oe_snippets .o_we_snippet_text_tools .active.btn:not(.o_we_no_toggle) svg .o_subdle, #oe_snippets > .o_we_customize_panel we-button.active:not(.o_we_no_toggle) svg .o_subdle, #oe_snippets > .o_we_customize_panel we-toggler.active:not(.o_we_no_toggle) svg .o_subdle, .colorpicker .active.o_colorpicker_reset:not(.o_we_no_toggle) svg .o_subdle, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > .o_overlay_move_options > .active:not(.o_we_no_toggle) svg .o_subdle, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > .o_overlay_move_options + .active:not(.o_we_no_toggle) svg .o_subdle{fill: rgba(157, 157, 157, 0.75);}#oe_snippets#oe_snippets .o_we_snippet_text_tools .active.btn:not(.o_we_no_toggle), #oe_snippets > .o_we_customize_panel we-button.active:not(.o_we_no_toggle), #oe_snippets > .o_we_customize_panel we-toggler.active:not(.o_we_no_toggle), .colorpicker .active.o_colorpicker_reset:not(.o_we_no_toggle), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > .o_overlay_move_options > .active:not(.o_we_no_toggle), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > .o_overlay_move_options + .active:not(.o_we_no_toggle){background-color: #2b2b33;}#oe_snippets #snippets_menu > button, .colorpicker .o_we_colorpicker_switch_pane_btn{display: -webkit-inline-box; display: -webkit-inline-flex; display: inline-flex; max-width: 100%; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; vertical-align: top; -webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto; justify-content: center; border: none; background-color: transparent; color: inherit; font-weight: normal;}#oe_snippets #snippets_menu > button > span, .colorpicker .o_we_colorpicker_switch_pane_btn > span{display: inline-block; padding: 0.6em 0.4em 0.5em;}#oe_snippets #snippets_menu > button.active > span, .colorpicker .active.o_we_colorpicker_switch_pane_btn > span{color: #FFFFFF; box-shadow: inset 0 -2px 0 #01bad2;}#oe_snippets{position: absolute; top: var(--o-we-toolbar-height); left: auto; bottom: 0; right: 0; position: fixed; z-index: 1036; display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-flow: column nowrap; flex-flow: column nowrap; width: 291px; border-left: 1px solid #2b2b33; background-color: #2b2b33; color: #D9D9D9; font-family: Roboto, "Montserrat", "Segoe UI", "Helvetica Neue", Helvetica, Arial, sans-serif; font-size: 12px; font-weight: 400; transition: transform 400ms ease 0s; transform: translateX(100%);}#oe_snippets .btn:not(.o_btn_preview){border-radius: 0; font-weight: normal; text-transform: none; padding: 0.375rem 0.75rem; font-size: 0.875rem; line-height: 1.5; border-radius: 0;}#oe_snippets .btn:not(.o_btn_preview).btn-primary{color: #FFFFFF; background-color: #00A09D; border-color: #00A09D; color: white;}#oe_snippets .btn:not(.o_btn_preview).btn-primary:hover{color: #FFFFFF; background-color: #007a77; border-color: #006d6b;}#oe_snippets .btn:not(.o_btn_preview).btn-primary:focus, #oe_snippets .btn:not(.o_btn_preview).btn-primary.focus{box-shadow: 0 0 0 0.2rem rgba(38, 174, 172, 0.5);}#oe_snippets .btn:not(.o_btn_preview).btn-primary.disabled, #oe_snippets .btn:not(.o_btn_preview).btn-primary:disabled{color: #FFFFFF; background-color: #00A09D; border-color: #00A09D;}#oe_snippets .btn:not(.o_btn_preview).btn-primary:not(:disabled):not(.disabled):active, #oe_snippets .btn:not(.o_btn_preview).btn-primary:not(:disabled):not(.disabled).active, .show > #oe_snippets .btn:not(.o_btn_preview).btn-primary.dropdown-toggle{color: #FFFFFF; background-color: #006d6b; border-color: #00605e;}#oe_snippets .btn:not(.o_btn_preview).btn-primary:not(:disabled):not(.disabled):active:focus, #oe_snippets .btn:not(.o_btn_preview).btn-primary:not(:disabled):not(.disabled).active:focus, .show > #oe_snippets .btn:not(.o_btn_preview).btn-primary.dropdown-toggle:focus{box-shadow: 0 0 0 0.2rem rgba(38, 174, 172, 0.5);}#oe_snippets .btn:not(.o_btn_preview).btn-secondary{color: #212529; background-color: white; border-color: white; color: #00A09D;}#oe_snippets .btn:not(.o_btn_preview).btn-secondary:hover{color: #212529; background-color: #ececec; border-color: #e6e5e5;}#oe_snippets .btn:not(.o_btn_preview).btn-secondary:focus, #oe_snippets .btn:not(.o_btn_preview).btn-secondary.focus{box-shadow: 0 0 0 0.2rem rgba(222, 222, 223, 0.5);}#oe_snippets .btn:not(.o_btn_preview).btn-secondary.disabled, #oe_snippets .btn:not(.o_btn_preview).btn-secondary:disabled{color: #212529; background-color: white; border-color: white;}#oe_snippets .btn:not(.o_btn_preview).btn-secondary:not(:disabled):not(.disabled):active, #oe_snippets .btn:not(.o_btn_preview).btn-secondary:not(:disabled):not(.disabled).active, .show > #oe_snippets .btn:not(.o_btn_preview).btn-secondary.dropdown-toggle{color: #212529; background-color: #e6e5e5; border-color: #dfdfdf;}#oe_snippets .btn:not(.o_btn_preview).btn-secondary:not(:disabled):not(.disabled):active:focus, #oe_snippets .btn:not(.o_btn_preview).btn-secondary:not(:disabled):not(.disabled).active:focus, .show > #oe_snippets .btn:not(.o_btn_preview).btn-secondary.dropdown-toggle:focus{box-shadow: 0 0 0 0.2rem rgba(222, 222, 223, 0.5);}#oe_snippets .btn:not(.o_btn_preview).btn-link{color: #212529; background-color: white; border-color: white; color: #00A09D;}#oe_snippets .btn:not(.o_btn_preview).btn-link:hover{color: #212529; background-color: #ececec; border-color: #e6e5e5;}#oe_snippets .btn:not(.o_btn_preview).btn-link:focus, #oe_snippets .btn:not(.o_btn_preview).btn-link.focus{box-shadow: 0 0 0 0.2rem rgba(222, 222, 223, 0.5);}#oe_snippets .btn:not(.o_btn_preview).btn-link.disabled, #oe_snippets .btn:not(.o_btn_preview).btn-link:disabled{color: #212529; background-color: white; border-color: white;}#oe_snippets .btn:not(.o_btn_preview).btn-link:not(:disabled):not(.disabled):active, #oe_snippets .btn:not(.o_btn_preview).btn-link:not(:disabled):not(.disabled).active, .show > #oe_snippets .btn:not(.o_btn_preview).btn-link.dropdown-toggle{color: #212529; background-color: #e6e5e5; border-color: #dfdfdf;}#oe_snippets .btn:not(.o_btn_preview).btn-link:not(:disabled):not(.disabled):active:focus, #oe_snippets .btn:not(.o_btn_preview).btn-link:not(:disabled):not(.disabled).active:focus, .show > #oe_snippets .btn:not(.o_btn_preview).btn-link.dropdown-toggle:focus{box-shadow: 0 0 0 0.2rem rgba(222, 222, 223, 0.5);}#oe_snippets .btn:not(.o_btn_preview).btn-success{color: #FFFFFF; background-color: #40ad67; border-color: #40ad67; color: white;}#oe_snippets .btn:not(.o_btn_preview).btn-success:hover{color: #FFFFFF; background-color: #369156; border-color: #328851;}#oe_snippets .btn:not(.o_btn_preview).btn-success:focus, #oe_snippets .btn:not(.o_btn_preview).btn-success.focus{box-shadow: 0 0 0 0.2rem rgba(93, 185, 126, 0.5);}#oe_snippets .btn:not(.o_btn_preview).btn-success.disabled, #oe_snippets .btn:not(.o_btn_preview).btn-success:disabled{color: #FFFFFF; background-color: #40ad67; border-color: #40ad67;}#oe_snippets .btn:not(.o_btn_preview).btn-success:not(:disabled):not(.disabled):active, #oe_snippets .btn:not(.o_btn_preview).btn-success:not(:disabled):not(.disabled).active, .show > #oe_snippets .btn:not(.o_btn_preview).btn-success.dropdown-toggle{color: #FFFFFF; background-color: #328851; border-color: #2f7e4b;}#oe_snippets .btn:not(.o_btn_preview).btn-success:not(:disabled):not(.disabled):active:focus, #oe_snippets .btn:not(.o_btn_preview).btn-success:not(:disabled):not(.disabled).active:focus, .show > #oe_snippets .btn:not(.o_btn_preview).btn-success.dropdown-toggle:focus{box-shadow: 0 0 0 0.2rem rgba(93, 185, 126, 0.5);}#oe_snippets .btn:not(.o_btn_preview).btn-info{color: #FFFFFF; background-color: #6999a8; border-color: #6999a8; color: white;}#oe_snippets .btn:not(.o_btn_preview).btn-info:hover{color: #FFFFFF; background-color: #568695; border-color: #517e8d;}#oe_snippets .btn:not(.o_btn_preview).btn-info:focus, #oe_snippets .btn:not(.o_btn_preview).btn-info.focus{box-shadow: 0 0 0 0.2rem rgba(128, 168, 181, 0.5);}#oe_snippets .btn:not(.o_btn_preview).btn-info.disabled, #oe_snippets .btn:not(.o_btn_preview).btn-info:disabled{color: #FFFFFF; background-color: #6999a8; border-color: #6999a8;}#oe_snippets .btn:not(.o_btn_preview).btn-info:not(:disabled):not(.disabled):active, #oe_snippets .btn:not(.o_btn_preview).btn-info:not(:disabled):not(.disabled).active, .show > #oe_snippets .btn:not(.o_btn_preview).btn-info.dropdown-toggle{color: #FFFFFF; background-color: #517e8d; border-color: #4d7784;}#oe_snippets .btn:not(.o_btn_preview).btn-info:not(:disabled):not(.disabled):active:focus, #oe_snippets .btn:not(.o_btn_preview).btn-info:not(:disabled):not(.disabled).active:focus, .show > #oe_snippets .btn:not(.o_btn_preview).btn-info.dropdown-toggle:focus{box-shadow: 0 0 0 0.2rem rgba(128, 168, 181, 0.5);}#oe_snippets .btn:not(.o_btn_preview).btn-warning{color: #212529; background-color: #f0ad4e; border-color: #f0ad4e; color: #33363e;}#oe_snippets .btn:not(.o_btn_preview).btn-warning:hover{color: #212529; background-color: #ed9d2b; border-color: #ec971f;}#oe_snippets .btn:not(.o_btn_preview).btn-warning:focus, #oe_snippets .btn:not(.o_btn_preview).btn-warning.focus{box-shadow: 0 0 0 0.2rem rgba(209, 153, 72, 0.5);}#oe_snippets .btn:not(.o_btn_preview).btn-warning.disabled, #oe_snippets .btn:not(.o_btn_preview).btn-warning:disabled{color: #212529; background-color: #f0ad4e; border-color: #f0ad4e;}#oe_snippets .btn:not(.o_btn_preview).btn-warning:not(:disabled):not(.disabled):active, #oe_snippets .btn:not(.o_btn_preview).btn-warning:not(:disabled):not(.disabled).active, .show > #oe_snippets .btn:not(.o_btn_preview).btn-warning.dropdown-toggle{color: #212529; background-color: #ec971f; border-color: #ea9214;}#oe_snippets .btn:not(.o_btn_preview).btn-warning:not(:disabled):not(.disabled):active:focus, #oe_snippets .btn:not(.o_btn_preview).btn-warning:not(:disabled):not(.disabled).active:focus, .show > #oe_snippets .btn:not(.o_btn_preview).btn-warning.dropdown-toggle:focus{box-shadow: 0 0 0 0.2rem rgba(209, 153, 72, 0.5);}#oe_snippets .btn:not(.o_btn_preview).btn-danger{color: #FFFFFF; background-color: #e6586c; border-color: #e6586c; color: #33363e;}#oe_snippets .btn:not(.o_btn_preview).btn-danger:hover{color: #FFFFFF; background-color: #e1374f; border-color: #df2c45;}#oe_snippets .btn:not(.o_btn_preview).btn-danger:focus, #oe_snippets .btn:not(.o_btn_preview).btn-danger.focus{box-shadow: 0 0 0 0.2rem rgba(234, 113, 130, 0.5);}#oe_snippets .btn:not(.o_btn_preview).btn-danger.disabled, #oe_snippets .btn:not(.o_btn_preview).btn-danger:disabled{color: #FFFFFF; background-color: #e6586c; border-color: #e6586c;}#oe_snippets .btn:not(.o_btn_preview).btn-danger:not(:disabled):not(.disabled):active, #oe_snippets .btn:not(.o_btn_preview).btn-danger:not(:disabled):not(.disabled).active, .show > #oe_snippets .btn:not(.o_btn_preview).btn-danger.dropdown-toggle{color: #FFFFFF; background-color: #df2c45; border-color: #dd213c;}#oe_snippets .btn:not(.o_btn_preview).btn-danger:not(:disabled):not(.disabled):active:focus, #oe_snippets .btn:not(.o_btn_preview).btn-danger:not(:disabled):not(.disabled).active:focus, .show > #oe_snippets .btn:not(.o_btn_preview).btn-danger.dropdown-toggle:focus{box-shadow: 0 0 0 0.2rem rgba(234, 113, 130, 0.5);}#oe_snippets.o_loaded{transform: none;}#oe_snippets *::selection{background: #03e1fe; color: #000000;}#oe_snippets #snippets_menu{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; display: -webkit-box; display: -webkit-flex; display: flex; background-color: #141217; box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2); color: #D9D9D9;}#oe_snippets .o_snippet_search_filter{position: relative; box-shadow: inset 0 -1px 0 #000000, 0 10px 10px rgba(0, 0, 0, 0.2); z-index: 2;}#oe_snippets .o_snippet_search_filter, #oe_snippets .o_snippet_search_filter .o_snippet_search_filter_input{width: 100%;}#oe_snippets .o_snippet_search_filter .o_snippet_search_filter_input{background-color: #2b2b33; padding: 10px 2em 10px 10px; border: 0; border-bottom: 1px solid #000000; color: #FFFFFF;}#oe_snippets .o_snippet_search_filter .o_snippet_search_filter_input::placeholder{font-style: italic; color: #9d9d9d;}#oe_snippets .o_snippet_search_filter .o_snippet_search_filter_input:focus{background-color: #3e3e46; outline: none;}#oe_snippets .o_snippet_search_filter .o_snippet_search_filter_reset{position: absolute; top: 10px; left: auto; bottom: 10px; right: 10px; display: -webkit-box; display: -webkit-flex; display: flex; justify-content: center; align-items: center; padding: 0 6px; color: #9d9d9d; cursor: pointer;}#oe_snippets .o_snippet_search_filter .o_snippet_search_filter_reset:hover, #oe_snippets .o_snippet_search_filter .o_snippet_search_filter_reset:focus, #oe_snippets .o_snippet_search_filter .o_snippet_search_filter_reset.focus{color: #FFFFFF;}#oe_snippets > #o_scroll, #oe_snippets > .o_we_customize_panel{min-height: 0; overflow: auto;}#oe_snippets > #o_scroll{background-color: #191922; padding: 0 10px; z-index: 1;}#oe_snippets > #o_scroll .o_panel, #oe_snippets > #o_scroll .o_panel_header{padding: 10px 0;}#oe_snippets > #o_scroll .o_panel_body{display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-wrap: wrap; flex-wrap: wrap; margin-left: -2px;}#oe_snippets > #o_scroll .o_panel_body > .oe_snippet{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 33.33333333%; background-clip: padding-box; border-left: 2px solid transparent; margin-bottom: 2px; user-select: none; cursor: url(/web/static/src/img/openhand.cur), grab;}#oe_snippets > #o_scroll .o_panel_body > .oe_snippet .oe_snippet_thumbnail_title{display: block; padding: 5px; text-align: center;}#oe_snippets > #o_scroll .o_panel_body > .oe_snippet.o_disabled .o_snippet_undroppable{position: absolute; top: 8px; left: auto; bottom: auto; right: 6px;}#oe_snippets > #o_scroll .o_panel_body > .oe_snippet.o_snippet_install .btn.o_install_btn{position: absolute; top: 10px; left: auto; bottom: auto; right: auto;}#oe_snippets > #o_scroll .o_panel_body > .oe_snippet.o_snippet_install:not(:hover) .btn.o_install_btn{display: none;}#oe_snippets > #o_scroll .o_panel_body > .oe_snippet.o_disabled, #oe_snippets > #o_scroll .o_panel_body > .oe_snippet.o_snippet_install{background-color: rgba(62, 62, 70, 0.2);}#oe_snippets > #o_scroll .o_panel_body > .oe_snippet.o_disabled .oe_snippet_thumbnail_img, #oe_snippets > #o_scroll .o_panel_body > .oe_snippet.o_snippet_install .oe_snippet_thumbnail_img{opacity: .4; filter: saturate(0) blur(1px);}#oe_snippets > #o_scroll #snippet_custom .oe_snippet{width: 100%;}#oe_snippets > #o_scroll #snippet_custom .oe_snippet, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe_snippet_thumbnail, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .o_delete_btn{display: -webkit-box; display: -webkit-flex; display: flex;}#oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe_snippet_thumbnail, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .o_delete_btn{align-items: center;}#oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe_snippet_thumbnail{min-width: 0;}#oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe_snippet_thumbnail_title{display: block; max-width: 100%; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; vertical-align: top;}#oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe_snippet_thumbnail_img{flex-shrink: 0; width: 40px; height: 32px; padding: 0;}#oe_snippets > #o_scroll #snippet_custom .oe_snippet .o_delete_btn{padding-top: 0; padding-bottom: 0;}#oe_snippets > .o_we_customize_panel{position: relative;}#oe_snippets > .o_we_customize_panel we-button.o_we_link{margin-top: 0; border: 0; padding: 0; background: 0;}#oe_snippets > .o_we_customize_panel we-toggler{padding-right: 2em; text-align: left;}#oe_snippets > .o_we_customize_panel we-toggler::after{content: ""; color: #9d9d9d;}#oe_snippets > .o_we_customize_panel we-toggler > img, #oe_snippets > .o_we_customize_panel we-toggler > svg{max-width: 100%;}#oe_snippets > .o_we_customize_panel we-toggler + *{display: none !important; border: 1px solid #000000; background-color: #141217; box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.5);}#oe_snippets > .o_we_customize_panel we-toggler.active{padding-right: 2em;}#oe_snippets > .o_we_customize_panel we-toggler.active::after{content: ""; color: #9d9d9d;}#oe_snippets > .o_we_customize_panel we-toggler.active + *{display: block !important;}#oe_snippets > .o_we_customize_panel we-toggler, #oe_snippets > .o_we_customize_panel we-toggler.active, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items we-button, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items we-button.active{position: relative;}#oe_snippets > .o_we_customize_panel we-toggler::after, #oe_snippets > .o_we_customize_panel we-toggler.active::after, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items we-button::after, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items we-button.active::after{position: absolute; top: 50%; left: auto; bottom: auto; right: 0.5em; transform: translateY(-50%); width: 1em; text-align: center; font-family: FontAwesome;}#oe_snippets > .o_we_customize_panel we-title{display: block; text-transform: capitalize;}#oe_snippets > .o_we_customize_panel we-customizeblock-options{position: relative; display: block; padding: 0 0 15px 0; background-color: #3e3e46; box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.8);}#oe_snippets > .o_we_customize_panel we-customizeblock-options > we-title{display: -webkit-box; display: -webkit-flex; display: flex; align-items: center; padding: 3px 10px 0 15px; background-color: #2b2b33; box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2), inset 0 -1px 0 rgba(0, 0, 0, 0.5); font-size: 13px;}#oe_snippets > .o_we_customize_panel we-customizeblock-options > we-title > span{display: inline-block; max-width: 100%; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; vertical-align: top; -webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto; cursor: pointer; color: #FFFFFF !important; line-height: 32px;}#oe_snippets > .o_we_customize_panel we-customizeblock-options > we-title > we-top-button-group{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; display: -webkit-box; display: -webkit-flex; display: flex; margin-left: auto; font-size: .9em;}#oe_snippets > .o_we_customize_panel we-customizeblock-options > we-title > we-top-button-group .oe_snippet_remove{font-size: 1.2em;}#oe_snippets > .o_we_customize_panel we-customizeblock-options > we-title > we-top-button-group we-customizeblock-option{display: -webkit-box; display: -webkit-flex; display: flex; padding: 0;}#oe_snippets > .o_we_customize_panel we-customizeblock-options > we-title > we-top-button-group we-button{margin-top: 0 !important; margin-left: 3px; padding: 0 3px;}#oe_snippets > .o_we_customize_panel we-customizeblock-options > we-title > we-top-button-group we-button.fa{margin-left: 6px;}#oe_snippets > .o_we_customize_panel we-customizeblock-option{position: relative; display: block; padding: 0 10px 0 15px;}#oe_snippets > .o_we_customize_panel we-customizeblock-option .dropdown-menu{position: static !important;}#oe_snippets > .o_we_customize_panel we-customizeblock-option > we-alert{background-color: #6999a8; display: block; padding: 6px;}#oe_snippets > .o_we_customize_panel we-customizeblock-option > we-alert we-title{margin-bottom: 6px; text-transform: uppercase; font-weight: bold;}#oe_snippets > .o_we_customize_panel we-customizeblock-option > we-title{margin-bottom: -4px; font-size: 13px; color: #FFFFFF; font-weight: 500;}#oe_snippets > .o_we_customize_panel we-customizeblock-option > we-title:not(:first-child){margin-top: 16px;}#oe_snippets > .o_we_customize_panel .o_we_fold_icon{position: absolute; top: 0; left: -15px; bottom: 0; right: 100%; display: -webkit-box; display: -webkit-flex; display: flex; justify-content: center; align-items: center; width: 15px;}#oe_snippets > .o_we_customize_panel .o_we_user_value_widget{margin-top: 8px;}#oe_snippets > .o_we_customize_panel .o_we_user_value_widget > div{display: -webkit-box; display: -webkit-flex; display: flex; align-items: center; min-height: 22px;}#oe_snippets > .o_we_customize_panel we-button.o_we_user_value_widget > div{display: block; max-width: 100%; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; vertical-align: top; min-height: 20px; line-height: 20px;}#oe_snippets > .o_we_customize_panel we-button.o_we_user_value_widget > div > img{margin-bottom: 1px;}#oe_snippets > .o_we_customize_panel we-button.o_we_user_value_widget > div > svg{margin-bottom: 2px;}#oe_snippets > .o_we_customize_panel we-button.o_we_user_value_widget.fa > div{display: none;}#oe_snippets > .o_we_customize_panel we-button.o_we_checkbox_wrapper.o_we_user_value_widget{min-width: 20px; padding: 0; border: none; background: none; cursor: default;}#oe_snippets > .o_we_customize_panel we-button.o_we_checkbox_wrapper.o_we_user_value_widget > we-title{cursor: pointer;}#oe_snippets > .o_we_customize_panel we-button.o_we_checkbox_wrapper.o_we_user_value_widget > div{display: -webkit-box; display: -webkit-flex; display: flex; min-height: 22px; line-height: 22px;}#oe_snippets > .o_we_customize_panel we-button.o_we_checkbox_wrapper.o_we_user_value_widget we-checkbox{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; display: -webkit-box; display: -webkit-flex; display: flex; align-items: center; width: 20px; height: 12px; background-color: #9d9d9d; border-radius: 10rem; cursor: pointer;}#oe_snippets > .o_we_customize_panel we-button.o_we_checkbox_wrapper.o_we_user_value_widget we-checkbox::after{content: ""; display: block; width: 11px; height: 10px; border-radius: 10rem; background-color: #FFFFFF; box-shadow: 0 2px 3px 0 #000000;}#oe_snippets > .o_we_customize_panel we-button.o_we_checkbox_wrapper.o_we_user_value_widget.active we-checkbox{background-color: #01bad2; -webkit-box-pack: end; justify-content: flex-end;}#oe_snippets > .o_we_customize_panel we-button.o_we_checkbox_wrapper.o_we_user_value_widget.active, #oe_snippets > .o_we_customize_panel we-button.o_we_checkbox_wrapper.o_we_user_value_widget:hover{color: #D9D9D9;}#oe_snippets > .o_we_customize_panel we-selection-items .o_we_user_value_widget{margin-top: 0;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget{position: relative;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget:not(.o_we_icon_select) we-toggler{-webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto; width: 159.6px;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget.o_we_widget_opened .o_we_dropdown_caret{position: relative; display: block; align-self: flex-end;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget.o_we_widget_opened .o_we_dropdown_caret::before, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget.o_we_widget_opened .o_we_dropdown_caret::after{content: ''; position: absolute; top: 100%; left: auto; bottom: auto; right: 2em; z-index: 1001; transform: translateX(50%); margin-top: 2px; border-bottom: 7px solid #000000; border-left: 8px solid transparent; border-right: 8px solid transparent;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget.o_we_widget_opened .o_we_dropdown_caret::after{border-bottom-color: #595964; border-left-width: 7px; border-right-width: 7px;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget:not(.o_we_so_color_palette) + we-button:not(:hover){background: none;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-toggler:empty::before{content: '/';}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items{position: absolute; top: 100%; left: 0; bottom: auto; right: 0; z-index: 1000; margin-top: 8px !important;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items:not(.o_we_has_pager){max-height: 600px; overflow-y: auto;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items:empty{line-height: 34px; background-color: #595964; color: #C6C6C6; padding-left: 2em;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items:empty::before{content: '/';}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-title{line-height: 34px;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items we-button{padding-left: 2em; border: none; background: none; background-clip: padding-box; background-color: #595964; color: #C6C6C6;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items we-button::after{content: ""; color: #9d9d9d; right: auto; left: 0.5em;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items we-button > we-title{flex-grow: 1;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items we-button > div, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items we-button > we-title{line-height: 34px;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items we-button > div img, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items we-button > div svg, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items we-button > we-title img, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items we-button > we-title svg{max-width: 100%;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items we-button:not(.d-none) ~ we-button{border-top: 1px solid transparent;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items we-button:hover{background-color: #2b2b33; color: #D9D9D9;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items we-button.active{padding-left: 2em; background-color: #42424c; color: #FFFFFF;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items we-button.active::after{content: ""; color: #9d9d9d; right: auto; left: 0.5em;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items we-button.active:after{color: #01bad2;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget .o_we_pager_header{display: -webkit-box; display: -webkit-flex; display: flex; align-items: center; justify-content: space-between; background-color: #595964; margin-bottom: 1px;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget .o_we_pager_header > b{padding: 6px; color: #FFFFFF;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget .o_we_pager_controls{display: -webkit-box; display: -webkit-flex; display: flex; align-items: center;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget .o_we_pager_controls > span{margin: 0 6px;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget .o_we_pager_next, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget .o_we_pager_prev{margin: 0.3em; padding: 6px; cursor: pointer; border: 1px solid currentColor; border-radius: 2px;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-select-page{display: none; width: 100%; max-height: 562.5px; overflow-y: auto;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-select-page.active{display: block;}#oe_snippets > .o_we_customize_panel we-button-group.o_we_user_value_widget we-selection-items{display: -webkit-box; display: -webkit-flex; display: flex; max-width: 100%;}#oe_snippets > .o_we_customize_panel we-button-group.o_we_user_value_widget we-selection-items we-button{padding: 0 6px; border-radius: 0;}#oe_snippets > .o_we_customize_panel we-button-group.o_we_user_value_widget we-selection-items we-button + we-button{border-left: none;}#oe_snippets > .o_we_customize_panel we-button-group.o_we_user_value_widget we-selection-items we-button:first-child{border-top-left-radius: 2px; border-bottom-left-radius: 2px;}#oe_snippets > .o_we_customize_panel we-button-group.o_we_user_value_widget we-selection-items we-button:last-child{border-top-right-radius: 2px; border-bottom-right-radius: 2px;}#oe_snippets > .o_we_customize_panel we-customizeblock-option > we-button-group.o_we_user_value_widget we-selection-items{-webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto; width: 159.6px;}#oe_snippets > .o_we_customize_panel we-customizeblock-option > we-button-group.o_we_user_value_widget we-selection-items we-button{display: -webkit-box; display: -webkit-flex; display: flex; justify-content: center; flex: 0 1 25%; padding: 1.5px 2px; text-align: center;}#oe_snippets > .o_we_customize_panel we-input.o_we_user_value_widget > div{-webkit-box-flex: 0; -webkit-flex: 0 1 auto; flex: 0 1 auto; width: 60px; border: 1px solid #000000; border-radius: 2px; background-color: #2b2b33;}#oe_snippets > .o_we_customize_panel we-input.o_we_user_value_widget > div:focus-within{border-color: #01bad2;}#oe_snippets > .o_we_customize_panel we-input.o_we_user_value_widget > div > we-button{border: none;}#oe_snippets > .o_we_customize_panel we-input.o_we_user_value_widget.o_we_large_input > div{-webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto;}#oe_snippets > .o_we_customize_panel we-input.o_we_user_value_widget input{box-sizing: content-box; -webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto; width: 0; min-width: 2ch; height: 20px; padding: 0 6px; border: none; border-radius: 0; background-color: transparent; color: inherit; font-family: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;}#oe_snippets > .o_we_customize_panel we-input.o_we_user_value_widget input::placeholder{color: #9d9d9d;}#oe_snippets > .o_we_customize_panel we-input.o_we_user_value_widget input:focus{outline: none;}#oe_snippets > .o_we_customize_panel we-input.o_we_user_value_widget span{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; padding-right: 6px; font-size: 11px; color: #9d9d9d;}#oe_snippets > .o_we_customize_panel .o_we_so_color_palette.o_we_user_value_widget .o_we_color_preview{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; display: block; width: 20px; height: 20px; border: 1px solid #000000; border-radius: 10rem; cursor: pointer;}#oe_snippets > .o_we_customize_panel .o_we_so_color_palette.o_we_user_value_widget .o_we_color_preview::after{box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.5);}#oe_snippets > .o_we_customize_panel .o_we_so_color_palette.o_we_user_value_widget.o_we_widget_opened .o_we_color_preview{border: 2px solid #01bad2;}#oe_snippets > .o_we_customize_panel .o_we_so_color_palette.o_we_user_value_widget.o_we_widget_opened .o_we_dropdown_caret::before, #oe_snippets > .o_we_customize_panel .o_we_so_color_palette.o_we_user_value_widget.o_we_widget_opened .o_we_dropdown_caret::after{right: 10px;}#oe_snippets > .o_we_customize_panel .o_we_so_color_palette.o_we_user_value_widget.o_we_widget_opened .o_we_dropdown_caret::after{border-bottom-width: 8px;}#oe_snippets > .o_we_customize_panel .o_we_so_color_palette.o_we_user_value_widget we-toggler{display: none;}#oe_snippets > .o_we_customize_panel we-matrix{overflow-y: auto;}#oe_snippets > .o_we_customize_panel we-matrix table{table-layout: fixed; width: 100%;}#oe_snippets > .o_we_customize_panel we-matrix table td, #oe_snippets > .o_we_customize_panel we-matrix table th{text-align: center;}#oe_snippets > .o_we_customize_panel we-matrix table td we-button, #oe_snippets > .o_we_customize_panel we-matrix table th we-button{display: inline-block; color: inherit; height: 100%;}#oe_snippets > .o_we_customize_panel we-matrix table td we-button.o_we_matrix_remove_col, #oe_snippets > .o_we_customize_panel we-matrix table td we-button.o_we_matrix_remove_row, #oe_snippets > .o_we_customize_panel we-matrix table th we-button.o_we_matrix_remove_col, #oe_snippets > .o_we_customize_panel we-matrix table th we-button.o_we_matrix_remove_row{display: none;}#oe_snippets > .o_we_customize_panel we-matrix table td input, #oe_snippets > .o_we_customize_panel we-matrix table th input{border: 1px solid #000000; background-color: #2b2b33; color: inherit; font-size: 12px; width: 100%;}#oe_snippets > .o_we_customize_panel we-matrix table td:last-child, #oe_snippets > .o_we_customize_panel we-matrix table th:last-child{width: 28px;}#oe_snippets > .o_we_customize_panel we-matrix table tr:last-child we-button{overflow: visible;}#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type="range"]{-webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto; width: 159.6px; height: 22px; padding: 0 1px 0 0; background-color: transparent; -webkit-appearance: none; -moz-appearance: none; appearance: none;}#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type="range"]:focus{outline: none;}#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type="range"]:focus::-webkit-slider-thumb{box-shadow: none;}#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type="range"]:focus::-moz-range-thumb{box-shadow: none;}#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type="range"]:focus::-ms-thumb{box-shadow: none;}#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type="range"]::-moz-focus-outer{border: 0;}#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type="range"]::-webkit-slider-thumb{width: 10px; height: 10px; margin-top: -3px; border: none; border-radius: 10rem; background-color: #01bad2; box-shadow: none; -webkit-appearance: none; -moz-appearance: none; appearance: none;}#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type="range"]::-webkit-slider-thumb:active{background-color: #01bad2;}#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type="range"]::-webkit-slider-runnable-track{width: 100%; height: 4px; cursor: pointer; background-color: #9d9d9d; border-color: transparent; border-radius: 10rem; box-shadow: none; position: relative;}#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type="range"]::-moz-range-thumb{width: 10px; height: 10px; border: none; border-radius: 10rem; background-color: #01bad2; box-shadow: none; -webkit-appearance: none; -moz-appearance: none; appearance: none;}#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type="range"]::-moz-range-thumb:active{background-color: #01bad2;}#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type="range"]::-moz-range-track{width: 100%; height: 4px; cursor: pointer; background-color: #9d9d9d; border-color: transparent; border-radius: 10rem; box-shadow: none;}#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type="range"]::-moz-range-progress{background-color: #01bad2; height: 4px; border-color: transparent; border-radius: 10rem;}#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type="range"]::-ms-thumb{width: 10px; height: 10px; margin-top: 0; margin-right: 0; margin-left: 0; border: none; border-radius: 10rem; background-color: #01bad2; box-shadow: none; -webkit-appearance: none; -moz-appearance: none; appearance: none;}#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type="range"]::-ms-thumb:active{background-color: #01bad2;}#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type="range"]::-ms-track{width: 100%; height: 4px; cursor: pointer; background-color: transparent; border-color: transparent; border-width: 5px; box-shadow: none;}#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type="range"]::-ms-fill-lower{background-color: #01bad2; border-radius: 10rem; border-radius: 1rem;}#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type="range"]::-ms-fill-upper{background-color: #9d9d9d; border-radius: 10rem;}#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type="range"].o_we_inverted_range{transform: rotate(180deg);}#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type="range"].o_we_inverted_range::-moz-range-track{background-color: #01bad2;}#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type="range"].o_we_inverted_range::-moz-range-progress{background-color: #9d9d9d;}#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type="range"].o_we_inverted_range::-ms-fill-lower{background-color: #9d9d9d;}#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type="range"].o_we_inverted_range::-ms-fill-upper{background-color: #01bad2;}#oe_snippets > .o_we_customize_panel we-multi.o_we_user_value_widget{margin-top: 0;}#oe_snippets > .o_we_customize_panel we-multi.o_we_user_value_widget > div{-webkit-flex-flow: row wrap; flex-flow: row wrap;}#oe_snippets > .o_we_customize_panel we-multi.o_we_user_value_widget > div > *{-webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto;}#oe_snippets > .o_we_customize_panel we-row{position: relative; margin-top: 8px;}#oe_snippets > .o_we_customize_panel we-row .o_we_user_value_widget{margin-top: 0; min-width: 4em;}#oe_snippets > .o_we_customize_panel we-row we-button.o_we_user_value_widget, #oe_snippets > .o_we_customize_panel we-row .o_we_so_color_palette.o_we_user_value_widget{min-width: auto;}#oe_snippets > .o_we_customize_panel we-row > div{display: -webkit-box; display: -webkit-flex; display: flex; align-items: center;}#oe_snippets > .o_we_customize_panel we-row > div > :not(.d-none) ~ *{margin-left: 3px;}#oe_snippets > .o_we_customize_panel we-row we-select.o_we_user_value_widget{position: static;}#oe_snippets > .o_we_customize_panel we-row.o_we_full_row > div{-webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto;}#oe_snippets > .o_we_customize_panel .o_we_user_value_widget, #oe_snippets > .o_we_customize_panel we-row{display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-flow: row wrap; flex-flow: row wrap;}#oe_snippets > .o_we_customize_panel .o_we_user_value_widget > we-title, #oe_snippets > .o_we_customize_panel we-row > we-title{width: 100%;}#oe_snippets > .o_we_customize_panel .o_we_user_value_widget > div, #oe_snippets > .o_we_customize_panel we-row > div{-webkit-box-flex: 0; -webkit-flex: 0 1 auto; flex: 0 1 auto; min-width: 0; margin-top: 8px;}#oe_snippets > .o_we_customize_panel .o_we_user_value_widget:not(.o_we_fw), #oe_snippets > .o_we_customize_panel we-row:not(.o_we_fw){-webkit-flex-flow: row nowrap; flex-flow: row nowrap; align-items: center;}#oe_snippets > .o_we_customize_panel .o_we_user_value_widget:not(.o_we_fw) > we-title, #oe_snippets > .o_we_customize_panel we-row:not(.o_we_fw) > we-title{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; display: inline-block; max-width: 100%; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; vertical-align: top; width: 106.4px; padding-right: 6px;}#oe_snippets > .o_we_customize_panel .o_we_user_value_widget:not(.o_we_fw) > div, #oe_snippets > .o_we_customize_panel we-row:not(.o_we_fw) > div{margin-top: 0;}#oe_snippets > .o_we_customize_panel we-collapse{position: relative; display: block; padding-left: 15px; padding-right: 10px; margin-right: -10px; margin-left: -15px; border-top: 4px solid transparent; padding-bottom: 4px; margin-bottom: -4px; background-clip: padding-box;}#oe_snippets > .o_we_customize_panel we-collapse > :first-child, #oe_snippets > .o_we_customize_panel we-collapse .o_we_collapse_toggler{margin-top: 4px;}#oe_snippets > .o_we_customize_panel we-collapse .o_we_collapse_toggler{position: absolute; top: 0; left: 0; bottom: auto; right: auto; width: 15px; height: 22px; display: -webkit-box; display: -webkit-flex; display: flex; align-items: center; justify-content: center; padding: 0; background: none; border: none;}#oe_snippets > .o_we_customize_panel we-collapse .o_we_collapse_toggler::after{content: '\f0da'; position: static; transform: none;}#oe_snippets > .o_we_customize_panel we-collapse .o_we_collapse_toggler.o_we_collapse_toggler_rtl::after{scale: -1 1;}#oe_snippets > .o_we_customize_panel we-collapse .o_we_collapse_toggler.active::after{content: '\f0d7';}#oe_snippets > .o_we_customize_panel we-collapse .o_we_collapse_toggler.active + *{background: none; border: none; box-shadow: none;}#oe_snippets > .o_we_customize_panel we-collapse.active{background-color: #2b2b33; box-shadow: inset 0 1px 0 rgba(0, 0, 0, 0.5), inset 0 -1px 0 rgba(255, 255, 255, 0.2);}#oe_snippets > .o_we_customize_panel we-collapse.active we-collapse.active, #oe_snippets > .o_we_customize_panel we-collapse.active we-collapse.active .o_we_collapse_toggler{background-color: #3e3e46;}#oe_snippets > .o_we_customize_panel .o_we_image_weight{display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-pack: end; justify-content: flex-end; align-items: center; margin: 2px 1px 16px;}#oe_snippets > .o_we_customize_panel .o_we_image_weight b{margin-left: 6px; font: 1em/1 bold SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; color: #40ad67;}#oe_snippets > .o_we_customize_panel .o_we_external_warning{margin-top: 8px;}#oe_snippets > .o_we_customize_panel .o_we_tag{padding: 3px 6px; border-radius: 5px; background-color: #000000;}#oe_snippets > .o_we_customize_panel .o_we_tag + .fa{margin: 0 0 0 5px;}#oe_snippets > .o_we_customize_panel .o_we_tag_wrapper{display: -webkit-inline-box; display: -webkit-inline-flex; display: inline-flex; margin: 6px 3px 0 0;}#oe_snippets > .o_we_customize_panel .o_wblog_new_tag div, #oe_snippets > .o_we_customize_panel .o_wblog_new_tag we-input{width: 100% !important;}#oe_snippets > .o_we_invisible_el_panel{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; max-height: 220px; overflow-y: auto; margin-top: auto; padding: 10px; background-color: #191922; box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2);}#oe_snippets > .o_we_invisible_el_panel .o_panel_header{padding: 8px 0;}#oe_snippets > .o_we_invisible_el_panel .o_we_invisible_entry{padding: 8px 6px; cursor: pointer;}#oe_snippets > .o_we_invisible_el_panel .o_we_invisible_entry:hover{background-color: #2b2b33;}#oe_snippets.o_we_backdrop > .o_we_customize_panel{-webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto;}#oe_snippets.o_we_backdrop > .o_we_customize_panel::after{content: ""; position: -webkit-sticky; position: sticky; top: auto; left: 0; bottom: 0; right: 0; display: block; height: 100vh; margin-top: -100vh; pointer-events: none; background: rgba(0, 0, 0, 0.2);}#oe_snippets.o_we_backdrop .o_we_widget_opened{z-index: 1000;}.o_we_cc_preview_wrapper{font-family: sans-serif !important; font-size: 15px !important; padding: 8px 8px 6.4px;}.o_we_cc_preview_wrapper > *{margin-bottom: 0 !important; line-height: 1 !important;}.o_we_color_combination_btn_text{color: inherit !important; font-family: inherit !important; font-size: 0.8em !important; margin-top: 0.5em !important;}.o_we_color_combination_btn_title{margin-top: 0 !important; font-size: 1.3em !important;}.o_we_color_combination_btn_btn{padding: 0.2em 3px 0.3em !important; border-radius: 2px !important; font-size: 0.8em !important;}.colorpicker{background-color: #595964; color: #D9D9D9;}.colorpicker .o_we_colorpicker_switch_panel{border-bottom: 1px solid #191922; box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2);}.colorpicker .o_we_colorpicker_switch_pane_btn{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto;}.colorpicker .o_colorpicker_reset{max-width: 40%;}.colorpicker .o_colorpicker_sections .o_colorpicker_widget .o_hex_div:focus-within, .colorpicker .o_colorpicker_sections .o_colorpicker_widget .o_rgba_div:focus-within{border-color: #01bad2;}.colorpicker .o_colorpicker_sections .o_colorpicker_widget .o_color_picker_inputs input{border: none;}.colorpicker .o_colorpicker_sections .o_colorpicker_widget .o_color_picker_inputs input:focus{outline: none;}.colorpicker .o_colorpicker_sections .o_we_color_btn, .colorpicker .o_colorpicker_sections .o_we_color_combination_btn{float: left; width: 12.5%; padding-top: 10%; margin: 0; border: 1px solid #595964; box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.5);}.colorpicker .o_colorpicker_sections .o_we_color_btn.o_colorpicker_reset, .colorpicker .o_colorpicker_sections .o_we_color_combination_btn.o_colorpicker_reset{background-color: transparent;}.colorpicker .o_colorpicker_sections .o_we_color_btn.o_colorpicker_reset::before, .colorpicker .o_colorpicker_sections .o_we_color_combination_btn.o_colorpicker_reset::before{position: absolute; top: 0; left: 0; bottom: 0; right: 0; font-family: FontAwesome !important; content: "\f00d" !important; display: -webkit-box; display: -webkit-flex; display: flex; justify-content: center; align-items: center; color: #e6586c;}.colorpicker .o_colorpicker_sections .o_we_color_combination_btn{float: none; width: 100%; padding: 0; margin: 0; border: 0; background-color: transparent; background-clip: padding-box; border-top: 8px solid transparent; border-bottom: 8px solid transparent;}.colorpicker .o_colorpicker_sections .o_we_color_combination_btn + .o_we_color_combination_btn{margin-top: -4px;}.colorpicker .o_colorpicker_sections .o_we_color_combination_btn.selected > .o_we_cc_preview_wrapper{box-shadow: 0 0 0 1px #40ad67 !important;}.colorpicker .o_colorpicker_sections .o_we_color_combination_btn.selected .o_we_color_combination_btn_title::before{content: "\f00c"; margin-right: 8px; font-size: 0.8em; font-family: FontAwesome; color: #40ad67;}.colorpicker .o_colorpicker_sections .o_we_color_combination_btn .o_we_cc_preview_wrapper:after{bottom: -1px;}.colorpicker .o_colorpicker_sections .o_colorpicker_section{padding-top: 8px;}.colorpicker .o_colorpicker_sections .o_colorpicker_section::after{content: ""; display: table; clear: both;}.colorpicker .o_colorpicker_sections .o_colorpicker_section .o_we_color_btn{position: relative;}.colorpicker .o_colorpicker_sections .o_colorpicker_section .o_we_color_btn.selected{box-shadow: inset 0 0 0 1px #595964, inset 0 0 0 3px #01bad2, inset 0 0 0 4px white;}.colorpicker .o_colorpicker_sections .o_colorpicker_section .o_we_color_btn.o_btn_transparent::before{background-color: transparent;}.colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name="transparent_grayscale"] .o_we_color_btn::before, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name="transparent_grayscale"] .o_we_color_btn::after, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name="theme"] .o_we_color_btn::before, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name="theme"] .o_we_color_btn::after, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name="reset"] .o_we_color_btn::before, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name="reset"] .o_we_color_btn::after{box-shadow: inherit;}@keyframes dropZoneInsert{to{background-color: rgba(135, 90, 123, 0.3);}}.oe_drop_zone{background-color: rgba(135, 90, 123, 0.15); animation: dropZoneInsert 1s linear 0s infinite alternate;}.oe_drop_zone.oe_insert{position: relative; z-index: 1035; width: 100%; min-width: 30px; height: 30px; min-height: 30px; margin: -15px 0; border: 2px dashed #2b2b33;}.oe_drop_zone.oe_insert.oe_vertical{width: 30px; float: left; margin: 0 -15px;}.oe_drop_zone.oe_drop_zone_danger{background-color: rgba(230, 88, 108, 0.15); color: #e6586c; border-color: #e6586c;}#oe_manipulators{position: relative; z-index: 1035;}#oe_manipulators .oe_overlay{position: absolute; top: auto; left: auto; bottom: auto; right: auto; display: none; height: 0; border-color: #01bad2; background: transparent; text-align: center; transition: opacity 400ms linear 0s;}#oe_manipulators .oe_overlay.o_overlay_hidden{opacity: 0; transition: none;}#oe_manipulators .oe_overlay.oe_active{display: block; z-index: 1;}#oe_manipulators .oe_overlay > .o_handles{position: absolute; top: -100000px; left: 0; bottom: auto; right: 0; border-color: inherit;}#oe_manipulators .oe_overlay > .o_handles:hover > .o_handle{background-color: rgba(1, 186, 210, 0.05);}#oe_manipulators .oe_overlay > .o_handles > .o_handle{position: relative; border: 0 solid transparent; border-color: inherit; transition: background 300ms ease 0s;}#oe_manipulators .oe_overlay > .o_handles > .o_handle.w{position: absolute; top: 100000px; left: 0; bottom: -100000px; right: auto; width: 8px; border-width: 2px; border-right-width: 0; cursor: e-resize;}#oe_manipulators .oe_overlay > .o_handles > .o_handle.w:after{position: absolute; top: 50%; left: 40%; bottom: auto; right: auto; margin-top: -10px;}#oe_manipulators .oe_overlay > .o_handles > .o_handle.e{position: absolute; top: 100000px; left: auto; bottom: -100000px; right: 0; width: 8px; border-right-width: 2px; cursor: w-resize;}#oe_manipulators .oe_overlay > .o_handles > .o_handle.e:after{position: absolute; top: 50%; left: auto; bottom: auto; right: 40%; margin-top: -10px;}#oe_manipulators .oe_overlay > .o_handles > .o_handle.n{position: absolute; top: 100000px; left: 0; bottom: auto; right: 0; height: 8px; border-top-width: 2px; cursor: ns-resize;}#oe_manipulators .oe_overlay > .o_handles > .o_handle.n:after{position: absolute; top: 40%; left: 50%; bottom: auto; right: auto; margin-left: -10px;}#oe_manipulators .oe_overlay > .o_handles > .o_handle.s{position: absolute; top: auto; left: 0; bottom: -100000px; right: 0; height: 8px; border-bottom-width: 2px; cursor: ns-resize;}#oe_manipulators .oe_overlay > .o_handles > .o_handle.s:after{position: absolute; top: auto; left: 50%; bottom: 40%; right: auto; margin-left: -10px;}#oe_manipulators .oe_overlay > .o_handles > .o_handle::after{z-index: 1; display: block; width: 20px; height: 20px; border: solid 1px #01606c; line-height: 18px; font-size: 14px; font-family: FontAwesome; background-color: #018d9f; color: white;}#oe_manipulators .oe_overlay > .o_handles > .o_handle.o_handle_start:after{background-color: rgba(89, 89, 100, 0.6); border-color: rgba(0, 0, 0, 0.2);}#oe_manipulators .oe_overlay > .o_handles > .o_handle:hover, #oe_manipulators .oe_overlay > .o_handles > .o_handle.o_active{background-color: rgba(1, 186, 210, 0.2);}#oe_manipulators .oe_overlay > .o_handles > .o_handle:hover::after, #oe_manipulators .oe_overlay > .o_handles > .o_handle.o_active::after{border-color: #018d9f; background-color: #01606c;}#oe_manipulators .oe_overlay > .o_handles > .o_handle.w:after, #oe_manipulators .oe_overlay > .o_handles > .o_handle.e:after{content: "\f07e";}#oe_manipulators .oe_overlay > .o_handles > .o_handle.s:after, #oe_manipulators .oe_overlay > .o_handles > .o_handle.n:after{content: "\f07d";}#oe_manipulators .oe_overlay > .o_handles > .o_handle.o_handle_start.w:after, #oe_manipulators .oe_overlay > .o_handles > .o_handle.o_handle_start.e:after{content: '\f061';}#oe_manipulators .oe_overlay > .o_handles > .o_handle.o_handle_start.n:after, #oe_manipulators .oe_overlay > .o_handles > .o_handle.o_handle_start.s:after{content: '\f063';}#oe_manipulators .oe_overlay > .o_handles > .o_handle.o_handle_end.w:after, #oe_manipulators .oe_overlay > .o_handles > .o_handle.o_handle_end.e:after{content: '\f060';}#oe_manipulators .oe_overlay > .o_handles > .o_handle.o_handle_end.n:after, #oe_manipulators .oe_overlay > .o_handles > .o_handle.o_handle_end.s:after{content: '\f062';}#oe_manipulators .oe_overlay > .o_handles > .o_handle.readonly{cursor: auto !important;}#oe_manipulators .oe_overlay > .o_handles > .o_handle.readonly:after{display: none !important;}#oe_manipulators .oe_overlay > .o_handles > .o_handle.readonly:hover{opacity: 0.5;}#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap{position: absolute; top: 100000px; left: 50%; bottom: auto; right: auto; transform: translate(-50%, -110%);}#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > .o_overlay_move_options{display: -webkit-box; display: -webkit-flex; display: flex;}#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > .o_overlay_move_options > *, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > .o_overlay_move_options + *{margin: 0 1px 0; min-width: 22px; padding: 0 3px; color: #FFFFFF;}#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > .fa-trash{margin-left: 6px; background-color: #a05968;}#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > .fa-trash:not(.oe_snippet_remove){opacity: 0.5;}#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > .o_overlay_move_options > .o_move_handle{cursor: move; width: 30px; height: 22px; background-image: url("/web_editor/static/src/img/snippets_options/o_overlay_move_drag.svg"); background-position: center; background-repeat: no-repeat;}#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover > .o_overlay_move_options > *, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover > .o_overlay_move_options + *{opacity: 0.6;}#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover > .o_overlay_move_options > *:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover > .o_overlay_move_options > *:focus, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover > .o_overlay_move_options > *.focus, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover > .o_overlay_move_options + *:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover > .o_overlay_move_options + *:focus, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover > .o_overlay_move_options + *.focus{opacity: 1;}#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover > .o_overlay_move_options > *:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover > .o_overlay_move_options + *:hover{border-color: #2b2c34; background-color: #2b2b33;}#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover > .fa-trash:hover{border-color: #2c2b33; background-color: #e6586c;}#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover > .fa-trash:hover:not(.oe_snippet_remove){opacity: 0.5;}#oe_manipulators .oe_overlay.o_top_cover > .o_handles > .o_overlay_options_wrap{top: auto; bottom: -100000px; transform: translate(-50%, 110%);}#oe_manipulators .oe_overlay.o_we_overlay_preview{pointer-events: none;}#oe_manipulators .oe_overlay.o_we_overlay_preview > .o_handles > .o_handle::after, #oe_manipulators .oe_overlay.o_we_overlay_preview > .o_handles .o_overlay_options_wrap{display: none;}#oe_manipulators .oe_overlay.o_we_background_position_overlay{background-color: rgba(0, 0, 0, 0.7); z-index: auto;}#oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_content{cursor: url(/web/static/src/img/openhand.cur), grab;}#oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_content .o_we_grabbing{cursor: grabbing;}#oe_manipulators .oe_overlay.o_we_background_position_overlay .o_overlay_background > *{display: block !important; top: 0 !important; right: 0 !important; bottom: 0 !important; left: 0 !important; transform: none !important; max-width: unset !important; max-height: unset !important;}.s-resize-important *{cursor: s-resize !important;}.n-resize-important *{cursor: n-resize !important;}.e-resize-important *{cursor: e-resize !important;}.w-resize-important *{cursor: w-resize !important;}.move-important *{cursor: move !important;}.dropdown-menu label .o_switch{margin: 0; padding: 2px 0;}.text-input-group{position: relative; margin-bottom: 45px;}.text-input-group input{font-size: 18px; padding: 10px 10px 10px 5px; display: block; width: 300px; border: none; border-bottom: 1px solid #757575;}.text-input-group input:focus{outline: none;}.text-input-group label{color: #999; font-size: 18px; font-weight: normal; position: absolute; top: 10px; left: 5px; bottom: auto; right: auto; pointer-events: none; transition: 0.2s ease all;}.text-input-group input:focus ~ label, .text-input-group input:valid ~ label{top: -20px; font-size: 14px; color: #5264AE;}.text-input-group .bar{position: relative; display: block; width: 300px;}.text-input-group .bar:before, .text-input-group .bar:after{content: ''; height: 2px; width: 0; bottom: 1px; position: absolute; top: auto; left: auto; bottom: auto; right: auto; background: #5264AE; transition: 0.2s ease all;}.text-input-group .bar:before{left: 50%;}.text-input-group .bar:after{right: 50%;}.text-input-group input:focus ~ .bar:before, .text-input-group input:focus ~ .bar:after{width: 50%;}.text-input-group .highlight{position: absolute; top: 25%; left: 0; bottom: auto; right: auto; height: 60%; width: 100px; pointer-events: none; opacity: 0.5;}.text-input-group input:focus ~ .highlight{animation: inputHighlighter 0.3s ease;}.oe_snippet_body{opacity: 0; animation: fadeInDownSmall 700ms forwards;}.o_container_preview{outline: 2px dashed #01bad2;}we-select.o_we_shape_menu we-button[data-shape]{padding: 0 !important;}we-select.o_we_shape_menu we-button[data-shape].active{border: 1px solid #40ad67 !important;}we-select.o_we_shape_menu we-button[data-shape] div{width: 100%;}we-select.o_we_shape_menu we-button[data-shape] .o_we_shape_btn_content{width: 100%; height: 75px;}.o_we_ui_loading{position: absolute; top: 0; left: 0; bottom: 0; right: 0; z-index: 1036; background-color: rgba(0, 0, 0, 0.2); color: #FFFFFF;}#oe_manipulators > .o_we_ui_loading{position: fixed; right: 291px;}.o_we_force_no_transition{transition: none !important;}

/* /web_editor/static/lib/jabberwock/jabberwock.css defined in bundle 'web_editor.assets_wysiwyg' */


/* /web_unsplash/static/src/scss/unsplash.scss defined in bundle 'web_editor.assets_wysiwyg' */
 .unsplash_error{padding: 30px 0;}.unsplash_error .access_key_box{padding: 9px; background-color: #fcfcfc; border: 1px solid #ededee;}.unsplash_error .access_key_box input{min-width: 300px;}