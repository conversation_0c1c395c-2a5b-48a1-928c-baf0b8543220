
/* /website_sale/static/src/snippets/s_dynamic_snippet_products/options.js defined in bundle 'website.assets_wysiwyg' */
odoo.define('website_sale.s_dynamic_snippet_products_options',function(require){'use strict';const options=require('web_editor.snippets.options');const s_dynamic_snippet_carousel_options=require('website.s_dynamic_snippet_carousel_options');const dynamicSnippetProductsOptions=s_dynamic_snippet_carousel_options.extend({init:function(){this._super.apply(this,arguments);this.productCategories={};},onBuilt:function(){this._super.apply(this,arguments);this.$target[0].dataset['snippet']='s_dynamic_snippet_products';this._rpc({route:'/website_sale/snippet/options_filters'}).then((data)=>{if(data.length){this.$target.get(0).dataset.filterId=data[0].id;this.$target.get(0).dataset.numberOfRecords=this.dynamicFilters[data[0].id].limit;this._refreshPublicWidgets();}});},_computeWidgetVisibility:function(widgetName,params){if(widgetName==='filter_opt'){return false;}
return this._super.apply(this,arguments);},_fetchProductCategories:function(){return this._rpc({model:'product.public.category',method:'search_read',kwargs:{domain:[],fields:['id','name'],}});},_renderCustomXML:async function(uiFragment){await this._super.apply(this,arguments);await this._renderProductCategorySelector(uiFragment);},_renderProductCategorySelector:async function(uiFragment){const productCategories=await this._fetchProductCategories();for(let index in productCategories){this.productCategories[productCategories[index].id]=productCategories[index];}
const productCategoriesSelectorEl=uiFragment.querySelector('[data-name="product_category_opt"]');return this._renderSelectUserValueWidgetButtons(productCategoriesSelectorEl,this.productCategories);},_setOptionsDefaultValues:function(){this._super.apply(this,arguments);const templateKeys=this.$el.find("we-select[data-attribute-name='templateKey'] we-selection-items we-button");if(templateKeys.length>0){this._setOptionValue('templateKey',templateKeys.attr('data-select-data-attribute'));}
const productCategories=this.$el.find("we-select[data-attribute-name='productCategoryId'] we-selection-items we-button");if(productCategories.length>0){this._setOptionValue('productCategoryId',productCategories.attr('data-select-data-attribute'));}},});options.registry.dynamic_snippet_products=dynamicSnippetProductsOptions;return dynamicSnippetProductsOptions;});;

/* /website/static/src/js/editor/editor.js defined in bundle 'website.assets_wysiwyg' */
odoo.define('website.editor',function(require){'use strict';var weWidgets=require('web_editor.widget');var wUtils=require('website.utils');weWidgets.LinkDialog.include({start:function(){wUtils.autocompleteWithPages(this,this.$('input[name="url"]'));return this._super.apply(this,arguments);},});});;

/* /website/static/src/js/editor/mega_menu.js defined in bundle 'website.assets_wysiwyg' */
;

/* /website/static/src/js/editor/snippets.editor.js defined in bundle 'website.assets_wysiwyg' */
odoo.define('website.snippet.editor',function(require){'use strict';const{qweb,_t,_lt}=require('web.core');const Dialog=require('web.Dialog');const publicWidget=require('web.public.widget');const weSnippetEditor=require('web_editor.snippet.editor');const wSnippetOptions=require('website.editor.snippets.options');const FontFamilyPickerUserValueWidget=wSnippetOptions.FontFamilyPickerUserValueWidget;weSnippetEditor.Class.include({xmlDependencies:(weSnippetEditor.Class.prototype.xmlDependencies||[]).concat(['/website/static/src/xml/website.editor.xml']),events:_.extend({},weSnippetEditor.Class.prototype.events,{'click .o_we_customize_theme_btn':'_onThemeTabClick',}),custom_events:Object.assign({},weSnippetEditor.Class.prototype.custom_events,{'gmap_api_request':'_onGMapAPIRequest','gmap_api_key_request':'_onGMapAPIKeyRequest',}),tabs:_.extend({},weSnippetEditor.Class.prototype.tabs,{THEME:'theme',}),optionsTabStructure:[['theme-colors',_lt("Theme Colors")],['theme-options',_lt("Theme Options")],['website-settings',_lt("Website Settings")],],_computeSnippetTemplates:function(html){const $html=$(html);const fontVariables=_.map($html.find('we-fontfamilypicker[data-variable]'),el=>{return el.dataset.variable;});FontFamilyPickerUserValueWidget.prototype.fontVariables=fontVariables;return this._super(...arguments);},async _configureGMapAPI({reconfigure,onlyIfUndefined}){if(!reconfigure&&!onlyIfUndefined){return false;}
const apiKey=await new Promise(resolve=>{this.getParent().trigger_up('gmap_api_key_request',{onSuccess:key=>resolve(key),});});const apiKeyValidation=apiKey?await this._validateGMapAPIKey(apiKey):{isValid:false,message:undefined,};if(!reconfigure&&onlyIfUndefined&&apiKey&&apiKeyValidation.isValid){return false;}
let websiteId;this.trigger_up('context_get',{callback:ctx=>websiteId=ctx['website_id'],});function applyError(message){const $apiKeyInput=this.find('#api_key_input');const $apiKeyHelp=this.find('#api_key_help');$apiKeyInput.addClass('is-invalid');$apiKeyHelp.empty().text(message);}
const $content=$(qweb.render('website.s_google_map_modal',{apiKey:apiKey,}));if(!apiKeyValidation.isValid&&apiKeyValidation.message){applyError.call($content,apiKeyValidation.message);}
return new Promise(resolve=>{let invalidated=false;const dialog=new Dialog(this,{size:'medium',title:_t("Google Map API Key"),buttons:[{text:_t("Save"),classes:'btn-primary',click:async(ev)=>{const valueAPIKey=dialog.$('#api_key_input').val();if(!valueAPIKey){applyError.call(dialog.$el,_t("Enter an API Key"));return;}
const $button=$(ev.currentTarget);$button.prop('disabled',true);const res=await this._validateGMapAPIKey(valueAPIKey);if(res.isValid){await this._rpc({model:'website',method:'write',args:[[websiteId],{google_maps_api_key:valueAPIKey},],});invalidated=true;dialog.close();}else{applyError.call(dialog.$el,res.message);}
$button.prop("disabled",false);}},{text:_t("Cancel"),close:true}],$content:$content,});dialog.on('closed',this,()=>resolve(invalidated));dialog.open();});},async _validateGMapAPIKey(key){try{const response=await fetch(`https://maps.googleapis.com/maps/api/staticmap?center=belgium&size=10x10&key=${encodeURIComponent(key)}`);const isValid=(response.status===200);return{isValid:isValid,message:!isValid&&_t("Invalid API Key. The following error was returned by Google:")+" "+(await response.text()),};}catch(err){return{isValid:false,message:_t("Check your connection and try again"),};}},_getScrollOptions(options={}){const finalOptions=this._super(...arguments);if(!options.offsetElements||!options.offsetElements.$top){const $header=$('#top');if($header.length){finalOptions.offsetElements=finalOptions.offsetElements||{};finalOptions.offsetElements.$top=$header;}}
return finalOptions;},async _handleGMapRequest(ev,gmapRequestEventName){ev.stopPropagation();const reconfigured=await this._configureGMapAPI({reconfigure:ev.data.reconfigure,onlyIfUndefined:ev.data.configureIfNecessary,});this.getParent().trigger_up(gmapRequestEventName,{refetch:reconfigured,editableMode:true,onSuccess:key=>ev.data.onSuccess(key),});},_updateLeftPanelContent:function({content,tab}){this._super(...arguments);this.$('.o_we_customize_theme_btn').toggleClass('active',tab===this.tabs.THEME);},_onGMapAPIRequest(ev){this._handleGMapRequest(ev,'gmap_api_request');},_onGMapAPIKeyRequest(ev){this._handleGMapRequest(ev,'gmap_api_key_request');},async _onThemeTabClick(ev){let releaseLoader;try{const promise=new Promise(resolve=>releaseLoader=resolve);this._execWithLoadingEffect(()=>promise,false,0);await new Promise(resolve=>requestAnimationFrame(()=>requestAnimationFrame(resolve)));if(!this.topFakeOptionEl){let el;for(const[elementName,title]of this.optionsTabStructure){const newEl=document.createElement(elementName);newEl.dataset.name=title;if(el){el.appendChild(newEl);}else{this.topFakeOptionEl=newEl;}
el=newEl;}
this.bottomFakeOptionEl=el;this.el.appendChild(this.topFakeOptionEl);}
this.topFakeOptionEl.classList.remove('d-none');const editorPromise=this._activateSnippet($(this.bottomFakeOptionEl));releaseLoader();releaseLoader=undefined;const editor=await editorPromise;this.topFakeOptionEl.classList.add('d-none');editor.toggleOverlay(false);this._updateLeftPanelContent({tab:this.tabs.THEME,});}catch(e){if(releaseLoader){releaseLoader();}
throw e;}},});weSnippetEditor.Editor.include({layoutElementsSelector:[weSnippetEditor.Editor.prototype.layoutElementsSelector,'.s_parallax_bg','.o_bg_video_container',].join(','),});publicWidget.registry.hoverableDropdown.include({start(){if(this.editableMode){this._onPageClick=this._onPageClick.bind(this);this.el.closest('#wrapwrap').addEventListener('click',this._onPageClick,{capture:true});}
return this._super.apply(this,arguments);},destroy(){if(this.editableMode){this.el.closest('#wrapwrap').removeEventListener('click',this._onPageClick,{capture:true});}
return this._super.apply(this,arguments);},_hideDropdowns(){for(const toggleEl of this.el.querySelectorAll('.dropdown.show .dropdown-toggle')){$(toggleEl).dropdown('hide');}},_onPageClick(ev){if(ev.target.closest('.dropdown.show')){return;}
this._hideDropdowns();},_onMouseEnter(ev){if(this.editableMode){if(this.el.querySelector('.dropdown.show')){return;}}
this._super(...arguments);},_onMouseLeave(ev){if(this.editableMode){return;}
this._super(...arguments);},});});;

/* /website/static/src/js/editor/rte.summernote.js defined in bundle 'website.assets_wysiwyg' */
odoo.define('website.rte.summernote',function(require){'use strict';var core=require('web.core');const rte=require('web_editor.rte');require('web_editor.rte.summernote');var eventHandler=$.summernote.eventHandler;var renderer=$.summernote.renderer;var tplIconButton=renderer.getTemplate().iconButton;var _t=core._t;var fn_tplPopovers=renderer.tplPopovers;renderer.tplPopovers=function(lang,options){var $popover=$(fn_tplPopovers.call(this,lang,options));$popover.find('.note-image-popover .btn-group:has([data-value="img-thumbnail"])').append(tplIconButton('fa fa-object-ungroup',{title:_t('Transform the picture (click twice to reset transformation)'),event:'transform',}));return $popover;};$.summernote.pluginEvents.transform=function(event,editor,layoutInfo,sorted){var $selection=layoutInfo.handle().find('.note-control-selection');var $image=$($selection.data('target'));if($image.data('transfo-destroy')){$image.removeData('transfo-destroy');return;}
$image.transfo();var mouseup=function(event){$('.note-popover button[data-event="transform"]').toggleClass('active',$image.is('[style*="transform"]'));};$(document).on('mouseup',mouseup);var mousedown=function(event){if(!$(event.target).closest('.transfo-container').length){$image.transfo('destroy');$(document).off('mousedown',mousedown).off('mouseup',mouseup);}
if($(event.target).closest('.note-popover').length){$image.data('transfo-destroy',true).attr('style',($image.attr('style')||'').replace(/[^;]*transform[\w:]*;?/g,''));}
$image.trigger('content_changed');};$(document).on('mousedown',mousedown);};var fn_boutton_update=eventHandler.modules.popover.button.update;eventHandler.modules.popover.button.update=function($container,oStyle){fn_boutton_update.call(this,$container,oStyle);$container.find('button[data-event="transform"]').toggleClass('active',$(oStyle.image).is('[style*="transform"]')).toggleClass('d-none',!$(oStyle.image).is('img'));};rte.Class.include({async start(){const res=await this._super(...arguments);this.__$editable.find('.s_company_team .o_not_editable img').prop('contenteditable',true);return res;},});});;

/* /website/static/src/js/editor/snippets.options.js defined in bundle 'website.assets_wysiwyg' */
odoo.define('website.editor.snippets.options',function(require){'use strict';const{ColorpickerWidget}=require('web.Colorpicker');const config=require('web.config');var core=require('web.core');var Dialog=require('web.Dialog');const dom=require('web.dom');const weUtils=require('web_editor.utils');var options=require('web_editor.snippets.options');const wUtils=require('website.utils');require('website.s_popup_options');var _t=core._t;var qweb=core.qweb;const InputUserValueWidget=options.userValueWidgetsRegistry['we-input'];const SelectUserValueWidget=options.userValueWidgetsRegistry['we-select'];const UrlPickerUserValueWidget=InputUserValueWidget.extend({custom_events:_.extend({},InputUserValueWidget.prototype.custom_events||{},{'website_url_chosen':'_onWebsiteURLChosen',}),events:_.extend({},InputUserValueWidget.prototype.events||{},{'click .o_we_redirect_to':'_onRedirectTo',}),start:async function(){await this._super(...arguments);const linkButton=document.createElement('we-button');const icon=document.createElement('i');icon.classList.add('fa','fa-fw','fa-external-link')
linkButton.classList.add('o_we_redirect_to');linkButton.title=_t("Redirect to URL in a new tab");linkButton.appendChild(icon);this.containerEl.appendChild(linkButton);this.el.classList.add('o_we_large_input');this.inputEl.classList.add('text-left');const options={position:{collision:'flip flipfit',},classes:{"ui-autocomplete":'o_website_ui_autocomplete'},};wUtils.autocompleteWithPages(this,$(this.inputEl),options);},_onWebsiteURLChosen:function(ev){this._value=this.inputEl.value;this._onUserValueChange(ev);},_onRedirectTo:function(){if(this._value){window.open(this._value,'_blank');}},});const FontFamilyPickerUserValueWidget=SelectUserValueWidget.extend({xmlDependencies:(SelectUserValueWidget.prototype.xmlDependencies||[]).concat(['/website/static/src/xml/website.editor.xml']),events:_.extend({},SelectUserValueWidget.prototype.events||{},{'click .o_we_add_google_font_btn':'_onAddGoogleFontClick','click .o_we_delete_google_font_btn':'_onDeleteGoogleFontClick',}),fontVariables:[],start:async function(){const style=window.getComputedStyle(document.documentElement);const nbFonts=parseInt(weUtils.getCSSVariableValue('number-of-fonts',style));const googleFontsProperty=weUtils.getCSSVariableValue('google-fonts',style);this.googleFonts=googleFontsProperty?googleFontsProperty.split(/\s*,\s*/g):[];this.googleFonts=this.googleFonts.map(font=>font.substring(1,font.length-1));const googleLocalFontsProperty=weUtils.getCSSVariableValue('google-local-fonts',style);this.googleLocalFonts=googleLocalFontsProperty?googleLocalFontsProperty.slice(1,-1).split(/\s*,\s*/g):[];this.googleFonts=this.googleFonts.filter(font=>{const localFonts=this.googleLocalFonts.map(localFont=>localFont.split(":")[0]);return localFonts.indexOf(`'${font}'`)===-1;});this.allFonts=[];await this._super(...arguments);const fontEls=[];const methodName=this.el.dataset.methodName||'customizeWebsiteVariable';const variable=this.el.dataset.variable;const themeFontsNb=nbFonts-(this.googleLocalFonts.length+this.googleFonts.length);_.times(nbFonts,fontNb=>{const realFontNb=fontNb+1;const fontEl=document.createElement('we-button');fontEl.classList.add(`o_we_option_font_${realFontNb}`);fontEl.dataset.variable=variable;fontEl.dataset[methodName]=weUtils.getCSSVariableValue(`font-number-${realFontNb}`,style);const font=weUtils.getCSSVariableValue(`font-number-${realFontNb}`,style);this.allFonts.push(font);fontEl.dataset[methodName]=font;fontEl.dataset.font=realFontNb;if(realFontNb<=themeFontsNb){fontEl.appendChild(Object.assign(document.createElement('i'),{role:'button',className:'text-info ml-2 fa fa-cloud',title:_t("This font is hosted and served to your visitors by Google servers"),}));}
fontEls.push(fontEl);this.menuEl.appendChild(fontEl);});if(this.googleLocalFonts.length){const googleLocalFontsEls=fontEls.splice(-this.googleLocalFonts.length);googleLocalFontsEls.forEach((el,index)=>{$(el).append(core.qweb.render('website.delete_google_font_btn',{index:index,local:true,}));});}
if(this.googleFonts.length){const googleFontsEls=fontEls.splice(-this.googleFonts.length);googleFontsEls.forEach((el,index)=>{$(el).append(core.qweb.render('website.delete_google_font_btn',{index:index,}));});}
$(this.menuEl).append($(core.qweb.render('website.add_google_font_btn',{variable:variable,})));},async setValue(){await this._super(...arguments);for(const className of this.menuTogglerEl.classList){if(className.match(/^o_we_option_font_\d+$/)){this.menuTogglerEl.classList.remove(className);}}
const activeWidget=this._userValueWidgets.find(widget=>!widget.isPreviewed()&&widget.isActive());if(activeWidget){this.menuTogglerEl.classList.add(`o_we_option_font_${activeWidget.el.dataset.font}`);}},_onAddGoogleFontClick:function(ev){const variable=$(ev.currentTarget).data('variable');const dialog=new Dialog(this,{title:_t("Add a Google Font"),$content:$(core.qweb.render('website.dialog.addGoogleFont')),buttons:[{text:_t("Save & Reload"),classes:'btn-primary',click:async()=>{const inputEl=dialog.el.querySelector('.o_input_google_font');let m=inputEl.value.match(/\bspecimen\/([\w+]+)/);if(!m){m=inputEl.value.match(/\bfamily=([\w+]+)/);if(!m){inputEl.classList.add('is-invalid');return;}}
let isValidFamily=false;try{const result=await fetch("https://fonts.googleapis.com/css?family="+m[1]+':300,300i,400,400i,700,700i',{method:'HEAD'});if(result.ok){isValidFamily=true;}}catch(error){console.error(error);}
if(!isValidFamily){inputEl.classList.add('is-invalid');return;}
const font=m[1].replace(/\+/g,' ');const googleFontServe=dialog.el.querySelector('#google_font_serve').checked;const fontName=`'${font}'`;const fontExistsLocally=this.googleLocalFonts.some(localFont=>localFont.split(':')[0]===fontName);const fontExistsOnServer=this.allFonts.includes(fontName);const preventFontAddition=fontExistsLocally||(fontExistsOnServer&&googleFontServe);if(preventFontAddition){inputEl.classList.add('is-invalid');inputEl.setCustomValidity(_t("This font already exists, you can only add it as a local font to replace the server version."));inputEl.reportValidity();return;}
if(googleFontServe){this.googleFonts.push(font);}else{this.googleLocalFonts.push(`'${font}': ''`);}
this.trigger_up('google_fonts_custo_request',{values:{[variable]:`'${font}'`},googleFonts:this.googleFonts,googleLocalFonts:this.googleLocalFonts,});},},{text:_t("Discard"),close:true,},],});dialog.open();},_onDeleteGoogleFontClick:async function(ev){ev.preventDefault();const values={};const save=await new Promise(resolve=>{Dialog.confirm(this,_t("Deleting a font requires a reload of the page. This will save all your changes and reload the page, are you sure you want to proceed?"),{confirm_callback:()=>resolve(true),cancel_callback:()=>resolve(false),});});if(!save){return;}
const googleFontIndex=parseInt(ev.target.dataset.fontIndex);const isLocalFont=ev.target.dataset.localFont;let googleFontName;if(isLocalFont){const googleFont=this.googleLocalFonts[googleFontIndex].split(':');googleFontName=googleFont[0].substring(1,googleFont[0].length-1);values['delete-font-attachment-id']=googleFont[1];this.googleLocalFonts.splice(googleFontIndex,1);}else{googleFontName=this.googleFonts[googleFontIndex];this.googleFonts.splice(googleFontIndex,1);}
const style=window.getComputedStyle(document.documentElement);_.each(FontFamilyPickerUserValueWidget.prototype.fontVariables,variable=>{const value=weUtils.getCSSVariableValue(variable,style);if(value.substring(1,value.length-1)===googleFontName){values[variable]='null';}});this.trigger_up('google_fonts_custo_request',{values:values,googleFonts:this.googleFonts,googleLocalFonts:this.googleLocalFonts,});},});const GPSPicker=InputUserValueWidget.extend({events:{'blur input':'_onInputBlur',},init(){this._super(...arguments);this._gmapCacheGPSToPlace={};},async willStart(){await this._super(...arguments);this._gmapLoaded=await new Promise(resolve=>{this.trigger_up('gmap_api_request',{editableMode:true,configureIfNecessary:true,onSuccess:key=>{if(!key){resolve(false);return;}
this._nearbySearch('(50.854975,4.3753899)',!!key).then(place=>resolve(!!place));},});});if(!this._gmapLoaded&&!this._gmapErrorNotified){this.trigger_up('user_value_widget_critical');return;}},async start(){await this._super(...arguments);this.el.classList.add('o_we_large_input');if(!this._gmapLoaded){return;}
this._gmapAutocomplete=new google.maps.places.Autocomplete(this.inputEl,{types:['geocode']});google.maps.event.addListener(this._gmapAutocomplete,'place_changed',this._onPlaceChanged.bind(this));},getMethodsParams:function(methodName){return Object.assign({gmapPlace:this._gmapPlace||{}},this._super(...arguments));},async setValue(){await this._super(...arguments);if(!this._gmapLoaded){return;}
this._gmapPlace=await this._nearbySearch(this._value);if(this._gmapPlace){this.inputEl.value=this._gmapPlace.formatted_address;}},async _nearbySearch(gps,notify=true){if(this._gmapCacheGPSToPlace[gps]){return this._gmapCacheGPSToPlace[gps];}
const p=gps.substring(1).slice(0,-1).split(',');const location=new google.maps.LatLng(p[0]||0,p[1]||0);return new Promise(resolve=>{const service=new google.maps.places.PlacesService(document.createElement('div'));service.nearbySearch({location:location,radius:1,},(results,status)=>{const GMAP_CRITICAL_ERRORS=[google.maps.places.PlacesServiceStatus.REQUEST_DENIED,google.maps.places.PlacesServiceStatus.UNKNOWN_ERROR];if(status===google.maps.places.PlacesServiceStatus.OK){service.getDetails({placeId:results[0].place_id,fields:['geometry','formatted_address'],},(place,status)=>{if(status===google.maps.places.PlacesServiceStatus.OK){this._gmapCacheGPSToPlace[gps]=place;resolve(place);}else if(GMAP_CRITICAL_ERRORS.includes(status)){if(notify){this._notifyGMapError();}
resolve();}});}else if(GMAP_CRITICAL_ERRORS.includes(status)){if(notify){this._notifyGMapError();}
resolve();}else{resolve();}});});},_notifyGMapError(){if(this._gmapErrorNotified){return;}
this._gmapErrorNotified=true;this.displayNotification({type:'danger',sticky:true,message:_t("A Google Map error occurred. Make sure to read the key configuration popup carefully."),});this.trigger_up('gmap_api_request',{editableMode:true,reconfigure:true,onSuccess:()=>{this._gmapErrorNotified=false;},});setTimeout(()=>this.trigger_up('user_value_widget_critical'));},_onPlaceChanged(ev){const gmapPlace=this._gmapAutocomplete.getPlace();if(gmapPlace&&gmapPlace.geometry){this._gmapPlace=gmapPlace;const location=this._gmapPlace.geometry.location;const oldValue=this._value;this._value=`(${location.lat()},${location.lng()})`;this._gmapCacheGPSToPlace[this._value]=gmapPlace;if(oldValue!==this._value){this._onUserValueChange(ev);}}},_onInputBlur(){},});options.userValueWidgetsRegistry['we-urlpicker']=UrlPickerUserValueWidget;options.userValueWidgetsRegistry['we-fontfamilypicker']=FontFamilyPickerUserValueWidget;options.userValueWidgetsRegistry['we-gpspicker']=GPSPicker;options.Class.include({xmlDependencies:(options.Class.prototype.xmlDependencies||[]).concat(['/website/static/src/xml/website.editor.xml']),custom_events:_.extend({},options.Class.prototype.custom_events||{},{'google_fonts_custo_request':'_onGoogleFontsCustoRequest',}),customizeWebsiteViews:async function(previewMode,widgetValue,params){await this._customizeWebsite(previewMode,widgetValue,params,'views');},customizeWebsiteVariable:async function(previewMode,widgetValue,params){await this._customizeWebsite(previewMode,widgetValue,params,'variable');},customizeWebsiteColor:async function(previewMode,widgetValue,params){await this._customizeWebsite(previewMode,widgetValue,params,'color');},async _checkIfWidgetsUpdateNeedReload(widgets){const needReload=await this._super(...arguments);if(needReload){return needReload;}
for(const widget of widgets){const methodsNames=widget.getMethodsNames();if(!methodsNames.includes('customizeWebsiteViews')&&!methodsNames.includes('customizeWebsiteVariable')&&!methodsNames.includes('customizeWebsiteColor')){continue;}
let paramsReload=false;if(widget.getMethodsParams('customizeWebsiteViews').reload||widget.getMethodsParams('customizeWebsiteVariable').reload||widget.getMethodsParams('customizeWebsiteColor').reload){paramsReload=true;}
if(paramsReload||config.isDebug('assets')){return(config.isDebug('assets')?_t("It appears you are in debug=assets mode, all theme customization options require a page reload in this mode."):true);}}
return false;},_computeWidgetState:async function(methodName,params){switch(methodName){case'customizeWebsiteViews':{const allXmlIDs=this._getXMLIDsFromPossibleValues(params.possibleValues);const enabledXmlIDs=await this._rpc({route:'/website/theme_customize_get',params:{'xml_ids':allXmlIDs,},});let mostXmlIDsStr='';let mostXmlIDsNb=0;for(const xmlIDsStr of params.possibleValues){const enableXmlIDs=xmlIDsStr.split(/\s*,\s*/);if(enableXmlIDs.length>mostXmlIDsNb&&enableXmlIDs.every(xmlID=>enabledXmlIDs.includes(xmlID))){mostXmlIDsStr=xmlIDsStr;mostXmlIDsNb=enableXmlIDs.length;}}
return mostXmlIDsStr;}
case'customizeWebsiteVariable':{return weUtils.getCSSVariableValue(params.variable);}
case'customizeWebsiteColor':{const bugfixedValue=weUtils.getCSSVariableValue(`bugfixed-${params.color}`);if(bugfixedValue){return bugfixedValue;}
return weUtils.getCSSVariableValue(params.color);}}
return this._super(...arguments);},_customizeWebsite:async function(previewMode,widgetValue,params,type){if(previewMode){return;}
switch(type){case'views':await this._customizeWebsiteViews(widgetValue,params);break;case'variable':await this._customizeWebsiteVariable(widgetValue,params);break;case'color':await this._customizeWebsiteColor(widgetValue,params);break;}
if(params.reload||config.isDebug('assets')){return;}
await this._reloadBundles();await new Promise((resolve,reject)=>{this.trigger_up('widgets_start_request',{editableMode:true,onSuccess:()=>resolve(),onFailure:()=>reject(),});});},_customizeWebsiteColor:async function(color,params){const baseURL='/website/static/src/scss/options/colors/';const colorType=params.colorType?(params.colorType+'_'):'';const url=`${baseURL}user_${colorType}color_palette.scss`;if(color){if(weUtils.isColorCombinationName(color)){color=parseInt(color);}else if(!ColorpickerWidget.isCSSColor(color)){color=`'${color}'`;}}
return this._makeSCSSCusto(url,{[params.color]:color});},_customizeWebsiteVariable:async function(value,params){return this._makeSCSSCusto('/website/static/src/scss/options/user_values.scss',{[params.variable]:value,});},_customizeWebsiteViews:async function(xmlID,params){const allXmlIDs=this._getXMLIDsFromPossibleValues(params.possibleValues);const enableXmlIDs=xmlID.split(/\s*,\s*/);const disableXmlIDs=allXmlIDs.filter(xmlID=>!enableXmlIDs.includes(xmlID));return this._rpc({route:'/website/theme_customize',params:{'enable':enableXmlIDs,'disable':disableXmlIDs,},});},_getXMLIDsFromPossibleValues:function(possibleValues){const allXmlIDs=[];for(const xmlIDsStr of possibleValues){allXmlIDs.push(...xmlIDsStr.split(/\s*,\s*/));}
return allXmlIDs.filter((v,i,arr)=>arr.indexOf(v)===i);},_makeSCSSCusto:async function(url,values){return this._rpc({route:'/website/make_scss_custo',params:{'url':url,'values':_.mapObject(values,v=>v||'null'),},});},_refreshPublicWidgets:async function($el){return new Promise((resolve,reject)=>{this.trigger_up('widgets_start_request',{editableMode:true,$target:$el||this.$target,onSuccess:resolve,onFailure:reject,});});},_reloadBundles:async function(){const bundles=await this._rpc({route:'/website/theme_customize_bundle_reload',});let $allLinks=$();const proms=_.map(bundles,(bundleURLs,bundleName)=>{var $links=$('link[href*="'+bundleName+'"]');$allLinks=$allLinks.add($links);var $newLinks=$();_.each(bundleURLs,url=>{$newLinks=$newLinks.add($('<link/>',{type:'text/css',rel:'stylesheet',href:url,}));});const linksLoaded=new Promise(resolve=>{let nbLoaded=0;$newLinks.on('load error',()=>{if(++nbLoaded>=$newLinks.length){resolve();}});});$links.last().after($newLinks);return linksLoaded;});await Promise.all(proms).then(()=>$allLinks.remove());},_select:async function(previewMode,widget){await this._super(...arguments);if(!widget.$el.closest('[data-no-widget-refresh="true"]').length){await this._refreshPublicWidgets();}},_onGoogleFontsCustoRequest:function(ev){const values=ev.data.values?_.clone(ev.data.values):{};const googleFonts=ev.data.googleFonts;const googleLocalFonts=ev.data.googleLocalFonts;if(googleFonts.length){values['google-fonts']="('"+googleFonts.join("', '")+"')";}else{values['google-fonts']='null';}
if(googleLocalFonts!==undefined&&googleLocalFonts.length){values['google-local-fonts']="("+googleLocalFonts.join(", ")+")";}else{values['google-local-fonts']='null';}
this.trigger_up('snippet_edition_request',{exec:async()=>{return this._makeSCSSCusto('/website/static/src/scss/options/user_values.scss',values);}});this.trigger_up('request_save',{reloadEditor:true,});},});function _getLastPreFilterLayerElement($el){const $bgVideo=$el.find('> .o_bg_video_container');if($bgVideo.length){return $bgVideo[0];}
const $parallaxEl=$el.find('> .s_parallax_bg');if($parallaxEl.length){return $parallaxEl[0];}
return null;}
options.registry.BackgroundToggler.include({toggleBgVideo(previewMode,widgetValue,params){if(!widgetValue){const[bgVideoWidget]=this._requestUserValueWidgets('bg_video_opt');const bgVideoOpt=bgVideoWidget.getParent();return bgVideoOpt._setBgVideo(false,'');}else{this._requestUserValueWidgets('bg_video_opt')[0].el.click();}},_computeWidgetState(methodName,params){if(methodName==='toggleBgVideo'){return this.$target[0].classList.contains('o_background_video');}
return this._super(...arguments);},_getLastPreFilterLayerElement(){const el=_getLastPreFilterLayerElement(this.$target);if(el){return el;}
return this._super(...arguments);},});options.registry.BackgroundShape.include({_getLastPreShapeLayerElement(){const el=this._super(...arguments);if(el){return el;}
return _getLastPreFilterLayerElement(this.$target);},_removeShapeEl(shapeEl){this.trigger_up('widgets_stop_request',{$target:$(shapeEl),});return this._super(...arguments);},});options.registry.BackgroundVideo=options.Class.extend({background:function(previewMode,widgetValue,params){if(previewMode==='reset'&&this.videoSrc){return this._setBgVideo(false,this.videoSrc);}
return this._setBgVideo(previewMode,widgetValue);},_computeWidgetState:function(methodName,params){if(methodName==='background'){if(this.$target[0].classList.contains('o_background_video')){return this.$('> .o_bg_video_container iframe').attr('src');}
return'';}
return this._super(...arguments);},_setBgVideo:async function(previewMode,value){this.$('> .o_bg_video_container').toggleClass('d-none',previewMode===true);if(previewMode!==false){return;}
this.videoSrc=value;var target=this.$target[0];target.classList.toggle('o_background_video',!!(value&&value.length));if(value&&value.length){target.dataset.bgVideoSrc=value;}else{delete target.dataset.bgVideoSrc;}
await this._refreshPublicWidgets();},});options.registry.OptionsTab=options.Class.extend({async configureApiKey(previewMode,widgetValue,params){return new Promise(resolve=>{this.trigger_up('gmap_api_key_request',{editableMode:true,reconfigure:true,onSuccess:()=>resolve(),});});},async customizeBodyBgType(previewMode,widgetValue,params){if(widgetValue==='NONE'){this.bodyImageType='image';return this.customizeBodyBg(previewMode,'',params);}
this.bodyImageType=widgetValue;const widget=this._requestUserValueWidgets(params.imagepicker)[0];widget.enable();},async customizeBodyBg(previewMode,widgetValue,params){await this.customizeWebsiteVariable(previewMode,this.bodyImageType,{variable:'body-image-type'});await this.customizeWebsiteVariable(previewMode,widgetValue?`'${widgetValue}'`:'',{variable:'body-image'});},async openCustomCodeDialog(previewMode,widgetValue,params){const libsProm=this._loadLibs({jsLibs:['/web/static/lib/ace/ace.js','/web/static/lib/ace/mode-xml.js',],});let websiteId;this.trigger_up('context_get',{callback:(ctx)=>{websiteId=ctx['website_id'];},});let website;const dataProm=this._rpc({model:'website',method:'read',args:[[websiteId],['custom_code_head','custom_code_footer']],}).then(websites=>{website=websites[0];});let fieldName,title,contentText;if(widgetValue==='head'){fieldName='custom_code_head';title=_t('Custom head code');contentText=_t('Enter code that will be added into the <head> of every page of your site.');}else{fieldName='custom_code_footer';title=_t('Custom end of body code');contentText=_t('Enter code that will be added before the </body> of every page of your site.');}
await Promise.all([libsProm,dataProm]);await new Promise(resolve=>{const $content=$(core.qweb.render('website.custom_code_dialog_content',{contentText,}));const aceEditor=this._renderAceEditor($content.find('.o_ace_editor_container')[0],website[fieldName]||'');const dialog=new Dialog(this,{title,$content,buttons:[{text:_t("Save"),classes:'btn-primary',click:async()=>{await this._rpc({model:'website',method:'write',args:[[websiteId],{[fieldName]:aceEditor.getValue()},],});},close:true,},{text:_t("Discard"),close:true,},],});dialog.on('closed',this,resolve);dialog.open();});},async switchTheme(previewMode,widgetValue,params){const save=await new Promise(resolve=>{Dialog.confirm(this,_t("Changing theme requires to leave the editor. This will save all your changes, are you sure you want to proceed? Be careful that changing the theme will reset all your color customizations."),{confirm_callback:()=>resolve(true),cancel_callback:()=>resolve(false),});});if(!save){return;}
this.trigger_up('request_save',{reload:false,onSuccess:()=>window.location.href='/web#action=website.theme_install_kanban_action',});},async _checkIfWidgetsUpdateNeedWarning(widgets){const warningMessage=await this._super(...arguments);if(warningMessage){return warningMessage;}
for(const widget of widgets){if(widget.getMethodsNames().includes('customizeWebsiteVariable')&&widget.getMethodsParams('customizeWebsiteVariable').variable==='color-palettes-number'){const hasCustomizedColors=weUtils.getCSSVariableValue('has-customized-colors');if(hasCustomizedColors&&hasCustomizedColors!=='false'){return _t("Changing the color palette will reset all your color customizations, are you sure you want to proceed?");}}}
return'';},async _computeWidgetState(methodName,params){if(methodName==='customizeBodyBgType'){const bgImage=$('#wrapwrap').css('background-image');if(bgImage==='none'){return"NONE";}
return weUtils.getCSSVariableValue('body-image-type');}
return this._super(...arguments);},async _computeWidgetVisibility(widgetName,params){if(widgetName==='body_bg_image_opt'){return false;}
return this._super(...arguments);},_renderAceEditor(node,content){const aceEditor=window.ace.edit(node);aceEditor.setTheme('ace/theme/monokai');aceEditor.setValue(content,1);aceEditor.setOptions({minLines:20,maxLines:Infinity,showPrintMargin:false,});aceEditor.renderer.setOptions({highlightGutterLine:true,showInvisibles:true,fontSize:14,});const aceSession=aceEditor.getSession();aceSession.setOptions({mode:"ace/mode/xml",useWorker:false,});return aceEditor;},async _renderCustomXML(uiFragment){uiFragment.querySelectorAll('we-colorpicker').forEach(el=>{el.dataset.lazyPalette='true';});},});options.registry.ThemeColors=options.registry.OptionsTab.extend({async start(){const style=window.getComputedStyle(document.documentElement);const supportOldColorSystem=weUtils.getCSSVariableValue('support-13-0-color-system',style)==='true';const hasCustomizedOldColorSystem=weUtils.getCSSVariableValue('has-customized-13-0-color-system',style)==='true';this._showOldColorSystemWarning=supportOldColorSystem&&hasCustomizedOldColorSystem;return this._super(...arguments);},async updateUIVisibility(){await this._super(...arguments);const oldColorSystemEl=this.el.querySelector('.o_old_color_system_warning');oldColorSystemEl.classList.toggle('d-none',!this._showOldColorSystemWarning);},async _renderCustomXML(uiFragment){const paletteSelectorEl=uiFragment.querySelector('[data-variable="color-palettes-number"]');const style=window.getComputedStyle(document.documentElement);const nbPalettes=parseInt(weUtils.getCSSVariableValue('number-of-color-palettes',style));for(let i=1;i<=nbPalettes;i++){const btnEl=document.createElement('we-button');btnEl.classList.add('o_palette_color_preview_button');btnEl.dataset.customizeWebsiteVariable=i;for(let c=1;c<=5;c++){const colorPreviewEl=document.createElement('span');colorPreviewEl.classList.add('o_palette_color_preview');const color=weUtils.getCSSVariableValue(`o-palette-${i}-o-color-${c}`,style);colorPreviewEl.style.backgroundColor=color;btnEl.appendChild(colorPreviewEl);}
paletteSelectorEl.appendChild(btnEl);}
for(let i=1;i<=5;i++){const collapseEl=document.createElement('we-collapse');const ccPreviewEl=$(qweb.render('web_editor.color.combination.preview'))[0];ccPreviewEl.classList.add('text-center',`o_cc${i}`);collapseEl.appendChild(ccPreviewEl);const editionEls=$(qweb.render('website.color_combination_edition',{number:i}));for(const el of editionEls){collapseEl.appendChild(el);}
uiFragment.appendChild(collapseEl);}
await this._super(...arguments);},});options.registry.menu_data=options.Class.extend({async start(){await this._super(...arguments);this.isWebsiteDesigner=await this._rpc({'model':'res.users','method':'has_group','args':['website.group_website_designer'],});},onFocus:function(){var self=this;const buttons=[{text:_t("Go to Link"),classes:'btn-primary',click:function(){self.trigger_up('request_save',{reload:false,onSuccess:function(){window.location.href=self.$target.attr('href');},});},},];if(this.isWebsiteDesigner){buttons.push({text:_t("Edit the menu"),classes:'btn-primary',click:function(){this.trigger_up('action_demand',{actionName:'edit_menu',params:[function(){var prom=new Promise(function(resolve,reject){self.trigger_up('request_save',{onSuccess:resolve,onFailure:reject,});});return prom;},],});},});}
buttons.push({text:_t("Stay on this page"),close:true});(new Dialog(this,{title:_t("Confirmation"),$content:$(core.qweb.render('website.leaving_current_page_edition')),buttons:buttons,})).open();},});options.registry.company_data=options.Class.extend({start:function(){var proto=options.registry.company_data.prototype;var prom;var self=this;if(proto.__link===undefined){prom=this._rpc({route:'/web/session/get_session_info'}).then(function(session){return self._rpc({model:'res.users',method:'read',args:[session.uid,['company_id']],});}).then(function(res){proto.__link='/web#action=base.action_res_company_form&view_type=form&id='+encodeURIComponent(res&&res[0]&&res[0].company_id[0]||1);});}
return Promise.all([this._super.apply(this,arguments),prom]);},onFocus:function(){var self=this;var proto=options.registry.company_data.prototype;Dialog.confirm(this,_t("Do you want to edit the company data ?"),{confirm_callback:function(){self.trigger_up('request_save',{reload:false,onSuccess:function(){window.location.href=proto.__link;},});},});},});options.registry.Carousel=options.Class.extend({start:function(){this.$target.carousel('pause');this.$indicators=this.$target.find('.carousel-indicators');this.$controls=this.$target.find('.carousel-control-prev, .carousel-control-next, .carousel-indicators');this.$controls.addClass('o_we_no_overlay');let _slideTimestamp;this.$target.on('slide.bs.carousel.carousel_option',()=>{_slideTimestamp=window.performance.now();setTimeout(()=>this.trigger_up('hide_overlay'));});this.$target.on('slid.bs.carousel.carousel_option',()=>{const _slideDuration=(window.performance.now()-_slideTimestamp);setTimeout(()=>{this.trigger_up('activate_snippet',{$snippet:this.$target.find('.carousel-item.active'),ifInactiveOptions:true,});this.$target.trigger('active_slide_targeted');},0.2*_slideDuration);});return this._super.apply(this,arguments);},destroy:function(){this._super.apply(this,arguments);this.$target.off('.carousel_option');},onBuilt:function(){this._assignUniqueID();},onClone:function(){this._assignUniqueID();},cleanForSave:function(){const $items=this.$target.find('.carousel-item');$items.removeClass('next prev left right active').first().addClass('active');this.$indicators.find('li').removeClass('active').empty().first().addClass('active');},_assignUniqueID:function(){const id='myCarousel'+Date.now();this.$target.attr('id',id);this.$target.find('[data-target]').attr('data-target','#'+id);_.each(this.$target.find('[data-slide], [data-slide-to]'),function(el){var $el=$(el);if($el.attr('data-target')){$el.attr('data-target','#'+id);}else if($el.attr('href')){$el.attr('href','#'+id);}});},});options.registry.CarouselItem=options.Class.extend({isTopOption:true,forceNoDeleteButton:true,start:function(){this.$carousel=this.$target.closest('.carousel');this.$indicators=this.$carousel.find('.carousel-indicators');this.$controls=this.$carousel.find('.carousel-control-prev, .carousel-control-next, .carousel-indicators');var leftPanelEl=this.$overlay.data('$optionsSection')[0];var titleTextEl=leftPanelEl.querySelector('we-title > span');this.counterEl=document.createElement('span');titleTextEl.appendChild(this.counterEl);return this._super(...arguments);},destroy:function(){this._super(...arguments);this.$carousel.off('.carousel_item_option');},updateUI:async function(){await this._super(...arguments);const $items=this.$carousel.find('.carousel-item');const $activeSlide=$items.filter('.active');const updatedText=` (${$activeSlide.index() + 1}/${$items.length})`;this.counterEl.textContent=updatedText;},addSlide:function(previewMode){const $items=this.$carousel.find('.carousel-item');this.$controls.removeClass('d-none');this.$indicators.append($('<li>',{'data-target':'#'+this.$carousel.attr('id'),'data-slide-to':$items.length,}));this.$indicators.append(' ');const $active=$items.filter('.active');$active.clone(false).removeClass('active').insertAfter($active);this.$carousel.carousel('next');},removeSlide:function(previewMode){const $items=this.$carousel.find('.carousel-item');const newLength=$items.length-1;if(!this.removing&&newLength>0){const $toDelete=$items.filter('.active').add(this.$indicators.find('.active'));this.$carousel.one('active_slide_targeted.carousel_item_option',()=>{$toDelete.remove();const indicatorsEls=this.$indicators[0].querySelectorAll('li');for(let i=0;i<indicatorsEls.length;i++){indicatorsEls[i].setAttribute('data-slide-to',i);}
this.$controls.toggleClass('d-none',newLength===1);this.$carousel.trigger('content_changed');this.removing=false;});this.removing=true;this.$carousel.carousel('prev');}},slide:function(previewMode,widgetValue,params){switch(widgetValue){case'left':this.$controls.filter('.carousel-control-prev')[0].click();break;case'right':this.$controls.filter('.carousel-control-next')[0].click();break;}},});options.registry.sizing_x=options.registry.sizing.extend({onClone:function(options){this._super.apply(this,arguments);if(options.isCurrent){var _class=this.$target.attr('class').replace(/\s*(offset-xl-|offset-lg-)([0-9-]+)/g,'');this.$target.attr('class',_class);}},_getSize:function(){var width=this.$target.closest('.row').width();var gridE=[1,2,3,4,5,6,7,8,9,10,11,12];var gridW=[0,1,2,3,4,5,6,7,8,9,10,11];this.grid={e:[_.map(gridE,v=>('col-lg-'+v)),_.map(gridE,v=>width/12*v),'width'],w:[_.map(gridW,v=>('offset-lg-'+v)),_.map(gridW,v=>width/12*v),'margin-left'],};return this.grid;},_onResize:function(compass,beginClass,current){if(compass==='w'||compass==='e'){const beginOffset=Number(beginClass.match(/offset-lg-([0-9-]+)|$/)[1]||beginClass.match(/offset-xl-([0-9-]+)|$/)[1]||0);if(compass==='w'){var beginCol=Number(beginClass.match(/col-lg-([0-9]+)|$/)[1]||0);var offset=Number(this.grid.w[0][current].match(/offset-lg-([0-9-]+)|$/)[1]||0);if(offset<0){offset=0;}
var colSize=beginCol-(offset-beginOffset);if(colSize<=0){colSize=1;offset=beginOffset+beginCol-1;}
this.$target.attr('class',this.$target.attr('class').replace(/\s*(offset-xl-|offset-lg-|col-lg-)([0-9-]+)/g,''));this.$target.addClass('col-lg-'+(colSize>12?12:colSize));if(offset>0){this.$target.addClass('offset-lg-'+offset);}}else if(beginOffset>0){const endCol=Number(this.grid.e[0][current].match(/col-lg-([0-9]+)|$/)[1]||0);if((endCol+beginOffset)>12){this.$target[0].className=this.$target[0].className.replace(/\s*(col-lg-)([0-9-]+)/g,'');this.$target[0].classList.add('col-lg-'+(12-beginOffset));}}}
this._super.apply(this,arguments);},});options.registry.layout_column=options.Class.extend({selectCount:async function(previewMode,widgetValue,params){const previousNbColumns=this.$('> .row').children().length;let $row=this.$('> .row');if(!$row.length){$row=this.$target.contents().wrapAll($('<div class="row"><div class="col-lg-12"/></div>')).parent().parent();}
const nbColumns=parseInt(widgetValue);await this._updateColumnCount($row,(nbColumns||1)-$row.children().length);await new Promise(resolve=>setTimeout(resolve));if(nbColumns===0){$row.contents().unwrap().contents().unwrap();this.trigger_up('activate_snippet',{$snippet:this.$target});}else if(previousNbColumns===0){this.trigger_up('activate_snippet',{$snippet:this.$('> .row').children().first()});}},_computeWidgetState:function(methodName,params){if(methodName==='selectCount'){return this.$('> .row').children().length;}
return this._super(...arguments);},_computeWidgetVisibility(widgetName,params){if(widgetName==='zero_cols_opt'){return this.$target.is('.s_allow_columns');}
return this._super(...arguments);},_updateColumnCount:async function($row,count){if(!count){return;}
if(count>0){var $lastColumn=$row.children().last();for(var i=0;i<count;i++){await new Promise(resolve=>{this.trigger_up('clone_snippet',{$snippet:$lastColumn,onSuccess:resolve});});}}else{var self=this;for(const el of $row.children().slice(count)){await new Promise(resolve=>{self.trigger_up('remove_snippet',{$snippet:$(el),onSuccess:resolve});});}}
this._resizeColumns($row.children());this.trigger_up('cover_update');},_resizeColumns:function($columns){const colsLength=$columns.length;var colSize=Math.floor(12/colsLength)||1;var colOffset=Math.floor((12-colSize*colsLength)/2);var colClass='col-lg-'+colSize;_.each($columns,function(column){var $column=$(column);$column.attr('class',$column.attr('class').replace(/\b(col|offset)-lg(-\d+)?\b/g,''));$column.addClass(colClass);});if(colOffset){$columns.first().addClass('offset-lg-'+colOffset);}},});options.registry.Parallax=options.Class.extend({async start(){this.parallaxEl=this.$target.find('> .s_parallax_bg')[0]||null;this._updateBackgroundOptions();this.$target.on('content_changed.ParallaxOption',this._onExternalUpdate.bind(this));return this._super(...arguments);},onFocus(){if(this.parallaxEl){this._refreshPublicWidgets();}},onMove(){this._refreshPublicWidgets();},destroy(){this._super(...arguments);this.$target.off('.ParallaxOption');},async selectDataAttribute(previewMode,widgetValue,params){await this._super(...arguments);if(params.attributeName!=='scrollBackgroundRatio'){return;}
const isParallax=(widgetValue!=='0');this.$target.toggleClass('parallax',isParallax);this.$target.toggleClass('s_parallax_is_fixed',widgetValue==='1');this.$target.toggleClass('s_parallax_no_overflow_hidden',(widgetValue==='0'||widgetValue==='1'));if(isParallax){if(!this.parallaxEl){this.parallaxEl=document.createElement('span');this.parallaxEl.classList.add('s_parallax_bg');this.$target.prepend(this.parallaxEl);}}else{if(this.parallaxEl){this.parallaxEl.remove();this.parallaxEl=null;}}
this._updateBackgroundOptions();},async _computeVisibility(widgetName){return!this.$target.hasClass('o_background_video');},async _computeWidgetState(methodName,params){if(methodName==='selectDataAttribute'&&params.parallaxTypeOpt){const attrName=params.attributeName;const attrValue=(this.$target[0].dataset[attrName]||params.attributeDefaultValue).trim();switch(attrValue){case'0':case'1':{return attrValue;}
default:{return(attrValue.startsWith('-')?'-1.5':'1.5');}}}
return this._super(...arguments);},_updateBackgroundOptions(){this.trigger_up('option_update',{optionNames:['BackgroundImage','BackgroundPosition','BackgroundOptimize'],name:'target',data:this.parallaxEl?$(this.parallaxEl):this.$target,});},_onExternalUpdate(ev){if(!this.parallaxEl){return;}
const bgImage=this.parallaxEl.style.backgroundImage;if(!bgImage||bgImage==='none'||this.$target.hasClass('o_background_video')){const widget=this._requestUserValueWidgets('parallax_none_opt')[0];widget.enable();widget.getParent().close();}},});options.registry.collapse=options.Class.extend({start:function(){var self=this;this.$target.on('shown.bs.collapse hidden.bs.collapse','[role="tabpanel"]',function(){self.trigger_up('cover_update');self.$target.trigger('content_changed');});return this._super.apply(this,arguments);},onBuilt:function(){this._createIDs();},onClone:function(){this._createIDs();},onMove:function(){this._createIDs();var $panel=this.$target.find('.collapse').removeData('bs.collapse');if($panel.attr('aria-expanded')==='true'){$panel.closest('.accordion').find('.collapse[aria-expanded="true"]').filter((i,el)=>(el!==$panel[0])).collapse('hide').one('hidden.bs.collapse',function(){$panel.trigger('shown.bs.collapse');});}},_createIDs:function(){let time=new Date().getTime();const $tablist=this.$target.closest('[role="tablist"]');const $tab=this.$target.find('[role="tab"]');const $panel=this.$target.find('[role="tabpanel"]');const setUniqueId=($elem,label)=>{let elemId=$elem.attr('id');if(!elemId||$('[id="'+elemId+'"]').length>1){do{time++;elemId=label+time;}while($('#'+elemId).length);$elem.attr('id',elemId);}
return elemId;};const tablistId=setUniqueId($tablist,'myCollapse');$panel.attr('data-parent','#'+tablistId);$panel.data('parent','#'+tablistId);const panelId=setUniqueId($panel,'myCollapseTab');$tab.attr('data-target','#'+panelId);$tab.data('target','#'+panelId);},});options.registry.HeaderNavbar=options.Class.extend({init(){this._super(...arguments);this.$target=this.$target.closest('#wrapwrap > header');},async start(){await this._super(...arguments);const signInOptionEl=this.el.querySelector('[data-customize-website-views="portal.user_sign_in"]');signInOptionEl.dataset.noPreview='true';},async updateUI(){await this._super(...arguments);if(!["'default'","'hamburger'","'sidebar'","'magazine'","'hamburger-full'"].includes(weUtils.getCSSVariableValue("header-template"))){const alignmentOptionTitleEl=this.el.querySelector('[data-name="header_alignment_opt"] we-title');alignmentOptionTitleEl.textContent=_t("Mobile Alignment");}},async _computeWidgetVisibility(widgetName,params){if(widgetName==='option_logo_height_scrolled'){return!!this.$('.navbar-brand').length;}
if(widgetName==='header_alignment_opt'){if(!this.$target[0].querySelector('.o_offcanvas_menu_toggler')){return!this.$target[0].querySelector('#oe_structure_header_hamburger_full_1, #oe_structure_header_magazine_1');}
return true;}
return this._super(...arguments);},});const VisibilityPageOptionUpdate=options.Class.extend({pageOptionName:undefined,showOptionWidgetName:undefined,shownValue:'',async start(){await this._super(...arguments);this._isShown().then(isShown=>{this.trigger_up('snippet_option_visibility_update',{show:isShown});});},async onTargetShow(){if(await this._isShown()){return;}
const widget=this._requestUserValueWidgets(this.showOptionWidgetName)[0];widget.enable();},async visibility(previewMode,widgetValue,params){const show=(widgetValue!=='hidden');await new Promise(resolve=>{this.trigger_up('action_demand',{actionName:'toggle_page_option',params:[{name:this.pageOptionName,value:show}],onSuccess:()=>resolve(),});});this.trigger_up('snippet_option_visibility_update',{show:show});},async _computeWidgetState(methodName,params){if(methodName==='visibility'){const shown=await this._isShown();return shown?this.shownValue:'hidden';}
return this._super(...arguments);},async _isShown(){return new Promise(resolve=>{this.trigger_up('action_demand',{actionName:'get_page_option',params:[this.pageOptionName],onSuccess:v=>resolve(!!v),});});},});options.registry.TopMenuVisibility=VisibilityPageOptionUpdate.extend({pageOptionName:'header_visible',showOptionWidgetName:'regular_header_visibility_opt',async visibility(previewMode,widgetValue,params){await this._super(...arguments);await this._changeVisibility(widgetValue);$(window).trigger('resize');},async _changeVisibility(widgetValue){const show=(widgetValue!=='hidden');if(!show){return;}
const transparent=(widgetValue==='transparent');await new Promise(resolve=>{this.trigger_up('action_demand',{actionName:'toggle_page_option',params:[{name:'header_overlay',value:transparent}],onSuccess:()=>resolve(),});});if(!transparent){return;}
await new Promise(resolve=>{this.trigger_up('action_demand',{actionName:'toggle_page_option',params:[{name:'header_color',value:''}],onSuccess:()=>resolve(),});});},async _computeWidgetState(methodName,params){const _super=this._super.bind(this);if(methodName==='visibility'){this.shownValue=await new Promise(resolve=>{this.trigger_up('action_demand',{actionName:'get_page_option',params:['header_overlay'],onSuccess:v=>resolve(v?'transparent':'regular'),});});}
return _super(...arguments);},_computeWidgetVisibility(widgetName,params){if(widgetName==='header_visibility_opt'){return this.$target[0].classList.contains('o_header_sidebar')?'':'true';}
return this._super(...arguments);},_renderCustomXML(uiFragment){const weSelectEl=uiFragment.querySelector('we-select#option_header_visibility');if(weSelectEl){weSelectEl.dataset.name='header_visibility_opt';}},});options.registry.topMenuColor=options.Class.extend({async selectStyle(previewMode,widgetValue,params){await this._super(...arguments);const className=widgetValue?(params.colorPrefix+widgetValue):'';await new Promise((resolve,reject)=>{this.trigger_up('action_demand',{actionName:'toggle_page_option',params:[{name:'header_color',value:className}],onSuccess:resolve,onFailure:reject,});});},_computeVisibility:async function(){const show=await this._super(...arguments);if(!show){return false;}
return new Promise(resolve=>{this.trigger_up('action_demand',{actionName:'get_page_option',params:['header_overlay'],onSuccess:value=>resolve(!!value),});});},});options.registry.MobileVisibility=options.Class.extend({showOnMobile(previewMode,widgetValue,params){if(widgetValue){this.$target[0].classList.remove(`d-md-${this.$target.css('display')}`);}
const classes=`d-none d-lg-${this.$target.css('display')}`;this.$target.toggleClass(classes,!widgetValue);},async _computeWidgetState(methodName,params){if(methodName==='showOnMobile'){const classList=[...this.$target[0].classList];return classList.includes('d-none')&&classList.some(className=>className.match(/^(d-md-|d-lg-)/g))?'':'true';}
return await this._super(...arguments);},});options.registry.HideFooter=VisibilityPageOptionUpdate.extend({pageOptionName:'footer_visible',showOptionWidgetName:'hide_footer_page_opt',shownValue:'shown',});options.registry.anchor=options.Class.extend({isTopOption:true,start:function(){this.$button=this.$el.find('we-button');const clipboard=new ClipboardJS(this.$button[0],{text:()=>this._getAnchorLink()});clipboard.on('success',()=>{this.displayNotification({type:'success',message:_.str.sprintf(_t("Anchor copied to clipboard<br>Link: %s"),this._getAnchorLink()),buttons:[{text:_t("Edit"),click:()=>this.openAnchorDialog(),primary:true}],});});return this._super.apply(this,arguments);},onClone:function(){this.$target.removeAttr('data-anchor');this.$target.filter(':not(.carousel)').removeAttr('id');},openAnchorDialog:function(previewMode,widgetValue,params){var self=this;var buttons=[{text:_t("Save & copy"),classes:'btn-primary',click:function(){var $input=this.$('.o_input_anchor_name');var anchorName=self._text2Anchor($input.val());if(self.$target[0].id===anchorName){this.close();return;}
const alreadyExists=!!document.getElementById(anchorName);this.$('.o_anchor_already_exists').toggleClass('d-none',!alreadyExists);$input.toggleClass('is-invalid',alreadyExists);if(!alreadyExists){self._setAnchorName(anchorName);this.close();self.$button[0].click();}},},{text:_t("Discard"),close:true,}];if(this.$target.attr('id')){buttons.push({text:_t("Remove"),classes:'btn-link ml-auto',icon:'fa-trash',close:true,click:function(){self._setAnchorName();},});}
new Dialog(this,{title:_t("Link Anchor"),$content:$(qweb.render('website.dialog.anchorName',{currentAnchor:decodeURIComponent(this.$target.attr('id')),})),buttons:buttons,}).open();},_setAnchorName:function(value){if(value){this.$target.attr({'id':value,'data-anchor':true,});}else{this.$target.removeAttr('id data-anchor');}
this.$target.trigger('content_changed');},_getAnchorLink:function(){if(!this.$target[0].id){const $titles=this.$target.find('h1, h2, h3, h4, h5, h6');const title=$titles.length>0?$titles[0].innerText:this.data.snippetName;const anchorName=this._text2Anchor(title);let n='';while(document.getElementById(anchorName+n)){n=(n||1)+1;}
this._setAnchorName(anchorName+n);}
return`${window.location.pathname}#${this.$target[0].id}`;},_text2Anchor:function(text){return encodeURIComponent(text.trim().replace(/\s+/g,'-'));},});options.registry.Box=options.Class.extend({async setShadow(previewMode,widgetValue,params){const styles=window.getComputedStyle(this.$target[0]);const currentBoxShadow=styles['box-shadow']||'none';const currentMode=currentBoxShadow==='none'?'':currentBoxShadow.includes('inset')?'inset':'outset';if(currentMode===widgetValue){return;}
if(previewMode===true){this._prevBoxShadow=currentBoxShadow;}
this.$target.toggleClass(params.shadowClass,!!widgetValue);let shadow='none';if(previewMode==='reset'){shadow=this._prevBoxShadow;}else{if(currentBoxShadow==='none'){shadow=this._getDefaultShadow(widgetValue,params.shadowClass)||'none';}else{if(widgetValue==='outset'){shadow=currentBoxShadow.replace('inset','').trim();}else if(widgetValue==='inset'){shadow=currentBoxShadow+' inset';}}}
await this.selectStyle(previewMode,shadow,Object.assign({cssProperty:'box-shadow'},params));},_computeWidgetState(methodName,params){if(methodName==='setShadow'){const shadowValue=this.$target.css('box-shadow');if(!shadowValue||shadowValue==='none'){return'';}
return this.$target.css('box-shadow').includes('inset')?'inset':'outset';}
return this._super(...arguments);},async _computeWidgetVisibility(widgetName,params){if(widgetName==='fake_inset_shadow_opt'){return false;}
return this._super(...arguments);},_getDefaultShadow(type,shadowClass){const el=document.createElement('div');if(type){el.classList.add(shadowClass);}
let shadow='';document.body.appendChild(el);switch(type){case'outset':{shadow=$(el).css('box-shadow');break;}
case'inset':{shadow=$(el).css('box-shadow')+' inset';break;}}
el.remove();return shadow;}});options.registry.HeaderBox=options.registry.Box.extend({async selectStyle(previewMode,widgetValue,params){if((params.variable||params.color)&&['border-width','border-style','border-color','border-radius','box-shadow'].includes(params.cssProperty)){if(previewMode){return;}
if(params.cssProperty==='border-color'){return this.customizeWebsiteColor(previewMode,widgetValue,params);}
return this.customizeWebsiteVariable(previewMode,widgetValue,params);}
return this._super(...arguments);},async setShadow(previewMode,widgetValue,params){if(params.variable){if(previewMode){return;}
const defaultShadow=this._getDefaultShadow(widgetValue,params.shadowClass);return this.customizeWebsiteVariable(previewMode,defaultShadow||'none',params);}
return this._super(...arguments);},});options.registry.CookiesBar=options.registry.SnippetPopup.extend({xmlDependencies:(options.registry.SnippetPopup.prototype.xmlDependencies||[]).concat(['/website/static/src/xml/website.cookies_bar.xml']),selectLayout:function(previewMode,widgetValue,params){let websiteId;this.trigger_up('context_get',{callback:function(ctx){websiteId=ctx['website_id'];},});const $template=$(qweb.render(`website.cookies_bar.${widgetValue}`,{websiteId:websiteId,}));const $content=this.$target.find('.modal-content');const selectorsToKeep=['.o_cookies_bar_text_button','.o_cookies_bar_text_policy','.o_cookies_bar_text_title','.o_cookies_bar_text_primary','.o_cookies_bar_text_secondary',];if(this.$savedSelectors===undefined){this.$savedSelectors=[];}
for(const selector of selectorsToKeep){const $currentLayoutEls=$content.find(selector).contents();const $newLayoutEl=$template.find(selector);if($currentLayoutEls.length){this.$savedSelectors[selector]=$currentLayoutEls;}
const $savedSelector=this.$savedSelectors[selector];if($newLayoutEl.length&&$savedSelector&&$savedSelector.length){$newLayoutEl.empty().append($savedSelector);}}
$content.empty().append($template);},});options.registry.CoverProperties=options.Class.extend({init:function(){this._super.apply(this,arguments);this.$image=this.$target.find('.o_record_cover_image');this.$filter=this.$target.find('.o_record_cover_filter');},start:function(){this.$filterValueOpts=this.$el.find('[data-filter-value]');return this._super.apply(this,arguments);},background:async function(previewMode,widgetValue,params){if(widgetValue===''){this.$image.css('background-image','');this.$target.removeClass('o_record_has_cover');}else{this.$image.css('background-image',`url('${widgetValue}')`);this.$target.addClass('o_record_has_cover');const $defaultSizeBtn=this.$el.find('.o_record_cover_opt_size_default');$defaultSizeBtn.click();$defaultSizeBtn.closest('we-select').click();}},filterValue:function(previewMode,widgetValue,params){this.$filter.css('opacity',widgetValue||0);this.$filter.toggleClass('oe_black',parseFloat(widgetValue)!==0);},updateUI:async function(){await this._super(...arguments);let coverClass=this.$el.find('[data-cover-opt-name="size"] we-button.active').data('selectClass')||'';const bg=this.$image.css('background-image');if(bg&&bg!=='none'){coverClass+=" o_record_has_cover";}
this.$target[0].dataset.coverClass=coverClass;this.$target[0].dataset.textAlignClass=this.$el.find('[data-cover-opt-name="text_align"] we-button.active').data('selectClass')||'';this.$target[0].dataset.filterValue=this.$filterValueOpts.filter('.active').data('filterValue')||0.0;let colorPickerWidget=null;this.trigger_up('user_value_widget_request',{name:'bg_color_opt',onSuccess:_widget=>colorPickerWidget=_widget,});const color=colorPickerWidget._value;const isCSSColor=ColorpickerWidget.isCSSColor(color);this.$target[0].dataset.bgColorClass=isCSSColor?'':weUtils.computeColorClasses([color])[0];this.$target[0].dataset.bgColorStyle=isCSSColor?`background-color: ${color};`:'';},_computeWidgetState:function(methodName,params){switch(methodName){case'filterValue':{return parseFloat(this.$filter.css('opacity')).toFixed(1);}
case'background':{const background=this.$image.css('background-image');if(background&&background!=='none'){return background.match(/^url\(["']?(.+?)["']?\)$/)[1];}
return'';}}
return this._super(...arguments);},_computeWidgetVisibility:function(widgetName,params){if(params.coverOptName){return this.$target.data(`use_${params.coverOptName}`)==='True';}
return this._super(...arguments);},});options.registry.ContainerWidth=options.Class.extend({cleanForSave:function(){this.$target.removeClass('o_container_preview');},selectClass:async function(previewMode,widgetValue,params){await this._super(...arguments);if(previewMode==='reset'){this.$target.removeClass('o_container_preview');}else if(previewMode){this.$target.addClass('o_container_preview');}},});options.registry.SnippetMove=options.Class.extend({start:function(){var $buttons=this.$el.find('we-button');var $overlayArea=this.$overlay.find('.o_overlay_move_options');$overlayArea.prepend($buttons[0]);$overlayArea.append($buttons[1]);return this._super(...arguments);},onFocus:function(){const $allOptions=this.$el.parent();if($allOptions.find('we-customizeblock-option').length<=1){$allOptions.addClass('d-none');}},moveSnippet:function(previewMode,widgetValue,params){const isNavItem=this.$target[0].classList.contains('nav-item');const $tabPane=isNavItem?$(this.$target.find('.nav-link')[0].hash):null;switch(widgetValue){case'prev':this.$target.prev().before(this.$target);if(isNavItem){$tabPane.prev().before($tabPane);}
break;case'next':this.$target.next().after(this.$target);if(isNavItem){$tabPane.next().after($tabPane);}
break;}
if(params.name==='move_up_opt'||params.name==='move_down_opt'){const mainScrollingEl=$().getScrollingElement()[0];const elTop=this.$target[0].getBoundingClientRect().top;const heightDiff=mainScrollingEl.offsetHeight-this.$target[0].offsetHeight;const bottomHidden=heightDiff<elTop;const hidden=elTop<0||bottomHidden;if(hidden){dom.scrollTo(this.$target[0],{extraOffset:50,forcedOffset:bottomHidden?heightDiff-50:undefined,easing:'linear',duration:500,});}}},});options.registry.ScrollButton=options.Class.extend({start:async function(){await this._super(...arguments);this.$button=this.$('.o_scroll_button');},updateUIVisibility:async function(){await this._super(...arguments);if(this.$button.length&&this.el.offsetParent===null){this.$button.detach();}},toggleButton:function(previewMode,widgetValue,params){if(widgetValue){if(!this.$button.length){const anchor=document.createElement('a');anchor.classList.add('o_scroll_button','mb-3','rounded-circle','align-items-center','justify-content-center','mx-auto','bg-primary','o_not_editable',);anchor.href='#';anchor.contentEditable="false";anchor.title=_t("Scroll down to next section");const arrow=document.createElement('i');arrow.classList.add('fa','fa-angle-down','fa-3x');anchor.appendChild(arrow);this.$button=$(anchor);}
this.$target.append(this.$button);}else{this.$button.detach();}},_computeWidgetState:function(methodName,params){switch(methodName){case'toggleButton':return!!this.$button.parent().length;}
return this._super(...arguments);},});options.registry.minHeight=options.Class.extend({_renderCustomXML(uiFragment){if(this.$target[0].dataset.snippet!=='s_image_gallery'){return;}
const minHeightEl=uiFragment.querySelector('we-button-group');if(!minHeightEl){return;}
minHeightEl.setAttribute('string',_t("Min-Height"));const heightEl=document.createElement('we-input');heightEl.setAttribute('string',_t("└ Height"));heightEl.dataset.name='image_gallery_height_opt';heightEl.dataset.unit='px';heightEl.dataset.selectStyle='';heightEl.dataset.cssProperty='height';heightEl.dataset.forceStyle='';uiFragment.appendChild(heightEl);},_computeWidgetVisibility(widgetName,params){if(widgetName==='image_gallery_height_opt'){return!this.$target[0].classList.contains('o_half_screen_height')&&!this.$target[0].classList.contains('o_full_screen_height');}
return this._super(...arguments);},});return{UrlPickerUserValueWidget:UrlPickerUserValueWidget,FontFamilyPickerUserValueWidget:FontFamilyPickerUserValueWidget,};});;

/* /website/static/src/snippets/s_facebook_page/options.js defined in bundle 'website.assets_wysiwyg' */
odoo.define('website.s_facebook_page_options',function(require){'use strict';const options=require('web_editor.snippets.options');options.registry.facebookPage=options.Class.extend({willStart:function(){var defs=[this._super.apply(this,arguments)];var defaults={href:'',id:'',height:215,width:350,tabs:'',small_header:true,hide_cover:true,show_facepile:false,};this.fbData=_.defaults(_.pick(this.$target.data(),_.keys(defaults)),defaults);if(!this.fbData.href){var self=this;defs.push(this._rpc({model:'website',method:'search_read',args:[[],['social_facebook']],limit:1,}).then(function(res){if(res){self.fbData.href=res[0].social_facebook||'';}}));}
return Promise.all(defs).then(()=>this._markFbElement()).then(()=>this._refreshPublicWidgets());},toggleOption:function(previewMode,widgetValue,params){let optionName=params.optionName;if(optionName.startsWith('tab.')){optionName=optionName.replace('tab.','');if(widgetValue){this.fbData.tabs=this.fbData.tabs.split(',').filter(t=>t!=='').concat([optionName]).join(',');}else{this.fbData.tabs=this.fbData.tabs.split(',').filter(t=>t!==optionName).join(',');}}else{if(optionName==='show_cover'){this.fbData.hide_cover=!widgetValue;}else{this.fbData[optionName]=widgetValue;}}
return this._markFbElement();},pageUrl:function(previewMode,widgetValue,params){this.fbData.href=widgetValue;return this._markFbElement();},_markFbElement:function(){return this._checkURL().then(()=>{if(this.fbData.tabs){this.fbData.height=this.fbData.tabs==='events'?300:500;}else if(this.fbData.small_header){this.fbData.height=this.fbData.show_facepile?165:70;}else{this.fbData.height=this.fbData.show_facepile?225:150;}
_.each(this.fbData,(value,key)=>{this.$target.attr('data-'+key,value);this.$target.data(key,value);});});},_computeWidgetState:function(methodName,params){const optionName=params.optionName;switch(methodName){case'toggleOption':{if(optionName.startsWith('tab.')){return this.fbData.tabs.split(',').includes(optionName.replace(/^tab./,''));}else{if(optionName==='show_cover'){return!this.fbData.hide_cover;}
return this.fbData[optionName];}}
case'pageUrl':{return this._checkURL().then(()=>this.fbData.href);}}
return this._super(...arguments);},_computeWidgetVisibility(widgetName,params){if(params.optionName==='show_facepile'){return false;}
return this._super(...arguments);},_checkURL:function(){const defaultURL='https://www.facebook.com/Odoo';const match=this.fbData.href.match(/^(https?:\/\/)?((www\.)?(fb|facebook)|(m\.)?facebook)\.com\/(((profile\.php\?id=|people\/[^/?#]+\/|(p\/)?[^/?#]+-)(?<id>[0-9]{15,16}))|(?<nameid>[\w.]+))($|[/?# ])/);if(match){const pageId=match.groups.nameid||match.groups.id;return fetch(`https://graph.facebook.com/${pageId}/picture`).then((res)=>{if(res.ok){this.fbData.id=pageId;}else{this.fbData.id="";this.fbData.href=defaultURL;}});}
this.fbData.id="";this.fbData.href=defaultURL;return Promise.resolve();},});});;

/* /website/static/src/snippets/s_image_gallery/options.js defined in bundle 'website.assets_wysiwyg' */
odoo.define('website.s_image_gallery_options',function(require){'use strict';var core=require('web.core');var weWidgets=require('wysiwyg.widgets');var options=require('web_editor.snippets.options');const wUtils=require("website.utils");var _t=core._t;var qweb=core.qweb;options.registry.gallery=options.Class.extend({xmlDependencies:['/website/static/src/snippets/s_image_gallery/000.xml'],start:function(){var self=this;this.$target.on('image_changed.gallery','img',function(ev){var $img=$(ev.currentTarget);var index=self.$target.find('.carousel-item.active').index();self.$('.carousel:first li[data-target]:eq('+index+')').css('background-image','url('+$img.attr('src')+')');});this.$target.on('click.gallery','.o_add_images',function(e){e.stopImmediatePropagation();self.addImages(false);});this.$target.on('dropped.gallery','img',function(ev){self.mode(null,self.getMode());if(!ev.target.height){$(ev.target).one('load',function(){setTimeout(function(){self.trigger_up('cover_update');});});}});const $container=this.$('> .container, > .container-fluid, > .o_container_small');let layoutPromise;if($container.find('> *:not(div)').length){layoutPromise=self._modeWithImageWait(null,self.getMode());}else{layoutPromise=Promise.resolve();}
return layoutPromise.then(this._super.apply(this,arguments));},onBuilt:function(){if(this.$target.find('.o_add_images').length){this.addImages(false);}
this._adaptNavigationIDs();},onClone:function(){this._adaptNavigationIDs();},cleanForSave:function(){if(this.$target.hasClass('slideshow')){this.$target.removeAttr('style');}},destroy(){this._super(...arguments);this.$target.off('.gallery');},addImages:function(previewMode){if(this.__imageDialogOpened){return Promise.resolve();}
this.__imageDialogOpened=true;const $images=this.$('img');var $container=this.$('> .container, > .container-fluid, > .o_container_small');var dialog=new weWidgets.MediaDialog(this,{multiImages:true,onlyImages:true,mediaWidth:1920});var lastImage=_.last(this._getImages());var index=lastImage?this._getIndex(lastImage):-1;return new Promise(resolve=>{let savedPromise=Promise.resolve();dialog.on('save',this,function(attachments){for(var i=0;i<attachments.length;i++){$('<img/>',{class:$images.length>0?$images[0].className:'img img-fluid d-block ',src:attachments[i].image_src,'data-index':++index,alt:attachments[i].description||'','data-name':_t('Image'),style:$images.length>0?$images[0].style.cssText:'',}).appendTo($container);}
if(attachments.length>0){savedPromise=this._modeWithImageWait('reset',this.getMode()).then(()=>{this.trigger_up('cover_update');});}});dialog.on('closed',this,()=>{this.__imageDialogOpened=false;return savedPromise.then(resolve);});dialog.open();});},columns:function(previewMode,widgetValue,params){const nbColumns=parseInt(widgetValue||'1');this.$target.attr('data-columns',nbColumns);this.mode(previewMode,this.getMode(),{});},getMode:function(){var mode='slideshow';if(this.$target.hasClass('o_masonry')){mode='masonry';}
if(this.$target.hasClass('o_grid')){mode='grid';}
if(this.$target.hasClass('o_nomode')){mode='nomode';}
return mode;},grid:function(){var imgs=this._getImages();var $row=$('<div/>',{class:'row s_nb_column_fixed'});var columns=this._getColumns();var colClass='col-lg-'+(12/columns);var $container=this._replaceContent($row);_.each(imgs,function(img,index){var $img=$(img);var $col=$('<div/>',{class:colClass});$col.append($img).appendTo($row);if((index+1)%columns===0){$row=$('<div/>',{class:'row s_nb_column_fixed'});$row.appendTo($container);}});this.$target.css('height','');},masonry:function(){var self=this;var imgs=this._getImages();var columns=this._getColumns();var colClass='col-lg-'+(12/columns);var cols=[];var $row=$('<div/>',{class:'row s_nb_column_fixed'});this._replaceContent($row);for(var c=0;c<columns;c++){var $col=$('<div/>',{class:'o_masonry_col o_snippet_not_selectable '+colClass});$row.append($col);cols.push($col[0]);}
if(this._masonryAwaitImages){this._masonryAwaitImagesPromise=new Promise(async resolve=>{for(const imgEl of imgs){let min=Infinity;let smallestColEl;for(const colEl of cols){const imgEls=colEl.querySelectorAll("img");const lastImgRect=imgEls.length&&imgEls[imgEls.length-1].getBoundingClientRect();const height=lastImgRect?Math.round(lastImgRect.top+lastImgRect.height):0;if(height<min){min=height;smallestColEl=colEl;}}
smallestColEl.append(imgEl);await wUtils.onceAllImagesLoaded(this.$target);}
resolve();});return;}
while(imgs.length){var min=Infinity;var $lowest;_.each(cols,function(col){var $col=$(col);var height=$col.is(':empty')?0:$col.find('img').last().offset().top+$col.find('img').last().height()-self.$target.offset().top;height=Math.round(height);if(height<min){min=height;$lowest=$col;}});$lowest.append(imgs.shift());}},mode:function(previewMode,widgetValue,params){widgetValue=widgetValue||'slideshow';this.$target.css('height','');this.$target.removeClass('o_nomode o_masonry o_grid o_slideshow').addClass('o_'+widgetValue);this[widgetValue]();this.trigger_up('cover_update');this._refreshPublicWidgets();},nomode:function(){var $row=$('<div/>',{class:'row s_nb_column_fixed'});var imgs=this._getImages();this._replaceContent($row);_.each(imgs,function(img){var wrapClass='col-lg-3';if(img.width>=img.height*2||img.width>600){wrapClass='col-lg-6';}
var $wrap=$('<div/>',{class:wrapClass}).append(img);$row.append($wrap);});},removeAllImages:function(previewMode){var $addImg=$('<div>',{class:'alert alert-info css_non_editable_mode_hidden text-center',});var $text=$('<span>',{class:'o_add_images',style:'cursor: pointer;',text:_t(" Add Images"),});var $icon=$('<i>',{class:' fa fa-plus-circle',});this._replaceContent($addImg.append($icon).append($text));},slideshow:function(){const imageEls=this._getImages();const images=_.map(imageEls,img=>({src:img.getAttribute('src'),alt:img.getAttribute('alt'),}));var currentInterval=this.$target.find('.carousel:first').attr('data-interval');var params={images:images,index:0,title:"",interval:currentInterval||0,id:'slideshow_'+new Date().getTime(),attrClass:imageEls.length>0?imageEls[0].className:'',attrStyle:imageEls.length>0?imageEls[0].style.cssText:'',},$slideshow=$(qweb.render('website.gallery.slideshow',params));this._replaceContent($slideshow);_.each(this.$('img'),function(img,index){$(img).attr({contenteditable:true,'data-index':index});});this.$target.css('height',Math.round(window.innerHeight*0.7));this.$target.off('slide.bs.carousel').off('slid.bs.carousel');this.$('li.fa').off('click');},notify:function(name,data){this._super(...arguments);if(name==='image_removed'){data.$image.remove();this.mode('reset',this.getMode());}else if(name==='image_index_request'){var imgs=this._getImages();var position=_.indexOf(imgs,data.$image[0]);if(position===0&&data.position==="prev"){data.position="last";}else if(position===imgs.length-1&&data.position==="next"){data.position="first";}
imgs.splice(position,1);switch(data.position){case'first':imgs.unshift(data.$image[0]);break;case'prev':imgs.splice(position-1,0,data.$image[0]);break;case'next':imgs.splice(position+1,0,data.$image[0]);break;case'last':imgs.push(data.$image[0]);break;}
position=imgs.indexOf(data.$image[0]);_.each(imgs,function(img,index){$(img).attr('data-index',index);});const currentMode=this.getMode();this.mode('reset',currentMode);if(currentMode==='slideshow'){const $carousel=this.$target.find('.carousel');$carousel.removeClass('slide');$carousel.carousel(position);this.$target.find('.carousel-indicators li').removeClass('active');this.$target.find('.carousel-indicators li[data-slide-to="'+position+'"]').addClass('active');this.trigger_up('activate_snippet',{$snippet:this.$target.find('.carousel-item.active img'),ifInactiveOptions:true,});$carousel.addClass('slide');}else{this.trigger_up('activate_snippet',{$snippet:data.$image,ifInactiveOptions:true,});}}},_adaptNavigationIDs:function(){var uuid=new Date().getTime();this.$target.find('.carousel').attr('id','slideshow_'+uuid);_.each(this.$target.find('[data-slide], [data-slide-to]'),function(el){var $el=$(el);if($el.attr('data-target')){$el.attr('data-target','#slideshow_'+uuid);}else if($el.attr('href')){$el.attr('href','#slideshow_'+uuid);}});},_computeWidgetState:function(methodName,params){switch(methodName){case'mode':{let activeModeName='slideshow';for(const modeName of params.possibleValues){if(this.$target.hasClass(`o_${modeName}`)){activeModeName=modeName;break;}}
this.activeMode=activeModeName;return activeModeName;}
case'columns':{return`${this._getColumns()}`;}}
return this._super(...arguments);},async _computeWidgetVisibility(widgetName,params){if(widgetName==='slideshow_mode_opt'){return false;}
return this._super(...arguments);},_getImages:function(){var imgs=this.$('img').get();var self=this;imgs.sort(function(a,b){return self._getIndex(a)-self._getIndex(b);});return imgs;},_getIndex:function(img){return img.dataset.index||0;},_getColumns:function(){return parseInt(this.$target.attr('data-columns'))||3;},_replaceContent:function($content){var $container=this.$('> .container, > .container-fluid, > .o_container_small');$container.empty().append($content);return $container;},_modeWithImageWait(previewMode,widgetValue,params){let promise;this._masonryAwaitImages=true;try{this.mode(previewMode,widgetValue,params);promise=this._masonryAwaitImagesPromise;}finally{this._masonryAwaitImages=false;this._masonryAwaitImagesPromise=undefined;}
return promise||Promise.resolve();},});options.registry.gallery_img=options.Class.extend({onRemove:function(){this.trigger_up('option_update',{optionName:'gallery',name:'image_removed',data:{$image:this.$target,},});},position:function(previewMode,widgetValue,params){this.trigger_up('option_update',{optionName:'gallery',name:'image_index_request',data:{$image:this.$target,position:widgetValue,},});},});});;

/* /website/static/src/snippets/s_countdown/options.js defined in bundle 'website.assets_wysiwyg' */
odoo.define('website.s_countdown_options',function(require){'use strict';const core=require('web.core');const options=require('web_editor.snippets.options');const CountdownWidget=require('website.s_countdown');const qweb=core.qweb;options.registry.countdown=options.Class.extend({events:_.extend({},options.Class.prototype.events||{},{'click .toggle-edit-message':'_onToggleEndMessageClick',}),cleanForSave:async function(){this.$target.find('.s_countdown_canvas_wrapper').removeClass("s_countdown_none");this.$target.find('.s_countdown_end_message').removeClass("s_countdown_enable_preview");},endAction:function(previewMode,widgetValue,params){this.$target[0].dataset.endAction=widgetValue;if(widgetValue==='message'){if(!this.$target.find('.s_countdown_end_message').length){const message=this.endMessage||qweb.render('website.s_countdown.end_message');this.$target.append(message);}}else{const $message=this.$target.find('.s_countdown_end_message').detach();if(this.showEndMessage){this._onToggleEndMessageClick();}
if($message.length){this.endMessage=$message[0].outerHTML;}}},layout:function(previewMode,widgetValue,params){switch(widgetValue){case'circle':this.$target[0].dataset.progressBarStyle='disappear';this.$target[0].dataset.progressBarWeight='thin';this.$target[0].dataset.layoutBackground='none';break;case'boxes':this.$target[0].dataset.progressBarStyle='none';this.$target[0].dataset.layoutBackground='plain';break;case'clean':this.$target[0].dataset.progressBarStyle='none';this.$target[0].dataset.layoutBackground='none';break;case'text':this.$target[0].dataset.progressBarStyle='none';this.$target[0].dataset.layoutBackground='none';break;}
this.$target[0].dataset.layout=widgetValue;},updateUIVisibility:async function(){await this._super(...arguments);const dataset=this.$target[0].dataset;this.$el.find('.toggle-edit-message').toggleClass('d-none',dataset.endAction!=='message');this.updateUIEndMessage();},updateUIEndMessage:function(){this.$target.find('.s_countdown_canvas_wrapper').toggleClass("s_countdown_none",this.showEndMessage===true&&this.$target.hasClass("hide-countdown"));this.$target.find('.s_countdown_end_message').toggleClass("s_countdown_enable_preview",this.showEndMessage===true);},_computeWidgetState:function(methodName,params){switch(methodName){case'endAction':case'layout':return this.$target[0].dataset[methodName];case'selectDataAttribute':{if(params.colorNames){params.attributeDefaultValue=CountdownWidget.prototype.defaultColor;}
break;}}
return this._super(...arguments);},_onToggleEndMessageClick:function(){this.showEndMessage=!this.showEndMessage;this.$el.find(".toggle-edit-message").toggleClass('text-primary',this.showEndMessage);this.updateUIEndMessage();this.trigger_up('cover_update');},});});;

/* /website/static/src/snippets/s_popup/options.js defined in bundle 'website.assets_wysiwyg' */
odoo.define('website.s_popup_options',function(require){'use strict';const options=require('web_editor.snippets.options');options.registry.SnippetPopup=options.Class.extend({start:function(){this.$target.on('click.SnippetPopup','.js_close_popup:not(a, .btn)',ev=>{ev.stopPropagation();this.onTargetHide();});this.$target.on('shown.bs.modal.SnippetPopup',()=>{this.trigger_up('snippet_option_visibility_update',{show:true});this.$target[0].querySelectorAll('.media_iframe_video').forEach(media=>{const iframe=media.querySelector('iframe');iframe.src=media.dataset.oeExpression||media.dataset.src;});});this.$target.on('hide.bs.modal.SnippetPopup',()=>{this.trigger_up('snippet_option_visibility_update',{show:false});this._removeIframeSrc();});this._removeIframeSrc();return this._super(...arguments);},destroy:function(){this._super(...arguments);this._removeIframeSrc();this.$target.off('.SnippetPopup');},onBuilt:function(){this._assignUniqueID();},onClone:function(){this._assignUniqueID();},onTargetShow:async function(){this.$target.modal('show');$(document.body).children('.modal-backdrop:last').addClass('d-none');},onTargetHide:async function(){return new Promise(resolve=>{const timeoutID=setTimeout(()=>{this.$target.off('hidden.bs.modal.popup_on_target_hide');resolve();},500);this.$target.one('hidden.bs.modal.popup_on_target_hide',()=>{clearTimeout(timeoutID);resolve();});this.$target.modal('hide');});},cleanForSave:function(){this.$target.removeClass("s_popup_overflow_page");},moveBlock:function(previewMode,widgetValue,params){const $container=$(widgetValue==='moveToFooter'?'footer#bottom':'main');this.$target.closest('.s_popup').prependTo($container.find('.oe_structure:o_editable').first());},setBackdrop(previewMode,widgetValue,params){const color=widgetValue?'var(--black-50)':'';this.$target[0].style.setProperty('background-color',color,'important');},_assignUniqueID:function(){this.$target.closest('.s_popup').attr('id','sPopup'+Date.now());},_computeWidgetState:function(methodName,params){switch(methodName){case'moveBlock':return this.$target.closest('footer#bottom').length?'moveToFooter':'moveToBody';}
return this._super(...arguments);},_removeIframeSrc(){this.$target.find('.media_iframe_video iframe').each((i,iframe)=>{iframe.src='';});},});});;

/* /website/static/src/snippets/s_product_catalog/options.js defined in bundle 'website.assets_wysiwyg' */
odoo.define('website.s_product_catalog_options',function(require){'use strict';const core=require('web.core');const options=require('web_editor.snippets.options');const _t=core._t;options.registry.ProductCatalog=options.Class.extend({toggleDescription:function(previewMode,widgetValue,params){const $dishes=this.$('.s_product_catalog_dish');const $name=$dishes.find('.s_product_catalog_dish_name');$name.toggleClass('s_product_catalog_dish_dot_leaders',!widgetValue);if(widgetValue){_.each($dishes,el=>{const $description=$(el).find('.s_product_catalog_dish_description');if($description.length){$description.removeClass('d-none');}else{const descriptionEl=document.createElement('p');descriptionEl.classList.add('s_product_catalog_dish_description','border-top','text-muted','pt-1','o_default_snippet_text');const iEl=document.createElement('i');iEl.textContent=_t("Add a description here");descriptionEl.appendChild(iEl);el.appendChild(descriptionEl);}});}else{_.each($dishes,el=>{const $description=$(el).find('.s_product_catalog_dish_description');if($description.hasClass('o_default_snippet_text')||$description.find('.o_default_snippet_text').length){$description.remove();}else{$description.addClass('d-none');}});}},_computeWidgetState:function(methodName,params){if(methodName==='toggleDescription'){const $description=this.$('.s_product_catalog_dish_description');return $description.length&&!$description.hasClass('d-none');}
return this._super(...arguments);},});});;

/* /website/static/src/snippets/s_chart/options.js defined in bundle 'website.assets_wysiwyg' */
odoo.define('website.s_chart_options',function(require){'use strict';var core=require('web.core');const{ColorpickerWidget}=require('web.Colorpicker');var options=require('web_editor.snippets.options');const weUtils=require('web_editor.utils');var _t=core._t;options.registry.InnerChart=options.Class.extend({custom_events:_.extend({},options.Class.prototype.custom_events,{'get_custom_colors':'_onGetCustomColors',}),events:_.extend({},options.Class.prototype.events,{'click we-button.add_column':'_onAddColumnClick','click we-button.add_row':'_onAddRowClick','click we-button.o_we_matrix_remove_col':'_onRemoveColumnClick','click we-button.o_we_matrix_remove_row':'_onRemoveRowClick','blur we-matrix input':'_onMatrixInputFocusOut','focus we-matrix input':'_onMatrixInputFocus',}),init:function(){this._super.apply(this,arguments);this.themeArray=['o-color-1','o-color-2','o-color-3','o-color-4','o-color-5'];this.style=window.getComputedStyle(document.documentElement);},start:function(){this.backSelectEl=this.el.querySelector('[data-name="chart_bg_color_opt"]');this.borderSelectEl=this.el.querySelector('[data-name="chart_border_color_opt"]');this.tableEl=this.el.querySelector('we-matrix table');const data=JSON.parse(this.$target[0].dataset.data);data.labels.forEach(el=>{this._addRow(el);});data.datasets.forEach((el,i)=>{if(this._isPieChart()){const headerBackgroundColor=this.themeArray[i]||this._randomColor();const headerBorderColor=this.themeArray[i]||this._randomColor();this._addColumn(el.label,el.data,headerBackgroundColor,headerBorderColor,el.backgroundColor,el.borderColor);}else{this._addColumn(el.label,el.data,el.backgroundColor,el.borderColor);}});this._displayRemoveColButton();this._displayRemoveRowButton();this._setDefaultSelectedInput();return this._super(...arguments);},updateUI:async function(){if(!this.lastEditableSelectedInput.closest('table')||this.colorPaletteSelectedInput&&!this.colorPaletteSelectedInput.closest('table')){this._setDefaultSelectedInput();}
await this._super(...arguments);this.backSelectEl.querySelector('we-title').textContent=this._isPieChart()?_t("Data Color"):_t("Dataset Color");this.borderSelectEl.querySelector('we-title').textContent=this._isPieChart()?_t("Data Border"):_t("Dataset Border");this.tableEl.querySelectorAll('input').forEach(el=>el.style.border='');const selector=this._isPieChart()?'td input':'tr:first-child input';this.tableEl.querySelectorAll(selector).forEach(el=>{const color=el.dataset.backgroundColor||el.dataset.borderColor;if(color){el.style.border='2px solid';el.style.borderColor=ColorpickerWidget.isCSSColor(color)?color:weUtils.getCSSVariableValue(color,this.style);}});},colorChange:async function(previewMode,widgetValue,params){if(widgetValue){this.colorPaletteSelectedInput.dataset[params.attributeName]=widgetValue;}else{delete this.colorPaletteSelectedInput.dataset[params.attributeName];}
await this._reloadGraph();await new Promise(resolve=>setTimeout(()=>{this.lastEditableSelectedInput.focus();resolve();}));},selectDataAttribute:async function(previewMode,widgetValue,params){await this._super(...arguments);if(params.attributeName==='type'){this._setDefaultSelectedInput();await this._reloadGraph();}},_computeWidgetState:function(methodName,params){if(methodName==='colorChange'){return this.colorPaletteSelectedInput&&this.colorPaletteSelectedInput.dataset[params.attributeName]||'';}
return this._super(...arguments);},_computeWidgetVisibility:function(widgetName,params){switch(widgetName){case'stacked_chart_opt':{return this._getColumnCount()>1;}
case'chart_bg_color_opt':case'chart_border_color_opt':{return!!this.colorPaletteSelectedInput;}}
return this._super(...arguments);},_reloadGraph:async function(){const jsonValue=this._matrixToChartData();if(this.$target[0].dataset.data!==jsonValue){this.$target[0].dataset.data=jsonValue;await this._refreshPublicWidgets();}},_matrixToChartData:function(){const data={labels:[],datasets:[],};this.tableEl.querySelectorAll('tr:first-child input').forEach(el=>{data.datasets.push({label:el.value||'',data:[],backgroundColor:this._isPieChart()?[]:el.dataset.backgroundColor||'',borderColor:this._isPieChart()?[]:el.dataset.borderColor||'',});});this.tableEl.querySelectorAll('tr:not(:first-child):not(:last-child)').forEach((el)=>{const title=el.querySelector('th input').value||'';data.labels.push(title);el.querySelectorAll('td input').forEach((el,i)=>{data.datasets[i].data.push(el.value||0);if(this._isPieChart()){data.datasets[i].backgroundColor.push(el.dataset.backgroundColor||'');data.datasets[i].borderColor.push(el.dataset.borderColor||'');}});});return JSON.stringify(data);},_makeDeleteButton:function(...classes){const rmbuttonEl=options.buildElement('we-button',null,{classes:['o_we_text_danger','o_we_link','fa','fa-fw','fa-minus',...classes],});const newEl=document.createElement('td');newEl.appendChild(rmbuttonEl);return newEl;},_addColumn:function(title,values,heardeBackgroundColor,headerBorderColor,cellBackgroundColors=[],cellBorderColors=[]){const firstRow=this.tableEl.querySelector('tr:first-child');const headerInput=this._makeCell('th',title,heardeBackgroundColor,headerBorderColor);firstRow.insertBefore(headerInput,firstRow.lastElementChild);this.tableEl.querySelectorAll('tr:not(:first-child):not(:last-child)').forEach((el,i)=>{const newCell=this._makeCell('td',values?values[i]:null,cellBackgroundColors[i]||this._randomColor(),cellBorderColors[i-1]);el.insertBefore(newCell,el.lastElementChild);});const lastRow=this.tableEl.querySelector('tr:last-child');const removeButton=this._makeDeleteButton('o_we_matrix_remove_col');lastRow.appendChild(removeButton);},_addRow:function(tilte){const trEl=document.createElement('tr');trEl.appendChild(this._makeCell('th',tilte));this.tableEl.querySelectorAll('tr:first-child input').forEach(()=>{trEl.appendChild(this._makeCell('td',null,this._randomColor()));});trEl.appendChild(this._makeDeleteButton('o_we_matrix_remove_row'));const tbody=this.tableEl.querySelector('tbody');tbody.insertBefore(trEl,tbody.lastElementChild);},_makeCell:function(tag,value,backgroundColor,borderColor){const newEl=document.createElement(tag);const contentEl=document.createElement('input');contentEl.type='text';contentEl.value=value||'';if(backgroundColor){contentEl.dataset.backgroundColor=backgroundColor;}
if(borderColor){contentEl.dataset.borderColor=borderColor;}
newEl.appendChild(contentEl);return newEl;},_displayRemoveColButton:function(colIndex){if(this._getColumnCount()>1){this._displayRemoveButton(colIndex,'o_we_matrix_remove_col');}},_displayRemoveRowButton:function(rowIndex){const rowCount=this.tableEl.rows.length-2;if(rowCount>1){this._displayRemoveButton(rowIndex,'o_we_matrix_remove_row');}},_displayRemoveButton:function(tdIndex,btnClass){const removeBtn=this.tableEl.querySelectorAll(`td we-button.${btnClass}`);removeBtn.forEach(el=>el.style.display='');const index=tdIndex<removeBtn.length?tdIndex:removeBtn.length-1;removeBtn[index].style.display='inline-block';},_isPieChart:function(){return['pie','doughnut'].includes(this.$target[0].dataset.type);},_getColumnCount:function(){return this.tableEl.rows[0].cells.length-2;},_setDefaultSelectedInput:function(){this.lastEditableSelectedInput=this.tableEl.querySelector('td input');if(this._isPieChart()){this.colorPaletteSelectedInput=this.lastEditableSelectedInput;}else{this.colorPaletteSelectedInput=this.tableEl.querySelector('th input');}},_randomColor:function(){return'#'+('00000'+(Math.random()*(1<<24)|0).toString(16)).slice(-6).toUpperCase();},_onGetCustomColors:function(ev){const data=JSON.parse(this.$target[0].dataset.data||'');let customColors=[];data.datasets.forEach(el=>{if(this._isPieChart()){customColors=customColors.concat(el.backgroundColor).concat(el.borderColor);}else{customColors.push(el.backgroundColor);customColors.push(el.borderColor);}});customColors=customColors.filter((el,i,array)=>{return!weUtils.getCSSVariableValue(el,this.style)&&array.indexOf(el)===i&&el!=='';});ev.data.onSuccess(customColors);},_onAddColumnClick:function(){const usedColor=Array.from(this.tableEl.querySelectorAll('tr:first-child input')).map(el=>el.dataset.backgroundColor);const color=this.themeArray.filter(el=>!usedColor.includes(el))[0]||this._randomColor();this._addColumn(null,null,color,color);this._reloadGraph().then(()=>{this._displayRemoveColButton();this.updateUI();});},_onAddRowClick:function(){this._addRow();this._reloadGraph().then(()=>{this._displayRemoveRowButton();this.updateUI();});},_onRemoveColumnClick:function(ev){const cell=ev.currentTarget.parentElement;const cellIndex=cell.cellIndex;this.tableEl.querySelectorAll('tr').forEach((el)=>{el.children[cellIndex].remove();});this._displayRemoveColButton(cellIndex-1);this._reloadGraph().then(()=>{this.updateUI();});},_onRemoveRowClick:function(ev){const row=ev.currentTarget.parentElement.parentElement;const rowIndex=row.rowIndex;row.remove();this._displayRemoveRowButton(rowIndex-1);this._reloadGraph().then(()=>{this.updateUI();});},_onMatrixInputFocusOut:function(ev){setTimeout(()=>{if(ev.currentTarget===document.activeElement){return;}
this._reloadGraph();});},_onMatrixInputFocus:function(ev){this.lastEditableSelectedInput=ev.target;const col=ev.target.parentElement.cellIndex;const row=ev.target.parentElement.parentElement.rowIndex;if(this._isPieChart()){this.colorPaletteSelectedInput=ev.target.parentNode.tagName==='TD'?ev.target:null;}else{this.colorPaletteSelectedInput=this.tableEl.querySelector(`tr:first-child th:nth-of-type(${col + 1}) input`);}
if(col>0){this._displayRemoveColButton(col-1);}
if(row>0){this._displayRemoveRowButton(row-1);}
this.updateUI();},});});;

/* /website/static/src/snippets/s_rating/options.js defined in bundle 'website.assets_wysiwyg' */
odoo.define('website.s_rating_options',function(require){'use strict';const weWidgets=require('wysiwyg.widgets');const options=require('web_editor.snippets.options');options.registry.Rating=options.Class.extend({start:function(){this.iconType=this.$target[0].dataset.icon;this.faClassActiveCustomIcons=this.$target[0].dataset.activeCustomIcon||'';this.faClassInactiveCustomIcons=this.$target[0].dataset.inactiveCustomIcon||'';return this._super.apply(this,arguments);},setIcons:function(previewMode,widgetValue,params){this.iconType=widgetValue;this._renderIcons();this.$target[0].dataset.icon=widgetValue;delete this.$target[0].dataset.activeCustomIcon;delete this.$target[0].dataset.inactiveCustomIcon;},customIcon:async function(previewMode,widgetValue,params){return new Promise(resolve=>{const dialog=new weWidgets.MediaDialog(this,{noImages:true,noDocuments:true,noVideos:true,mediaWidth:1920},$('<i/>'));this._saving=false;dialog.on('save',this,function(attachments){this._saving=true;const customClass='fa '+attachments.className;const $activeIcons=this.$target.find('.s_rating_active_icons > i');const $inactiveIcons=this.$target.find('.s_rating_inactive_icons > i');const $icons=params.customActiveIcon==='true'?$activeIcons:$inactiveIcons;$icons.removeClass().addClass(customClass);this.faClassActiveCustomIcons=$activeIcons.length>0?$activeIcons.attr('class'):customClass;this.faClassInactiveCustomIcons=$inactiveIcons.length>0?$inactiveIcons.attr('class'):customClass;this.$target[0].dataset.activeCustomIcon=this.faClassActiveCustomIcons;this.$target[0].dataset.inactiveCustomIcon=this.faClassInactiveCustomIcons;this.$target[0].dataset.icon='custom';this.iconType='custom';resolve();});dialog.on('closed',this,function(){if(!this._saving){resolve();}});dialog.open();});},activeIconsNumber:function(previewMode,widgetValue,params){this.nbActiveIcons=parseInt(widgetValue);this._createIcons();},totalIconsNumber:function(previewMode,widgetValue,params){this.nbTotalIcons=Math.max(parseInt(widgetValue),1);this._createIcons();},_computeWidgetState:function(methodName,params){switch(methodName){case'setIcons':{return this.$target[0].dataset.icon;}
case'activeIconsNumber':{this.nbActiveIcons=this.$target.find('.s_rating_active_icons > i').length;return this.nbActiveIcons;}
case'totalIconsNumber':{this.nbTotalIcons=this.$target.find('.s_rating_icons i').length;return this.nbTotalIcons;}}
return this._super(...arguments);},_createIcons:function(){const $activeIcons=this.$target.find('.s_rating_active_icons');const $inactiveIcons=this.$target.find('.s_rating_inactive_icons');this.$target.find('.s_rating_icons i').remove();for(let i=0;i<this.nbTotalIcons;i++){if(i<this.nbActiveIcons){$activeIcons.append('<i/> ');}else{$inactiveIcons.append('<i/> ');}}
this._renderIcons();},_renderIcons:function(){const icons={'fa-star':'fa-star-o','fa-thumbs-up':'fa-thumbs-o-up','fa-circle':'fa-circle-o','fa-square':'fa-square-o','fa-heart':'fa-heart-o'};const faClassActiveIcons=(this.iconType==="custom")?this.faClassActiveCustomIcons:'fa '+this.iconType;const faClassInactiveIcons=(this.iconType==="custom")?this.faClassInactiveCustomIcons:'fa '+icons[this.iconType];const $activeIcons=this.$target.find('.s_rating_active_icons > i');const $inactiveIcons=this.$target.find('.s_rating_inactive_icons > i');$activeIcons.removeClass().addClass(faClassActiveIcons);$inactiveIcons.removeClass().addClass(faClassInactiveIcons);},});});;

/* /website/static/src/snippets/s_tabs/options.js defined in bundle 'website.assets_wysiwyg' */
odoo.define('website.s_tabs_options',function(require){'use strict';const options=require('web_editor.snippets.options');options.registry.NavTabs=options.Class.extend({isTopOption:true,start:function(){this._findLinksAndPanes();return this._super.apply(this,arguments);},onBuilt:function(){this._generateUniqueIDs();},onClone:function(){this._generateUniqueIDs();},addTab:function(previewMode,widgetValue,params){var $activeItem=this.$navLinks.filter('.active').parent();var $activePane=this.$tabPanes.filter('.active');var $navItem=$activeItem.clone();var $navLink=$navItem.find('.nav-link').removeClass('active show');var $tabPane=$activePane.clone().removeClass('active show');$navItem.insertAfter($activeItem);$tabPane.insertAfter($activePane);this._findLinksAndPanes();this._generateUniqueIDs();$navLink.tab('show');},removeTab:function(previewMode,widgetValue,params){var self=this;var $activeLink=this.$navLinks.filter('.active');var $activePane=this.$tabPanes.filter('.active');var $next=this.$navLinks.eq((this.$navLinks.index($activeLink)+1)%this.$navLinks.length);return new Promise(resolve=>{$next.one('shown.bs.tab',function(){$activeLink.parent().remove();$activePane.remove();self._findLinksAndPanes();resolve();});$next.tab('show');});},_computeWidgetVisibility:async function(widgetName,params){if(widgetName==='remove_tab_opt'){return(this.$tabPanes.length>2);}
return this._super(...arguments);},_findLinksAndPanes:function(){this.$navLinks=this.$target.find('.nav:first .nav-link');this.$tabPanes=this.$target.find('.tab-content:first .tab-pane');},_generateUniqueIDs:function(){for(var i=0;i<this.$navLinks.length;i++){var id=_.now()+'_'+_.uniqueId();var idLink='nav_tabs_link_'+id;var idContent='nav_tabs_content_'+id;this.$navLinks.eq(i).attr({'id':idLink,'href':'#'+idContent,'aria-controls':idContent,});this.$tabPanes.eq(i).attr({'id':idContent,'aria-labelledby':idLink,});}},});options.registry.NavTabsStyle=options.Class.extend({setStyle:function(previewMode,widgetValue,params){const $nav=this.$target.find('.s_tabs_nav:first .nav');const isPills=widgetValue==='pills';$nav.toggleClass('nav-tabs card-header-tabs',!isPills);$nav.toggleClass('nav-pills',isPills);this.$target.find('.s_tabs_nav:first').toggleClass('card-header',!isPills).toggleClass('mb-3',isPills);this.$target.toggleClass('card',!isPills);this.$target.find('.s_tabs_content:first').toggleClass('card-body',!isPills);},setDirection:function(previewMode,widgetValue,params){const isVertical=widgetValue==='vertical';this.$target.toggleClass('row s_col_no_resize s_col_no_bgcolor',isVertical);this.$target.find('.s_tabs_nav:first .nav').toggleClass('flex-column',isVertical);this.$target.find('.s_tabs_nav:first > .nav-link').toggleClass('py-2',isVertical);this.$target.find('.s_tabs_nav:first').toggleClass('col-md-3',isVertical);this.$target.find('.s_tabs_content:first').toggleClass('col-md-9',isVertical);},_computeWidgetState:function(methodName,params){switch(methodName){case'setStyle':return this.$target.find('.s_tabs_nav:first .nav').hasClass('nav-pills')?'pills':'tabs';case'setDirection':return this.$target.find('.s_tabs_nav:first .nav').hasClass('flex-column')?'vertical':'horizontal';}
return this._super(...arguments);},});});;

/* /website/static/src/snippets/s_progress_bar/options.js defined in bundle 'website.assets_wysiwyg' */
odoo.define('website.s_progress_bar_options',function(require){'use strict';const core=require('web.core');const utils=require('web.utils');const options=require('web_editor.snippets.options');const _t=core._t;options.registry.progress=options.Class.extend({display:function(previewMode,widgetValue,params){if(this.$target.hasClass('progress')){this.$target.removeClass('progress');this.$target.find('.progress-bar').wrap($('<div/>',{class:'progress',}));this.$target.find('.progress-bar span').addClass('s_progress_bar_text');}
let $text=this.$target.find('.s_progress_bar_text');if(!$text.length){$text=$('<span/>').addClass('s_progress_bar_text').html(_t('80% Development'));}
if(widgetValue==='inline'){$text.appendTo(this.$target.find('.progress-bar'));}else{$text.insertBefore(this.$target.find('.progress'));}},progressBarValue:function(previewMode,widgetValue,params){let value=parseInt(widgetValue);value=utils.confine(value,0,100);const $progressBar=this.$target.find('.progress-bar');const $progressBarText=this.$target.find('.s_progress_bar_text');$progressBarText.text($progressBarText.text().replace(/[0-9]+%/,value+'%'));$progressBar.attr("aria-valuenow",value);$progressBar.css("width",value+"%");},_computeWidgetState:function(methodName,params){switch(methodName){case'display':{const isInline=this.$target.find('.s_progress_bar_text').parent('.progress-bar').length;return isInline?'inline':'below';}
case'progressBarValue':{return this.$target.find('.progress-bar').attr('aria-valuenow')+'%';}}
return this._super(...arguments);},});});;

/* /website/static/src/snippets/s_blockquote/options.js defined in bundle 'website.assets_wysiwyg' */
odoo.define('website.s_blockquote_options',function(require){'use strict';const options=require('web_editor.snippets.options');options.registry.Blockquote=options.Class.extend({display:function(previewMode,widgetValue,params){this.$target.find('.s_blockquote_avatar').toggleClass('d-none',widgetValue!=='classic');const $blockquote=this.$target.find('.s_blockquote_content');if(widgetValue==='cover'){$blockquote.css({"background-image":"url('/web/image/website.s_blockquote_cover_default_image')"});$blockquote.css({"background-position":"50% 50%"});$blockquote.addClass('oe_img_bg');if(!$blockquote.find('.o_we_bg_filter').length){const bgFilterEl=document.createElement('div');bgFilterEl.classList.add('o_we_bg_filter','bg-white-50');$blockquote.prepend(bgFilterEl);}}else{$blockquote.css({"background-image":""});$blockquote.css({"background-position":""});$blockquote.removeClass('oe_img_bg');$blockquote.find('.o_we_bg_filter').remove();$blockquote.find('.s_blockquote_filter').contents().unwrap();}
this.$target.find('.s_blockquote_icon').toggleClass('d-none',widgetValue==='minimalist');this.$target.find('footer').toggleClass('d-none',widgetValue==='minimalist');},});});;

/* /website/static/src/snippets/s_showcase/options.js defined in bundle 'website.assets_wysiwyg' */
odoo.define('website.s_showcase_options',function(require){'use strict';const options=require('web_editor.snippets.options');options.registry.Showcase=options.Class.extend({onMove:function(){const $showcaseCol=this.$target.parent().closest('.row > div');const isLeftCol=$showcaseCol.index()<=0;const $title=this.$target.children('.s_showcase_title');$title.toggleClass('flex-lg-row-reverse',isLeftCol);$showcaseCol.find('.s_showcase_icon.ml-3').removeClass('ml-3').addClass('ml-lg-3');$title.find('.s_showcase_icon').toggleClass('mr-lg-0 ml-lg-3',isLeftCol);},});});;

/* /website/static/src/snippets/s_table_of_content/options.js defined in bundle 'website.assets_wysiwyg' */
odoo.define('website.s_table_of_content_options',function(require){'use strict';const options=require('web_editor.snippets.options');options.registry.TableOfContent=options.Class.extend({start:function(){this.targetedElements='h1, h2';const $headings=this.$target.find(this.targetedElements);if($headings.length>0){this._generateNav();}
const targetNode=this.$target.find('.s_table_of_content_main')[0];const config={attributes:false,childList:true,subtree:true,characterData:true};this.observer=new MutationObserver(()=>this._generateNav());this.observer.observe(targetNode,config);return this._super(...arguments);},destroy:function(){this.observer.disconnect();this._super(...arguments);},onClone:function(){this._generateNav();},_generateNav:function(ev){const $nav=this.$target.find('.s_table_of_content_navbar');const $headings=this.$target.find(this.targetedElements);$nav.empty();_.each($headings,el=>{const $el=$(el);const id='table_of_content_heading_'+_.now()+'_'+_.uniqueId();$('<a>').attr('href',"#"+id).addClass('table_of_content_link list-group-item list-group-item-action py-2 border-0 rounded-0').text($el.text()).appendTo($nav);$el.attr('id',id);$el[0].dataset.anchor='true';});const tocMainEl=this.$target[0].querySelector('.s_table_of_content_main');if(tocMainEl&&tocMainEl.children.length===0){this.trigger_up('go_to_parent',{$snippet:this.$target});this.trigger_up('will_remove_snippet',{$target:this.$target});this.$target[0].remove();}else{$nav.find('a:first').addClass('active');}},});options.registry.TableOfContentNavbar=options.Class.extend({navbarPosition:function(previewMode,widgetValue,params){const $navbar=this.$target;const $mainContent=this.$target.parent().find('.s_table_of_content_main');if(widgetValue==='top'||widgetValue==='left'){$navbar.prev().before($navbar);}
if(widgetValue==='left'||widgetValue==='right'){$navbar.removeClass('s_table_of_content_horizontal_navbar col-lg-12').addClass('s_table_of_content_vertical_navbar col-lg-3');$mainContent.removeClass('col-lg-12').addClass('col-lg-9');$navbar.find('.s_table_of_content_navbar').removeClass('list-group-horizontal-md');}
if(widgetValue==='right'){$navbar.next().after($navbar);}
if(widgetValue==='top'){$navbar.removeClass('s_table_of_content_vertical_navbar col-lg-3').addClass('s_table_of_content_horizontal_navbar col-lg-12');$navbar.find('.s_table_of_content_navbar').addClass('list-group-horizontal-md');$mainContent.removeClass('col-lg-9').addClass('col-lg-12');}},_computeWidgetState:function(methodName,params){switch(methodName){case'navbarPosition':{const $navbar=this.$target;if($navbar.hasClass('s_table_of_content_horizontal_navbar')){return'top';}else{const $mainContent=$navbar.parent().find('.s_table_of_content_main');return $navbar.prev().is($mainContent)===true?'right':'left';}}}
return this._super(...arguments);},});options.registry.TableOfContentMainColumns=options.Class.extend({forceNoDeleteButton:true,start:function(){const leftPanelEl=this.$overlay.data('$optionsSection')[0];leftPanelEl.querySelector('.oe_snippet_clone').classList.add('d-none');return this._super.apply(this,arguments);},});});;

/* /website/static/src/snippets/s_timeline/options.js defined in bundle 'website.assets_wysiwyg' */
odoo.define('website.s_timeline_options',function(require){'use strict';const options=require('web_editor.snippets.options');options.registry.Timeline=options.Class.extend({start:function(){var $buttons=this.$el.find('we-button');var $overlayArea=this.$overlay.find('.o_overlay_options_wrap');$overlayArea.append($('<div/>').append($buttons));return this._super(...arguments);},timelineCard:function(previewMode,widgetValue,params){const $timelineRow=this.$target.closest('.s_timeline_row');$timelineRow.toggleClass('flex-row-reverse flex-row');},});});;

/* /website/static/src/snippets/s_media_list/options.js defined in bundle 'website.assets_wysiwyg' */
odoo.define('website.s_media_list_options',function(require){'use strict';const options=require('web_editor.snippets.options');options.registry.MediaItemLayout=options.Class.extend({layout:function(previewMode,widgetValue,params){const $image=this.$target.find('.s_media_list_img_wrapper');const $content=this.$target.find('.s_media_list_body');for(const possibleValue of params.possibleValues){$image.removeClass(`col-lg-${possibleValue}`);$content.removeClass(`col-lg-${12 - possibleValue}`);}
$image.addClass(`col-lg-${widgetValue}`);$content.addClass(`col-lg-${12 - widgetValue}`);},_computeWidgetState(methodName,params){switch(methodName){case'layout':{const $image=this.$target.find('.s_media_list_img_wrapper');for(const possibleValue of params.possibleValues){if($image.hasClass(`col-lg-${possibleValue}`)){return possibleValue;}}}}
return this._super(...arguments);},});});;

/* /website/static/src/snippets/s_google_map/options.js defined in bundle 'website.assets_wysiwyg' */
odoo.define('options.s_google_map_options',function(require){'use strict';const{_t}=require('web.core');const options=require('web_editor.snippets.options');options.registry.GoogleMap=options.Class.extend({resetMapColor(previewMode,widgetValue,params){this.$target[0].dataset.mapColor='';},setFormattedAddress(previewMode,widgetValue,params){this.$target[0].dataset.pinAddress=params.gmapPlace.formatted_address;},async showDescription(previewMode,widgetValue,params){const descriptionEl=this.$target[0].querySelector('.description');if(widgetValue&&!descriptionEl){this.$target.append($(`
                <div class="description">
                    <font>${_t('Visit us:')}</font>
                    <span>${_t('Our office is located in the northeast of Brussels. TEL (*************')}</span>
                </div>`));}else if(!widgetValue&&descriptionEl){descriptionEl.remove();}},_computeWidgetState(methodName,params){if(methodName==='showDescription'){return this.$target[0].querySelector('.description')?'true':'';}
return this._super(...arguments);},});});;

/* /website/static/src/snippets/s_dynamic_snippet/options.js defined in bundle 'website.assets_wysiwyg' */
odoo.define('website.s_dynamic_snippet_options',function(require){'use strict';const options=require('web_editor.snippets.options');const dynamicSnippetOptions=options.Class.extend({init:function(){this._super.apply(this,arguments);this.dynamicFilters={};this.dynamicFilterTemplates={};},onBuilt:function(){this.$target[0].dataset['snippet']='s_dynamic_snippet';this._setOptionsDefaultValues();},selectDataAttribute:function(previewMode,widgetValue,params){this._super.apply(this,arguments);if(params.attributeName==='filterId'&&previewMode===false){this.$target.get(0).dataset.numberOfRecords=this.dynamicFilters[parseInt(widgetValue)].limit;}},_fetchDynamicFilters:function(){return this._rpc({route:'/website/snippet/options_filters'});},_fetchDynamicFilterTemplates:function(){return this._rpc({route:'/website/snippet/filter_templates'});},_renderCustomXML:async function(uiFragment){await this._renderDynamicFiltersSelector(uiFragment);await this._renderDynamicFilterTemplatesSelector(uiFragment);},_renderDynamicFiltersSelector:async function(uiFragment){const dynamicFilters=await this._fetchDynamicFilters();for(let index in dynamicFilters){this.dynamicFilters[dynamicFilters[index].id]=dynamicFilters[index];}
const filtersSelectorEl=uiFragment.querySelector('[data-name="filter_opt"]');return this._renderSelectUserValueWidgetButtons(filtersSelectorEl,this.dynamicFilters);},_renderSelectUserValueWidgetButtons:async function(selectUserValueWidgetElement,data){for(let id in data){const button=document.createElement('we-button');button.dataset.selectDataAttribute=id;button.innerHTML=data[id].name;selectUserValueWidgetElement.appendChild(button);}},_renderDynamicFilterTemplatesSelector:async function(uiFragment){const dynamicFilterTemplates=await this._fetchDynamicFilterTemplates();for(let index in dynamicFilterTemplates){this.dynamicFilterTemplates[dynamicFilterTemplates[index].key]=dynamicFilterTemplates[index];}
const templatesSelectorEl=uiFragment.querySelector('[data-name="template_opt"]');return this._renderSelectUserValueWidgetButtons(templatesSelectorEl,this.dynamicFilterTemplates);},_setOptionsDefaultValues:function(){this._setOptionValue('numberOfElements',4);this._setOptionValue('numberOfElementsSmallDevices',1);},_setOptionValue:function(optionName,value){if(this.$target.get(0).dataset[optionName]===undefined){this.$target.get(0).dataset[optionName]=value;}},});options.registry.dynamic_snippet=dynamicSnippetOptions;return dynamicSnippetOptions;});;

/* /website/static/src/snippets/s_dynamic_snippet_carousel/options.js defined in bundle 'website.assets_wysiwyg' */
odoo.define('website.s_dynamic_snippet_carousel_options',function(require){'use strict';const options=require('web_editor.snippets.options');const s_dynamic_snippet_options=require('website.s_dynamic_snippet_options');const dynamicSnippetCarouselOptions=s_dynamic_snippet_options.extend({onBuilt(){this._super(...arguments);this.$target[0].dataset['snippet']='s_dynamic_snippet_carousel';},_setOptionsDefaultValues:function(){this._super.apply(this,arguments);this._setOptionValue('carouselInterval','5000');}});options.registry.dynamic_snippet_carousel=dynamicSnippetCarouselOptions;return dynamicSnippetCarouselOptions;});;

/* /website/static/src/js/editor/wysiwyg_multizone.js defined in bundle 'website.assets_wysiwyg' */
odoo.define('web_editor.wysiwyg.multizone',function(require){'use strict';var Wysiwyg=require('web_editor.wysiwyg');var snippetsEditor=require('web_editor.snippet.editor');function toggleDropdown($toggles,show){return Promise.all(_.map($toggles,toggle=>{var $toggle=$(toggle);var $dropdown=$toggle.parent();var shown=$dropdown.hasClass('show');if(shown===show){return;}
var toShow=!shown;return new Promise(resolve=>{$dropdown.one(toShow?'shown.bs.dropdown':'hidden.bs.dropdown',()=>resolve());$toggle.dropdown(toShow?'show':'hide');});})).then(()=>$toggles);}
var WysiwygMultizone=Wysiwyg.extend({start:function(){var self=this;this.options.toolbarHandler=$('#web_editor-top-edit');this.options.saveElement=function($el,context,withLang){var outerHTML=this._getEscapedElement($el).prop('outerHTML');return self._saveElement(outerHTML,self.options.recordInfo,$el[0]);};var $megaMenuToggles=this.$('.o_mega_menu_toggle');$megaMenuToggles.removeAttr('data-toggle').dropdown('dispose');$megaMenuToggles.on('click.wysiwyg_multizone',ev=>{var $toggle=$(ev.currentTarget);var dispose=($els=>$els.dropdown('dispose'));toggleDropdown($megaMenuToggles.not($toggle),false).then(dispose);toggleDropdown($toggle).then(dispose).then($el=>{var isShown=$el.parent().hasClass('show');this.editor.snippetsMenu.toggleMegaMenuSnippets(isShown);});});for(const el of this.$('.oe_structure')){if(!el.innerHTML.trim()){el.innerHTML='';}}
const $headerZones=this._getEditableArea().filter((i,el)=>el.closest('header#top')!==null);const selector='.oe_structure[id*="oe_structure"]:not(.oe_structure_multi)';$headerZones.find(selector).addBack(selector).addClass('oe_structure_solo');return this._super.apply(this,arguments).then(()=>{if(this.$('.o_mega_menu').hasClass('show')){this.editor.snippetsMenu.toggleMegaMenuSnippets(true);}});},save:function(){if(this.isDirty()){return this._restoreMegaMenus().then(()=>this.editor.save(false)).then(()=>({isDirty:true}));}else{return Promise.resolve({isDirty:false});}},destroy:function(){this._restoreMegaMenus();this._super.apply(this,arguments);},_getEditableArea:function(){return $(':o_editable');},_saveCoverProperties:function(editable){var el=editable.closest('.o_record_cover_container');if(!el){return;}
var resModel=el.dataset.resModel;var resID=parseInt(el.dataset.resId);if(!resModel||!resID){throw new Error('There should be a model and id associated to the cover');}
this.__savedCovers=this.__savedCovers||{};this.__savedCovers[resModel]=this.__savedCovers[resModel]||[];if(this.__savedCovers[resModel].includes(resID)){return;}
this.__savedCovers[resModel].push(resID);var cssBgImage=$(el.querySelector('.o_record_cover_image')).css('background-image');var coverProps={'background-image':cssBgImage.replace(/"/g,'').replace(window.location.protocol+"//"+window.location.host,''),'background_color_class':el.dataset.bgColorClass,'background_color_style':el.dataset.bgColorStyle,'opacity':el.dataset.filterValue,'resize_class':el.dataset.coverClass,'text_align_class':el.dataset.textAlignClass,};return this._rpc({model:resModel,method:'write',args:[resID,{'cover_properties':JSON.stringify(coverProps)}],});},_saveElement:function(outerHTML,recordInfo,editable){var promises=[];var $el=$(editable);var viewID=$el.data('oe-id');if(viewID){promises.push(this._rpc({model:'ir.ui.view',method:'save',args:[viewID,outerHTML,$el.data('oe-xpath')||null,],context:recordInfo.context,}));}
if($el.data('oe-field')==='mega_menu_content'){var classes=_.without($el.attr('class').split(' '),'dropdown-menu','o_mega_menu','show');promises.push(this._rpc({model:'website.menu',method:'write',args:[[parseInt($el.data('oe-id'))],{'mega_menu_classes':classes.join(' '),},],}));}
var prom=this._saveCoverProperties(editable);if(prom){promises.push(prom);}
return Promise.all(promises);},_restoreMegaMenus:function(){var $megaMenuToggles=this.$('.o_mega_menu_toggle');$megaMenuToggles.off('.wysiwyg_multizone').attr('data-toggle','dropdown').dropdown({});return toggleDropdown($megaMenuToggles,false);},});snippetsEditor.Class.include({toggleMegaMenuSnippets:function(show){setTimeout(()=>this._activateSnippet(false));this._showMegaMenuSnippets=show;this._filterSnippets();},_filterSnippets(search){this._super(...arguments);if(!this._showMegaMenuSnippets){this.el.querySelector('#snippet_mega_menu').classList.add('d-none');}},_insertDropzone:function($hook){var $hookParent=$hook.parent();var $dropzone=this._super(...arguments);$dropzone.attr('data-editor-message',$hookParent.attr('data-editor-message'));$dropzone.attr('data-editor-sub-message',$hookParent.attr('data-editor-sub-message'));return $dropzone;},});return WysiwygMultizone;});;

/* /website/static/src/js/editor/wysiwyg_multizone_translate.js defined in bundle 'website.assets_wysiwyg' */
odoo.define('web_editor.wysiwyg.multizone.translate',function(require){'use strict';var core=require('web.core');var webDialog=require('web.Dialog');var WysiwygMultizone=require('web_editor.wysiwyg.multizone');var rte=require('web_editor.rte');var Dialog=require('wysiwyg.widgets.Dialog');var websiteNavbarData=require('website.navbar');var _t=core._t;var RTETranslatorWidget=rte.Class.extend({_saveElement:function($el,context,withLang){var self=this;if($el.data('oe-translation-id')){return this._rpc({model:'ir.translation',method:'save_html',args:[[+$el.data('oe-translation-id')],this._getEscapedElement($el).html()],context:context,});}
return this._super($el,context,withLang===undefined?true:withLang);},});var AttributeTranslateDialog=Dialog.extend({init:function(parent,options,node){this._super(parent,_.extend({title:_t("Translate Attribute"),buttons:[{text:_t("Close"),classes:'btn-primary',click:this.save}],},options||{}));this.translation=$(node).data('translation');},start:function(){var $group=$('<div/>',{class:'form-group'}).appendTo(this.$el);_.each(this.translation,function(node,attr){var $node=$(node);var $label=$('<label class="col-form-label"></label>').text(attr);var $input=$('<input class="form-control"/>').val($node.html());$input.on('change keyup',function(){var value=$input.val();$node.html(value).trigger('change',node);$node.data('$node').attr($node.data('attribute'),value).trigger('translate');$node.trigger('change');});$group.append($label).append($input);});return this._super.apply(this,arguments);}});const SelectTranslateDialog=Dialog.extend({init:function(parent,options){this._super(parent,{...options,title:_t("Translate Selection Option"),buttons:[{text:_t("Close"),click:this.save}],});this.optionEl=this.options.targetEl;this.translationObject=this.optionEl.closest('[data-oe-translation-id]');},start:function(){const inputEl=document.createElement('input');inputEl.className='form-control my-3';inputEl.value=this.optionEl.textContent;inputEl.addEventListener('keyup',()=>{this.optionEl.textContent=inputEl.value;const translationUpdated=inputEl.value!==this.optionEl.dataset.initialTranslationValue;this.translationObject.classList.toggle('o_dirty',translationUpdated);this.optionEl.classList.toggle('oe_translated',translationUpdated);});this.el.appendChild(inputEl);return this._super(...arguments);},});var WysiwygTranslate=WysiwygMultizone.extend({custom_events:_.extend({},WysiwygMultizone.prototype.custom_events||{},{ready_to_save:'_onSave',rte_change:'_onChange',}),init:function(parent,options){this.lang=options.lang;options.recordInfo=_.defaults({context:{lang:this.lang}},options.recordInfo,options);this._super.apply(this,arguments);},start:function(){var self=this;this.$webEditorTopEdit=$('<div id="web_editor-top-edit"></div>').prependTo(document.body);this.options.toolbarHandler=this.$webEditorTopEdit;this.editor=new(this.Editor)(this,Object.assign({Editor:RTETranslatorWidget},this.options));this.$editor=this.editor.rte.editable();var promise=this.editor.prependTo(this.$editor[0].ownerDocument.body);return promise.then(function(){self._relocateEditorBar();var attrs=['placeholder','title','alt'];const $editable=self._getEditableArea();_.each(attrs,function(attr){$editable.filter('['+attr+'*="data-oe-translation-id="]').filter(':empty, input, select, textarea, img').each(function(){var $node=$(this);var translation=$node.data('translation')||{};var trans=$node.attr(attr);var match=trans.match(/<span [^>]*data-oe-translation-id="([0-9]+)"[^>]*>(.*)<\/span>/);var $trans=$(trans).addClass('d-none o_editable o_editable_translatable_attribute').appendTo('body');$trans.data('$node',$node).data('attribute',attr);translation[attr]=$trans[0];$node.attr(attr,match[2]);var select2=$node.data('select2');if(select2){select2.blur();$node.on('translate',function(){select2.blur();});$node=select2.container.find('input');}
$node.addClass('o_translatable_attribute').data('translation',translation);});});$editable.filter('[data-oe-translation-id] > select').each((index,select)=>{select.parentElement.classList.remove('o_is_inline_editable');const selectTranslationEl=document.createElement('div');selectTranslationEl.className='o_translation_select';const optionNames=[...select.options].map(option=>option.text);optionNames.forEach(option=>{const optionEl=document.createElement('div');optionEl.textContent=option;optionEl.dataset.initialTranslationValue=option;optionEl.className='o_translation_select_option';selectTranslationEl.appendChild(optionEl);});select.before(selectTranslationEl);});self.translations=[];self.$editables_attr=self._getEditableArea().filter('.o_translatable_attribute');self.$editables_attribute=$('.o_editable_translatable_attribute');self.$editables_attribute.on('change',function(){self.trigger_up('rte_change',{target:this});});self._markTranslatableNodes();});},destroy:function(){this._super(...arguments);this.$webEditorTopEdit.remove();},isDirty:function(){return this._super()||this.$editables_attribute.hasClass('o_dirty');},_getEditableArea:function(){var $editables=this._super();return $editables.add(this.$editables_attribute);},_getRecordInfo:function(options){options=options||{};var recordInfo=this._super(options);var $editable=$(options.target).closest(this._getEditableArea());if(!$editable.length){$editable=$(this._getFocusedEditable());}
recordInfo.context.lang=this.lang;recordInfo.translation_id=$editable.data('oe-translation-id')|0;return recordInfo;},_editorOptions:function(){var options=this._super();options.toolbar=[['font',['bold','italic','underline','clear']],['fontsize',['fontsize']],['color',['color']],['history',['undo','redo']],];return options;},_onChange:function(ev){var $node=$(ev.data.target);if(!$node.length){return;}
$node.find('div,p').each(function(){var $p=$(this);$p.after($p.html()).remove();});var trans=this._getTranlationObject($node[0]);const updated=trans.value!==$node.html().replace(/[ \t\n\r]+/,' ');$node.toggleClass('o_dirty',updated);const $target=$node.data('$node');if($target){$target.toggleClass('oe_translated',updated);}},_getTranlationObject:function(node){var $node=$(node);var id=+$node.data('oe-translation-id');if(!id){id=$node.data('oe-model')+','+$node.data('oe-id')+','+$node.data('oe-field');}
var trans=_.find(this.translations,function(trans){return trans.id===id;});if(!trans){this.translations.push(trans={'id':id});}
return trans;},_markTranslatableNodes:function(){var self=this;this._getEditableArea().each(function(){var $node=$(this);var trans=self._getTranlationObject(this);trans.value=(trans.value?trans.value:$node.html()).replace(/[ \t\n\r]+/,' ');});this._getEditableArea().prependEvent('click.translator',function(ev){if(ev.ctrlKey||!$(ev.target).is(':o_editable')){return;}
ev.preventDefault();ev.stopPropagation();});this.$editables_attr.each(function(){var $node=$(this);var translation=$node.data('translation');_.each(translation,function(node,attr){var trans=self._getTranlationObject(node);trans.value=(trans.value?trans.value:$node.html()).replace(/[ \t\n\r]+/,' ');trans.state=node.dataset.oeTranslationState;$node.attr('data-oe-translation-state',(trans.state||'to_translate'));});});this.$editables_attr.add(this._getEditableArea().filter('.o_translation_select_option')).prependEvent('mousedown.translator click.translator mouseup.translator',function(ev){if(ev.ctrlKey){return;}
ev.preventDefault();ev.stopPropagation();if(ev.type!=='mousedown'){return;}
const targetEl=ev.target;if(targetEl.closest('.o_translation_select')){new SelectTranslateDialog(self,{size:'medium',targetEl}).open();}else{new AttributeTranslateDialog(self,{},targetEl).open();}});},_onSave:function(ev){ev.stopPropagation();for(const optionsEl of this.el.querySelectorAll('.o_translation_select')){const selectEl=optionsEl.nextElementSibling;const translatedOptions=optionsEl.children;const selectOptions=selectEl.tagName==='SELECT'?[...selectEl.options]:[];if(selectOptions.length===translatedOptions.length){selectOptions.map((option,i)=>{option.text=translatedOptions[i].textContent;});}
optionsEl.remove();}},});return WysiwygTranslate;});;

/* /website/static/src/js/editor/widget_link.js defined in bundle 'website.assets_wysiwyg' */
odoo.define('website.editor.link',function(require){'use strict';var weWidgets=require('wysiwyg.widgets');var wUtils=require('website.utils');weWidgets.LinkDialog.include({xmlDependencies:(weWidgets.LinkDialog.prototype.xmlDependencies||[]).concat(['/website/static/src/xml/website.editor.xml']),events:_.extend({},weWidgets.LinkDialog.prototype.events||{},{'change select[name="link_anchor"]':'_onAnchorChange','input input[name="url"]':'_onURLInput',}),custom_events:_.extend({},weWidgets.LinkDialog.prototype.custom_events||{},{website_url_chosen:'_onAutocompleteClose',}),LINK_DEBOUNCE:1000,init:function(){this._super.apply(this,arguments);this._adaptPageAnchor=_.debounce(this._adaptPageAnchor,this.LINK_DEBOUNCE);},start:function(){var def=this._super.apply(this,arguments);wUtils.autocompleteWithPages(this,this.$('input[name="url"]'));this.opened(this._adaptPageAnchor.bind(this));return def;},_adaptPageAnchor:function(){var urlInputValue=this.$('input[name="url"]').val();var $pageAnchor=this.$('.o_link_dialog_page_anchor');if(!$pageAnchor.length){return;}
var isFromWebsite=urlInputValue[0]==='/';var $selectMenu=this.$('select[name="link_anchor"]');var $anchorsLoading=this.$('.o_anchors_loading');if($selectMenu.data("anchor-for")!==urlInputValue){$anchorsLoading.removeClass('d-none');$pageAnchor.toggleClass('d-none',!isFromWebsite);$selectMenu.empty();const urlWithoutHash=urlInputValue.split("#")[0];wUtils.loadAnchors(urlWithoutHash).then(function(anchors){_.each(anchors,function(anchor){$selectMenu.append($('<option>',{text:anchor}));});always();}).guardedCatch(always);}else{always();}
function always(){$anchorsLoading.addClass('d-none');const anchor=`#${urlInputValue.split('#')[1]}`;let anchorIndex=-1;if(anchor){const optionEls=$selectMenu[0].querySelectorAll('option');anchorIndex=Array.from(optionEls).findIndex(el=>el.textContent===anchor);}
$selectMenu.prop("selectedIndex",anchorIndex);}
$selectMenu.data("anchor-for",urlInputValue);},_onAutocompleteClose:function(){this._onURLInput();},_onAnchorChange:function(){var anchorValue=this.$('[name="link_anchor"]').val();var $urlInput=this.$('[name="url"]');var urlInputValue=$urlInput.val();if(urlInputValue.indexOf('#')>-1){urlInputValue=urlInputValue.substr(0,urlInputValue.indexOf('#'));}
$urlInput.val(urlInputValue+anchorValue);},_onURLInput:function(){this._super.apply(this,arguments);this._adaptPageAnchor();},});});;

/* /website/static/src/js/widgets/media.js defined in bundle 'website.assets_wysiwyg' */
odoo.define('website.widgets.media',function(require){'use strict';const{ImageWidget}=require('wysiwyg.widgets.media');ImageWidget.include({_getAttachmentsDomain(){const domain=this._super(...arguments);domain.push('|',['url','=',false],'!',['url','=like','/web/image/website.%']);domain.push(['key','=',false]);return domain;}});});;

/* /website_mass_mailing/static/src/js/website_mass_mailing.editor.js defined in bundle 'website.assets_wysiwyg' */
odoo.define('website_mass_mailing.editor',function(require){'use strict';var core=require('web.core');var rpc=require('web.rpc');var WysiwygMultizone=require('web_editor.wysiwyg.multizone');var WysiwygTranslate=require('web_editor.wysiwyg.multizone.translate');var options=require('web_editor.snippets.options');var wUtils=require('website.utils');const qweb=core.qweb;var _t=core._t;options.registry.mailing_list_subscribe=options.Class.extend({popup_template_id:"editor_new_mailing_list_subscribe_button",popup_title:_t("Add a Newsletter Subscribe Button"),select_mailing_list:function(previewMode,value){var self=this;var def=wUtils.prompt({'id':this.popup_template_id,'window_title':this.popup_title,'select':_t("Newsletter"),'init':function(field,dialog){return rpc.query({model:'mailing.list',method:'name_search',args:['',[['is_public','=',true]]],context:self.options.recordInfo.context,}).then(function(data){$(dialog).find('.btn-primary').prop('disabled',!data.length);var list_id=self.$target.attr("data-list-id");$(dialog).on('show.bs.modal',function(){if(list_id!=="0"){$(dialog).find('select').val(list_id);};});return data;});},});def.then(function(result){self.$target.attr("data-list-id",result.val);});return def;},toggleThanksButton(previewMode,widgetValue,params){const subscribeBtnEl=this.$target[0].querySelector('.js_subscribe_btn');const thanksBtnEl=this.$target[0].querySelector('.js_subscribed_btn');thanksBtnEl.classList.toggle('o_disable_preview',!widgetValue);thanksBtnEl.classList.toggle('o_enable_preview',widgetValue);subscribeBtnEl.classList.toggle('o_enable_preview',!widgetValue);subscribeBtnEl.classList.toggle('o_disable_preview',widgetValue);},onBuilt:function(){var self=this;this._super();this.select_mailing_list('click').guardedCatch(function(){self.getParent()._onRemoveClick($.Event("click"));});},cleanForSave(){const previewClasses=['o_disable_preview','o_enable_preview'];this.$target[0].querySelector('.js_subscribe_btn').classList.remove(...previewClasses);this.$target[0].querySelector('.js_subscribed_btn').classList.remove(...previewClasses);},_computeWidgetState(methodName,params){if(methodName!=='toggleThanksButton'){return this._super(...arguments);}
const subscribeBtnEl=this.$target[0].querySelector('.js_subscribe_btn');return subscribeBtnEl&&subscribeBtnEl.classList.contains('o_disable_preview')?'true':'';},_renderCustomXML(uiFragment){const checkboxEl=document.createElement('we-checkbox');checkboxEl.setAttribute('string',_t("Display Thanks Button"));checkboxEl.dataset.toggleThanksButton='true';checkboxEl.dataset.noPreview='true';checkboxEl.dataset.noWidgetRefresh='true';uiFragment.appendChild(checkboxEl);},});options.registry.recaptchaSubscribe=options.Class.extend({xmlDependencies:['/google_recaptcha/static/src/xml/recaptcha.xml'],toggleRecaptchaLegal:function(previewMode,value,params){const recaptchaLegalEl=this.$target[0].querySelector('.o_recaptcha_legal_terms');if(recaptchaLegalEl){recaptchaLegalEl.remove();}else{const template=document.createElement('template');template.innerHTML=qweb.render("google_recaptcha.recaptcha_legal_terms");this.$target[0].appendChild(template.content.firstElementChild);}},_computeWidgetState:function(methodName,params){switch(methodName){case'toggleRecaptchaLegal':return!this.$target[0].querySelector('.o_recaptcha_legal_terms')||'';}
return this._super(...arguments);},});options.registry.newsletter_popup=options.registry.mailing_list_subscribe.extend({popup_template_id:"editor_new_mailing_list_subscribe_popup",popup_title:_t("Add a Newsletter Subscribe Popup"),start:function(){this.$target.on('hidden.bs.modal.newsletter_popup_option',()=>{this.trigger_up('snippet_option_visibility_update',{show:false});});return this._super(...arguments);},onTargetShow:function(){this.$target.data('quick-open',true);return this._refreshPublicWidgets();},onTargetHide:function(){const $modal=this.$('.modal');if($modal.length&&$modal.is('.modal_shown')){$modal.modal('hide');}},cleanForSave:function(){var self=this;var content=this.$target.data('content');if(content){const $layout=$('<div/>',{html:content});const previewClasses=['o_disable_preview','o_enable_preview'];$layout[0].querySelector('.js_subscribe_btn').classList.remove(...previewClasses);$layout[0].querySelector('.js_subscribed_btn').classList.remove(...previewClasses);this.trigger_up('get_clean_html',{$layout:$layout,callback:function(html){self.$target.data('content',html);},});}},destroy:function(){this.$target.off('.newsletter_popup_option');this._super.apply(this,arguments);},select_mailing_list:function(){var self=this;return this._super.apply(this,arguments).then(function(){self.$target.data('quick-open',true);self.$target.removeData('content');return self._refreshPublicWidgets();});},});WysiwygMultizone.include({_saveElement:function(outerHTML,recordInfo,editable){var self=this;var defs=[this._super.apply(this,arguments)];var $popups=$(editable).find('.o_newsletter_popup');_.each($popups,function(popup){var $popup=$(popup);var content=$popup.data('content');if(content){defs.push(self._rpc({route:'/website_mass_mailing/set_content',params:{'newsletter_id':parseInt($popup.attr('data-list-id')),'content':content,},}));}});return Promise.all(defs);},});WysiwygTranslate.include({start:function(){this.$target.on('click.newsletter_popup_option','.o_edit_popup',function(ev){alert(_t('Website popups can only be translated through mailing list configuration in the Email Marketing app.'));});this._super.apply(this,arguments);},});options.registry.Parallax.include({async _computeVisibility(){if(this.$target[0].closest('.o_newsletter_popup')){return false;}
return this._super.apply(this,arguments);},});});