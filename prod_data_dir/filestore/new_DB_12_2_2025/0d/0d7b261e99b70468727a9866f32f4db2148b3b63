
/* /web/static/src/js/services/session.js defined in bundle 'web.assets_frontend_lazy' */
odoo.define('web.session',function(require){"use strict";var Session=require('web.Session');var modules=odoo._modules;var session=new Session(undefined,undefined,{modules:modules,use_cors:false});session.is_bound=session.session_bind();return session;});;

/* /web/static/src/js/public/public_env.js defined in bundle 'web.assets_frontend_lazy' */
odoo.define("web.public_env",function(require){"use strict";const commonEnv=require("web.commonEnv");return commonEnv;});;

/* /web/static/src/js/public/public_crash_manager.js defined in bundle 'web.assets_frontend_lazy' */
odoo.define('web.PublicCrashManager',function(require){"use strict";const core=require('web.core');const CrashManager=require('web.CrashManager').CrashManager;const PublicCrashManager=CrashManager.extend({_displayWarning(message,title,options){this.displayNotification(Object.assign({},options,{title,message,sticky:true,}));},});core.serviceRegistry.add('crash_manager',PublicCrashManager);return{CrashManager:PublicCrashManager,};});;

/* /web/static/src/js/public/public_notification.js defined in bundle 'web.assets_frontend_lazy' */
odoo.define('web.public.Notification',function(require){'use strict';var Notification=require('web.Notification');Notification.include({xmlDependencies:['/web/static/src/xml/notification.xml'],});});;

/* /web/static/src/js/public/public_root.js defined in bundle 'web.assets_frontend_lazy' */
odoo.define('web.public.root',function(require){'use strict';var ajax=require('web.ajax');var dom=require('web.dom');const env=require('web.public_env');var session=require('web.session');var utils=require('web.utils');var publicWidget=require('web.public.widget');var publicRootRegistry=new publicWidget.RootWidgetRegistry();function getLang(){var html=document.documentElement;return(html.getAttribute('lang')||'en_US').replace('-','_');}
var lang=utils.get_cookie('frontend_lang')||getLang();var localeDef=ajax.loadJS('/web/webclient/locale/'+lang.replace('-','_'));var PublicRoot=publicWidget.RootWidget.extend({events:_.extend({},publicWidget.RootWidget.prototype.events||{},{'submit .js_website_submit_form':'_onWebsiteFormSubmit','click .js_disable_on_click':'_onDisableOnClick',}),custom_events:_.extend({},publicWidget.RootWidget.prototype.custom_events||{},{call_service:'_onCallService',context_get:'_onContextGet',main_object_request:'_onMainObjectRequest',widgets_start_request:'_onWidgetsStartRequest',widgets_stop_request:'_onWidgetsStopRequest',}),init:function(){this._super.apply(this,arguments);this.env=env;this.publicWidgets=[];},willStart:function(){return Promise.all([this._super.apply(this,arguments),session.is_bound,localeDef]);},start:function(){var defs=[this._super.apply(this,arguments),this._startWidgets()];this.$(".o_image[data-mimetype^='image']").each(function(){var $img=$(this);if(/gif|jpe|jpg|png/.test($img.data('mimetype'))&&$img.data('src')){$img.css('background-image',"url('"+$img.data('src')+"')");}});if(window.location.hash.indexOf("scrollTop=")>-1){this.el.scrollTop=+window.location.hash.match(/scrollTop=([0-9]+)/)[1];}
if($.fn.placeholder){$('input, textarea').placeholder();}
this.$el.children().on('error.datetimepicker',this._onDateTimePickerError.bind(this));return Promise.all(defs);},_getContext:function(context){return _.extend({'lang':getLang(),},context||{});},_getExtraContext:function(context){return this._getContext(context);},_getPublicWidgetsRegistry:function(options){return publicWidget.registry;},_getRegistry:function(){return publicRootRegistry;},_startWidgets:function($from,options){var self=this;if($from===undefined){$from=this.$('#wrapwrap');if(!$from.length){$from=this.$el;}}
if(options===undefined){options={};}
this._stopWidgets($from);var defs=_.map(this._getPublicWidgetsRegistry(options),function(PublicWidget){var selector=PublicWidget.prototype.selector||'';var $target=dom.cssFind($from,selector,true);var defs=_.map($target,function(el){var widget=new PublicWidget(self,options);self.publicWidgets.push(widget);return widget.attachTo($(el));});return Promise.all(defs);});return Promise.all(defs);},_stopWidgets:function($from){var removedWidgets=_.map(this.publicWidgets,function(widget){if(!$from||$from.filter(widget.el).length||$from.find(widget.el).length){widget.destroy();return widget;}
return null;});this.publicWidgets=_.difference(this.publicWidgets,removedWidgets);},_onCallService:function(ev){function _computeContext(context,noContextKeys){context=_.extend({},this._getContext(),context);if(noContextKeys){context=_.omit(context,noContextKeys);}
return JSON.parse(JSON.stringify(context));}
const payload=ev.data;let args=payload.args||[];if(payload.service==='ajax'&&payload.method==='rpc'){args=args.concat(ev.target);var route=args[0];if(_.str.startsWith(route,'/web/dataset/call_kw/')){var params=args[1];var options=args[2];var noContextKeys;if(options){noContextKeys=options.noContextKeys;args[2]=_.omit(options,'noContextKeys');}
params.kwargs.context=_computeContext.call(this,params.kwargs.context,noContextKeys);}}else if(payload.service==='ajax'&&payload.method==='loadLibs'){args[1]=_computeContext.call(this,args[1]);}
const service=this.env.services[payload.service];const result=service[payload.method].apply(service,args);payload.callback(result);},_onContextGet:function(ev){if(ev.data.extra){ev.data.callback(this._getExtraContext(ev.data.context));}else{ev.data.callback(this._getContext(ev.data.context));}},_onMainObjectRequest:function(ev){var repr=$('html').data('main-object');var m=repr.match(/(.+)\((\d+),(.*)\)/);ev.data.callback({model:m[1],id:m[2]|0,});},_onWidgetsStartRequest:function(ev){this._startWidgets(ev.data.$target,ev.data.options).then(ev.data.onSuccess).guardedCatch(ev.data.onFailure);},_onWidgetsStopRequest:function(ev){this._stopWidgets(ev.data.$target);},_onWebsiteFormSubmit:function(ev){var $buttons=$(ev.currentTarget).find('button[type="submit"], a.a-submit');_.each($buttons,function(btn){var $btn=$(btn);$btn.html('<i class="fa fa-spinner fa-spin"></i> '+$btn.text());$btn.prop('disabled',true);});},_onDisableOnClick:function(ev){$(ev.currentTarget).addClass('disabled');},_onDateTimePickerError:function(ev){return false;},});return{PublicRoot:PublicRoot,publicRootRegistry:publicRootRegistry,};});;

/* /web/static/src/js/public/public_root_instance.js defined in bundle 'web.assets_frontend_lazy' */
odoo.define('root.widget',function(require){'use strict';const AbstractService=require('web.AbstractService');const env=require('web.public_env');var lazyloader=require('web.public.lazyloader');var rootData=require('web.public.root');owl.config.mode=env.isDebug()?"dev":"prod";owl.Component.env=env;AbstractService.prototype.deployServices(env);var publicRoot=new rootData.PublicRoot(null);return lazyloader.allScriptsLoaded.then(function(){return publicRoot.attachTo(document.body).then(function(){return publicRoot;});});});;

/* /web/static/src/js/public/public_widget.js defined in bundle 'web.assets_frontend_lazy' */
odoo.define('web.public.widget',function(require){'use strict';var Class=require('web.Class');var dom=require('web.dom');var mixins=require('web.mixins');var session=require('web.session');var Widget=require('web.Widget');var RootWidget=Widget.extend({custom_events:_.extend({},Widget.prototype.custom_events||{},{'registry_update':'_onRegistryUpdate','get_session':'_onGetSession',}),init:function(){this._super.apply(this,arguments);this._widgets=[];this._listenToUpdates=false;this._getRegistry().setParent(this);},start:function(){var defs=[this._super.apply(this,arguments)];defs.push(this._attachComponents());this._listenToUpdates=true;return Promise.all(defs);},_attachComponent:function(childInfo,$from){var self=this;var $elements=dom.cssFind($from||this.$el,childInfo.selector);var defs=_.map($elements,function(element){var w=new childInfo.Widget(self);self._widgets.push(w);return w.attachTo(element);});return Promise.all(defs);},_attachComponents:function($from){var self=this;var childInfos=this._getRegistry().get();var defs=_.map(childInfos,function(childInfo){return self._attachComponent(childInfo,$from);});return Promise.all(defs);},_getRegistry:function(){},_onGetSession:function(event){if(event.data.callback){event.data.callback(session);}},_onRegistryUpdate:function(ev){ev.stopPropagation();if(this._listenToUpdates){this._attachComponent(ev.data);}},});var RootWidgetRegistry=Class.extend(mixins.EventDispatcherMixin,{init:function(){mixins.EventDispatcherMixin.init.call(this);this._registry=[];},add:function(Widget,selector){var registryInfo={Widget:Widget,selector:selector,};this._registry.push(registryInfo);this.trigger_up('registry_update',registryInfo);},get:function(){return this._registry;},});var PublicWidget=Widget.extend({selector:false,events:{},init:function(parent,options){this._super.apply(this,arguments);this.options=options||{};},destroy:function(){if(this.selector){var $oldel=this.$el;this.setElement(null);}
this._super.apply(this,arguments);if(this.selector){this.$el=$oldel;this.el=$oldel[0];this.$target=this.$el;this.target=this.el;}},setElement:function(){this._super.apply(this,arguments);if(this.selector){this.$target=this.$el;this.target=this.el;}},_delegateEvents:function(){var self=this;var originalEvents=this.events;var events={};_.each(this.events,function(method,event){if(typeof method!=='string'){events[event]=method;return;}
var methodOptions=method.split(' ');if(methodOptions.length<=1){events[event]=method;return;}
var isAsync=_.contains(methodOptions,'async');if(!isAsync){events[event]=method;return;}
method=self.proxy(methodOptions[methodOptions.length-1]);if(_.str.startsWith(event,'click')){method=dom.makeButtonHandler(method);}else{method=dom.makeAsyncHandler(method);}
events[event]=method;});this.events=events;this._super.apply(this,arguments);this.events=originalEvents;},_getContext:function(extra,extraContext){var context;this.trigger_up('context_get',{extra:extra||false,context:extraContext,callback:function(ctx){context=ctx;},});return context;},});var registry={};registry._fixAppleCollapse=PublicWidget.extend({selector:'div[data-toggle="collapse"]',events:{'click':function(){},},});return{RootWidget:RootWidget,RootWidgetRegistry:RootWidgetRegistry,Widget:PublicWidget,registry:registry,};});;

/* /web_editor/static/src/js/frontend/loader.js defined in bundle 'web.assets_frontend_lazy' */
odoo.define('web_editor.loader',function(require){'use strict';var Wysiwyg=require('web_editor.wysiwyg.root');function load(parent,textarea,options){var loading=textarea.nextElementSibling;if(loading&&!loading.classList.contains('o_wysiwyg_loading')){loading=null;}
if(!textarea.value.match(/\S/)){textarea.value='<p><br/></p>';}
var wysiwyg=new Wysiwyg(parent,options);return wysiwyg.attachTo(textarea).then(()=>{if(loading){loading.parentNode.removeChild(loading);}
return wysiwyg;});}
return{load:load,};});;

/* /web_tour/static/src/js/public/tour_manager.js defined in bundle 'web.assets_frontend_lazy' */
odoo.define('web_tour.public.TourManager',function(require){'use strict';var TourManager=require('web_tour.TourManager');var lazyloader=require('web.public.lazyloader');TourManager.include({_waitBeforeTourStart:function(){return this._super.apply(this,arguments).then(function(){return lazyloader.allScriptsLoaded;}).then(function(){return new Promise(function(resolve){setTimeout(resolve);});});},});});;

/* /bus/static/src/js/longpolling_bus.js defined in bundle 'web.assets_frontend_lazy' */
odoo.define('bus.Longpolling',function(require){"use strict";var Bus=require('web.Bus');var ServicesMixin=require('web.ServicesMixin');var LongpollingBus=Bus.extend(ServicesMixin,{PARTNERS_PRESENCE_CHECK_PERIOD:30000,ERROR_RETRY_DELAY:10000,POLL_ROUTE:'/longpolling/poll',_isActive:null,_lastNotificationID:0,_isOdooFocused:true,_pollRetryTimeout:null,init:function(parent,params){this._super.apply(this,arguments);this._id=_.uniqueId('bus');this._longPollingBusId=this._id;this._options={};this._channels=[];this._lastPresenceTime=new Date().getTime();$(window).on("focus."+this._longPollingBusId,this._onFocusChange.bind(this,{focus:true}));$(window).on("blur."+this._longPollingBusId,this._onFocusChange.bind(this,{focus:false}));$(window).on("unload."+this._longPollingBusId,this._onFocusChange.bind(this,{focus:false}));$(window).on("click."+this._longPollingBusId,this._onPresence.bind(this));$(window).on("keydown."+this._longPollingBusId,this._onPresence.bind(this));$(window).on("keyup."+this._longPollingBusId,this._onPresence.bind(this));},destroy:function(){this.stopPolling();$(window).off("focus."+this._longPollingBusId);$(window).off("blur."+this._longPollingBusId);$(window).off("unload."+this._longPollingBusId);$(window).off("click."+this._longPollingBusId);$(window).off("keydown."+this._longPollingBusId);$(window).off("keyup."+this._longPollingBusId);this._super();},addChannel:function(channel){if(this._channels.indexOf(channel)===-1){this._channels.push(channel);if(this._pollRpc){this._pollRpc.abort();}else{this.startPolling();}}},deleteChannel:function(channel){var index=this._channels.indexOf(channel);if(index!==-1){this._channels.splice(index,1);if(this._pollRpc){this._pollRpc.abort();}}},isOdooFocused:function(){return this._isOdooFocused;},startPolling:function(){if(this._isActive===null){this._poll=this._poll.bind(this);}
if(!this._isActive){this._isActive=true;this._poll();}},stopPolling:function(){this._isActive=false;this._channels=[];clearTimeout(this._pollRetryTimeout);if(this._pollRpc){this._pollRpc.abort();}},updateOption:function(key,value){this._options[key]=value;},_getLastPresence:function(){return this._lastPresenceTime;},_poll:function(){var self=this;if(!this._isActive){return;}
var now=new Date().getTime();var options=_.extend({},this._options,{bus_inactivity:now-this._getLastPresence(),});var data={channels:this._channels,last:this._lastNotificationID,options:options};this._pollRpc=this._makePoll(data);this._pollRpc.then(function(result){self._pollRpc=false;self._onPoll(result);self._poll();}).guardedCatch(function(result){self._pollRpc=false;result.event.preventDefault();if(result.message==="XmlHttpRequestError abort"){self._poll();}else{self._pollRetryTimeout=setTimeout(self._poll,self.ERROR_RETRY_DELAY+(Math.floor((Math.random()*20)+1)*1000));}});},_makePoll:function(data){return this._rpc({route:this.POLL_ROUTE,params:data},{shadow:true,timeout:60000});},_onFocusChange:function(params){this._isOdooFocused=params.focus;if(params.focus){this._lastPresenceTime=new Date().getTime();this.trigger('window_focus',this._isOdooFocused);}},_onPoll:function(notifications){var self=this;var notifs=_.map(notifications,function(notif){if(notif.id>self._lastNotificationID){self._lastNotificationID=notif.id;}
return[notif.channel,notif.message];});this.trigger("notification",notifs);return notifs;},_onPresence:function(){this._lastPresenceTime=new Date().getTime();},});return LongpollingBus;});;

/* /bus/static/src/js/crosstab_bus.js defined in bundle 'web.assets_frontend_lazy' */
odoo.define('bus.CrossTab',function(require){"use strict";var Longpolling=require('bus.Longpolling');var session=require('web.session');var CrossTabBus=Longpolling.extend({TAB_HEARTBEAT_PERIOD:10000,MASTER_TAB_HEARTBEAT_PERIOD:1500,HEARTBEAT_OUT_OF_DATE_PERIOD:5000,HEARTBEAT_KILL_OLD_PERIOD:15000,LOCAL_STORAGE_PREFIX:'bus',_isMasterTab:false,_isRegistered:false,init:function(){this._super.apply(this,arguments);var now=new Date().getTime();this._sanitizedOrigin=session.origin.replace(/:\/{0,2}/g,'_');this._id=_.uniqueId(this.LOCAL_STORAGE_PREFIX)+':'+now;if(this._callLocalStorage('getItem','last_ts',0)+50000<now){this._callLocalStorage('removeItem','last');}
this._lastNotificationID=this._callLocalStorage('getItem','last',0);this.call('local_storage','onStorage',this,this._onStorage);},destroy:function(){this._super();clearTimeout(this._heartbeatTimeout);},addChannel:function(){this._super.apply(this,arguments);this._callLocalStorage('setItem','channels',this._channels);},deleteChannel:function(){this._super.apply(this,arguments);this._callLocalStorage('setItem','channels',this._channels);},getTabId:function(){return this._id;},isMasterTab:function(){return this._isMasterTab;},startPolling:function(){if(this._isActive===null){this._heartbeat=this._heartbeat.bind(this);}
if(!this._isRegistered){this._isRegistered=true;var peers=this._callLocalStorage('getItem','peers',{});peers[this._id]=new Date().getTime();this._callLocalStorage('setItem','peers',peers);this._registerWindowUnload();if(!this._callLocalStorage('getItem','master')){this._startElection();}
this._heartbeat();if(this._isMasterTab){this._callLocalStorage('setItem','channels',this._channels);this._callLocalStorage('setItem','options',this._options);}else{this._channels=this._callLocalStorage('getItem','channels',this._channels);this._options=this._callLocalStorage('getItem','options',this._options);}
return;}
if(this._isMasterTab){this._super.apply(this,arguments);}},updateOption:function(){this._super.apply(this,arguments);this._callLocalStorage('setItem','options',this._options);},_callLocalStorage:function(method,key,param){return this.call('local_storage',method,this._generateKey(key),param);},_generateKey:function(key){return this.LOCAL_STORAGE_PREFIX+'.'+this._sanitizedOrigin+'.'+key;},_getLastPresence:function(){return this._callLocalStorage('getItem','lastPresence')||this._super();},_heartbeat:function(){var now=new Date().getTime();var heartbeatValue=parseInt(this._callLocalStorage('getItem','heartbeat',0));var peers=this._callLocalStorage('getItem','peers',{});if((heartbeatValue+this.HEARTBEAT_OUT_OF_DATE_PERIOD)<now){this._startElection();heartbeatValue=parseInt(this._callLocalStorage('getItem','heartbeat',0));}
if(this._isMasterTab){var cleanedPeers={};for(var peerName in peers){if(peers[peerName]+this.HEARTBEAT_KILL_OLD_PERIOD>now){cleanedPeers[peerName]=peers[peerName];}}
if(heartbeatValue!==this.lastHeartbeat){this._isMasterTab=false;this.lastHeartbeat=0;peers[this._id]=now;this._callLocalStorage('setItem','peers',peers);this.stopPolling();this.trigger('no_longer_master');}else{this.lastHeartbeat=now;this._callLocalStorage('setItem','heartbeat',now);this._callLocalStorage('setItem','peers',cleanedPeers);}}else{peers[this._id]=now;this._callLocalStorage('setItem','peers',peers);}
var hbPeriod=this._isMasterTab?this.MASTER_TAB_HEARTBEAT_PERIOD:this.TAB_HEARTBEAT_PERIOD;if(this._lastPresenceTime+hbPeriod>now){this._callLocalStorage('setItem','lastPresence',this._lastPresenceTime);}
this._heartbeatTimeout=setTimeout(this._heartbeat.bind(this),hbPeriod);},_registerWindowUnload:function(){$(window).on('unload.'+this._id,this._onUnload.bind(this));},_startElection:function(){if(this._isMasterTab){return;}
var now=new Date().getTime();var peers=this._callLocalStorage('getItem','peers',{});var heartbeatKillOld=now-this.HEARTBEAT_KILL_OLD_PERIOD;var newMaster;for(var peerName in peers){if(peers[peerName]<heartbeatKillOld){continue;}
newMaster=peerName;break;}
if(newMaster===this._id){this.lastHeartbeat=now;this._callLocalStorage('setItem','heartbeat',this.lastHeartbeat);this._callLocalStorage('setItem','master',true);this._isMasterTab=true;this.startPolling();this.trigger('become_master');delete peers[newMaster];this._callLocalStorage('setItem','peers',peers);}},_onFocusChange:function(params){this._super.apply(this,arguments);this._callLocalStorage('setItem','focus',params.focus);},_onPoll:function(notifications){var notifs=this._super(notifications);if(this._isMasterTab&&notifs.length){this._callLocalStorage('setItem','last',this._lastNotificationID);this._callLocalStorage('setItem','last_ts',new Date().getTime());this._callLocalStorage('setItem','notification',notifs);}},_onStorage:function(e){var value=JSON.parse(e.newValue);var key=e.key;if(this._isRegistered&&key===this._generateKey('master')&&!value){this._startElection();}
if(key===this._generateKey('last')){this._lastNotificationID=value||0;}
else if(key===this._generateKey('notification')){if(!this._isMasterTab){this.trigger("notification",value);}}
else if(key===this._generateKey('channels')){this._channels=value;}
else if(key===this._generateKey('options')){this._options=value;}
else if(key===this._generateKey('focus')){this._isOdooFocused=value;this.trigger('window_focus',this._isOdooFocused);}},_onUnload:function(){var peers=this._callLocalStorage('getItem','peers')||{};delete peers[this._id];this._callLocalStorage('setItem','peers',peers);if(this._isMasterTab){this._callLocalStorage('removeItem','master');}},});return CrossTabBus;});;

/* /bus/static/src/js/services/bus_service.js defined in bundle 'web.assets_frontend_lazy' */
odoo.define('bus.BusService',function(require){"use strict";var CrossTab=require('bus.CrossTab');var core=require('web.core');var ServicesMixin=require('web.ServicesMixin');const session=require('web.session');var BusService=CrossTab.extend(ServicesMixin,{dependencies:['local_storage'],_audio:null,init:function(env){this.env=env;this._super();},_trigger_up:function(ev){if(ev.name==='call_service'){const payload=ev.data;let args=payload.args||[];if(payload.service==='ajax'&&payload.method==='rpc'){args=args.concat(ev.target);}
const service=this.env.services[payload.service];const result=service[payload.method].apply(service,args);payload.callback(result);}},start:function(){},sendNotification:function(title,content,callback){if(window.Notification&&Notification.permission==="granted"){if(this.isMasterTab()){try{this._sendNativeNotification(title,content,callback);}catch(error){if(error.message.indexOf('ServiceWorkerRegistration')>-1){this.do_notify(title,content);this._beep();}else{throw error;}}}}else{this.do_notify(title,content);if(this.isMasterTab()){this._beep();}}},onNotification:function(){this.on.apply(this,["notification"].concat(Array.prototype.slice.call(arguments)));},_beep:function(){if(typeof(Audio)!=="undefined"){if(!this._audio){this._audio=new Audio();var ext=this._audio.canPlayType("audio/ogg; codecs=vorbis")?".ogg":".mp3";this._audio.src=session.url("/mail/static/src/audio/ting"+ext);}
Promise.resolve(this._audio.play()).catch(_.noop);}},_sendNativeNotification:function(title,content,callback){var notification=new Notification(_.unescape(title),{body:_.unescape(content),icon:"/mail/static/src/img/odoobot_transparent.png"});notification.onclick=function(){window.focus();if(this.cancel){this.cancel();}else if(this.close){this.close();}
if(callback){callback();}};},});core.serviceRegistry.add('bus_service',BusService);return BusService;});;

/* /web_unsplash/static/src/js/unsplash_beacon.js defined in bundle 'web.assets_frontend_lazy' */
odoo.define('web_unsplash.beacon',function(require){'use strict';var publicWidget=require('web.public.widget');publicWidget.registry.UnsplashBeacon=publicWidget.Widget.extend({selector:'#wrapwrap',start:function(){var unsplashImages=_.map(this.$('img[src*="/unsplash/"]'),function(img){return img.src.split('/unsplash/')[1].split('/')[0];});if(unsplashImages.length){this._rpc({route:'/web_unsplash/get_app_id',}).then(function(appID){if(!appID){return;}
$.get('https://views.unsplash.com/v',{'photo_id':unsplashImages.join(','),'app_id':appID,});});}
return this._super.apply(this,arguments);},});});;

/* /auth_signup/static/src/js/signup.js defined in bundle 'web.assets_frontend_lazy' */
odoo.define('auth_signup.signup',function(require){'use strict';var publicWidget=require('web.public.widget');publicWidget.registry.SignUpForm=publicWidget.Widget.extend({selector:'.oe_signup_form',events:{'submit':'_onSubmit',},_onSubmit:function(){var $btn=this.$('.oe_login_buttons > button[type="submit"]');$btn.attr('disabled','disabled');$btn.prepend('<i class="fa fa-refresh fa-spin"/> ');},});});;

/* /odex_backend_theme/static/src/js/custom_login.js defined in bundle 'web.assets_frontend_lazy' */
$(document).ready(function(){$('.card-body div.border-top:last-child a:last-child').remove()
$('.card-body div.border-top:last-child a').removeClass('border-right pr-2 mr-1')});