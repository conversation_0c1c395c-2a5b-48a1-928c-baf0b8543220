
/* /website/static/src/js/set_view_track.js defined in bundle 'website.assets_editor' */
odoo.define('website.set_view_track',function(require){"use strict";var CustomizeMenu=require('website.customizeMenu');var Widget=require('web.Widget');var TrackPage=Widget.extend({template:'website.track_page',xmlDependencies:['/website/static/src/xml/track_page.xml'],events:{'change #switch-track-page':'_onTrackChange',},start:function(){this.$input=this.$('#switch-track-page');this._isTracked().then((data)=>{if(data[0]['track']){this.track=true;this.$input.attr('checked','checked');}else{this.track=false;}});},_isTracked:function(val){var viewid=$('html').data('viewid');if(!viewid){return Promise.reject();}else{return this._rpc({model:'ir.ui.view',method:'read',args:[[viewid],['track']],});}},_onTrackChange:function(ev){var checkboxValue=this.$input.is(':checked');if(checkboxValue!==this.track){this.track=checkboxValue;this._trackPage(checkboxValue);}},_trackPage:function(val){var viewid=$('html').data('viewid');if(!viewid){return Promise.reject();}else{return this._rpc({model:'ir.ui.view',method:'write',args:[[viewid],{track:val}],});}},});CustomizeMenu.include({_loadCustomizeOptions:function(){var self=this;var def=this._super.apply(this,arguments);return def.then(function(){if(!self.__trackpageLoaded){self.__trackpageLoaded=true;self.trackPage=new TrackPage(self);self.trackPage.appendTo(self.$el.children('.dropdown-menu'));}});},});});;

/* /website/static/src/js/editor/editor_menu.js defined in bundle 'website.assets_editor' */
odoo.define('website.editor.menu',function(require){'use strict';var Dialog=require('web.Dialog');var dom=require('web.dom');var Widget=require('web.Widget');var core=require('web.core');var Wysiwyg=require('web_editor.wysiwyg.root');var _t=core._t;var WysiwygMultizone=Wysiwyg.extend({assetLibs:Wysiwyg.prototype.assetLibs.concat(['website.compiled_assets_wysiwyg']),_getWysiwygContructor:function(){return odoo.__DEBUG__.services['web_editor.wysiwyg.multizone'];}});var EditorMenu=Widget.extend({template:'website.editorbar',xmlDependencies:['/website/static/src/xml/website.editor.xml'],events:{'click button[data-action=undo]':'_onUndoClick','click button[data-action=redo]':'_onRedoClick','click button[data-action=save]':'_onSaveClick','click button[data-action=cancel]':'_onCancelClick',},custom_events:{request_save:'_onSnippetRequestSave',get_clean_html:'_onGetCleanHTML',},willStart:function(){var self=this;this.$el=null;return this._super().then(function(){var $wrapwrap=$('#wrapwrap');$wrapwrap.removeClass('o_editable');self.editable($wrapwrap).addClass('o_editable');self.wysiwyg=self._wysiwygInstance();});},start:function(){var self=this;this.$el.css({width:'100%'});return this.wysiwyg.attachTo($('#wrapwrap')).then(function(){self.trigger_up('edit_mode');self.$el.css({width:''});});},destroy:function(){this.trigger_up('readonly_mode');this._super.apply(this,arguments);},cancel:function(reload){var self=this;var def=new Promise(function(resolve,reject){if(!self.wysiwyg.isDirty()){resolve();}else{var confirm=Dialog.confirm(self,_t("If you discard the current edits, all unsaved changes will be lost. You can cancel to return to edit mode."),{confirm_callback:resolve,});confirm.on('closed',self,reject);}});return def.then(function(){self.trigger_up('edition_will_stopped');var $wrapwrap=$('#wrapwrap');self.editable($wrapwrap).removeClass('o_editable');if(reload!==false){window.onbeforeunload=null;self.wysiwyg.destroy();return self._reload();}else{self.wysiwyg.destroy();self.trigger_up('readonly_mode');self.trigger_up('edition_was_stopped');self.destroy();}});},save:async function(reload){if(this._saving){return false;}
var self=this;this._saving=true;this.trigger_up('edition_will_stopped',{noWidgetsStop:true,});return this.wysiwyg.save(false).then(function(result){var $wrapwrap=$('#wrapwrap');self.editable($wrapwrap).removeClass('o_editable');if(!result.isDirty){self.cancel(reload);}else if(result.isDirty&&reload!==false){$('body').removeClass('o_connected_user');return self._reload();}else{self.wysiwyg.destroy();self.trigger_up('edition_was_stopped');self.destroy();}
return true;}).guardedCatch(()=>{this._saving=false;});},editable:function($wrapwrap){return $wrapwrap.find('[data-oe-model]').not('.o_not_editable').filter(function(){var $parent=$(this).closest('.o_editable, .o_not_editable');return!$parent.length||$parent.hasClass('o_editable');}).not('link, script').not('[data-oe-readonly]').not('img[data-oe-field="arch"], br[data-oe-field="arch"], input[data-oe-field="arch"]').not('.oe_snippet_editor').not('hr, br, input, textarea').add('.o_editable');},_wysiwygInstance:function(){var context;this.trigger_up('context_get',{callback:function(ctx){context=ctx;},});return new WysiwygMultizone(this,{snippets:'website.snippets',recordInfo:{context:context,data_res_model:'website',data_res_id:context.website_id,}});},_reload:function(){$('body').addClass('o_wait_reload');this.wysiwyg.destroy();this.$el.hide();window.location.hash='scrollTop='+window.document.body.scrollTop;window.location.reload(true);return new Promise(function(){});},_onCancelClick:function(){this.cancel(true);},_onGetCleanHTML:function(ev){ev.data.callback(this.wysiwyg.getValue({$layout:ev.data.$layout}));},_onSnippetRequestSave:function(ev){this.save(false).then(ev.data.onSuccess,ev.data.onFailure);},_onSaveClick:function(ev){const restore=dom.addButtonLoadingEffect(ev.currentTarget);this.save().then(restore).guardedCatch(restore);},_onUndoClick(){$('.note-history [data-event=undo]').first().click();},_onRedoClick(){$('.note-history [data-event=redo]').first().click();},});return EditorMenu;});;

/* /website/static/src/js/editor/editor_menu_translate.js defined in bundle 'website.assets_editor' */
odoo.define('website.editor.menu.translate',function(require){'use strict';require('web.dom_ready');var core=require('web.core');var Dialog=require('web.Dialog');var localStorage=require('web.local_storage');var Wysiwyg=require('web_editor.wysiwyg.root');var EditorMenu=require('website.editor.menu');var _t=core._t;var localStorageNoDialogKey='website_translator_nodialog';var TranslatorInfoDialog=Dialog.extend({template:'website.TranslatorInfoDialog',xmlDependencies:Dialog.prototype.xmlDependencies.concat(['/website/static/src/xml/translator.xml']),init:function(parent,options){this._super(parent,_.extend({title:_t("Translation Info"),buttons:[{text:_t("Ok, never show me this again"),classes:'btn-primary',close:true,click:this._onStrongOk.bind(this)},{text:_t("Ok"),close:true}],},options||{}));},_onStrongOk:function(){localStorage.setItem(localStorageNoDialogKey,true);},});var WysiwygTranslate=Wysiwyg.extend({assetLibs:Wysiwyg.prototype.assetLibs.concat(['website.compiled_assets_wysiwyg']),_getWysiwygContructor:function(){return odoo.__DEBUG__.services['web_editor.wysiwyg.multizone.translate'];}});var TranslatorMenu=EditorMenu.extend({start:function(){if(!localStorage.getItem(localStorageNoDialogKey)){new TranslatorInfoDialog(this).open();}
return this._super();},editable:function($wrapwrap){var selector='[data-oe-translation-id], '+'[data-oe-model][data-oe-id][data-oe-field], '+'[placeholder*="data-oe-translation-id="], '+'[title*="data-oe-translation-id="], '+'[alt*="data-oe-translation-id="]';var $edit=$wrapwrap.find(selector);$edit.filter(':has('+selector+')').attr('data-oe-readonly',true);return $edit.not('[data-oe-readonly]');},_wysiwygInstance:function(){var context;this.trigger_up('context_get',{callback:function(ctx){context=ctx;},});return new WysiwygTranslate(this,{lang:context.lang});},});return TranslatorMenu;});;

/* /website/static/src/js/menu/content.js defined in bundle 'website.assets_editor' */
odoo.define('website.contentMenu',function(require){'use strict';var Class=require('web.Class');var core=require('web.core');var Dialog=require('web.Dialog');var time=require('web.time');var weWidgets=require('wysiwyg.widgets');var websiteNavbarData=require('website.navbar');var websiteRootData=require('website.root');var Widget=require('web.Widget');var _t=core._t;var qweb=core.qweb;var PagePropertiesDialog=weWidgets.Dialog.extend({template:'website.pagesMenu.page_info',xmlDependencies:weWidgets.Dialog.prototype.xmlDependencies.concat(['/website/static/src/xml/website.pageProperties.xml']),events:_.extend({},weWidgets.Dialog.prototype.events,{'keyup input#page_name':'_onNameChanged','keyup input#page_url':'_onUrlChanged','change input#create_redirect':'_onCreateRedirectChanged','click input#visibility_password':'_onPasswordClicked','change input#visibility_password':'_onPasswordChanged','change select#visibility':'_onVisibilityChanged','error.datetimepicker':'_onDateTimePickerError',}),init:function(parent,page_id,options){var self=this;var serverUrl=window.location.origin+'/';var length_url=serverUrl.length;var serverUrlTrunc=serverUrl;if(length_url>30){serverUrlTrunc=serverUrl.slice(0,14)+'..'+serverUrl.slice(-14);}
this.serverUrl=serverUrl;this.serverUrlTrunc=serverUrlTrunc;this.current_page_url=window.location.pathname;this.page_id=page_id;var buttons=[{text:_t("Save"),classes:'btn-primary',click:this.save},{text:_t("Discard"),classes:'mr-auto',close:true},];if(options.fromPageManagement){buttons.push({text:_t("Go To Page"),icon:'fa-globe',classes:'btn-link',click:function(e){window.location.href='/'+self.page.url;},});}
buttons.push({text:_t("Duplicate Page"),icon:'fa-clone',classes:'btn-link',click:function(e){this.$el.closest('.modal').addClass('d-none');_clonePage.call(this,self.page_id);},});buttons.push({text:_t("Delete Page"),icon:'fa-trash',classes:'btn-link',click:function(e){_deletePage.call(this,self.page_id,options.fromPageManagement);},});this._super(parent,_.extend({},{title:_t("Page Properties"),size:'medium',buttons:buttons,},options||{}));},willStart:function(){var defs=[this._super.apply(this,arguments)];var self=this;defs.push(this._rpc({model:'website.page',method:'get_page_properties',args:[this.page_id],}).then(function(page){page.url=_.str.startsWith(page.url,'/')?page.url.substring(1):page.url;page.hasSingleGroup=page.group_id!==undefined;self.page=page;}));return Promise.all(defs);},start:function(){var self=this;var defs=[this._super.apply(this,arguments)];this.$('.ask_for_redirect').addClass('d-none');this.$('.redirect_type').addClass('d-none');this.$('.warn_about_call').addClass('d-none');if(this.page.visibility!=='password'){this.$('.show_visibility_password').addClass('d-none');}
if(this.page.visibility!=='restricted_group'){this.$('.show_group_id').addClass('d-none');}
this.autocompleteWithGroups(this.$('#group_id'));defs.push(this._getPageDependencies(this.page_id).then(function(dependencies){var dep_text=[];_.each(dependencies,function(value,index){if(value.length>0){dep_text.push(value.length+' '+index.toLowerCase());}});dep_text=dep_text.join(', ');self.$('#dependencies_redirect').html(qweb.render('website.show_page_dependencies',{dependencies:dependencies,dep_text:dep_text}));self.$('a.o_dependencies_redirect_link').on('click',()=>{self.$('.o_dependencies_redirect_list_popover').popover({html:true,title:_t('Dependencies'),boundary:'viewport',placement:'right',trigger:'focus',content:()=>{return qweb.render('website.get_tooltip_dependencies',{dependencies:dependencies,});},template:qweb.render('website.page_dependencies_popover'),}).popover('toggle');});}));defs.push(this._getSupportedMimetype().then(function(mimetypes){self.supportedMimetype=mimetypes;}));defs.push(this._getPageKeyDependencies(this.page_id).then(function(dependencies){var dep_text=[];_.each(dependencies,function(value,index){if(value.length>0){dep_text.push(value.length+' '+index.toLowerCase());}});dep_text=dep_text.join(', ');self.$('.warn_about_call').html(qweb.render('website.show_page_key_dependencies',{dependencies:dependencies,dep_text:dep_text}));self.$('.warn_about_call [data-toggle="popover"]').popover({container:'body',});}));defs.push(this._rpc({model:'res.users',method:'has_group',args:['website.group_multi_website']}).then(function(has_group){if(!has_group){self.$('#website_restriction').addClass('hidden');}}));var datepickersOptions={minDate:moment({y:1000}),maxDate:moment().add(200,'y'),calendarWeeks:true,icons:{time:'fa fa-clock-o',date:'fa fa-calendar',next:'fa fa-chevron-right',previous:'fa fa-chevron-left',up:'fa fa-chevron-up',down:'fa fa-chevron-down',},locale:moment.locale(),format:time.getLangDatetimeFormat(),widgetPositioning:{horizontal:'auto',vertical:'top',},widgetParent:'body',};if(this.page.date_publish){datepickersOptions.defaultDate=time.str_to_datetime(this.page.date_publish);}
this.$('#date_publish_container').datetimepicker(datepickersOptions);return Promise.all(defs);},destroy:function(){$('.popover').popover('hide');return this._super.apply(this,arguments);},save:function(data){var self=this;var context;this.trigger_up('context_get',{callback:function(ctx){context=ctx;},});var url=this.$('#page_url').val();var $datePublish=this.$("#date_publish");$datePublish.closest(".form-group").removeClass('o_has_error').find('.form-control, .custom-select').removeClass('is-invalid');var datePublish=$datePublish.val();if(datePublish!==""){datePublish=this._parse_date(datePublish);if(!datePublish){$datePublish.closest(".form-group").addClass('o_has_error').find('.form-control, .custom-select').addClass('is-invalid');return;}}
var params={id:this.page.id,name:this.$('#page_name').val(),url:url.replace(/\/{2,}/g,'/'),is_menu:this.$('#is_menu').prop('checked'),is_homepage:this.$('#is_homepage').prop('checked'),website_published:this.$('#is_published').prop('checked'),create_redirect:this.$('#create_redirect').prop('checked'),redirect_type:this.$('#redirect_type').val(),website_indexed:this.$('#is_indexed').prop('checked'),visibility:this.$('#visibility').val(),date_publish:datePublish,};if(this.page.hasSingleGroup&&this.$('#visibility').val()==='restricted_group'){params['group_id']=this.$('#group_id').data('group-id');}
if(this.$('#visibility').val()==='password'){var field_pwd=$('#visibility_password');if(!field_pwd.get(0).reportValidity()){return;}
if(field_pwd.data('dirty')){params['visibility_pwd']=field_pwd.val();}}
this._rpc({model:'website.page',method:'save_page_info',args:[[context.website_id],params],}).then(function(url){var mo;self.trigger_up('main_object_request',{callback:function(value){mo=value;},});if(mo.model==='website.page'){window.location.href=url.toLowerCase();}else{window.location.reload(true);}});},_getPageDependencies:function(moID){return this._rpc({model:'website',method:'page_search_dependencies',args:[moID],});},_getPageKeyDependencies:function(moID){return this._rpc({model:'website',method:'page_search_key_dependencies',args:[moID],});},_getSupportedMimetype:function(){return this._rpc({model:'website',method:'guess_mimetype',});},_getMainObject:function(){var repr=$('html').data('main-object');var m=repr.match(/(.+)\((\d+),(.*)\)/);return{model:m[1],id:m[2]|0,};},_parse_date:function(value){var datetime=moment(value,time.getLangDatetimeFormat(),true);if(datetime.isValid()){return time.datetime_to_str(datetime.toDate());}
else{return false;}},autocompleteWithGroups:function($input){$input.autocomplete({source:(request,response)=>{return this._rpc({model:'res.groups',method:'search_read',args:[[['name','ilike',request.term]],['display_name']],kwargs:{limit:15,},}).then(founds=>{founds=founds.map(g=>({'id':g['id'],'label':g['display_name']}));response(founds);});},change:(ev,ui)=>{var $target=$(ev.target);if(!ui.item){$target.val("");$target.removeData('group-id');}else{$target.data('group-id',ui.item.id);}},});},_onUrlChanged:function(){var url=this.$('input#page_url').val();this.$('.ask_for_redirect').toggleClass('d-none',url===this.page.url);},_onNameChanged:function(){var name=this.$('input#page_name').val();var ext='.'+this.page.name.split('.').pop();if(ext in this.supportedMimetype&&ext!=='.html'){this.$('.warn_about_call').toggleClass('d-none',name===this.page.name);}},_onCreateRedirectChanged:function(){var createRedirect=this.$('input#create_redirect').prop('checked');this.$('.redirect_type').toggleClass('d-none',!createRedirect);},_onVisibilityChanged:function(ev){this.$('.show_visibility_password').toggleClass('d-none',ev.target.value!=='password');this.$('.show_group_id').toggleClass('d-none',ev.target.value!=='restricted_group');this.$('#visibility_password').attr('required',ev.target.value==='password');},_onDateTimePickerError:function(ev){return false;},_onPasswordClicked:function(ev){ev.target.value='';this._onPasswordChanged();},_onPasswordChanged:function(){this.$('#visibility_password').data('dirty',1);},});var MenuEntryDialog=weWidgets.LinkDialog.extend({xmlDependencies:weWidgets.LinkDialog.prototype.xmlDependencies.concat(['/website/static/src/xml/website.contentMenu.xml']),init:function(parent,options,editable,data){this._super(parent,_.extend({title:_t("Add a menu item"),},options||{}),editable,_.extend({needLabel:true,text:data.name||'',isNewWindow:data.new_window,},data||{}));this.menuType=data.menuType;},start:function(){this.$('.o_link_dialog_preview').remove();this.$('input[name="is_new_window"], .link-style').closest('.form-group').remove();this.$modal.find('.modal-lg').removeClass('modal-lg');this.$('form.col-lg-8').removeClass('col-lg-8').addClass('col-12');this.$('label[for="o_link_dialog_label_input"]').text(_t("Menu Label"));if(this.menuType==='mega'){var $url=this.$('input[name="url"]');$url.val('#').trigger('change');$url.closest('.form-group').addClass('d-none');}
return this._super.apply(this,arguments);},save:function(){var $e=this.$('#o_link_dialog_label_input');if(!$e.val()||!$e[0].checkValidity()){$e.closest('.form-group').addClass('o_has_error').find('.form-control, .custom-select').addClass('is-invalid');$e.focus();return;}
return this._super.apply(this,arguments);},});var SelectEditMenuDialog=weWidgets.Dialog.extend({template:'website.contentMenu.dialog.select',xmlDependencies:weWidgets.Dialog.prototype.xmlDependencies.concat(['/website/static/src/xml/website.contentMenu.xml']),init:function(parent,options){var self=this;self.roots=[{id:null,name:_t("Top Menu")}];$('[data-content_menu_id]').each(function(){self.roots.push({id:$(this).data('content_menu_id'),name:$(this).attr('name')||$(this).data('menu_name')});});this._super(parent,_.extend({},{title:_t("Select a Menu"),save_text:_t("Continue")},options||{}));},save:function(){this.final_data=parseInt(this.$el.find('select').val()||null);this._super.apply(this,arguments);},});var EditMenuDialog=weWidgets.Dialog.extend({template:'website.contentMenu.dialog.edit',xmlDependencies:weWidgets.Dialog.prototype.xmlDependencies.concat(['/website/static/src/xml/website.contentMenu.xml']),events:_.extend({},weWidgets.Dialog.prototype.events,{'click a.js_add_menu':'_onAddMenuButtonClick','click button.js_delete_menu':'_onDeleteMenuButtonClick','click button.js_edit_menu':'_onEditMenuButtonClick',}),init:function(parent,options,rootID){this._super(parent,_.extend({},{title:_t("Edit Menu"),size:'medium',},options||{}));this.rootID=rootID;},willStart:function(){var defs=[this._super.apply(this,arguments)];var context;this.trigger_up('context_get',{callback:function(ctx){context=ctx;},});defs.push(this._rpc({model:'website.menu',method:'get_tree',args:[context.website_id,this.rootID],}).then(menu=>{this.menu=menu;this.rootMenuID=menu.fields['id'];this.flat=this._flatenize(menu);this.toDelete=[];}));return Promise.all(defs);},start:function(){var r=this._super.apply(this,arguments);this.$('.oe_menu_editor').nestedSortable({listType:'ul',handle:'div',items:'li',maxLevels:2,toleranceElement:'> div',forcePlaceholderSize:true,opacity:0.6,placeholder:'oe_menu_placeholder',tolerance:'pointer',attribute:'data-menu-id',expression:'()(.+)',isAllowed:(placeholder,placeholderParent,currentItem)=>{return!placeholderParent||!currentItem[0].dataset.megaMenu&&!placeholderParent[0].dataset.megaMenu;},});return r;},save:function(){var _super=this._super.bind(this);var newMenus=this.$('.oe_menu_editor').nestedSortable('toArray',{startDepthCount:0});var levels=[];var data=[];var context;this.trigger_up('context_get',{callback:function(ctx){context=ctx;},});newMenus.forEach(menu=>{if(menu.id){levels[menu.depth]=(levels[menu.depth]||0)+1;var menuFields=this.flat[menu.id].fields;menuFields['sequence']=levels[menu.depth];menuFields['parent_id']=menu['parent_id']||this.rootMenuID;data.push(menuFields);}});return this._rpc({model:'website.menu',method:'save',args:[context.website_id,{'data':data,'to_delete':this.toDelete,}],}).then(function(){return _super();});},_flatenize:function(node,_dict){_dict=_dict||{};_dict[node.fields['id']]=node;node.children.forEach(child=>{this._flatenize(child,_dict);});return _dict;},_onAddMenuButtonClick:function(ev){var menuType=ev.currentTarget.dataset.type;var dialog=new MenuEntryDialog(this,{},null,{menuType:menuType,});dialog.on('save',this,link=>{var newMenu={'fields':{'id':_.uniqueId('new-'),'name':_.unescape(link.text),'url':link.url,'new_window':link.isNewWindow,'is_mega_menu':menuType==='mega','sequence':0,'parent_id':false,},'children':[],'is_homepage':false,};this.flat[newMenu.fields['id']]=newMenu;this.$('.oe_menu_editor').append(qweb.render('website.contentMenu.dialog.submenu',{submenu:newMenu}));});dialog.open();},_onDeleteMenuButtonClick:function(ev){var $menu=$(ev.currentTarget).closest('[data-menu-id]');var menuID=parseInt($menu.data('menu-id'));if(menuID){this.toDelete.push(menuID);}
$menu.remove();},_onEditMenuButtonClick:function(ev){var $menu=$(ev.currentTarget).closest('[data-menu-id]');var menuID=$menu.data('menu-id');var menu=this.flat[menuID];if(menu){var dialog=new MenuEntryDialog(this,{},null,_.extend({menuType:menu.fields['is_mega_menu']?'mega':undefined,},menu.fields));dialog.on('save',this,link=>{_.extend(menu.fields,{'name':_.unescape(link.text),'url':link.url,'new_window':link.isNewWindow,});$menu.find('.js_menu_label').first().text(menu.fields['name']);});dialog.open();}else{Dialog.alert(null,"Could not find menu entry");}},});var PageOption=Class.extend({init:function(name,value,setValueCallback){this.name=name;this.value=value;this.isDirty=false;this.setValueCallback=setValueCallback;},setValue:function(value){if(value===undefined){value=!this.value;}
this.setValueCallback.call(this,value);this.value=value;this.isDirty=true;},});var ContentMenu=websiteNavbarData.WebsiteNavbarActionWidget.extend({xmlDependencies:['/website/static/src/xml/website.xml'],actions:_.extend({},websiteNavbarData.WebsiteNavbarActionWidget.prototype.actions||{},{edit_menu:'_editMenu',get_page_option:'_getPageOption',on_save:'_onSave',page_properties:'_pageProperties',toggle_page_option:'_togglePageOption',}),pageOptionsSetValueCallbacks:{header_overlay:function(value){$('#wrapwrap').toggleClass('o_header_overlay',value);},header_color:function(value){$('#wrapwrap > header').removeClass(this.value).addClass(value);},header_visible:function(value){$('#wrapwrap > header').toggleClass('d-none o_snippet_invisible',!value);},footer_visible:function(value){$('#wrapwrap > footer').toggleClass('d-none o_snippet_invisible',!value);},},start:function(){var self=this;this.pageOptions={};_.each($('.o_page_option_data'),function(el){var value=el.value;if(value==="True"){value=true;}else if(value==="False"){value=false;}
self.pageOptions[el.name]=new PageOption(el.name,value,self.pageOptionsSetValueCallbacks[el.name]);});return this._super.apply(this,arguments);},_editMenu:function(beforeReloadCallback){var self=this;return new Promise(function(resolve){function resolveWhenEditMenuDialogIsCancelled(rootID){return self._openEditMenuDialog(rootID,beforeReloadCallback).then(resolve);}
if($('[data-content_menu_id]').length){var select=new SelectEditMenuDialog(self);select.on('save',self,resolveWhenEditMenuDialogIsCancelled);select.on('cancel',self,resolve);select.open();}else{resolveWhenEditMenuDialogIsCancelled(null);}});},_openEditMenuDialog:function(rootID,beforeReloadCallback){var self=this;return new Promise(function(resolve){var dialog=new EditMenuDialog(self,{},rootID);dialog.on('save',self,function(){if(beforeReloadCallback){beforeReloadCallback().then(function(){window.location.reload(true);});}else{window.location.reload(true);}});dialog.on('cancel',self,resolve);dialog.open();});},_getPageOption:function(name){var option=this.pageOptions[name];if(!option){return Promise.reject();}
return Promise.resolve(option.value);},_onSave:function(){var self=this;var defs=_.map(this.pageOptions,function(option,optionName){if(option.isDirty){return self._togglePageOption({name:optionName,value:option.value,},true,true);}});return Promise.all(defs);},_pageProperties:function(){var mo;this.trigger_up('main_object_request',{callback:function(value){mo=value;},});var dialog=new PagePropertiesDialog(this,mo.id,{}).open();return dialog.opened();},_togglePageOption:function(params,forceSave,noReload){var mo;this.trigger_up('main_object_request',{callback:function(value){mo=value;},});if(mo.model!=='website.page'){return Promise.reject();}
var option=this.pageOptions[params.name];if(!option){return Promise.reject();}
option.setValue(params.value);if(!forceSave){return Promise.resolve();}
var vals={};vals[params.name]=option.value;var prom=this._rpc({model:'website.page',method:'write',args:[[mo.id],vals],});if(noReload){return prom;}
return prom.then(function(){window.location.reload();return new Promise(function(){});});},});var PageManagement=Widget.extend({xmlDependencies:['/website/static/src/xml/website.xml'],events:{'click a.js_page_properties':'_onPagePropertiesButtonClick','click a.js_clone_page':'_onClonePageButtonClick','click a.js_delete_page':'_onDeletePageButtonClick',},_getPageDependencies:function(moID){return this._rpc({model:'website',method:'page_search_dependencies',args:[moID],});},_onPagePropertiesButtonClick:function(ev){var moID=$(ev.currentTarget).data('id');var dialog=new PagePropertiesDialog(this,moID,{'fromPageManagement':true}).open();return dialog;},_onClonePageButtonClick:function(ev){var pageId=$(ev.currentTarget).data('id');_clonePage.call(this,pageId);},_onDeletePageButtonClick:function(ev){var pageId=$(ev.currentTarget).data('id');_deletePage.call(this,pageId,true);},});function _deletePage(pageId,fromPageManagement){var self=this;new Promise(function(resolve,reject){self._getPageDependencies(pageId).then(function(dependencies){return new Promise(function(confirmResolve,confirmReject){Dialog.safeConfirm(self,"",{title:_t("Delete Page"),$content:$(qweb.render('website.delete_page',{dependencies:dependencies})),confirm_callback:confirmResolve,cancel_callback:resolve,});});}).then(function(){return self._rpc({model:'website.page',method:'unlink',args:[pageId],});}).then(function(){if(fromPageManagement){window.location.reload(true);}else{window.location.href='/';}},reject);});}
function _clonePage(pageId){var self=this;new Promise(function(resolve,reject){Dialog.confirm(this,undefined,{title:_t("Duplicate Page"),$content:$(qweb.render('website.duplicate_page_action_dialog')),confirm_callback:function(){return self._rpc({model:'website.page',method:'clone_page',args:[pageId,this.$('#page_name').val(),],}).then(function(path){window.location.href=path;}).guardedCatch(reject);},cancel_callback:reject,}).on('closed',null,reject);});}
websiteNavbarData.websiteNavbarRegistry.add(ContentMenu,'#content-menu');websiteRootData.websiteRootRegistry.add(PageManagement,'#list_website_pages');return{PagePropertiesDialog:PagePropertiesDialog,ContentMenu:ContentMenu,EditMenuDialog:EditMenuDialog,MenuEntryDialog:MenuEntryDialog,SelectEditMenuDialog:SelectEditMenuDialog,};});;

/* /website/static/src/js/menu/customize.js defined in bundle 'website.assets_editor' */
odoo.define('website.customizeMenu',function(require){'use strict';var core=require('web.core');var Widget=require('web.Widget');var websiteNavbarData=require('website.navbar');var WebsiteAceEditor=require('website.ace');var qweb=core.qweb;var CustomizeMenu=Widget.extend({xmlDependencies:['/website/static/src/xml/website.editor.xml'],events:{'show.bs.dropdown':'_onDropdownShow','click .dropdown-item[data-view-key]':'_onCustomizeOptionClick',},willStart:function(){this.viewName=$(document.documentElement).data('view-xmlid');return this._super.apply(this,arguments);},start:function(){if(!this.viewName){_.defer(this.destroy.bind(this));}
if(this.$el.is('.show')){this._loadCustomizeOptions();}
return this._super.apply(this,arguments);},_doCustomize:function(viewKey){return this._rpc({route:'/website/toggle_switchable_view',params:{'view_key':viewKey,},}).then(function(){window.location.reload();return new Promise(function(){});});},_loadCustomizeOptions:function(){if(this.__customizeOptionsLoaded){return Promise.resolve();}
this.__customizeOptionsLoaded=true;var $menu=this.$el.children('.dropdown-menu');return this._rpc({route:'/website/get_switchable_related_views',params:{key:this.viewName,},}).then(function(result){var currentGroup='';if(result.length){$menu.append($('<div/>',{class:'dropdown-divider',role:'separator',}));}
_.each(result,function(item){if(currentGroup!==item.inherit_id[1]){currentGroup=item.inherit_id[1];$menu.append('<li class="dropdown-header">'+currentGroup+'</li>');}
var $a=$('<a/>',{href:'#',class:'dropdown-item','data-view-key':item.key,role:'menuitem'}).append(qweb.render('website.components.switch',{id:'switch-'+item.id,label:item.name}));$a.find('input').prop('checked',!!item.active);$menu.append($a);});});},_onCustomizeOptionClick:function(ev){ev.preventDefault();var viewKey=$(ev.currentTarget).data('viewKey');this._doCustomize(viewKey);},_onDropdownShow:function(){this._loadCustomizeOptions();},});var AceEditorMenu=websiteNavbarData.WebsiteNavbarActionWidget.extend({actions:_.extend({},websiteNavbarData.WebsiteNavbarActionWidget.prototype.actions||{},{close_all_widgets:'_hideEditor',edit:'_enterEditMode',ace:'_launchAce',}),start:function(){if(window.location.hash.substr(0,WebsiteAceEditor.prototype.hash.length)===WebsiteAceEditor.prototype.hash){this._launchAce();}
return this._super.apply(this,arguments);},_enterEditMode:function(){this._hideEditor();},_hideEditor:function(){if(this.globalEditor){this.globalEditor.do_hide();}},_launchAce:function(){var self=this;var prom=new Promise(function(resolve,reject){self.trigger_up('action_demand',{actionName:'close_all_widgets',onSuccess:resolve,});});prom.then(function(){if(self.globalEditor){self.globalEditor.do_show();return Promise.resolve();}else{var currentHash=window.location.hash;var indexOfView=currentHash.indexOf("?res=");var initialResID=undefined;if(indexOfView>=0){initialResID=currentHash.substr(indexOfView+("?res=".length));var parsedResID=parseInt(initialResID,10);if(parsedResID){initialResID=parsedResID;}}
self.globalEditor=new WebsiteAceEditor(self,$(document.documentElement).data('view-xmlid'),{initialResID:initialResID,defaultBundlesRestriction:['web.assets_frontend','web.assets_frontend_minimal','web.assets_frontend_lazy',],});return self.globalEditor.appendTo(document.body);}});return prom;},});websiteNavbarData.websiteNavbarRegistry.add(CustomizeMenu,'#customize-menu');websiteNavbarData.websiteNavbarRegistry.add(AceEditorMenu,'#html_editor');return CustomizeMenu;});;

/* /website/static/src/js/menu/debug_manager.js defined in bundle 'website.assets_editor' */
odoo.define('website.debugManager',function(require){'use strict';var config=require('web.config');var DebugManager=require('web.DebugManager');var websiteNavbarData=require('website.navbar');var DebugManagerMenu=websiteNavbarData.WebsiteNavbar.include({start:function(){if(config.isDebug()){new DebugManager(this).prependTo(this.$('.o_menu_systray'));}
return this._super.apply(this,arguments);},});return DebugManagerMenu;});;

/* /website/static/src/js/menu/edit.js defined in bundle 'website.assets_editor' */
odoo.define('website.editMenu',function(require){'use strict';var core=require('web.core');var EditorMenu=require('website.editor.menu');var websiteNavbarData=require('website.navbar');var _t=core._t;var EditPageMenu=websiteNavbarData.WebsiteNavbarActionWidget.extend({assetLibs:['web_editor.compiled_assets_wysiwyg','website.compiled_assets_wysiwyg'],xmlDependencies:['/website/static/src/xml/website.editor.xml'],actions:_.extend({},websiteNavbarData.WebsiteNavbarActionWidget.prototype.actions,{edit:'_startEditMode',on_save:'_onSave',}),custom_events:_.extend({},websiteNavbarData.WebsiteNavbarActionWidget.custom_events||{},{content_will_be_destroyed:'_onContentWillBeDestroyed',content_was_recreated:'_onContentWasRecreated',snippet_will_be_cloned:'_onSnippetWillBeCloned',snippet_cloned:'_onSnippetCloned',snippet_dropped:'_onSnippetDropped',edition_will_stopped:'_onEditionWillStop',edition_was_stopped:'_onEditionWasStopped',}),init:function(){this._super.apply(this,arguments);var context;this.trigger_up('context_get',{extra:true,callback:function(ctx){context=ctx;},});this._editorAutoStart=(context.editable&&window.location.search.indexOf('enable_editor')>=0);var url=window.location.href.replace(/([?&])&*enable_editor[^&#]*&?/,'\$1');window.history.replaceState({},null,url);},start:function(){var def=this._super.apply(this,arguments);if(this._editorAutoStart){return Promise.all([def,this._startEditMode()]);}
var $wrap=this._targetForEdition().filter('#wrapwrap.homepage').find('#wrap');if($wrap.length&&$wrap.html().trim()===''){this.$welcomeMessage=$(core.qweb.render('website.homepage_editor_welcome_message'));this.$welcomeMessage.addClass('o_homepage_editor_welcome_message');this.$welcomeMessage.css('min-height',$wrap.parent('main').height()-($wrap.outerHeight(true)-$wrap.height()));$wrap.empty().append(this.$welcomeMessage);}
return def;},_startEditMode:async function(){var self=this;if(this.editModeEnable){return;}
this.trigger_up('widgets_stop_request',{$target:this._targetForEdition(),});if(this.$welcomeMessage){this.$welcomeMessage.detach();}
this.editModeEnable=true;await new EditorMenu(this).prependTo(document.body);this._addEditorMessages();var res=await new Promise(function(resolve,reject){self.trigger_up('widgets_start_request',{editableMode:true,onSuccess:resolve,onFailure:reject,});});this.$editorMessageElements.mousedown();return res;},_onSave:function(){},_addEditorMessages:function(){const $target=this._targetForEdition();const $skeleton=$target.find('.oe_structure.oe_empty, [data-oe-type="html"]').filter(':o_editable');this.$editorMessageElements=$skeleton.not('[data-editor-message]').attr('data-editor-message',_t('DRAG BUILDING BLOCKS HERE'));$skeleton.attr('contenteditable',function(){return!$(this).is(':empty');});},_targetForEdition:function(){return $('#wrapwrap');},_onContentWillBeDestroyed:function(ev){this.trigger_up('widgets_stop_request',{$target:ev.data.$target,});},_onContentWasRecreated:function(ev){this.trigger_up('widgets_start_request',{editableMode:true,$target:ev.data.$target,});},_onEditionWillStop:function(ev){this.$editorMessageElements&&this.$editorMessageElements.removeAttr('data-editor-message');if(ev.data.noWidgetsStop){return;}
this.trigger_up('widgets_stop_request',{$target:this._targetForEdition(),});},_onEditionWasStopped:function(ev){this.trigger_up('widgets_start_request',{$target:this._targetForEdition(),});this.editModeEnable=false;},_onSnippetWillBeCloned:function(ev){this.trigger_up('widgets_stop_request',{$target:ev.data.$target,});},_onSnippetCloned:function(ev){this.trigger_up('widgets_start_request',{editableMode:true,$target:ev.data.$target,});if(ev.data.$origin){this.trigger_up('widgets_start_request',{editableMode:true,$target:ev.data.$origin,});}},_onSnippetDropped:function(ev){this.trigger_up('widgets_start_request',{editableMode:true,$target:ev.data.$target,});this._addEditorMessages();},});websiteNavbarData.websiteNavbarRegistry.add(EditPageMenu,'#edit-page-menu');});;

/* /website/static/src/js/menu/mobile_view.js defined in bundle 'website.assets_editor' */
odoo.define('website.mobile',function(require){'use strict';var core=require('web.core');var Dialog=require('web.Dialog');var websiteNavbarData=require('website.navbar');var _t=core._t;var MobilePreviewDialog=Dialog.extend({start:function(){var self=this;this.$modal.addClass('oe_mobile_preview');this.$modal.on('click','.modal-header',function(){self.$el.toggleClass('o_invert_orientation');});this.$iframe=$('<iframe/>',{id:'mobile-viewport',src:$.param.querystring(window.location.href,'mobilepreview'),});this.$iframe.on('load',function(e){self.$iframe.contents().find('body').removeClass('o_connected_user');self.$iframe.contents().find('#oe_main_menu_navbar').remove();});this.$iframe.appendTo(this.$el);return this._super.apply(this,arguments);},});var MobileMenu=websiteNavbarData.WebsiteNavbarActionWidget.extend({actions:_.extend({},websiteNavbarData.WebsiteNavbarActionWidget.prototype.actions||{},{'show-mobile-preview':'_onMobilePreviewClick',}),_onMobilePreviewClick:function(){if(this.mobilePreview&&!this.mobilePreview.isDestroyed()){return this.mobilePreview.close();}
this.mobilePreview=new MobilePreviewDialog(this,{title:_t('Mobile preview')+' <span class="fa fa-refresh"/>',}).open();},});websiteNavbarData.websiteNavbarRegistry.add(MobileMenu,'#mobile-menu');return{MobileMenu:MobileMenu,MobilePreviewDialog:MobilePreviewDialog,};});;

/* /website/static/src/js/menu/new_content.js defined in bundle 'website.assets_editor' */
odoo.define('website.newMenu',function(require){'use strict';var core=require('web.core');var Dialog=require('web.Dialog');var websiteNavbarData=require('website.navbar');var wUtils=require('website.utils');var tour=require('web_tour.tour');const{qweb,_t}=core;var enableFlag='enable_new_content';var NewContentMenu=websiteNavbarData.WebsiteNavbarActionWidget.extend({xmlDependencies:['/website/static/src/xml/website.editor.xml'],actions:_.extend({},websiteNavbarData.WebsiteNavbarActionWidget.prototype.actions||{},{close_all_widgets:'_handleCloseDemand',new_page:'_createNewPage',}),events:_.extend({},websiteNavbarData.WebsiteNavbarActionWidget.prototype.events||{},{'click':'_onBackgroundClick','click [data-module-id]':'_onModuleIdClick','keydown':'_onBackgroundKeydown',}),newContentText:{failed:_t('Failed to install "%s"'),installInProgress:_t("The installation of an App is already in progress."),installNeeded:_t('Do you want to install the "%s" App?'),installPleaseWait:_t('Installing "%s"'),},start:function(){this.pendingInstall=false;this.$newContentMenuChoices=this.$('#o_new_content_menu_choices');var $modules=this.$newContentMenuChoices.find('.o_new_content_element');_.each($modules,function(el,index){var $el=$(el);$el.data('original-index',index);if($el.data('module-id')){$el.appendTo($el.parent());$el.find('a i, a p').addClass('o_uninstalled_module');}});this.$firstLink=this.$newContentMenuChoices.find('a:eq(0)');this.$lastLink=this.$newContentMenuChoices.find('a:last');if($.deparam.querystring()[enableFlag]!==undefined){Object.keys(tour.tours).forEach(el=>{let element=tour.tours[el];if(element.steps[0].trigger=='#new-content-menu > a'&&!element.steps[0].extra_trigger){element.steps[0].auto=true;}});this._showMenu();}
this.$loader=$(qweb.render('website.new_content_loader'));return this._super.apply(this,arguments);},_createNewPage:function(){return wUtils.prompt({id:'editor_new_page',window_title:_t("New Page"),input:_t("Page Title"),init:function(){var $group=this.$dialog.find('div.form-group');$group.removeClass('mb0');var $add=$('<div/>',{'class':'form-group mb0 row'}).append($('<span/>',{'class':'offset-md-3 col-md-9 text-left'}).append(qweb.render('website.components.switch',{id:'switch_addTo_menu',label:_t("Add to menu")})));$add.find('input').prop('checked',true);$group.after($add);}}).then(function(result){const val=result.val.replace(/^\/*/,"");var $dialog=result.dialog;if(!val){return;}
var url='/website/add/'+encodeURIComponent(val);const res=wUtils.sendRequest(url,{add_menu:$dialog.find('input[type="checkbox"]').is(':checked')||'',});return new Promise(function(){});});},_handleCloseDemand:function(){this._hideMenu();},_focusFirstLink:function(){this.$firstLink.focus();},_focusLastLink:function(){this.$lastLink.focus();},_hideMenu:function(){this.shown=false;this.$newContentMenuChoices.addClass('o_hidden');$('body').removeClass('o_new_content_open');},_install:function(moduleId){this.pendingInstall=true;$('body').css('pointer-events','none');return this._rpc({model:'ir.module.module',method:'button_immediate_install',args:[[moduleId]],}).guardedCatch(function(){$('body').css('pointer-events','');});},_showMenu:function(){var self=this;return new Promise(function(resolve,reject){self.trigger_up('action_demand',{actionName:'close_all_widgets',onSuccess:resolve,});}).then(function(){self.firstTab=true;self.shown=true;self.$newContentMenuChoices.removeClass('o_hidden');$('body').addClass('o_new_content_open');self.$('> a').focus();wUtils.removeLoader();});},_addLoader(moduleName){const newContentLoaderText=_.str.sprintf(_t("Building your %s"),moduleName);this.$loader.find('#new_content_loader_text').replaceWith(newContentLoaderText);$('body').append(this.$loader);},_removeLoader(){this.$loader.remove();},_onBackgroundClick:function(ev){if(this.$newContentMenuChoices.hasClass('o_hidden')){this._showMenu();}else{this._hideMenu();}},_onBackgroundKeydown:function(ev){if(!this.shown){return;}
switch(ev.which){case $.ui.keyCode.ESCAPE:this._hideMenu();ev.stopPropagation();break;case $.ui.keyCode.TAB:if(ev.shiftKey){if(this.firstTab||document.activeElement===this.$firstLink[0]){this._focusLastLink();ev.preventDefault();}}else{if(this.firstTab||document.activeElement===this.$lastLink[0]){this._focusFirstLink();ev.preventDefault();}}
this.firstTab=false;break;}},_onModuleIdClick:function(ev){var self=this;var $el=$(ev.currentTarget);var $i=$el.find('a i');var $p=$el.find('a p');var title=$p.text();var content='';var buttons;var moduleId=$el.data('module-id');var name=$el.data('module-shortdesc');ev.stopPropagation();ev.preventDefault();if(this.pendingInstall){content=this.newContentText.installInProgress;}else{content=_.str.sprintf(this.newContentText.installNeeded,name);buttons=[{text:_t("Install"),classes:'btn-primary',close:true,click:function(){var $finalPosition=self.$newContentMenuChoices.find('.o_new_content_element:not([data-module-id])').filter(function(){return $(this).data('original-index')<$el.data('original-index');}).last();if($finalPosition){$el.fadeTo(400,0,function(){if(!$el.hasClass('o_new_content_element_once')){$el.insertAfter($finalPosition);}
$i.removeClass().addClass('fa fa-spin fa-spinner fa-pulse').css('background-image','none');$p.removeClass('o_uninstalled_module').text(_.str.sprintf(self.newContentText.installPleaseWait,name));$el.fadeTo(1000,1);self._addLoader(name);});}
self._install(moduleId).then(function(){var origin=window.location.origin;var redirectURL=$el.find('a').data('url')||(window.location.pathname+'?'+enableFlag);window.location.href=origin+redirectURL;self._removeLoader();},function(){$i.removeClass().addClass('fa fa-exclamation-triangle');$p.text(_.str.sprintf(self.newContentText.failed,name));});}},{text:_t("Cancel"),close:true,}];}
new Dialog(this,{title:title,size:'medium',$content:$('<div/>',{text:content}),buttons:buttons}).open();},});websiteNavbarData.websiteNavbarRegistry.add(NewContentMenu,'.o_new_content_menu');return NewContentMenu;});;

/* /website/static/src/js/menu/seo.js defined in bundle 'website.assets_editor' */
odoo.define('website.seo',function(require){'use strict';var core=require('web.core');var Class=require('web.Class');var Dialog=require('web.Dialog');var mixins=require('web.mixins');var rpc=require('web.rpc');var Widget=require('web.Widget');var weWidgets=require('wysiwyg.widgets');var websiteNavbarData=require('website.navbar');var _t=core._t;var WORD_SEPARATORS_REGEX='([\\u2000-\\u206F\\u2E00-\\u2E7F\'!"#\\$%&\\(\\)\\*\\+,\\-\\.\\/:;<=>\\?¿¡@\\[\\]\\^_`\\{\\|\\}~\\s]+|^|$)';var Suggestion=Widget.extend({template:'website.seo_suggestion',xmlDependencies:['/website/static/src/xml/website.seo.xml'],events:{'click .o_seo_suggestion':'select',},init:function(parent,options){this.keyword=options.keyword;this._super(parent);},select:function(){this.trigger('selected',this.keyword);},});var SuggestionList=Widget.extend({template:'website.seo_suggestion_list',xmlDependencies:['/website/static/src/xml/website.seo.xml'],init:function(parent,options){this.root=options.root;this.language=options.language;this.htmlPage=options.htmlPage;this._super(parent);},start:function(){this.refresh();},refresh:function(){var self=this;self.$el.append(_t("Loading..."));var context;this.trigger_up('context_get',{callback:function(ctx){context=ctx;},});var language=self.language||context.lang.toLowerCase();this._rpc({route:'/website/seo_suggest',params:{keywords:self.root,lang:language,},}).then(function(keyword_list){self.addSuggestions(JSON.parse(keyword_list));});},addSuggestions:function(keywords){var self=this;self.$el.empty();var regex=new RegExp(WORD_SEPARATORS_REGEX+self.root+WORD_SEPARATORS_REGEX,'gi');keywords=_.map(_.uniq(keywords),function(word){return word.replace(regex,'').trim();});_.each(keywords,function(keyword){if(keyword){var suggestion=new Suggestion(self,{keyword:keyword,});suggestion.on('selected',self,function(word,language){self.trigger('selected',word,language);});suggestion.appendTo(self.$el);}});},});var Keyword=Widget.extend({template:'website.seo_keyword',xmlDependencies:['/website/static/src/xml/website.seo.xml'],events:{'click a[data-action=remove-keyword]':'destroy',},init:function(parent,options){this.keyword=options.word;this.language=options.language;this.htmlPage=options.htmlPage;this.used_h1=this.htmlPage.isInHeading1(this.keyword);this.used_h2=this.htmlPage.isInHeading2(this.keyword);this.used_content=this.htmlPage.isInBody(this.keyword);this._super(parent);},start:function(){var self=this;this.$('.o_seo_keyword_suggestion').empty();this.suggestionList=new SuggestionList(this,{root:this.keyword,language:this.language,htmlPage:this.htmlPage,});this.suggestionList.on('selected',this,function(word,language){this.trigger('selected',word,language);});return this.suggestionList.appendTo(this.$('.o_seo_keyword_suggestion')).then(function(){self.htmlPage.on('title-changed',self,self._updateTitle);self.htmlPage.on('description-changed',self,self._updateDescription);self._updateTitle();self._updateDescription();});},destroy:function(){this.trigger('removed');this._super();},_updateTitle:function(){var $title=this.$('.js_seo_keyword_title');if(this.htmlPage.isInTitle(this.keyword)){$title.css('visibility','visible');}else{$title.css('visibility','hidden');}},_updateDescription:function(){var $description=this.$('.js_seo_keyword_description');if(this.htmlPage.isInDescription(this.keyword)){$description.css('visibility','visible');}else{$description.css('visibility','hidden');}},});var KeywordList=Widget.extend({template:'website.seo_list',xmlDependencies:['/website/static/src/xml/website.seo.xml'],maxKeywords:10,init:function(parent,options){this.htmlPage=options.htmlPage;this._super(parent);},start:function(){var self=this;var existingKeywords=self.htmlPage.keywords();if(existingKeywords.length>0){_.each(existingKeywords,function(word){self.add.call(self,word);});}},keywords:function(){var result=[];this.$('.js_seo_keyword').each(function(){result.push($(this).data('keyword'));});return result;},isFull:function(){return this.keywords().length>=this.maxKeywords;},exists:function(word){return _.contains(this.keywords(),word);},add:async function(candidate,language){var self=this;var word=candidate?candidate.replace(/[,;.:<>]+/g,' ').replace(/ +/g,' ').trim().toLowerCase():'';if(word&&!self.isFull()&&!self.exists(word)){var keyword=new Keyword(self,{word:word,language:language,htmlPage:this.htmlPage,});keyword.on('removed',self,function(){self.trigger('list-not-full');self.trigger('content-updated',true);});keyword.on('selected',self,function(word,language){self.trigger('selected',word,language);});await keyword.appendTo(self.$el);}
if(self.isFull()){self.trigger('list-full');}
self.trigger('content-updated');},});var Preview=Widget.extend({template:'website.seo_preview',xmlDependencies:['/website/static/src/xml/website.seo.xml'],init:function(parent,options){this.title=options.title;this.url=options.url;this.description=options.description;if(this.description.length>160){this.description=this.description.substring(0,159)+'…';}
this._super(parent);},});var HtmlPage=Class.extend(mixins.PropertiesMixin,{init:function(){mixins.PropertiesMixin.init.call(this);this.initTitle=this.title();this.defaultTitle=$('meta[name="default_title"]').attr('content');this.initDescription=this.description();},url:function(){return window.location.origin+window.location.pathname;},title:function(){return $('head title').text().trim();},changeTitle:function(title){$('head title').text(title.trim()||this.defaultTitle);this.trigger('title-changed',title);},description:function(){return($('meta[name=description]').attr('content')||'').trim();},changeDescription:function(description){$('meta[name=description]').attr('content',description);this.trigger('description-changed',description);},keywords:function(){var $keywords=$('meta[name=keywords]');var parsed=($keywords.length>0)&&$keywords.attr('content')&&$keywords.attr('content').split(',');return(parsed&&parsed[0])?parsed:[];},changeKeywords:function(keywords){$('meta[name=keywords]').attr('content',keywords.join(','));},headers:function(tag){return $('#wrap '+tag).map(function(){return $(this).text();});},getOgMeta:function(){var ogImageUrl=$('meta[property="og:image"]').attr('content');var title=$('meta[property="og:title"]').attr('content');var description=$('meta[property="og:description"]').attr('content');return{ogImageUrl:ogImageUrl&&ogImageUrl.replace(window.location.origin,''),metaTitle:title,metaDescription:description,};},images:function(){return $('#wrap img').filter(function(){return this.naturalHeight>=200&&this.naturalWidth>=200;}).map(function(){return{src:this.getAttribute('src'),alt:this.getAttribute('alt'),};});},company:function(){return $('html').attr('data-oe-company-name');},bodyText:function(){return $('body').children().not('.oe_seo_configuration').text();},heading1:function(){return $('body').children().not('.oe_seo_configuration').find('h1').text();},heading2:function(){return $('body').children().not('.oe_seo_configuration').find('h2').text();},isInBody:function(text){return new RegExp(WORD_SEPARATORS_REGEX+text+WORD_SEPARATORS_REGEX,'gi').test(this.bodyText());},isInTitle:function(text){return new RegExp(WORD_SEPARATORS_REGEX+text+WORD_SEPARATORS_REGEX,'gi').test(this.title());},isInDescription:function(text){return new RegExp(WORD_SEPARATORS_REGEX+text+WORD_SEPARATORS_REGEX,'gi').test(this.description());},isInHeading1:function(text){return new RegExp(WORD_SEPARATORS_REGEX+text+WORD_SEPARATORS_REGEX,'gi').test(this.heading1());},isInHeading2:function(text){return new RegExp(WORD_SEPARATORS_REGEX+text+WORD_SEPARATORS_REGEX,'gi').test(this.heading2());},});var MetaTitleDescription=Widget.extend({template:'website.seo_meta_title_description',xmlDependencies:['/website/static/src/xml/website.seo.xml'],events:{'input input[name=website_meta_title]':'_titleChanged','input input[name=website_seo_name]':'_seoNameChanged','input textarea[name=website_meta_description]':'_descriptionOnInput','change textarea[name=website_meta_description]':'_descriptionOnChange',},maxRecommendedDescriptionSize:300,minRecommendedDescriptionSize:50,showDescriptionTooSmall:false,init:function(parent,options){this.htmlPage=options.htmlPage;this.canEditTitle=!!options.canEditTitle;this.canEditDescription=!!options.canEditDescription;this.canEditUrl=!!options.canEditUrl;this.isIndexed=!!options.isIndexed;this.seoName=options.seoName;this.seoNameDefault=options.seoNameDefault;this.seoNameHelp=options.seoNameHelp;this.previewDescription=options.previewDescription;this._super(parent,options);},start:function(){this.$title=this.$('input[name=website_meta_title]');this.$seoName=this.$('input[name=website_seo_name]');this.$seoNamePre=this.$('span.seo_name_pre');this.$seoNamePost=this.$('span.seo_name_post');this.$description=this.$('textarea[name=website_meta_description]');this.$warning=this.$('div#website_meta_description_warning');this.$preview=this.$('.js_seo_preview');if(!this.canEditTitle){this.$title.attr('disabled',true);}
if(!this.canEditDescription){this.$description.attr('disabled',true);}
if(this.htmlPage.title().trim()!==this.htmlPage.defaultTitle.trim()){this.$title.val(this.htmlPage.title());}
if(this.htmlPage.description().trim()!==this.previewDescription){this.$description.val(this.htmlPage.description());}
if(this.canEditUrl){this.previousSeoName=this.seoName;this.$seoName.val(this.seoName);this.$seoName.attr('placeholder',this.seoNameDefault);const splitsUrl=window.location.pathname.split(this.previousSeoName||this.seoNameDefault);this.$seoNamePre.text(splitsUrl[0]);this.$seoNamePost.text(splitsUrl.slice(-1)[0]);}
this._descriptionOnChange();},getTitle:function(){return this.$title.val().trim()||this.htmlPage.defaultTitle;},getUrl:function(){const path=window.location.pathname.replace(this.previousSeoName||this.seoNameDefault,(this.$seoName.length&&this.$seoName.val()?this.$seoName.val().trim():this.$seoName.attr('placeholder')));return window.location.origin+path},getDescription:function(){return this.getRealDescription()||this.previewDescription;},getRealDescription:function(){return this.$description.val()||'';},_titleChanged:function(){var self=this;self._renderPreview();self.trigger('title-changed');},_seoNameChanged:function(){var self=this;const slugified=this.$seoName.val().toString().trim().normalize('NFKD').toLowerCase().replace(/\s+/g,'-').replace(/[^\w\-]+/g,'').replace(/\-\-+/g,'-');this.$seoName.val(slugified);self._renderPreview();},_descriptionOnChange:function(){this.showDescriptionTooSmall=true;this._descriptionOnInput();},_descriptionOnInput:function(){var length=this.getDescription().length;if(length>=this.minRecommendedDescriptionSize){this.showDescriptionTooSmall=true;}else if(length===0){this.showDescriptionTooSmall=false;}
if(length>this.maxRecommendedDescriptionSize){this.$warning.text(_t('Your description looks too long.')).show();}else if(this.showDescriptionTooSmall&&length<this.minRecommendedDescriptionSize){this.$warning.text(_t('Your description looks too short.')).show();}else{this.$warning.hide();}
this._renderPreview();this.trigger('description-changed');},_renderPreview:function(){var indexed=this.isIndexed;var preview="";if(indexed){preview=new Preview(this,{title:this.getTitle(),description:this.getDescription(),url:this.getUrl(),});}else{preview=new Preview(this,{description:_t("You have hidden this page from search results. It won't be indexed by search engines."),});}
this.$preview.empty();preview.appendTo(this.$preview);},});var MetaKeywords=Widget.extend({template:'website.seo_meta_keywords',xmlDependencies:['/website/static/src/xml/website.seo.xml'],events:{'keyup input[name=website_meta_keywords]':'_confirmKeyword','click button[data-action=add]':'_addKeyword',},init:function(parent,options){this.htmlPage=options.htmlPage;this._super(parent,options);},start:function(){var self=this;this.$input=this.$('input[name=website_meta_keywords]');this.keywordList=new KeywordList(this,{htmlPage:this.htmlPage});this.keywordList.on('list-full',this,function(){self.$input.attr({readonly:'readonly',placeholder:"Remove a keyword first"});self.$('button[data-action=add]').prop('disabled',true).addClass('disabled');});this.keywordList.on('list-not-full',this,function(){self.$input.removeAttr('readonly').attr('placeholder',"");self.$('button[data-action=add]').prop('disabled',false).removeClass('disabled');});this.keywordList.on('selected',this,function(word,language){self.keywordList.add(word,language);});this.keywordList.on('content-updated',this,function(removed){self._updateTable(removed);});return this.keywordList.insertAfter(this.$('.table thead')).then(function(){self._getLanguages();self._updateTable();});},_addKeyword:function(){var $language=this.$('select[name=seo_page_language]');var keyword=this.$input.val();var language=$language.val().toLowerCase();this.keywordList.add(keyword,language);this.$input.val('').focus();},_confirmKeyword:function(e){if(e.keyCode===13){this._addKeyword();}},_getLanguages:function(){var self=this;var context;this.trigger_up('context_get',{callback:function(ctx){context=ctx;},});this._rpc({route:'/website/get_languages',}).then(function(data){self.$('#language-box').html(core.qweb.render('Configurator.language_promote',{'language':data,'def_lang':context.lang}));});},_updateTable:function(removed){var min=removed?1:0;if(this.keywordList.keywords().length>min){this.$('table').show();}else{this.$('table').hide();}},});var MetaImageSelector=Widget.extend({template:'website.seo_meta_image_selector',xmlDependencies:['/website/static/src/xml/website.seo.xml'],events:{'click .o_meta_img_upload':'_onClickUploadImg','click .o_meta_img':'_onClickSelectImg',},init:function(parent,data){this.metaTitle=data.title||'';this.activeMetaImg=data.metaImg;this.serverUrl=data.htmlpage.url();const imgField=data.hasSocialDefaultImage?'social_default_image':'logo';data.pageImages.unshift(_.str.sprintf('/web/image/website/%s/%s',odoo.session_info.website_id,imgField));this.images=_.uniq(data.pageImages);this.customImgUrl=_.contains(data.pageImages.map((img)=>new URL(img,window.location.origin).pathname),new URL(data.metaImg,window.location.origin).pathname)?false:data.metaImg;this.previewDescription=data.previewDescription;this._setDescription(this.previewDescription);this._super(parent);},setTitle:function(title){this.metaTitle=title;this._updateTemplateBody();},setDescription:function(description){this._setDescription(description);this._updateTemplateBody();},_setDescription:function(description){this.metaDescription=description||this.previewDescription;if(this.metaDescription.length>160){this.metaDescription=this.metaDescription.substring(0,159)+'…';}},_updateTemplateBody:function(){this.$el.empty();this.images=_.uniq(this.images);this.$el.append(core.qweb.render('website.og_image_body',{widget:this}));},_onClickSelectImg:function(ev){var $img=$(ev.currentTarget);this.activeMetaImg=$img.find('img').attr('src');this._updateTemplateBody();},_onClickUploadImg:function(ev){var self=this;var $image=$('<img/>');var mediaDialog=new weWidgets.MediaDialog(this,{onlyImages:true,res_model:'ir.ui.view',},$image[0]);mediaDialog.open();mediaDialog.on('save',this,function(image){self.activeMetaImg=image.src;self.customImgUrl=image.src;self._updateTemplateBody();});},});var SeoConfigurator=Dialog.extend({template:'website.seo_configuration',xmlDependencies:Dialog.prototype.xmlDependencies.concat(['/website/static/src/xml/website.seo.xml']),canEditTitle:false,canEditDescription:false,canEditKeywords:false,canEditLanguage:false,canEditUrl:false,init:function(parent,options){options=options||{};_.defaults(options,{title:_t('Optimize SEO'),buttons:[{text:_t('Save'),classes:'btn-primary',click:this.update},{text:_t('Discard'),close:true},],});this._super(parent,options);},start:function(){var self=this;this.$modal.addClass('oe_seo_configuration');this.htmlPage=new HtmlPage();this.disableUnsavableFields().then(function(){self.metaImageSelector=new MetaImageSelector(self,{htmlpage:self.htmlPage,hasSocialDefaultImage:self.hasSocialDefaultImage,title:self.htmlPage.getOgMeta().metaTitle,metaImg:self.metaImg||self.htmlPage.getOgMeta().ogImageUrl,pageImages:_.pluck(self.htmlPage.images().get(),'src'),previewDescription:_t('The description will be generated by social media based on page content unless you specify one.'),});self.metaImageSelector.appendTo(self.$('.js_seo_image'));self.metaTitleDescription=new MetaTitleDescription(self,{htmlPage:self.htmlPage,canEditTitle:self.canEditTitle,canEditDescription:self.canEditDescription,canEditUrl:self.canEditUrl,isIndexed:self.isIndexed,previewDescription:_t('The description will be generated by search engines based on page content unless you specify one.'),seoNameHelp:_t('This value will be escaped to be compliant with all major browsers and used in url. Keep it empty to use the default name of the record.'),seoName:self.seoName,seoNameDefault:self.seoNameDefault,});self.metaTitleDescription.on('title-changed',self,self.titleChanged);self.metaTitleDescription.on('description-changed',self,self.descriptionChanged);self.metaTitleDescription.appendTo(self.$('.js_seo_meta_title_description'));self.metaKeywords=new MetaKeywords(self,{htmlPage:self.htmlPage});self.metaKeywords.appendTo(self.$('.js_seo_meta_keywords'));});},destroy:function(){if(!this.savedData){this.htmlPage.changeTitle(this.htmlPage.initTitle);this.htmlPage.changeDescription(this.htmlPage.initDescription);}
this._super.apply(this,arguments);},disableUnsavableFields:function(){var self=this;return this.loadMetaData().then(function(data){self.reloadOnSave=data.website_id===undefined?false:!data.website_id;self.isIndexed=(data&&('website_indexed'in data))?data.website_indexed:true;self.canEditTitle=data&&('website_meta_title'in data);self.canEditDescription=data&&('website_meta_description'in data);self.canEditKeywords=data&&('website_meta_keywords'in data);self.metaImg=data.website_meta_og_img;self.hasSocialDefaultImage=data.has_social_default_image;self.canEditUrl=data&&('seo_name'in data);self.seoName=self.canEditUrl&&data.seo_name;self.seoNameDefault=self.canEditUrl&&data.seo_name_default;if(!self.canEditTitle&&!self.canEditDescription&&!self.canEditKeywords){self.$footer.find('button[data-action=update]').attr('disabled',true);}});},update:function(){var self=this;var data={};if(this.canEditTitle){data.website_meta_title=this.metaTitleDescription.$title.val();}
if(this.canEditDescription){data.website_meta_description=this.metaTitleDescription.$description.val();}
if(this.canEditKeywords){data.website_meta_keywords=this.metaKeywords.keywordList.keywords().join(', ');}
if(this.canEditUrl){if(this.metaTitleDescription.$seoName.val()!=this.metaTitleDescription.previousSeoName){data.seo_name=this.metaTitleDescription.$seoName.val();self.reloadOnSave=true;}}
data.website_meta_og_img=this.metaImageSelector.activeMetaImg;this.saveMetaData(data).then(function(){if(self.reloadOnSave){window.location.href=self.htmlPage.url();}else{self.htmlPage.changeKeywords(self.metaKeywords.keywordList.keywords());self.savedData=true;self.close();}});},getMainObject:function(){var mainObject;this.trigger_up('main_object_request',{callback:function(value){mainObject=value;},});return mainObject;},getSeoObject:function(){var seoObject;this.trigger_up('seo_object_request',{callback:function(value){seoObject=value;},});return seoObject;},loadMetaData:function(){var obj=this.getSeoObject()||this.getMainObject();return new Promise(function(resolve,reject){if(!obj){resolve(null);}else{rpc.query({route:"/website/get_seo_data",params:{'res_id':obj.id,'res_model':obj.model,},}).then(function(data){var meta=data;meta.model=obj.model;resolve(meta);}).guardedCatch(reject);}});},saveMetaData:function(data){var obj=this.getSeoObject()||this.getMainObject();if(!obj){return Promise.reject();}else{return this._rpc({model:obj.model,method:'write',args:[[obj.id],data],});}},titleChanged:function(){var self=this;_.defer(function(){var title=self.metaTitleDescription.getTitle();self.htmlPage.changeTitle(title);self.metaImageSelector.setTitle(title);});},descriptionChanged:function(){var self=this;_.defer(function(){var description=self.metaTitleDescription.getRealDescription();self.htmlPage.changeDescription(description);self.metaImageSelector.setDescription(description);});},});var SeoMenu=websiteNavbarData.WebsiteNavbarActionWidget.extend({actions:_.extend({},websiteNavbarData.WebsiteNavbarActionWidget.prototype.actions||{},{'promote-current-page':'_promoteCurrentPage',}),init:function(parent,options){this._super(parent,options);if($.deparam.querystring().enable_seo!==undefined){this._promoteCurrentPage();}},_promoteCurrentPage:function(){new SeoConfigurator(this).open();},});websiteNavbarData.websiteNavbarRegistry.add(SeoMenu,'#promote-menu');return{SeoConfigurator:SeoConfigurator,SeoMenu:SeoMenu,};});;

/* /website/static/src/js/menu/translate.js defined in bundle 'website.assets_editor' */
odoo.define('website.translateMenu',function(require){'use strict';var utils=require('web.utils');var TranslatorMenu=require('website.editor.menu.translate');var websiteNavbarData=require('website.navbar');var TranslatePageMenu=websiteNavbarData.WebsiteNavbarActionWidget.extend({assetLibs:['web_editor.compiled_assets_wysiwyg','website.compiled_assets_wysiwyg'],actions:_.extend({},websiteNavbarData.WebsiteNavbar.prototype.actions||{},{edit_master:'_goToMasterPage',translate:'_startTranslateMode',}),start:function(){var context;this.trigger_up('context_get',{extra:true,callback:function(ctx){context=ctx;},});this._mustEditTranslations=context.edit_translations;if(this._mustEditTranslations){var url=window.location.href.replace(/([?&])&*edit_translations[^&#]*&?/,'\$1');window.history.replaceState({},null,url);this._startTranslateMode();}
return this._super.apply(this,arguments);},_goToMasterPage:function(){var current=document.createElement('a');current.href=window.location.toString();current.search+=(current.search?'&':'?')+'enable_editor=1';current.pathname=current.pathname.substr(Math.max(0,current.pathname.indexOf('/',1)));var link=document.createElement('a');link.href='/website/lang/default';link.search+=(link.search?'&':'?')+'r='+encodeURIComponent(current.pathname+current.search+current.hash);window.location=link.href;return new Promise(function(){});},_startTranslateMode:function(){if(!this._mustEditTranslations){window.location.search+='&edit_translations';return new Promise(function(){});}
var translator=new TranslatorMenu(this);$('.dropdown-menu').on('click','.o_editable',function(ev){ev.stopPropagation();});return translator.prependTo(document.body);},});websiteNavbarData.websiteNavbarRegistry.add(TranslatePageMenu,'.o_menu_systray:has([data-action="translate"])');});;

/* /website/static/src/js/tours/homepage.js defined in bundle 'website.assets_editor' */
odoo.define("website.tour.homepage",function(require){"use strict";const wTourUtils=require("website.tour_utils");const snippets=[{id:'s_cover',name:'Cover',},{id:'s_text_image',name:'Text - Image',},{id:'s_three_columns',name:'Columns',},{id:'s_picture',name:'Picture',},{id:'s_quotes_carousel',name:'Quotes',},{id:'s_call_to_action',name:'Call to Action',},];wTourUtils.registerThemeHomepageTour('homepage',[wTourUtils.dragNDrop(snippets[0]),wTourUtils.clickOnText(snippets[0],'h1'),wTourUtils.goBackToBlocks(),wTourUtils.dragNDrop(snippets[1]),wTourUtils.dragNDrop(snippets[2]),wTourUtils.dragNDrop(snippets[3]),wTourUtils.dragNDrop(snippets[4]),wTourUtils.dragNDrop(snippets[5]),wTourUtils.clickOnSnippet(snippets[5],'top'),wTourUtils.changeBackgroundColor(),wTourUtils.clickOnSave(),]);});;

/* /website/static/src/js/tours/tour_utils.js defined in bundle 'website.assets_editor' */
odoo.define("website.tour_utils",function(require){"use strict";const core=require("web.core");const _t=core._t;var tour=require("web_tour.tour");function addMedia(position="right"){return{trigger:`.modal-content footer .btn-primary`,content:_t("<b>Add</b> the selected image."),position:position,run:"click",};}
function changeBackground(snippet,position="bottom"){return{trigger:".o_we_customize_panel .o_we_edit_image",content:_t("<b>Customize</b> any block through this menu. Try to change the background image of this block."),position:position,run:"click",};}
function changeBackgroundColor(position="bottom"){return{trigger:".o_we_customize_panel .o_we_color_preview",content:_t("<b>Customize</b> any block through this menu. Try to change the background color of this block."),position:position,run:"click",};}
function selectColorPalette(position="left"){return{trigger:".o_we_customize_panel .o_we_so_color_palette we-selection-items",alt_trigger:".o_we_customize_panel .o_we_color_preview",content:_t(`<b>Select</b> a Color Palette.`),position:position,run:'click',location:position==='left'?'#oe_snippets':undefined,};}
function changeColumnSize(position="right"){return{trigger:`.oe_overlay.ui-draggable.o_we_overlay_sticky.oe_active .o_handle.e`,content:_t("<b>Slide</b> this button to change the column size."),position:position,};}
function changeIcon(snippet,index=0,position="bottom"){return{trigger:`#wrapwrap .${snippet.id} i:eq(${index})`,content:_t("<b>Double click on an icon</b> to change it with one of your choice."),position:position,run:"dblclick",};}
function changeImage(snippet,position="bottom"){return{trigger:`#wrapwrap .${snippet.id} img`,content:_t("<b>Double click on an image</b> to change it with one of your choice."),position:position,run:"dblclick",};}
function changeOption(optionName,weName='',optionTooltipLabel='',position="bottom"){const option_block=`we-customizeblock-option[class='snippet-option-${optionName}']`
return{trigger:`${option_block} ${weName}, ${option_block} [title='${weName}']`,content:_.str.sprintf(_t("<b>Click</b> on this option to change the %s of the block."),optionTooltipLabel),position:position,run:"click",};}
function selectNested(trigger,optionName,alt_trigger=null,optionTooltipLabel='',position="top"){const option_block=`we-customizeblock-option[class='snippet-option-${optionName}']`;return{trigger:trigger,content:_.str.sprintf(_t("<b>Select</b> a %s."),optionTooltipLabel),alt_trigger:alt_trigger==null?undefined:`${option_block} ${alt_trigger}`,position:position,run:'click',location:position==='left'?'#oe_snippets':undefined,};}
function changePaddingSize(direction){let paddingDirection="n";let position="top";if(direction==="bottom"){paddingDirection="s";position="bottom";}
return{trigger:`.oe_overlay.ui-draggable.o_we_overlay_sticky.oe_active .o_handle.${paddingDirection}`,content:_.str.sprintf(_t("<b>Slide</b> this button to change the %s padding"),direction),consumeEvent:'mousedown',position:position,};}
function clickOnEdit(position="bottom"){return{trigger:"a[data-action=edit]",content:_t("<b>Click Edit</b> to start designing your homepage."),extra_trigger:".homepage",position:position,};}
function clickOnEditAndWaitEditMode(position="bottom"){return[{content:_t("<b>Click Edit</b> to start designing your homepage."),trigger:"a[data-action=edit]",position:position,},{content:"Check that we are in edit mode",trigger:'#oe_snippets.o_loaded',run:()=>null,}];}
function clickOnSnippet(snippet,position="bottom"){return{trigger:`#wrapwrap .${snippet.id}`,content:_t("<b>Click on a snippet</b> to access its options menu."),position:position,run:"click",};}
function clickOnSave(position="bottom"){return{trigger:"button[data-action=save]",in_modal:false,content:_t("Good job! It's time to <b>Save</b> your work."),position:position,};}
function clickOnText(snippet,element,position="bottom"){return{trigger:`#wrapwrap .${snippet.id} ${element}`,content:_t("<b>Click on a text</b> to start editing it."),position:position,run:"text",consumeEvent:"input",};}
function dragNDrop(snippet,position="bottom"){return{trigger:`#oe_snippets .oe_snippet[name="${snippet.name}"] .oe_snippet_thumbnail:not(.o_we_already_dragging)`,extra_trigger:"body.editor_enable.editor_has_snippets",moveTrigger:'.oe_drop_zone',content:_.str.sprintf(_t("Drag the <b>%s</b> building block and drop it at the bottom of the page."),snippet.name),position:position,run:"drag_and_drop #wrapwrap > footer",};}
function goBackToBlocks(position="bottom"){return{trigger:'.o_we_add_snippet_btn',content:_t("Click here to go back to block tab."),position:position,run:"click",};}
function goToOptions(position="bottom"){return{trigger:'.o_we_customize_theme_btn',content:_t("Go to the Options tab"),position:position,run:"click",};}
function selectHeader(position="bottom"){return{trigger:`header#top`,content:_t(`<b>Click</b> on this header to configure it.`),position:position,run:"click",};}
function selectSnippetColumn(snippet,index=0,position="bottom"){return{trigger:`#wrapwrap .${snippet.id} .row div[class*="col-lg-"]:eq(${index})`,content:_t("<b>Click</b> on this column to access its options."),position:position,run:"click",};}
function prepend_trigger(steps,prepend_text=''){for(const step of steps){if(!step.noPrepend&&prepend_text){step.trigger=prepend_text+step.trigger;}}
return steps;}
function registerThemeHomepageTour(name,steps){tour.register(name,{url:"/?enable_editor=1",sequence:1010,saveAs:"homepage",},prepend_trigger(steps,"html[data-view-xmlid='website.homepage'] "));}
return{addMedia,changeBackground,changeBackgroundColor,changeColumnSize,changeIcon,changeImage,changeOption,changePaddingSize,clickOnEdit,clickOnEditAndWaitEditMode,clickOnSave,clickOnSnippet,clickOnText,dragNDrop,goBackToBlocks,goToOptions,selectColorPalette,selectHeader,selectNested,selectSnippetColumn,registerThemeHomepageTour,};});;

/* /website/static/src/js/widgets/ace.js defined in bundle 'website.assets_editor' */
odoo.define("website.ace",function(require){"use strict";var AceEditor=require('web_editor.ace');var WebsiteAceEditor=AceEditor.extend({hash:'#advanced-view-editor',do_hide:function(){this._super.apply(this,arguments);window.location.hash="";},_displayResource:function(){this._super.apply(this,arguments);this._updateHash();},_saveResources:function(){return this._super.apply(this,arguments).then((function(){var defs=[];if(this.currentType==='xml'){var selectedView=_.findWhere(this.views,{id:this._getSelectedResource()});var context;this.trigger_up('context_get',{callback:function(ctx){context=ctx;},});defs.push(this._rpc({model:'ir.ui.view',method:'search_read',fields:['id'],domain:[['key','=',selectedView.key],['website_id','=',context.website_id]],}).then((function(view){if(view[0]){this._updateHash(view[0].id);}}).bind(this)));}
return Promise.all(defs).then((function(){window.location.reload();return new Promise(function(){});}));}).bind(this));},_switchType(type){const ret=this._super(...arguments);if(type==='scss'){this._displayResource('/website/static/src/scss/user_custom_rules.scss');}
return ret;},_resetResource:function(){return this._super.apply(this,arguments).then((function(){window.location.reload();return new Promise(function(){});}).bind(this));},_updateHash:function(resID){window.location.hash=this.hash+"?res="+(resID||this._getSelectedResource());},});return WebsiteAceEditor;});;

/* /website_links/static/src/js/website_links_menu.js defined in bundle 'website.assets_editor' */
odoo.define('website_links.website_links_menu',function(require){'use strict';var publicWidget=require('web.public.widget');var websiteNavbarData=require('website.navbar');var WebsiteLinksMenu=publicWidget.Widget.extend({start:function(){this.$el.attr('href','/r?u='+encodeURIComponent(window.location.href));return this._super.apply(this,arguments);},});websiteNavbarData.websiteNavbarRegistry.add(WebsiteLinksMenu,'#o_website_links_share_page');});;

/* /website_form/static/src/snippets/s_website_form/options.js defined in bundle 'website.assets_editor' */
odoo.define('website_form_editor',function(require){'use strict';const core=require('web.core');const FormEditorRegistry=require('website_form.form_editor_registry');const options=require('web_editor.snippets.options');const qweb=core.qweb;const _t=core._t;const FormEditor=options.Class.extend({xmlDependencies:['/website_form/static/src/xml/website_form_editor.xml','/google_recaptcha/static/src/xml/recaptcha.xml',],_fetchFieldRecords:async function(field){field.required=field.required?1:null;if(field.records){return field.records;}
if(field.type==='selection'){field.records=field.selection.map(el=>({id:el[0],display_name:el[1],}));}else if(field.relation&&field.relation!=='ir.attachment'){field.records=await this._rpc({model:field.relation,method:'search_read',args:[field.domain,['display_name']],});}
return field.records;},_generateUniqueID(){return`o${Math.random().toString(36).substring(2, 15)}`;},_getCustomField:function(type,name){return{name:name,string:name,custom:true,type:type,records:[{id:_t('Option 1'),display_name:_t('Option 1'),},{id:_t('Option 2'),display_name:_t('Option 2'),},{id:_t('Option 3'),display_name:_t('Option 3'),}],};},_getDefaultFormat:function(){return{labelWidth:this.$target[0].querySelector('.s_website_form_label').style.width,labelPosition:'left',multiPosition:'horizontal',requiredMark:this._isRequiredMark(),optionalMark:this._isOptionalMark(),mark:this._getMark(),};},_getMark:function(){return this.$target[0].dataset.mark;},_isOptionalMark:function(){return this.$target[0].classList.contains('o_mark_optional');},_isRequiredMark:function(){return this.$target[0].classList.contains('o_mark_required');},_renderField:function(field){field.id=this._generateUniqueID();const template=document.createElement('template');template.innerHTML=qweb.render("website_form.field_"+field.type,{field:field}).trim();return template.content.firstElementChild;},});const FieldEditor=FormEditor.extend({init:function(){this._super.apply(this,arguments);this.formEl=this.$target[0].closest('form');},_getActiveField:function(){let field;const labelText=this.$target.find('.s_website_form_label_content').text();if(this._isFieldCustom()){field=this._getCustomField(this.$target[0].dataset.type,labelText);}else{field=Object.assign({},this.fields[this._getFieldName()]);field.string=labelText;field.type=this._getFieldType();}
field.records=this._getListItems();this._setActiveProperties(field);return field;},_getFieldFormat:function(){let requiredMark,optionalMark;const mark=this.$target[0].querySelector('.s_website_form_mark');if(mark){requiredMark=this._isFieldRequired();optionalMark=!requiredMark;}
const multipleInput=this._getMultipleInputs();const format={labelPosition:this._getLabelPosition(),labelWidth:this.$target[0].querySelector('.s_website_form_label').style.width,multiPosition:multipleInput&&multipleInput.dataset.display||'horizontal',col:[...this.$target[0].classList].filter(el=>el.match(/^col-/g)).join(' '),requiredMark:requiredMark,optionalMark:optionalMark,mark:mark&&mark.textContent,};return format;},_getFieldName:function(){const multipleName=this.$target[0].querySelector('.s_website_form_multiple');return multipleName?multipleName.dataset.name:this.$target[0].querySelector('.s_website_form_input').name;},_getFieldType:function(){return this.$target[0].dataset.type;},_getLabelPosition:function(){const label=this.$target[0].querySelector('.s_website_form_label');if(this.$target[0].querySelector('.row:not(.s_website_form_multiple)')){return label.classList.contains('text-right')?'right':'left';}else{return label.classList.contains('d-none')?'none':'top';}},_getMultipleInputs:function(){return this.$target[0].querySelector('.s_website_form_multiple');},_getPlaceholder:function(){const input=this._getPlaceholderInput();return input?input.placeholder:'';},_getPlaceholderInput:function(){return this.$target[0].querySelector('input[type="text"], input[type="email"], input[type="number"], input[type="tel"], input[type="url"], textarea');},_isFieldCustom:function(){return!!this.$target[0].classList.contains('s_website_form_custom');},_isFieldRequired:function(){const classList=this.$target[0].classList;return classList.contains('s_website_form_required')||classList.contains('s_website_form_model_required');},_setActiveProperties(field){const classList=this.$target[0].classList;const textarea=this.$target[0].querySelector('textarea');field.placeholder=this._getPlaceholder();field.rows=textarea&&textarea.rows;field.required=classList.contains('s_website_form_required');field.modelRequired=classList.contains('s_website_form_model_required');field.hidden=classList.contains('s_website_form_field_hidden');field.formatInfo=this._getFieldFormat();},_setPlaceholder:function(value){const input=this._getPlaceholderInput();if(input){input.placeholder=value;}},});options.registry.WebsiteFormEditor=FormEditor.extend({events:_.extend({},options.Class.prototype.events||{},{'click .toggle-edit-message':'_onToggleEndMessageClick',}),willStart:async function(){const _super=this._super.bind(this);this.modelCantChange=this.$target.attr('hide-change-model')!==undefined;if(this.modelCantChange){return _super(...arguments);}
this.models=await this._rpc({model:"ir.model",method:"search_read",args:[[['website_form_access','=',true]],['id','model','name','website_form_label','website_form_key']],});const targetModelName=this.$target[0].dataset.model_name||'mail.mail';this.activeForm=_.findWhere(this.models,{model:targetModelName});this.selectActionEl=document.createElement('we-select');this.selectActionEl.setAttribute('string','Action');this.selectActionEl.dataset.noPreview='true';this.models.forEach(el=>{const option=document.createElement('we-button');option.textContent=el.website_form_label;option.dataset.selectAction=el.id;this.selectActionEl.append(option);});return _super(...arguments);},start:function(){const proms=[this._super(...arguments)];this.$target.attr('contentEditable',false);this.$target.find('.s_website_form_send, .s_website_form_recaptcha').attr('contentEditable',true);this.$message=this.$target.parent().find('.s_website_form_end_message');this.showEndMessage=false;if(!this.$target[0].dataset.model_name){proms.push(this._applyFormModel());}
return Promise.all(proms);},cleanForSave:function(){const model=this.$target[0].dataset.model_name;if(model){const fields=[...this.$target[0].querySelectorAll('.s_website_form_field:not(.s_website_form_custom) .s_website_form_input')].map(el=>el.name);if(fields.length){this._rpc({model:'ir.model.fields',method:'formbuilder_whitelist',args:[model,_.uniq(fields)],});}}
if(this.$message.length){this.$target.removeClass('d-none');this.$message.addClass("d-none");}
this.$target.find('input[name],textarea[name]').each(function(){var original=$(this).data('website_form_original_default_value');if(original!==undefined&&$(this).val()===original){$(this).val('').removeAttr('value');}});},updateUI:async function(){if(this.rerender){this.rerender=false;await this._rerenderXML();return;}
await this._super.apply(this,arguments);this.updateUIEndMessage();},updateUIEndMessage:function(){this.$target.toggleClass("d-none",this.showEndMessage);this.$message.toggleClass("d-none",!this.showEndMessage);this.$el.find(".toggle-edit-message").toggleClass('text-primary',this.showEndMessage);},notify:function(name,data){this._super(...arguments);if(name==='field_mark'){this._setLabelsMark();}else if(name==='add_field'){const field=this._getCustomField('char','Custom Text');field.formatInfo=data.formatInfo;field.formatInfo.requiredMark=this._isRequiredMark();field.formatInfo.optionalMark=this._isOptionalMark();field.formatInfo.mark=this._getMark();const fieldEl=this._renderField(field);data.$target.after(fieldEl);this.trigger_up('activate_snippet',{$snippet:$(fieldEl),});}},addActionField:function(previewMode,value,params){const fieldName=params.fieldName;if(params.isSelect==='true'){value=parseInt(value);}
this._addHiddenField(value,fieldName);},onSuccess:function(previewMode,value,params){this.$target[0].dataset.successMode=value;if(value==='message'){if(!this.$message.length){this.$message=$(qweb.render('website_form.s_website_form_end_message'));}
this.$target.after(this.$message);}else{this.showEndMessage=false;this.$message.remove();}},selectAction:async function(previewMode,value,params){if(this.modelCantChange){return;}
await this._applyFormModel(parseInt(value));this.rerender=true;},selectClass:function(previewMode,value,params){this._super(...arguments);if(params.name==='field_mark_select'){this._setLabelsMark();}},setMark:function(previewMode,value,params){this.$target[0].dataset.mark=value.trim();this._setLabelsMark();},toggleRecaptchaLegal:function(previewMode,value,params){const recaptchaLegalEl=this.$target[0].querySelector('.s_website_form_recaptcha');if(recaptchaLegalEl){recaptchaLegalEl.remove();}else{const template=document.createElement('template');const labelWidth=this.$target[0].querySelector('.s_website_form_label').style.width;template.innerHTML=qweb.render("webite_form.s_website_form_recaptcha_legal",{labelWidth:labelWidth});const legal=template.content.firstElementChild;legal.setAttribute('contentEditable',true);this.$target.find('.s_website_form_submit').before(legal);}},_computeWidgetState:function(methodName,params){switch(methodName){case'selectAction':return this.activeForm.id;case'addActionField':{const value=this.$target.find(`.s_website_form_dnone input[name="${params.fieldName}"]`).val();if(value){return value;}else{return params.isSelect?'0':'';}}
case'onSuccess':return this.$target[0].dataset.successMode;case'setMark':return this._getMark();case'toggleRecaptchaLegal':return!this.$target[0].querySelector('.s_website_form_recaptcha')||'';}
return this._super(...arguments);},_renderCustomXML:function(uiFragment){if(this.modelCantChange){return;}
const firstOption=uiFragment.childNodes[0];uiFragment.insertBefore(this.selectActionEl.cloneNode(true),firstOption);const formKey=this.activeForm.website_form_key;const formInfo=FormEditorRegistry.get(formKey);if(!formInfo||!formInfo.fields){return;}
const proms=formInfo.fields.map(field=>this._fetchFieldRecords(field));return Promise.all(proms).then(()=>{formInfo.fields.forEach(field=>{let option;switch(field.type){case'many2one':option=this._buildSelect(field);break;case'char':option=this._buildInput(field);break;}
if(field.required){const currentValue=this.$target.find(`.s_website_form_dnone input[name="${field.name}"]`).val();const defaultValue=field.defaultValue||field.records[0].id;this._addHiddenField(currentValue||defaultValue,field.name);}
uiFragment.insertBefore(option,firstOption);});});},_addHiddenField:function(value,fieldName){this.$target.find(`.s_website_form_dnone:has(input[name="${fieldName}"])`).remove();if(value){const hiddenField=qweb.render('website_form.field_hidden',{field:{name:fieldName,value:value,},});this.$target.find('.s_website_form_submit').before(hiddenField);}},_buildInput:function(field){const inputEl=document.createElement('we-input');inputEl.dataset.noPreview='true';inputEl.dataset.fieldName=field.name;inputEl.dataset.addActionField='';inputEl.setAttribute('string',field.string);inputEl.classList.add('o_we_large_input');return inputEl;},_buildSelect:function(field){const selectEl=document.createElement('we-select');selectEl.dataset.noPreview='true';selectEl.dataset.fieldName=field.name;selectEl.dataset.isSelect='true';selectEl.setAttribute('string',field.string);if(!field.required){const noneButton=document.createElement('we-button');noneButton.textContent='None';noneButton.dataset.addActionField=0;selectEl.append(noneButton);}
field.records.forEach(el=>{const button=document.createElement('we-button');button.textContent=el.display_name;button.dataset.addActionField=el.id;selectEl.append(button);});return selectEl;},_applyFormModel:async function(modelId){let oldFormInfo;if(modelId){const oldFormKey=this.activeForm.website_form_key;if(oldFormKey){oldFormInfo=FormEditorRegistry.get(oldFormKey);}
this.$target.find('.s_website_form_field').remove();this.activeForm=_.findWhere(this.models,{id:modelId});}
const formKey=this.activeForm.website_form_key;const formInfo=FormEditorRegistry.get(formKey);if(!this.$target[0].dataset.successMode){this.$target[0].dataset.successMode='redirect';}
if(this.$target[0].dataset.successMode==='redirect'){const currentSuccessPage=this.$target[0].dataset.successPage;if(formInfo&&formInfo.successPage){this.$target[0].dataset.successPage=formInfo.successPage;}else if(!oldFormInfo||(oldFormInfo!==formInfo&&oldFormInfo.successPage&&currentSuccessPage===oldFormInfo.successPage)){this.$target[0].dataset.successPage='/contactus-thank-you';}}
this.$target[0].dataset.model_name=this.activeForm.model;if(formInfo){const formatInfo=this._getDefaultFormat();await formInfo.formFields.forEach(async field=>{field.formatInfo=formatInfo;await this._fetchFieldRecords(field);this.$target.find('.s_website_form_submit, .s_website_form_recaptcha').first().before(this._renderField(field));});}},_setLabelsMark:function(){this.$target[0].querySelectorAll('.s_website_form_mark').forEach(el=>el.remove());const mark=this._getMark();if(!mark){return;}
let fieldsToMark=[];const requiredSelector='.s_website_form_model_required, .s_website_form_required';const fields=Array.from(this.$target[0].querySelectorAll('.s_website_form_field'));if(this._isRequiredMark()){fieldsToMark=fields.filter(el=>el.matches(requiredSelector));}else if(this._isOptionalMark()){fieldsToMark=fields.filter(el=>!el.matches(requiredSelector));}
fieldsToMark.forEach(field=>{let span=document.createElement('span');span.classList.add('s_website_form_mark');span.textContent=` ${mark}`;field.querySelector('.s_website_form_label').appendChild(span);});},_onToggleEndMessageClick:function(){this.showEndMessage=!this.showEndMessage;this.updateUIEndMessage();this.trigger_up('activate_snippet',{$snippet:this.showEndMessage?this.$message:this.$target,});},});const authorizedFieldsCache={};options.registry.WebsiteFieldEditor=FieldEditor.extend({events:_.extend({},FieldEditor.prototype.events,{'click we-button.o_we_select_remove_option':'_onRemoveItemClick','click we-button.o_we_list_add_optional':'_onAddCustomItemClick','click we-button.o_we_list_add_existing':'_onAddExistingItemClick','click we-list we-select':'_onAddItemSelectClick','input we-list input':'_onListItemInput',}),init:function(){this._super.apply(this,arguments);this.rerender=true;},willStart:async function(){const _super=this._super.bind(this);const model=this.formEl.dataset.model_name;let getFields;if(model in authorizedFieldsCache){getFields=authorizedFieldsCache[model];}else{getFields=this._rpc({model:"ir.model",method:"get_authorized_fields",args:[model],});authorizedFieldsCache[model]=getFields;}
this.existingFields=await getFields.then(fields=>{this.fields=_.each(fields,function(field,fieldName){field.name=fieldName;field.domain=field.domain||[];});return Object.keys(fields).map(key=>{const field=fields[key];const button=document.createElement('we-button');button.textContent=field.string;button.dataset.existingField=field.name;return button;}).sort((a,b)=>(a.textContent>b.textContent)?1:(a.textContent<b.textContent)?-1:0);});return _super(...arguments);},cleanForSave:function(){this.$target[0].querySelectorAll('#editable_select').forEach(el=>el.remove());const select=this._getSelect();if(select&&this.listTable){select.style.display='';select.innerHTML='';this.listTable.querySelectorAll('input').forEach(el=>{const option=document.createElement('option');option.textContent=el.value;option.value=this._isFieldCustom()?el.value:el.name;select.appendChild(option);});}},updateUI:async function(){if(this.rerender){const select=this._getSelect();if(select&&!this.$target[0].querySelector('#editable_select')){select.style.display='none';const editableSelect=document.createElement('div');editableSelect.id='editable_select';editableSelect.classList='form-control s_website_form_input';select.parentElement.appendChild(editableSelect);}
this.rerender=false;await this._rerenderXML().then(()=>this._renderList());return;}
await this._super.apply(this,arguments);},onFocus:function(){this.rerender=true;},onClone(){this._renderList();const field=this._getActiveField();const fieldEl=this._renderField(field);this._replaceFieldElement(fieldEl);},customField:async function(previewMode,value,params){if(!value){return;}
const name=this.el.querySelector(`[data-custom-field="${value}"]`).textContent;const field=this._getCustomField(value,`Custom ${name}`);this._setActiveProperties(field);await this._replaceField(field);this.rerender=true;},existingField:async function(previewMode,value,params){if(!value){return;}
const field=Object.assign({},this.fields[value]);this._setActiveProperties(field);await this._replaceField(field);this.rerender=true;},setLabelText:function(previewMode,value,params){this.$target.find('.s_website_form_label_content').text(value);if(this._isFieldCustom()){const multiple=this.$target[0].querySelector('.s_website_form_multiple');if(multiple){multiple.dataset.name=value;}
this.$target[0].querySelectorAll('.s_website_form_input').forEach(el=>el.name=value);}},setPlaceholder:function(previewMode,value,params){this._setPlaceholder(value);},selectLabelPosition:async function(previewMode,value,params){const field=this._getActiveField();field.formatInfo.labelPosition=value;await this._replaceField(field);this.rerender=true;},selectType:async function(previewMode,value,params){const field=this._getActiveField();field.type=value;await this._replaceField(field);},multiCheckboxDisplay:function(previewMode,value,params){const target=this._getMultipleInputs();target.querySelectorAll('.checkbox, .radio').forEach(el=>{if(value==='horizontal'){el.classList.add('col-lg-4','col-md-6');}else{el.classList.remove('col-lg-4','col-md-6');}});target.dataset.display=value;},toggleRequired:function(previewMode,value,params){const isRequired=this.$target[0].classList.contains(params.activeValue);this.$target[0].classList.toggle(params.activeValue,!isRequired);this.$target[0].querySelectorAll('input, select, textarea').forEach(el=>el.toggleAttribute('required',!isRequired));this.trigger_up('option_update',{optionName:'WebsiteFormEditor',name:'field_mark',});},_computeWidgetState:function(methodName,params){switch(methodName){case'customField':return this._isFieldCustom()?this._getFieldType():'';case'existingField':return this._isFieldCustom()?'':this._getFieldName();case'setLabelText':return this.$target.find('.s_website_form_label_content').text();case'setPlaceholder':return this._getPlaceholder();case'selectLabelPosition':return this._getLabelPosition();case'selectType':return this._getFieldType();case'multiCheckboxDisplay':{const target=this._getMultipleInputs();return target?target.dataset.display:'';}
case'toggleRequired':return this.$target[0].classList.contains(params.activeValue)?params.activeValue:'false';}
return this._super(...arguments);},_computeWidgetVisibility:function(widgetName,params){switch(widgetName){case'char_input_type_opt':return!this.$target[0].classList.contains('s_website_form_custom')&&['char','email','tel','url'].includes(this.$target[0].dataset.type);case'multi_check_display_opt':return!!this._getMultipleInputs();case'placeholder_opt':return!!this._getPlaceholderInput();case'required_opt':case'hidden_opt':case'type_opt':return!this.$target[0].classList.contains('s_website_form_model_required');}
return this._super(...arguments);},_renderCustomXML:function(uiFragment){const selectEl=uiFragment.querySelector('we-select[data-name="type_opt"]');const currentFieldName=this._getFieldName();const fieldsInForm=Array.from(this.formEl.querySelectorAll('.s_website_form_field:not(.s_website_form_custom) .s_website_form_input')).map(el=>el.name).filter(el=>el!==currentFieldName);const availableFields=this.existingFields.filter(el=>!fieldsInForm.includes(el.dataset.existingField));if(availableFields.length){const title=document.createElement('we-title');title.textContent='Existing fields';availableFields.unshift(title);availableFields.forEach(option=>selectEl.append(option.cloneNode(true)));}},_replaceField:async function(field){await this._fetchFieldRecords(field);const fieldEl=this._renderField(field);this._replaceFieldElement(fieldEl);},_replaceFieldElement(fieldEl){[...this.$target[0].childNodes].forEach(node=>node.remove());[...fieldEl.childNodes].forEach(node=>this.$target[0].appendChild(node));[...fieldEl.attributes].forEach(el=>this.$target[0].removeAttribute(el.nodeName));[...fieldEl.attributes].forEach(el=>this.$target[0].setAttribute(el.nodeName,el.nodeValue));},_renderList:function(){let addItemButton,addItemTitle,listTitle;const select=this._getSelect();const multipleInputs=this._getMultipleInputs();this.listTable=document.createElement('table');const isCustomField=this._isFieldCustom();if(select){listTitle='Options List';addItemTitle='Add new Option';select.querySelectorAll('option').forEach(opt=>{this._addItemToTable(opt.value,opt.textContent.trim());});this._renderListItems();}else if(multipleInputs){listTitle=multipleInputs.querySelector('.radio')?'Radio List':'Checkbox List';addItemTitle='Add new Checkbox';multipleInputs.querySelectorAll('.checkbox, .radio').forEach(opt=>{this._addItemToTable(opt.querySelector('input').value,opt.querySelector('.s_website_form_check_label').textContent.trim());});}else{return;}
if(isCustomField){addItemButton=document.createElement('we-button');addItemButton.textContent=addItemTitle;addItemButton.classList.add('o_we_list_add_optional');addItemButton.dataset.noPreview='true';}else{addItemButton=document.createElement('we-select');addItemButton.classList.add('o_we_user_value_widget');const togglerEl=document.createElement('we-toggler');togglerEl.textContent=addItemTitle;addItemButton.appendChild(togglerEl);const selectMenuEl=document.createElement('we-selection-items');addItemButton.appendChild(selectMenuEl);this._loadListDropdown(selectMenuEl);}
const selectInputEl=document.createElement('we-list');const title=document.createElement('we-title');title.textContent=listTitle;selectInputEl.appendChild(title);const tableWrapper=document.createElement('div');tableWrapper.classList.add('oe_we_table_wraper');tableWrapper.appendChild(this.listTable);selectInputEl.appendChild(tableWrapper);selectInputEl.appendChild(addItemButton);this.el.insertBefore(selectInputEl,this.el.querySelector('[data-set-placeholder]'));this._makeListItemsSortable();},_loadListDropdown:function(selectMenu){selectMenu=selectMenu||this.el.querySelector('we-list we-selection-items');if(selectMenu){selectMenu.innerHTML='';const field=Object.assign({},this.fields[this._getFieldName()]);this._fetchFieldRecords(field).then(()=>{let buttonItems;const optionIds=Array.from(this.listTable.querySelectorAll('input')).map(opt=>{return field.type==='selection'?opt.name:parseInt(opt.name);});const availableRecords=(field.records||[]).filter(el=>!optionIds.includes(el.id));if(availableRecords.length){buttonItems=availableRecords.map(el=>{const option=document.createElement('we-button');option.classList.add('o_we_list_add_existing');option.dataset.addOption=el.id;option.dataset.noPreview='true';option.textContent=el.display_name;return option;});}else{const title=document.createElement('we-title');title.textContent='No more records';buttonItems=[title];}
buttonItems.forEach(button=>selectMenu.appendChild(button));});}},_makeListItemsSortable:function(){$(this.listTable).sortable({axis:'y',handle:'.o_we_drag_handle',items:'tr',cursor:'move',opacity:0.6,stop:(event,ui)=>{this._renderListItems();},});},_addItemToTable:function(id,text){const isCustomField=this._isFieldCustom();const draggableEl=document.createElement('we-button');draggableEl.classList.add('o_we_drag_handle','o_we_link','fa','fa-fw','fa-arrows');draggableEl.dataset.noPreview='true';const inputEl=document.createElement('input');inputEl.type='text';if(text){inputEl.value=text;}
if(!isCustomField&&id){inputEl.name=id;}
inputEl.disabled=!isCustomField;const trEl=document.createElement('tr');const buttonEl=document.createElement('we-button');buttonEl.classList.add('o_we_select_remove_option','o_we_link','o_we_text_danger','fa','fa-fw','fa-minus');buttonEl.dataset.removeOption=id;buttonEl.dataset.noPreview='true';const draggableTdEl=document.createElement('td');const inputTdEl=document.createElement('td');const buttonTdEl=document.createElement('td');draggableTdEl.appendChild(draggableEl);trEl.appendChild(draggableTdEl);inputTdEl.appendChild(inputEl);trEl.appendChild(inputTdEl);buttonTdEl.appendChild(buttonEl);trEl.appendChild(buttonTdEl);this.listTable.appendChild(trEl);if(isCustomField){inputEl.focus();}},_renderListItems:function(){const multiInputsWrap=this._getMultipleInputs();const selectWrap=this.$target[0].querySelector('#editable_select');const isRequiredField=this._isFieldRequired();const name=this._getFieldName();if(multiInputsWrap){const type=multiInputsWrap.querySelector('.radio')?'radio':'checkbox';multiInputsWrap.innerHTML='';const params={field:{name:name,id:this._generateUniqueID(),required:isRequiredField,formatInfo:{multiPosition:multiInputsWrap.dataset.display,}}};this._getListItems().forEach((record,idx)=>{params.record_index=idx;params.record=record;const template=document.createElement('template');template.innerHTML=qweb.render(`website_form.${type}`,params);multiInputsWrap.appendChild(template.content.firstElementChild);});}else if(selectWrap){selectWrap.innerHTML='';this.listTable.querySelectorAll('input').forEach(el=>{const option=document.createElement('div');option.id=(el.name||el.value);option.classList.add('s_website_form_select_item');option.textContent=el.value;selectWrap.appendChild(option);});}},_getListItems:function(){if(!this.listTable){return null;}
const isCustomField=this._isFieldCustom();const records=[];this.listTable.querySelectorAll('input').forEach(el=>{const id=isCustomField?el.value:el.name;records.push({id:id,display_name:el.value,});});return records;},_getSelect:function(){return this.$target[0].querySelector('select');},_onRemoveItemClick:function(ev){ev.target.closest('tr').remove();this._loadListDropdown();this._renderListItems();},_onAddCustomItemClick:function(ev){this._addItemToTable();this._makeListItemsSortable();this._renderListItems();},_onAddExistingItemClick:function(ev){const value=ev.currentTarget.dataset.addOption;this._addItemToTable(value,ev.currentTarget.textContent);this._makeListItemsSortable();this._loadListDropdown();this._renderListItems();},_onAddItemSelectClick:function(ev){ev.currentTarget.querySelector('we-toggler').classList.toggle('active');},_onListItemInput:function(){this._renderListItems();},});options.registry.AddFieldForm=FormEditor.extend({isTopOption:true,addField:async function(previewMode,value,params){const field=this._getCustomField('char','Custom Text');field.formatInfo=this._getDefaultFormat();const fieldEl=this._renderField(field);this.$target.find('.s_website_form_submit, .s_website_form_recaptcha').first().before(fieldEl);this.trigger_up('activate_snippet',{$snippet:$(fieldEl),});},});options.registry.AddField=FieldEditor.extend({isTopOption:true,addField:async function(previewMode,value,params){this.trigger_up('option_update',{optionName:'WebsiteFormEditor',name:'add_field',data:{formatInfo:this._getFieldFormat(),$target:this.$target,},});},});const DisableOverlayButtonOption=options.Class.extend({disableButton:function(buttonName,message){const className='oe_snippet_'+buttonName;this.$overlay.add(this.$overlay.data('$optionsSection')).on('click','.'+className,this.preventButton);const $button=this.$overlay.add(this.$overlay.data('$optionsSection')).find('.'+className);$button.attr('title',message).tooltip({delay:0});$button.removeClass(className);},preventButton:function(event){event.preventDefault();event.stopImmediatePropagation();}});options.registry.WebsiteFormFieldModel=DisableOverlayButtonOption.extend({start:function(){this.disableButton('clone',_t('You can\'t duplicate a model field.'));return this._super.apply(this,arguments);}});options.registry.WebsiteFormFieldRequired=DisableOverlayButtonOption.extend({start:function(){this.disableButton('remove',_t('You can\'t remove a field that is required by the model itself.'));return this._super.apply(this,arguments);}});options.registry.WebsiteFormSubmitRequired=DisableOverlayButtonOption.extend({start:function(){this.disableButton('remove',_t('You can\'t remove the submit button of the form'));this.disableButton('clone',_t('You can\'t duplicate the submit button of the form.'));return this._super.apply(this,arguments);}});});;

/* /website_form/static/src/js/website_form_editor_registry.js defined in bundle 'website.assets_editor' */
odoo.define('website_form.form_editor_registry',function(require){'use strict';var Registry=require('web.Registry');return new Registry();});odoo.define('website_form.send_mail_form',function(require){'use strict';var core=require('web.core');var FormEditorRegistry=require('website_form.form_editor_registry');var _t=core._t;FormEditorRegistry.add('send_mail',{formFields:[{type:'char',custom:true,required:true,name:'Your Name',},{type:'tel',custom:true,name:'Phone Number',},{type:'email',modelRequired:true,name:'email_from',string:'Your Email',},{type:'char',custom:true,name:'Your Company',},{type:'char',modelRequired:true,name:'subject',string:'Subject',},{type:'text',custom:true,required:true,name:'Your Question',}],fields:[{name:'email_to',type:'char',required:true,string:_t('Recipient Email'),defaultValue:'<EMAIL>',}],});});;

/* /website_form_project/static/src/js/website_form_project_editor.js defined in bundle 'website.assets_editor' */
odoo.define('website_form_project.form',function(require){'use strict';var core=require('web.core');var FormEditorRegistry=require('website_form.form_editor_registry');var _t=core._t;FormEditorRegistry.add('create_task',{formFields:[{type:'char',modelRequired:true,name:'name',string:'Task Title',},{type:'email',modelRequired:true,name:'email_from',string:'Your Email',},{type:'char',name:'description',string:'Description',}],fields:[{name:'project_id',type:'many2one',relation:'project.project',string:_t('Project'),}],});});;

/* /website_sale/static/src/js/website_sale.editor.js defined in bundle 'website.assets_editor' */
odoo.define('website_sale.add_product',function(require){'use strict';var core=require('web.core');var wUtils=require('website.utils');var WebsiteNewMenu=require('website.newMenu');var _t=core._t;WebsiteNewMenu.include({actions:_.extend({},WebsiteNewMenu.prototype.actions||{},{new_product:'_createNewProduct',}),_createNewProduct:function(){var self=this;return wUtils.prompt({id:"editor_new_product",window_title:_t("New Product"),input:_t("Name"),}).then(function(result){if(!result.val){return;}
return self._rpc({route:'/shop/add_product',params:{name:result.val,},}).then(function(url){window.location.href=url;return new Promise(function(){});});});},});});odoo.define('website_sale.editor',function(require){'use strict';var options=require('web_editor.snippets.options');var publicWidget=require('web.public.widget');const{Class:EditorMenuBar}=require('web_editor.editor');const{qweb}=require('web.core');EditorMenuBar.include({custom_events:Object.assign(EditorMenuBar.prototype.custom_events,{get_ribbons:'_onGetRibbons',get_ribbon_classes:'_onGetRibbonClasses',delete_ribbon:'_onDeleteRibbon',set_ribbon:'_onSetRibbon',set_product_ribbon:'_onSetProductRibbon',}),async willStart(){const _super=this._super.bind(this);let ribbons=[];if(this._isProductListPage()){ribbons=await this._rpc({model:'product.ribbon',method:'search_read',fields:['id','html','bg_color','text_color','html_class'],});}
this.ribbons=Object.fromEntries(ribbons.map(ribbon=>[ribbon.id,ribbon]));this.originalRibbons=Object.assign({},this.ribbons);this.productTemplatesRibbons=[];this.deletedRibbonClasses='';return _super(...arguments);},async save(){const _super=this._super.bind(this);await this._saveRibbons();return _super(...arguments);},async _saveRibbons(){if(!this._isProductListPage()){return;}
const originalIds=Object.keys(this.originalRibbons).map(id=>parseInt(id));const currentIds=Object.keys(this.ribbons).map(id=>parseInt(id));const ribbons=Object.values(this.ribbons);const created=ribbons.filter(ribbon=>!originalIds.includes(ribbon.id));const deletedIds=originalIds.filter(id=>!currentIds.includes(id));const modified=ribbons.filter(ribbon=>{if(created.includes(ribbon)){return false;}
const original=this.originalRibbons[ribbon.id];return Object.entries(ribbon).some(([key,value])=>value!==original[key]);});const proms=[];let createdRibbonIds;if(created.length>0){proms.push(this._rpc({method:'create',model:'product.ribbon',args:[created.map(ribbon=>{ribbon=Object.assign({},ribbon);delete ribbon.id;return ribbon;})],}).then(ids=>createdRibbonIds=ids));}
modified.forEach(ribbon=>proms.push(this._rpc({method:'write',model:'product.ribbon',args:[[ribbon.id],ribbon],})));if(deletedIds.length>0){proms.push(this._rpc({method:'unlink',model:'product.ribbon',args:[deletedIds],}));}
await Promise.all(proms);const localToServer=Object.assign(this.ribbons,Object.fromEntries(created.map((ribbon,index)=>[ribbon.id,{id:createdRibbonIds[index]}])),{'false':{id:false}},);const finalTemplateRibbons=this.productTemplatesRibbons.reduce((acc,{templateId,ribbonId})=>{acc[templateId]=ribbonId;return acc;},{});const ribbonTemplates=Object.entries(finalTemplateRibbons).reduce((acc,[templateId,ribbonId])=>{if(!acc[ribbonId]){acc[ribbonId]=[];}
acc[ribbonId].push(parseInt(templateId));return acc;},{});const setProductTemplateRibbons=Object.entries(ribbonTemplates).map(([ribbonId,templateIds])=>{const id=currentIds.includes(parseInt(ribbonId))?ribbonId:false;return[id,templateIds];}).map(([ribbonId,templateIds])=>this._rpc({method:'write',model:'product.template',args:[templateIds,{'website_ribbon_id':localToServer[ribbonId].id}],}));return Promise.all(setProductTemplateRibbons);},_isProductListPage(){return $('#products_grid').length!==0;},_onGetRibbons(ev){ev.data.callback(Object.assign({},this.ribbons));},_onGetRibbonClasses(ev){const classes=Object.values(this.ribbons).reduce((classes,ribbon)=>{return classes+` ${ribbon.html_class}`;},'')+this.deletedRibbonClasses;ev.data.callback(classes);},_onDeleteRibbon(ev){this.deletedRibbonClasses+=` ${this.ribbons[ev.data.id].html_class}`;delete this.ribbons[ev.data.id];},_onSetRibbon(ev){const{ribbon}=ev.data;const previousRibbon=this.ribbons[ribbon.id];if(previousRibbon){this.deletedRibbonClasses+=` ${previousRibbon.html_class}`;}
this.ribbons[ribbon.id]=ribbon;},_onSetProductRibbon(ev){const{templateId,ribbonId}=ev.data;this.productTemplatesRibbons.push({templateId,ribbonId});},});publicWidget.registry.websiteSaleCurrency=publicWidget.Widget.extend({selector:'.oe_website_sale',disabledInEditableMode:false,edit_events:{'click .oe_currency_value:o_editable':'_onCurrencyValueClick',},_onCurrencyValueClick:function(ev){$(ev.currentTarget).selectContent();},});function reload(){if(window.location.href.match(/\?enable_editor/)){window.location.reload();}else{window.location.href=window.location.href.replace(/\?(enable_editor=1&)?|#.*|$/,'?enable_editor=1&');}}
options.registry.WebsiteSaleGridLayout=options.Class.extend({start:function(){this.ppg=parseInt(this.$target.closest('[data-ppg]').data('ppg'));this.ppr=parseInt(this.$target.closest('[data-ppr]').data('ppr'));return this._super.apply(this,arguments);},onFocus:function(){var listLayoutEnabled=this.$target.closest('#products_grid').hasClass('o_wsale_layout_list');this.$el.filter('.o_wsale_ppr_submenu').toggleClass('d-none',listLayoutEnabled);},setPpg:function(previewMode,widgetValue,params){const PPG_LIMIT=10000;const ppg=parseInt(widgetValue);if(!ppg||ppg<1){return false;}
this.ppg=Math.min(ppg,PPG_LIMIT);return this._rpc({route:'/shop/change_ppg',params:{'ppg':this.ppg,},}).then(()=>reload());},setPpr:function(previewMode,widgetValue,params){this.ppr=parseInt(widgetValue);this._rpc({route:'/shop/change_ppr',params:{'ppr':this.ppr,},}).then(reload);},_computeWidgetState:function(methodName,params){switch(methodName){case'setPpg':{return this.ppg;}
case'setPpr':{return this.ppr;}}
return this._super(...arguments);},});options.registry.WebsiteSaleProductsItem=options.Class.extend({xmlDependencies:(options.Class.prototype.xmlDependencies||[]).concat(['/website_sale/static/src/xml/website_sale_utils.xml']),events:_.extend({},options.Class.prototype.events||{},{'mouseenter .o_wsale_soptions_menu_sizes table':'_onTableMouseEnter','mouseleave .o_wsale_soptions_menu_sizes table':'_onTableMouseLeave','mouseover .o_wsale_soptions_menu_sizes td':'_onTableItemMouseEnter','click .o_wsale_soptions_menu_sizes td':'_onTableItemClick',}),willStart:async function(){const _super=this._super.bind(this);this.ppr=this.$target.closest('[data-ppr]').data('ppr');this.productTemplateID=parseInt(this.$target.find('[data-oe-model="product.template"]').data('oe-id'));this.ribbons=await new Promise(resolve=>this.trigger_up('get_ribbons',{callback:resolve}));return _super(...arguments);},start:function(){this._resetRibbonDummy();return this._super(...arguments);},onFocus:function(){var listLayoutEnabled=this.$target.closest('#products_grid').hasClass('o_wsale_layout_list');this.$el.find('.o_wsale_soptions_menu_sizes').toggleClass('d-none',listLayoutEnabled);this.rerender=true;},onBlur:function(){this._resetRibbonDummy();this._toggleEditingUI(false);},selectStyle(previewMode,widgetValue,params){const proms=[this._super(...arguments)];if(params.cssProperty==='background-color'&&params.colorNames.includes(widgetValue)){proms.push(this.selectStyle(previewMode,'',{applyTo:'.o_wsale_ribbon_dummy',cssProperty:'color'}));}
return Promise.all(proms);},async setRibbon(previewMode,widgetValue,params){if(previewMode==='reset'){widgetValue=this.prevRibbonId;}else{this.prevRibbonId=this.$target[0].dataset.ribbonId;}
this.$target[0].dataset.ribbonId=widgetValue;this.trigger_up('set_product_ribbon',{templateId:this.productTemplateID,ribbonId:widgetValue||false,});const ribbon=this.ribbons[widgetValue]||{html:'',bg_color:'',text_color:'',html_class:''};const $ribbons=$(`[data-ribbon-id="${widgetValue}"] .o_ribbon:not(.o_wsale_ribbon_dummy)`);$ribbons.html(ribbon.html);let htmlClasses;this.trigger_up('get_ribbon_classes',{callback:classes=>htmlClasses=classes});$ribbons.removeClass(htmlClasses);$ribbons.addClass(ribbon.html_class||'');$ribbons.css('color',ribbon.text_color||'');$ribbons.css('background-color',ribbon.bg_color||'');if(!this.ribbons[widgetValue]){$(`[data-ribbon-id="${widgetValue}"]`).each((index,product)=>delete product.dataset.ribbonId);}
this._resetRibbonDummy();this._toggleEditingUI(false);},editRibbon(previewMode,widgetValue,params){this.saveMethod='modify';this._toggleEditingUI(true);},createRibbon(previewMode,widgetValue,params){this.saveMethod='create';this.setRibbon(false);this.$ribbon.html('Ribbon text');this.$ribbon.addClass('bg-primary o_ribbon_left');this._toggleEditingUI(true);this.isCreating=true;},async deleteRibbon(previewMode,widgetValue,params){if(this.isCreating){this.isCreating=false;this._resetRibbonDummy();return this._toggleEditingUI(false);}
const{ribbonId}=this.$target[0].dataset;this.trigger_up('delete_ribbon',{id:ribbonId});this.ribbons=await new Promise(resolve=>this.trigger_up('get_ribbons',{callback:resolve}));this.rerender=true;await this.setRibbon(false,ribbonId);},async saveRibbon(previewMode,widgetValue,params){const text=this.$ribbon.html().trim();if(!text){return;}
const ribbon={'html':text,'bg_color':this.$ribbon[0].style.backgroundColor,'text_color':this.$ribbon[0].style.color,'html_class':this.$ribbon.attr('class').split(' ').filter(c=>!['d-none','o_wsale_ribbon_dummy','o_ribbon'].includes(c)).join(' '),};ribbon.id=this.saveMethod==='modify'?parseInt(this.$target[0].dataset.ribbonId):Date.now();this.trigger_up('set_ribbon',{ribbon:ribbon});this.ribbons=await new Promise(resolve=>this.trigger_up('get_ribbons',{callback:resolve}));this.rerender=true;await this.setRibbon(false,ribbon.id);},setRibbonHtml(previewMode,widgetValue,params){this.$ribbon.html(widgetValue);},setRibbonMode(previewMode,widgetValue,params){this.$ribbon[0].className=this.$ribbon[0].className.replace(/o_(ribbon|tag)_(left|right)/,`o_${widgetValue}_$2`);},setRibbonPosition(previewMode,widgetValue,params){this.$ribbon[0].className=this.$ribbon[0].className.replace(/o_(ribbon|tag)_(left|right)/,`o_$1_${widgetValue}`);},changeSequence:function(previewMode,widgetValue,params){this._rpc({route:'/shop/change_sequence',params:{id:this.productTemplateID,sequence:widgetValue,},}).then(reload);},updateUI:async function(){await this._super.apply(this,arguments);var sizeX=parseInt(this.$target.attr('colspan')||1);var sizeY=parseInt(this.$target.attr('rowspan')||1);var $size=this.$el.find('.o_wsale_soptions_menu_sizes');$size.find('tr:nth-child(-n + '+sizeY+') td:nth-child(-n + '+sizeX+')').addClass('selected');$size.find('tr td:nth-child(n + '+parseInt(this.ppr+1)+')').hide();if(this.rerender){this.rerender=false;return this._rerenderXML();}},updateUIVisibility:async function(){const isEditing=this.$el.find('[data-name="ribbon_options"]').hasClass('d-none');await this._super(...arguments);this._toggleEditingUI(isEditing);},async _renderCustomXML(uiFragment){const $select=$(uiFragment.querySelector('.o_wsale_ribbon_select'));this.ribbons=await new Promise(resolve=>this.trigger_up('get_ribbons',{callback:resolve}));if(!this.$ribbon){this._resetRibbonDummy();}
const classes=this.$ribbon[0].className;this.$ribbon[0].className='';const defaultTextColor=window.getComputedStyle(this.$ribbon[0]).color;this.$ribbon[0].className=classes;Object.values(this.ribbons).forEach(ribbon=>{const colorClasses=ribbon.html_class.split(' ').filter(className=>!/^o_(ribbon|tag)_(left|right)$/.test(className)).join(' ');$select.append(qweb.render('website_sale.ribbonSelectItem',{ribbon,colorClasses,isTag:/o_tag_(left|right)/.test(ribbon.html_class),isLeft:/o_(tag|ribbon)_left/.test(ribbon.html_class),textColor:ribbon.text_color||(colorClasses?'currentColor':defaultTextColor),}));});},async _computeWidgetState(methodName,params){const classList=this.$ribbon[0].classList;switch(methodName){case'setRibbon':return this.$target.attr('data-ribbon-id')||'';case'setRibbonHtml':return this.$ribbon.html();case'setRibbonMode':{if(classList.contains('o_ribbon_left')||classList.contains('o_ribbon_right')){return'ribbon';}
return'tag';}
case'setRibbonPosition':{if(classList.contains('o_tag_left')||classList.contains('o_ribbon_left')){return'left';}
return'right';}}
return this._super(methodName,params);},_toggleEditingUI(state){this.$el.find('[data-name="ribbon_options"]').toggleClass('d-none',state);this.$el.find('[data-name="ribbon_customize_opt"]').toggleClass('d-none',!state);this.$('.o_ribbon:not(.o_wsale_ribbon_dummy)').toggleClass('d-none',state);this.$ribbon.toggleClass('d-none',!state);},_resetRibbonDummy(){if(this.$ribbon){this.$ribbon.remove();}
const $original=this.$('.o_ribbon');this.$ribbon=$original.clone().addClass('d-none o_wsale_ribbon_dummy').appendTo($original.parent());},_onTableMouseEnter:function(ev){$(ev.currentTarget).addClass('oe_hover');},_onTableMouseLeave:function(ev){$(ev.currentTarget).removeClass('oe_hover');},_onTableItemMouseEnter:function(ev){var $td=$(ev.currentTarget);var $table=$td.closest("table");var x=$td.index()+1;var y=$td.parent().index()+1;var tr=[];for(var yi=0;yi<y;yi++){tr.push("tr:eq("+yi+")");}
var $selectTr=$table.find(tr.join(","));var td=[];for(var xi=0;xi<x;xi++){td.push("td:eq("+xi+")");}
var $selectTd=$selectTr.find(td.join(","));$table.find("td").removeClass("select");$selectTd.addClass("select");},_onTableItemClick:function(ev){var $td=$(ev.currentTarget);var x=$td.index()+1;var y=$td.parent().index()+1;this._rpc({route:'/shop/change_size',params:{id:this.productTemplateID,x:x,y:y,},}).then(reload);},});});;

/* /website_sale/static/src/js/website_sale_form_editor.js defined in bundle 'website.assets_editor' */
odoo.define('website_sale.form',function(require){'use strict';var FormEditorRegistry=require('website_form.form_editor_registry');FormEditorRegistry.add('create_customer',{formFields:[{type:'char',modelRequired:true,name:'name',string:'Your Name',},{type:'email',required:true,name:'email',string:'Your Email',},{type:'tel',name:'phone',string:'Phone Number',},{type:'char',name:'company_name',string:'Company Name',}],});});;

/* /website_sale/static/src/js/tours/website_sale_shop_frontend.js defined in bundle 'website.assets_editor' */
odoo.define("website_sale.tour_shop_frontend",function(require){"use strict";var tour=require("web_tour.tour");var steps=require("website_sale.tour_shop");tour.register("shop",{url:"/shop",sequence:130,},steps);});;

/* /website_crm/static/src/js/website_crm_editor.js defined in bundle 'website.assets_editor' */
odoo.define('website_crm.form',function(require){'use strict';var core=require('web.core');var FormEditorRegistry=require('website_form.form_editor_registry');var _t=core._t;FormEditorRegistry.add('create_lead',{formFields:[{type:'char',required:true,name:'contact_name',string:'Your Name',},{type:'tel',name:'phone',string:'Phone Number',},{type:'email',required:true,name:'email_from',string:'Your Email',},{type:'char',required:true,name:'partner_name',string:'Your Company',},{type:'char',modelRequired:true,name:'name',string:'Subject',},{type:'text',required:true,name:'description',string:'Your Question',}],fields:[{name:'team_id',type:'many2one',relation:'crm.team',domain:[['use_opportunities','=',true]],string:_t('Sales Team'),title:_t('Assign leads/opportunities to a sales team.'),},{name:'user_id',type:'many2one',relation:'res.users',domain:[['share','=',false]],string:_t('Salesperson'),title:_t('Assign leads/opportunities to a salesperson.'),}],});});