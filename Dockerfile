# Odoo 14.0 Docker Image
FROM python:3.8-slim-bullseye

# Set environment variables
ENV ODOO_USER=odoo \
    ODOO_HOME=/opt/odoo \
    ODOO_VERSION=14.0 \
    DEBIAN_FRONTEND=noninteractive

# Install system dependencies
RUN apt-get update && apt-get install -y \
    # Core dependencies
    python3-dev \
    libxml2-dev \
    libxslt1-dev \
    libldap2-dev \
    libsasl2-dev \
    libtiff5-dev \
    libjpeg62-turbo-dev \
    libopenjp2-7-dev \
    zlib1g-dev \
    libfreetype6-dev \
    libpq-dev \
    # Additional tools
    wget \
    curl \
    git \
    build-essential \
    # PDF generation
    wkhtmltopdf \
    # Node.js for some addons
    nodejs \
    npm \
    # Cleanup
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Create odoo user
RUN useradd -m -d $ODOO_HOME -s /bin/bash $ODOO_USER

# Set working directory
WORKDIR $ODOO_HOME

# Copy and extract Odoo core
COPY odoo-14.0-core.tar.gz .
RUN tar -xzf odoo-14.0-core.tar.gz && \
    rm odoo-14.0-core.tar.gz

# Copy and extract custom addons
COPY platform-odex25-light.tar.gz .
RUN tar -xzf platform-odex25-light.tar.gz && \
    rm platform-odex25-light.tar.gz

# Copy and extract data directory
COPY prod_data_dir.tar.gz .
RUN tar -xzf prod_data_dir.tar.gz && \
    rm prod_data_dir.tar.gz

# Install Python dependencies
RUN python3 -m pip install --upgrade pip && \
    pip3 install -r $ODOO_HOME/odoo-14.0/requirements.txt

# Copy configuration file
COPY docker-odoo.conf /etc/odoo-server.conf

# Set proper permissions
RUN chown -R $ODOO_USER:$ODOO_USER $ODOO_HOME

# Create directories for logs and additional data
RUN mkdir -p /var/log/odoo && \
    chown $ODOO_USER:$ODOO_USER /var/log/odoo

# Switch to odoo user
USER $ODOO_USER

# Expose ports
EXPOSE 8069 8072

# Set the default command
CMD ["python3", "/opt/odoo/odoo-14.0/odoo-bin", "-c", "/etc/odoo-server.conf"]
