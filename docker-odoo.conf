[options]
# Database settings
db_host = db
db_port = 5432
db_user = odooprod
db_password = odooprod
db_maxconn = 64

# Admin password (change this for production)
admin_passwd = 5XdF*CVsdb2$3%dsei5I$

# Server settings
xmlrpc_port = 8069
longpolling_port = 8072
xmlrpc_interface = 0.0.0.0
netrpc_interface = 0.0.0.0

# Proxy mode for Docker
proxy_mode = True

# Data directory
data_dir = /opt/odoo/prod_data_dir

# Addons path - Updated for Docker container paths
addons_path = /opt/odoo/odoo-14.0/addons,/opt/odoo/odoo-14.0/odoo/addons,/opt/odoo/platform-odex25-light/master/odex25_accounting,/opt/odoo/platform-odex25-light/master/odex25_bi,/opt/odoo/platform-odex25-light/master/odex25_fleet,/opt/odoo/platform-odex25-light/master/odex25_hr,/opt/odoo/platform-odex25-light/master/odex25_project,/opt/odoo/platform-odex25-light/master/odex25_realstate,/opt/odoo/platform-odex25-light/master/odex25_transactions,/opt/odoo/platform-odex25-light/master/odex25_base,/opt/odoo/platform-odex25-light/master/odex25_dms,/opt/odoo/platform-odex25-light/master/odex25_helpdesk,/opt/odoo/platform-odex25-light/master/odex25_maintenance,/opt/odoo/platform-odex25-light/master/odex25_purchase,/opt/odoo/platform-odex25-light/master/odex25_sales,/opt/odoo/platform-odex25-light/master/odex25_website,/opt/odoo/platform-odex25-light/master/openeducat_erp

# Performance settings (adjusted for Docker)
limit_memory_hard = **********
limit_memory_soft = **********
limit_request = 8192
limit_time_cpu = 600
limit_time_real = 1200
workers = 2
max_cron_threads = 1

# Logging
log_level = info
logfile = /var/log/odoo/odoo.log
log_db = False

# Security
list_db = False
