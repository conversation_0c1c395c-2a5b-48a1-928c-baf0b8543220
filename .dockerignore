# Docker ignore file for Odoo project

# Git files
.git
.gitignore

# Docker files (don't copy into container)
Dockerfile
docker-compose.yml
.dockerignore

# Documentation
README.md
*.md

# Logs
*.log
logs/

# Temporary files
*.tmp
*.temp
.DS_Store
Thumbs.db

# IDE files
.vscode/
.idea/
*.swp
*.swo

# Python cache
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
*.so

# Virtual environments
venv/
env/
.env.local

# Node modules (if any)
node_modules/

# Backup files
*.bak
*.backup

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
