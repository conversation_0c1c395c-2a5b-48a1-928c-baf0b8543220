version: '3.8'

services:
  # PostgreSQL Database
  db:
    image: postgres:13
    container_name: odoo_postgres
    environment:
      POSTGRES_DB: odoo_production
      POSTGRES_USER: odooprod
      POSTGRES_PASSWORD: odooprod
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data/pgdata
      - ./init-db:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    restart: unless-stopped
    networks:
      - odoo_network

  # Odoo Application
  odoo:
    build: .
    container_name: odoo_app
    depends_on:
      - db
    environment:
      - HOST=db
      - USER=odooprod
      - PASSWORD=odooprod
    volumes:
      - odoo_data:/opt/odoo/prod_data_dir
      - odoo_logs:/var/log/odoo
      - ./docker-odoo.conf:/etc/odoo-server.conf:ro
    ports:
      - "8069:8069"  # Main Odoo port (mapped from 7981)
      - "8072:8072"  # Longpolling port (mapped from 7982)
    restart: unless-stopped
    networks:
      - odoo_network
    command: ["python3", "/opt/odoo/odoo-14.0/odoo-bin", "-c", "/etc/odoo-server.conf"]

  # pgAdmin (Optional - for database management)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: odoo_pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
      PGADMIN_LISTEN_PORT: 80
    ports:
      - "8080:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    depends_on:
      - db
    restart: unless-stopped
    networks:
      - odoo_network

volumes:
  postgres_data:
    driver: local
  odoo_data:
    driver: local
  odoo_logs:
    driver: local
  pgadmin_data:
    driver: local

networks:
  odoo_network:
    driver: bridge
